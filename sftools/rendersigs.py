import os
import openpyxl
import sys
from selenium import webdriver
import numpy as np
from PIL import Image
import json
import re
import csv

def bbox(im):
    a = np.array(im)[:,:,:3]  # keep RGB only
    m = np.any(a != [255, 255, 255], axis=2)
    coords = np.argwhere(m)
    if not coords.size:
        return (0, 0, im.width, im.height)
    y0, x0, y1, x1 = *np.min(coords, axis=0), *np.max(coords, axis=0)
    return (x0, y0, x1+1, y1+1)


def main(filename: str):
    with open(filename, 'r') as f:
        csvin = csv.DictReader(f)

        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--hide-scrollbars')
        selenium_driver = webdriver.Chrome(options=chrome_options)
        selenium_driver.set_window_size(900,1000)

        count = 0
        os.makedirs('sigs', exist_ok=True)
        for row in csvin:
            # get signature data
            signature_id = row['signature_id']
            html = row['html']
            plain = row['plain']

            if not signature_id or not html:
                continue

            if 'newemails.thereferralrating.com' in html:
                continue

            html = html.replace('/agencyLogos/', 'agencyLogos/')
            html = html.replace('{body}', ' ')
            html = html.replace('{opt_out_link}', ' ')
            stripped_from_name = re.sub(r'[^a-z]', '', row['sigature_name'].lower().strip())

            # save metadata
            with open(f"sigs/{signature_id}.html", "w", encoding="utf-8") as f:
                f.write(html)
            with open(f"sigs/{signature_id}.txt", "w", encoding="utf-8") as f:
                f.write(plain)
            with open(f"sigs/{signature_id}.json", "w", encoding="utf-8") as f:
                f.write(json.dumps({"signature_id": signature_id,
                                    'html': html,
                                    'plain': plain,
                                    'stripped_from_name': stripped_from_name}))

            # get raw screenshot
            htmlpath = os.path.abspath(f"sigs/{signature_id}.html")
            selenium_driver.get(f"file://{htmlpath}")
            selenium_driver.save_screenshot(f"sigs/{signature_id}.raw.png")

            # crop and save it!
            im = Image.open(f"sigs/{signature_id}.raw.png")
            box = bbox(im)
            im2 = im.crop(box)
            im2.save(f"sigs/{signature_id}.png")

            count += 1
            if (count % 100) == 0:
                print(count)


if __name__ == "__main__":
    filename = sys.argv[1]
    main(filename)
