import argparse
from datetime import datetime, date
from bson import ObjectId
from simple_salesforce import Salesforce, format_soql
from lib import sfapi, portaldbapi
from lib.settings import settings


SG_EVENT_TO_SF_FIELD_MAP = {
    'delivered': {
        'live_survey_participant': 'Survey_Email_Delivered__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Delivered__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Delivered__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Delivered__c',
    },
    'open': {
        'live_survey_participant': 'Survey_Email_Opened__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Opened__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Opened__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Opened__c',
    },
    'click': {
        'live_survey_participant': 'Survey_Email_Clicked__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Clicked__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Clicked__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Clicked__c',
    },
    'bounce': {
        'live_survey_participant': 'Survey_Email_Bounced__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Bounced__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Bounced__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Bounced__c',
    },
}


def get_sg_events_by_panel_member(collection, survey_panel_member_id:str|None = None) -> dict[str, list]:
    all_collection_records_ids:list[ObjectId] = []
    sg_events_by_panel_member:dict[str, list] = {}

    # build the query to get the SG events
    # we can skip `machine_open` events
    query:dict = {
        'sg_machine_open': {'$ne': True}
    }
    if survey_panel_member_id:
        query['survey_panel_member_id'] = survey_panel_member_id

    # grab all the SG events from portaldb (and group them by panel member)
    for event in collection.find(query):
        all_collection_records_ids.append(event.get('_id'))
        panel_member_id:str = event.get('survey_panel_member_id')
        sg_events_by_panel_member.setdefault(panel_member_id, []).append(event)

    # get earliest record 
    earliest_event:datetime = min(all_collection_records_ids, key=lambda oid: oid.generation_time)
    earliest_event:date = earliest_event.generation_time.date()
    
    return sg_events_by_panel_member, earliest_event


def get_qualifying_sf_survey_panel_members(sf:Salesforce, survey_panel_member_id:str|None, earliest_event_datetime:datetime) -> list[str]:
    survey_panel_members_ids:list[str] = []

    soql:str = '''
        WHERE Survey_Client__r.Current_Round__c = True
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c >= {earliest_event_datetime}
    '''
    if survey_panel_member_id:
        soql = soql + " AND Id = {survey_panel_member_id}"

    query:str = format_soql(soql, earliest_event_datetime=earliest_event_datetime, survey_panel_member_id=survey_panel_member_id)
        
    for spm in sfapi.get_all(sf, sfapi.SurveyPanelMember, bulk=True, query_suffix=query):
        survey_panel_members_ids.append(spm.Id)

    return survey_panel_members_ids


def main(survey_panel_member_id:str|None = None, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SENDGRID_EVENTS_COLLECTION]

    batch:list[dict] = []

    # get the SG events by panel member
    sg_events_by_panel_member, earliest_event_datetime = get_sg_events_by_panel_member(collection, survey_panel_member_id)

    # get all survey panel members from SF that started the survey after gathering SG events
    sf_survey_panel_members_ids = get_qualifying_sf_survey_panel_members(sf, survey_panel_member_id, earliest_event_datetime)

    # iterate over the events per panel member and build up the required updates
    for panel_member_id, sg_events in sg_events_by_panel_member.items():
        # if the panel member is not in the SF list, skip it
        if panel_member_id not in sf_survey_panel_members_ids:
            continue

        pm_update:dict[str, str|bool] = {
            'Id': panel_member_id,
            'Survey_Email_Delivered__c': False,
            'Survey_Email_Opened__c': False,
            'Survey_Email_Clicked__c': False,
            'Survey_Email_Bounced__c': False,
            'Survey_Email_Reminder_1_Delivered__c': False,
            'Survey_Email_Reminder_1_Opened__c': False,
            'Survey_Email_Reminder_1_Clicked__c': False,
            'Survey_Email_Reminder_1_Bounced__c': False,
            'Survey_Email_Reminder_2_Delivered__c': False,
            'Survey_Email_Reminder_2_Opened__c': False,
            'Survey_Email_Reminder_2_Clicked__c': False,
            'Survey_Email_Reminder_2_Bounced__c': False,
            'Survey_Email_Reminder_3_Delivered__c': False,
            'Survey_Email_Reminder_3_Opened__c': False,
            'Survey_Email_Reminder_3_Clicked__c': False,
            'Survey_Email_Reminder_3_Bounced__c': False,
        }

        for event in sg_events:
            event_type:str = event['event']
            template:str = event['template']

            # if the event type is not one we track, skip it
            if event_type not in SG_EVENT_TO_SF_FIELD_MAP:
                continue

            # if the template is not one we track, skip it
            if template not in SG_EVENT_TO_SF_FIELD_MAP[event_type]:
                continue

            # get the SF field name to update
            sf_field_name = SG_EVENT_TO_SF_FIELD_MAP[event_type][template]

            pm_update[sf_field_name] = True
        
        batch.append(pm_update)

    if writeit:
        sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_panel_member_id', help='ID of the SPM to process')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the SF')
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.writeit)