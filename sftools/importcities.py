import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
from lib import sfimport


def main(filename, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Market))
    print("DONE POPULATING CACHE")

    markets_by_name = {}
    for o in sfimport.sfcache.values():
        if isinstance(o, sfapi.Market):
            markets_by_name[o.Name] = o

    # load data from the CSV file
    with open(filename) as file:
        # we read in all rows, then sort by date descending so the most recent values are the ones take priority
        reader = csv.DictReader(file)
        for row in reader:

            office_name = row['office_name'].strip()
            market_name = row['market_name'].strip()

            market = markets_by_name.get(market_name)
            if not market:
                print("Market not found: %s" % market_name)
                continue
            if market.MarketType != 'Country':
                print("Market is not a country: %s" % market_name)
                continue

            city = markets_by_name.get(office_name)
            if city:
                continue

            sfimport.get_or_create_cached_market(office_name, market, 'City')

    for v in sfimport.new_markets:
        print(v)

    if writeit:
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_markets, sfapi.Market)



if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    args = ap.parse_args()

    main(args.filename, args.writeit)
