import argparse
from pymongo import UpdateOne
from lib import portaldbapi


def extract_contact_ids(panel):
    return set(contact["contact_id"] for contact in panel)


def main(survey_client_id: str|None = None, writeit: bool = False):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    batch = []

    query = {}
    if survey_client_id:
        query = {'Id': survey_client_id}

    projection = {
        '_id': 1,
        'Id': 1,
        'account_name': 1,
        'client_name': 1,
        'confirmed_panel': 1,
    }

    # get all confirm panels from Mongo
    confirm_panels = collection.find(query, projection)

    for cp in confirm_panels:
        confirmed_panel = cp.get('confirmed_panel', [])

        # if this confirm panel has no user confirmations, just skip it - we have nothing to do
        if len(confirmed_panel) == 0:
            print(f'>> Skipping confirmed panel {cp["Id"]} as it has no confirmed panel data')
            continue

        for i in range(len(confirmed_panel)):
            current_panel = confirmed_panel[i].get('panel', [])
            current_ids = extract_contact_ids(current_panel)

            # Compare with previous panel if exists
            if i + 1 < len(confirmed_panel):
                previous_panel = confirmed_panel[i + 1].get('panel', [])
                previous_ids = extract_contact_ids(previous_panel)

                added_ids = current_ids - previous_ids
                removed_ids = previous_ids - current_ids

                # Get full dicts for added and removed
                added = [
                    {'id': c['contact_id'], 'name': c['contact_name'], 'account': cp['account_name'], 'client': cp['client_name']} 
                    for c in current_panel 
                    if c['contact_id'] in added_ids
                ]
                removed = [
                    {'id': c['contact_id'], 'name': c['contact_name'], 'account': cp['account_name'], 'client': cp['client_name']} 
                    for c in previous_panel 
                    if c['contact_id'] in removed_ids
                ]
            else:
                # No previous panel to compare to
                added = [
                    {'id': c['contact_id'], 'name': c['contact_name'], 'account': cp['account_name'], 'client': cp['client_name']} 
                    for c in current_panel
                ]
                removed = []

            confirmed_panel[i]['stats'] = {
                'added': added,
                'removed': removed
            }
        
        # add to batch for updating
        batch.append(UpdateOne(
            {'_id': cp['_id']}, 
            {'$set': {'confirmed_panel': confirmed_panel}}
        ))
        
    # write to Mongo
    if writeit:
        collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_client_id', default=None, help='single survey client to run for')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.survey_client_id, args.writeit)