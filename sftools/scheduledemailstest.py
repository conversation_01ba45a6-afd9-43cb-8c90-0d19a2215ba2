import csv
from lib import portaldbapi


def main():
    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)

    scheduled_emails = set()

    query:dict = {
        "scheduled_date_local": {
            "$regex": "^2025-03-11T"
        },
        "deleted": {"$exists": False}
    }
    projection:dict = {
        '_id': 0,
        'contact_id': 1
    }

    for doc in collection.find(query, projection):
        scheduled_emails.add(doc['contact_id'])

    with open('blah.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=['contact_id'])
        csvout.writeheader()

        for item in scheduled_emails:        
            csvout.writerow({
                'contact_id': item,
            })


if __name__ == "__main__":
    main()
