import boto3
import os
import io
import zipfile
import datetime
import re
import argparse
from lib.settings import settings


def find_files_in_range(client, datefrom: datetime.datetime, dateto: datetime.datetime):
    paginator = client.get_paginator('list_objects_v2')

    d = datefrom
    while d <= dateto:
        path = d.strftime('crc-sf/yy=%Y/mm=%m/dd=%d')

        for result in paginator.paginate(Bucket=settings.SF_DATA_BUCKET, Prefix=path):
            for key in result.get('Contents', []):
                yield key['Key']

        d += datetime.timedelta(days=1)


def find_matching_files(client, key, filename, skip_content=False):
    if os.path.isfile(f"sfdumps/{key}"):
        with open(f"sfdumps/{key}", "rb") as f:
            data = io.BytesIO(f.read())

    else:
        obj = client.get_object(Bucket=settings.SF_DATA_BUCKET, Key=key)
        data = io.BytesIO(obj['Body'].read())

        # cache it locally
        dirname, _ = os.path.split(key)
        os.makedirs(f"sfdumps/{dirname}", exist_ok=True)
        with open(f"sfdumps/{key}", "wb") as f:
            f.write(data.getbuffer())

    with zipfile.ZipFile(data, 'r') as zip_ref:
        for zip_filename in zip_ref.namelist():
            if not filename or zip_filename == filename:
                if skip_content:
                    yield zip_filename, None

                with zip_ref.open(zip_filename) as extracted_file:
                    yield zip_filename, extracted_file


def main(startdate, enddate, filename_for, grep_for):
    client = boto3.client('s3')
    grep_for_re = re.compile(grep_for) if grep_for else None
    datetime_re = re.compile(".*/crc-sf-(.*)-(.*).zip$")

    printed_header = False
    for s3_key in find_files_in_range(client, startdate, enddate):
        dtbits = datetime_re.match(s3_key)
        if dtbits:
            d = dtbits.group(1)
            t = dtbits.group(2)
            s3_key_stamp = datetime.datetime.strptime(f"{d} {t}", "%Y%m%d %H%M%S")
        else:
            print(f"Failed to parse datetime from s3 key {s3_key}")
            continue

        for zip_filename, f in find_matching_files(client, s3_key, filename_for):
            header = None
            for row in f.read().decode('utf-8').split('\n'):
                if not header:
                    header = row
                    header = f"zip_stamp,zip_filename,{header}"
                    continue

                if grep_for_re is None or grep_for_re.search(row):
                    if not printed_header:
                        print(header)
                        printed_header = True

                    row = f"{s3_key_stamp.isoformat()},{zip_filename},{row}"
                    print(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('startdate', type=str, help="start date in the format YYYY-MM-DD")
    ap.add_argument('enddate', type=str, help="end date in the format YYYY-MM-DD (inclusive)")
    ap.add_argument('filename_for', type=str, help="filename to search for, use empty string to search all files")
    ap.add_argument('grep_for', type=str, help="regex to search for in the matched file content")
    args = ap.parse_args()

    startdate = datetime.datetime.strptime(args.startdate, "%Y-%m-%d")
    enddate = datetime.datetime.strptime(args.enddate, "%Y-%m-%d")

    main(startdate, enddate, args.filename_for, args.grep_for)
