import boto3
import datetime
import argparse
import requests
import csv


def main(appid, domain, startdate, enddate):
    client = boto3.client('amplify', region_name='eu-west-1')

    d = startdate
    while d <= enddate:
        print(d)
        response = client.generate_access_logs(startTime=d,
                                               endTime=d + datetime.timedelta(hours=1),
                                               domainName=domain,
                                               appId=appid)
        logurl = response['logUrl']

        r = requests.get(logurl)
        r.raise_for_status()
        with open(f'amplify-{appid}-{domain}-{d.isoformat().replace(':', '')}.csv', 'wb') as f:
            for chunk in r.iter_content(chunk_size=8192):
                f.write(chunk)

        d += datetime.timedelta(hours=1)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("appid", help="appid")
    ap.add_argument("domain", help="domain")
    ap.add_argument("startdate", help="Start date")
    ap.add_argument("enddate", help="End date (inclusive)")
    args = ap.parse_args()

    startdate = datetime.datetime.strptime(args.startdate, '%Y-%m-%d')
    enddate = datetime.datetime.strptime(args.enddate, '%Y-%m-%d') + datetime.timedelta(days=1)

    main(args.appid, args.domain, startdate, enddate)
