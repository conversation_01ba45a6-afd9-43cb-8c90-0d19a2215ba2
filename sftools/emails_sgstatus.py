import argparse
from lib.settings import settings
from sendgrid import SendGridAPIClient
import json
import requests
import csv
import time
import sys


def query_sendgrid(sg, email):
    query = f"""
to_email="{email}"
"""

    # fetch all the emails from SG
    emails = sg.client.messages.get(
        query_params={
            'query': query
        }
    )
    if emails.status_code != 200:
        raise Exception(f"Error: {emails.status_code}")
    body = json.loads(emails.body)

    detailed_messages = []
    for msg in body['messages']:
        response = requests.get(f'{sg.host}/v3/messages/{msg["msg_id"]}', headers={'Authorization': f'Bearer {sg.api_key}'})
        response.raise_for_status()
        detailed_messages.append(response.json())

    print("---------------------------------------------------------")
    print(email)
    for msg in detailed_messages:
        events = msg.pop('events')

        print()
        print(json.dumps(msg))
        for event in events:
            print("   ", json.dumps(event))

    sys.stdout.flush()


# def persist_seen_emails(seenemails):
#     with open("seenemails.json", "w") as f:
#         f.write(json.dumps(list(seenemails)))


# def restore_seen_emails():
#     try:
#         with open("seenemails.json") as f:
#             return set(json.loads(f.read()))
#     except FileNotFoundError:
#         return set()


def main(email, csvfilename):
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    if email:
        query_sendgrid(sg, email)

    if csvfilename:
        with open(csvfilename) as f:
            csvin = csv.DictReader(f)
            for row in csvin:
                email = row['email']

                while True:
                    try:
                        query_sendgrid(sg, email)
                        break
                    except Exception as e:
                        print(e, file=sys.stderr)
                        print("Retrying...", file=sys.stderr)
                        time.sleep(30)
                        continue


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--email")
    ap.add_argument("--csv")
    args = ap.parse_args()

    main(args.email, args.csv)

