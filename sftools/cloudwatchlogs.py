import boto3
import datetime
import argparse
import csv

def iter_log_streams(client, log_group_name, startdate, enddate):
    print("Finding log streams, please wait...")

    nextToken = True
    while nextToken:
        if nextToken is True:
            response = client.describe_log_streams(
                logGroupName=log_group_name,
                orderBy='LogStreamName',
            )
        else:
            response = client.describe_log_streams(
                logGroupName=log_group_name,
                orderBy='LogStreamName',
                nextToken=nextToken
            )
        nextToken = response.get('nextToken')

        for stream in response['logStreams']:
            firstEventTimestamp = datetime.datetime.fromtimestamp(stream['firstEventTimestamp'] / 1000, tz=datetime.timezone.utc)
            lastEventTimestamp = datetime.datetime.fromtimestamp(stream['lastEventTimestamp'] / 1000, tz=datetime.timezone.utc)
            if firstEventTimestamp > enddate or lastEventTimestamp < startdate:
                continue

            yield stream


def iter_log_events(client, log_group_name, log_stream_name, startdate, enddate):
    startdate_ts = int(startdate.timestamp() * 1000)
    enddate_ts = int(enddate.timestamp() * 1000)

    nextToken = None
    while True:
        if nextToken is None:
            response = client.get_log_events(
                logGroupName=log_group_name,
                logStreamName=log_stream_name,
                startTime=startdate_ts,
                endTime=enddate_ts,
                startFromHead=True
            )
        else:
            response = client.get_log_events(
                logGroupName=log_group_name,
                logStreamName=log_stream_name,
                startTime=startdate_ts,
                endTime=enddate_ts,
                startFromHead=True,
                nextToken=nextToken
            )
        newNextToken = response.get('nextForwardToken')
        if newNextToken == nextToken:
            break
        nextToken = newNextToken

        for event in response['events']:
            yield event


def main(log_group_name, startdate, enddate):
    client = boto3.client('logs', region_name='eu-west-1')

    date_logs = {}

    log_streams = list(iter_log_streams(client, log_group_name, startdate, enddate))
    log_streams = sorted(log_streams, key=lambda x: x['firstEventTimestamp'])
    for log_stream in log_streams:
        firstEventTimestamp = datetime.datetime.fromtimestamp(log_stream['firstEventTimestamp'] / 1000)
        print(log_stream['logStreamName'], firstEventTimestamp)

        for log_event in iter_log_events(client, log_group_name, log_stream['logStreamName'], startdate, enddate):
            log_event['timestamp'] = datetime.datetime.fromtimestamp(log_event['timestamp'] / 1000)
            log_event['message'] = log_event['message'].strip()
            log_event.pop('ingestionTime', None)
            date = log_event['timestamp'].strftime('%Y-%m-%d')

            if date not in date_logs:
                date_logfile = open(f'log-{date}.csv', 'w')
                date_logcsv = csv.DictWriter(date_logfile, fieldnames=['timestamp', 'message'])
                date_logs[date] = (date_logfile, date_logcsv)

            logfile, logcsv = date_logs[date]
            logcsv.writerow(log_event)

    for logfile, logcsv in date_logs.values():
        logfile.flush()
        logfile.close()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("log_group", help="Log group name")
    ap.add_argument("startdate", help="Start date")
    ap.add_argument("enddate", help="End date (inclusive)")
    args = ap.parse_args()

    startdate = datetime.datetime.strptime(args.startdate, '%Y-%m-%d').replace(tzinfo=datetime.timezone.utc)
    enddate = datetime.datetime.strptime(args.enddate, '%Y-%m-%d').replace(tzinfo=datetime.timezone.utc) + datetime.timedelta(days=1)

    main(args.log_group, startdate, enddate)
