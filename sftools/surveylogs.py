import re
import boto3
import time
import argparse
import csv
from datetime import datetime
from lib import sfapi
from lib.settings import settings
from simple_salesforce import format_soql



survey_id_regex = r"GET /survey/(\d+)"
log_group_name = '/aws/lambda/crc-api-survey-prod'
filter_pattern = "survey"
success = ["200"]
error = ["400", "401", "403", "404", "405", "409", "429", "500", "502", "503", "504"]


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # Initialize the CloudWatch Logs client
    client = boto3.client('logs')

    # Convert the start date to a Unix timestamp in milliseconds
    start_time = datetime(2024, 10, 21, 0, 0, 0)
    start_time = int(time.mktime(start_time.timetuple()))

    # End time is the current time (in milliseconds)
    end_time = int(time.time() * 1000)

    valid_log_entries = []
    unique_survey_ids = set()

    print('start: fetching logs')

    # Fetch log events using filter_log_events method
    response = client.filter_log_events(
        logGroupName=log_group_name,
        startTime=start_time,
        endTime=end_time,
        filterPattern=filter_pattern,
        limit=50
    )

    while 'nextToken' in response:
        next_token = response['nextToken']
        response = client.filter_log_events(
            logGroupName=log_group_name,
            startTime=start_time,
            filterPattern=filter_pattern,
            endTime=end_time,
            limit=50,
            nextToken=next_token
        )
    
        events = response.get('events', [])
        
        for event in events:
            timestamp = event['timestamp']

            message = event['message']
            message = message.strip()
            message = [line for line in message.splitlines() if line.strip() != '']
            message = ''.join(message)

            if "GET /survey/" in message and any(message.endswith(code) for code in success):
                survey_id = re.search(r'/survey/([a-zA-Z0-9]+)', message)
                if survey_id:
                    unique_survey_ids.add(survey_id.group(1))
                    valid_log_entries.append({
                        'timestamp': timestamp,
                        'survey_id': survey_id.group(1)
                    })
                    #print(f"Timestamp: {event['timestamp']}, Survey ID: {survey_id.group(1)}")
    
    print('end: fetching logs')

    survey_details_lookup = {}

    print('start: fetching sf data')

    # Get all survey panel members from SF
    soql = """
        SELECT Id,
               Has_Responded__c,
               SurveyJsId__c,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.Name,
               (SELECT Id,
                       Response_Date_Time__c
            FROM Survey_Responses__r)
        FROM Survey_Panel_Member__c
    """
    query = format_soql(soql)
    for row in sf.query_all_iter(query):
        if not row:
            continue
        if row['SurveyJsId__c'] not in survey_details_lookup:
            survey_details_lookup[row['SurveyJsId__c']] = []
        survey_details_lookup[row['SurveyJsId__c']].append(row)

    print('end: fetching sf data')

    print('start: build csv')

    # Look round log entries and add to csv
    with open('surveyaccesslogs.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=['salesforce_survey_panel_member_id', 'salesforce_contact_id', 'email', 'name', 'survey_id', 'access_time', 'responded_to_survey', 'salesforce_link'])
        csvout.writeheader()

        for log in valid_log_entries:
            survey = survey_details_lookup.get(log['survey_id'], [])

            if len(survey) == 0:
                continue

            panel_member_ids = '|'.join([pm.get('Id') for pm in survey])
            #survey_response = survey[0].get('records', [{}])[0] or {} 

            timestamp_in_seconds = log['timestamp'] / 1000
            dt = datetime.fromtimestamp(timestamp_in_seconds)
            formatted_dt = dt.strftime('%Y-%m-%d %H:%M:%S')
        
            csvout.writerow({
                'salesforce_survey_panel_member_id': panel_member_ids,
                'salesforce_contact_id': survey[0].get('Contact__r', {}).get('Id'),
                'email': survey[0].get('Contact__r', {}).get('Email'),
                'name': survey[0].get('Contact__r', {}).get('Name'),
                'survey_id': survey[0].get('SurveyJsId__c'),
                'access_time': formatted_dt,
                'responded_to_survey': survey[0].get('Has_Responded__c'),
                #'responded_to_survey_time': survey_response.get('Response_Date_Time__c'),
                'salesforce_link': f'https://clientrelationship.lightning.force.com/lightning/r/Contact/{survey[0].get('Contact__r', {}).get('Id')}/view',
            })
    
    print('end: build csv')


def lambda_handler(event, _):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
