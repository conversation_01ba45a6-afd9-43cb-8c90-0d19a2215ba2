import csv
import datetime
import argparse
from lib import portaldbapi
from pymongo import UpdateOne


UNIQUE_FIELD = "norm_key"
FIELDS = [] 


def main(csv_path, survey_type, writeit=False, skip_existing=False):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.norms

    norm_keys = collection.find({}, {UNIQUE_FIELD: 1})
    norm_keys = [norm_key[UNIQUE_FIELD] for norm_key in norm_keys]

    batch = []
    sync_date = datetime.datetime.now(tz=datetime.UTC)

    print("Processing CSV file...")
    with open(csv_path, newline='') as csvfile:
        reader = csv.DictReader(csvfile)

        for row in reader:
            row['survey_type'] = survey_type
            row['_lastsynced'] = sync_date
            print(f"norm_key: {row['norm_key']}")
            # Check if a record with the same unique field already exists
            if not skip_existing or (skip_existing and row[UNIQUE_FIELD] not in norm_keys):
                batch.append(UpdateOne({'norm_key': row['norm_key'], 'survey_type': row['survey_type']}, {'$set': row}, upsert=True))
            else:
                print(f"Skipping duplicate record with {UNIQUE_FIELD} = {row[UNIQUE_FIELD]}")
        
    if batch and writeit:
        print(f"writing {len(batch)} norms documents")
        collection.bulk_write(batch)
        print(f"wrote {len(batch)} norms documents")
    else:
        print(f"Dry run, skipping writing {len(batch)} norms documents")
    

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('csv_path', type=str, help='Path to the CSV file')
    parser.add_argument('survey_type', type=str, choices=['trr', 'barometer'], help='The type of survey the norm data relates to (trr or barometer)')
    parser.add_argument("--writeit", action="store_true", help="Actually write to the database")

    args = parser.parse_args()

    main(args.csv_path, args.survey_type, args.writeit)

