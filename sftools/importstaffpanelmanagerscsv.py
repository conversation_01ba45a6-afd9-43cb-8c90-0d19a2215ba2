import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy
from sftools.importsurveycsv import load_customer_client_name_mapping

WHICH_CUSTOMER_SURVEY = 0   # 0: latest, -1: prior

def find_leaf_accounts(forest_root: dict, account_forest_by_id: dict) -> list[sfapi.Account]:
    result = []
    if not forest_root.get('children'):
        result.append(forest_root['account'])

    else:
        for child in forest_root['children']:
            result.extend(find_leaf_accounts(account_forest_by_id[child.Id], account_forest_by_id))

    return result


def get_market_tree_children(markets: dict[str, sfapi.Market], parent: sfapi.Market):
    result = {}

    for item in markets.values():
        if item.ParentMarketId == parent.Id:
            result[item.Id] = item
            result.update(get_market_tree_children(markets, item))

    return result


def import_csv(accounts_filename, importtype, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    if importtype == 'advertising':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_ADVERTISING_STAFF
        organisational_levels = sfimport.ORGANISATION_LEVELS_ADVERTISING
        market_column_name = 'market'
        office_market_column_name = 'office'
    elif importtype == 'manufacturing':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_MANUFACTURING_STAFF
        organisational_levels = sfimport.ORGANISATION_LEVELS_MANUFACTURING
        market_column_name = 'country market'
        office_market_column_name = 'site'
    else:
        raise Exception(f'Invalid import type {importtype}')
    customer_client_name_mapping = load_customer_client_name_mapping()

    agency_dev_prefix = os.environ.get('AGENCY_DEV_PREFIX')
    if agency_dev_prefix:
        agency_dev_prefix = agency_dev_prefix.strip()

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Contact', 'Market', 'CustomerSurvey', 'SurveyClient', 'SurveyPanelManager', 'Team']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject, bulk=False))
    print("Cache populated")

    # generate account forest
    root_accounts, account_forest_by_id, account_forest_by_name = sfimport.generate_account_forest()

    # find all the accounts in salesforce
    all_accounts_by_id: dict[str, sfapi.Account] = {}
    all_accounts_by_name: dict[str, sfapi.Account] = {}
    all_customer_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        all_accounts_by_id[account.Id] = account
        all_accounts_by_name.setdefault(account.Name, []).append(account)
        if account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            all_customer_client_accounts_by_name.setdefault(account.Name, []).append(account)

    # find all the teams in salesforce
    all_teams_by_name_and_account: dict[str, sfapi.Team] = {}
    for team in sfimport.sfcache.values():
        if not isinstance(team, sfapi.Team):
            continue
        all_teams_by_name_and_account[f"{team.Name}-{team.AccountId}"] = team

    # find all the markets in salesforce
    all_markets_by_name_and_type: dict[str, sfapi.Market] = {}
    all_markets_by_id: dict[str, sfapi.Market] = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        all_markets_by_name_and_type[f'{market.Name}-{market.MarketType}'] = market
        all_markets_by_id[market.Id] = market

    # find all the contact by email
    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        if not isinstance(contact, sfapi.Contact):
            continue
        all_contacts_by_email[contact.Email.lower()] = contact

    # find all the customer surveys by id
    all_customer_surveys_by_id: dict[str, sfapi.CustomerSurvey] = {}
    for customer_survey in sfimport.sfcache.values():
        if not isinstance(customer_survey, sfapi.CustomerSurvey):
            continue
        all_customer_surveys_by_id[customer_survey.Id] = customer_survey

    # find all the survey clients by customer survey id. sort them by time order descending
    all_survey_clients_by_agency_id_and_team_id_and_customer_client_id: dict[str, list[sfapi.SurveyClient]] = {}
    for survey_client in sfimport.sfcache.values():
        if not isinstance(survey_client, sfapi.SurveyClient):
            continue
        customer_survey = all_customer_surveys_by_id[survey_client.CustomerSurveyId]

        all_survey_clients_by_agency_id_and_team_id_and_customer_client_id.setdefault(customer_survey.CustomerId, {}).setdefault(customer_survey.TeamId, {}).setdefault(survey_client.CustomersClientId, []).append((customer_survey, survey_client))
    for agencyid, teams_and_cc_and_survey_clients in all_survey_clients_by_agency_id_and_team_id_and_customer_client_id.items():
        for teamid, cc_and_survey_clients in teams_and_cc_and_survey_clients.items():
            for customer_client_id, survey_clients in cc_and_survey_clients.items():
                survey_clients.sort(key=lambda x: x[0].LiveSurveyStartDate, reverse=True)

    # load data from the CSV file
    duffcount = 0
    with open(accounts_filename) as file:
        reader = csv.reader(file)

        # figure out the header row
        header_row_lookup = {}
        header_row = []
        for col_idx, v in enumerate(next(reader)):
            if not v:
                continue
            v = v.strip().lower()
            header_row.append(v)
            header_row_lookup[v] = col_idx

        # load all the rows into ram and convert them into dicts + enumerate them
        all_rows = []
        for i, row in enumerate(reader):
            outrow = {}
            emptycount = 0
            for k, colidx in header_row_lookup.items():
                if colidx >= len(row):
                    v = ''
                else:
                    v = row[colidx]

                outrow[k] = v
                if not v:
                    emptycount += 1

            if emptycount >= len(outrow):
                continue

            outrow['row_number'] = i + 1
            all_rows.append(outrow)

        # file to contain duff records
        duff_file = open('duffrecords-staff.csv', 'w')
        duff_header_row = copy.copy(header_row)
        for col in ['row_number', 'reason', 'customer_hierarchy', 'unsafe_email_address', 'sf_market', 'sf_office_market']:
            if col not in duff_header_row:
                duff_header_row.append(col)
        duff_writer = csv.DictWriter(duff_file, fieldnames=duff_header_row)
        duff_writer.writeheader()

        # preprocess each row and compute various things
        contact_customer_hierachies = {}
        for row in all_rows:
            row['reason'] = None

            # find the market if there is one
            market_name = row[market_column_name].strip()
            if market_name:
                sf_market = all_markets_by_name_and_type.get(f'{market_name}-Country')
                if not sf_market:
                    row['reason'] = f'UNKNOWN MARKET'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
            else:
                sf_market = None

            # =======================================================
            # decode the office market information
            office_market_name = row[office_market_column_name].strip()
            if office_market_name:
                office_market_name = sfimport.MARKET_MAP.get(f'{office_market_name}-City', office_market_name)

                sf_office_market = all_markets_by_name_and_type.get(f'{office_market_name}-City')
                if not sf_office_market:
                    row['reason']= 'INVALID OFFICE MARKET'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                if sf_office_market.MarketType != 'City':
                    row['reason']= f'INVALID OFFICE MARKET TYPE {sf_office_market.MarketType} (NOT CITY)'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
            else:
                sf_office_market = None

            if sf_office_market and not sf_market:
                row['reason'] = 'OFFICE MARKET WITHOUT A MARKET'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            # compute the account hierarchy with the correct names
            agency_account = None
            customer_hierarchy: list[sfapi.Account] = []
            had_agency_account_lookup_error = False
            for colname, organisational_level in organisational_level_fields:
                v = row[colname].strip()
                if v and agency_dev_prefix:
                    v = f'{agency_dev_prefix} {v}'
                if not v:
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(v, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    row['reason'] = f'MISSING AGENCY ACCOUNT {v}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    duffcount += 1
                    break
                if agency_account.OrganisationalLevel != organisational_level:
                    row['reason'] = f'UNEXPECTED AGENCY ACCOUNT LEVEL {v} {organisational_level} != {agency_account.OrganisationalLevel}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    duffcount += 1
                    break

                customer_hierarchy.append(agency_account)
            if had_agency_account_lookup_error:
                continue

            if not customer_hierarchy:
                row['reason'] = 'NO AGENCY ACCOUNTS'
                duff_writer.writerow(row)
                duffcount += 1
                continue
            leaf_agency_name = customer_hierarchy[-1].Name

            # make up the with market level
            if sf_market:
                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(f"{leaf_agency_name} {sf_market.Name}", agency_account, accountRecordTypes[crc_customer_record_type]))
                if agency_account:
                    customer_hierarchy.append(agency_account)

            # finally, add on the office level
            if sf_office_market:
                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(f"{leaf_agency_name} {sf_office_market.Name}", agency_account, accountRecordTypes[crc_customer_record_type]))
                if agency_account:
                    customer_hierarchy.append(agency_account)

            # stash stuff for later
            row['sf_market'] = sf_market
            row['sf_office_market'] = sf_office_market
            row['customer_hierarchy'] = customer_hierarchy
            row['unsafe_email_address'] = unsafe_email_address = row['email address'].lower().strip().strip(';').strip(',')

            # get list of hierarchies for each contact/email. convert them into dicts so its easy to compare
            customer_hierarchy_dict = {x.OrganisationalLevel: x for x in customer_hierarchy}
            contact_customer_hierachies.setdefault(unsafe_email_address, []).append(customer_hierarchy_dict)

        # for each contact, figure out attachment point within the organisation hierarchy..
        # this is the lowest common level in the hierarchy
        contact_agency_account_by_unsafe_email = {}
        for unsafe_email_address, email_customer_hierachies in contact_customer_hierachies.items():
            contact_common_level = None
            for colname in organisational_levels:
                level_values = set([h.get(colname).Name if h.get(colname) else None for h in email_customer_hierachies])
                if len(level_values) == 1:
                    if level_values.pop() is not None:
                        contact_common_level = colname
                else:
                    break

            if not contact_common_level:
                print(unsafe_email_address, 'NO COMMON LEVEL')
                exit()
            contact_agency_account_by_unsafe_email[unsafe_email_address] = email_customer_hierachies[0][contact_common_level]

        # finally, its the main row processing loop!
        contacts_to_patch = {}
        for row in all_rows:
            # row already has an error of some sort
            if row.get('reason'):
                continue

            sf_market = row['sf_market']
            sf_office_market = row['sf_office_market']

            # =======================================================
            # contact details
            unsafe_email_address = row['unsafe_email_address']
            safe_email_address = sfimport.make_safe_email(unsafe_email_address)
            first_name = row['first name'].strip()
            last_name = row['last name'].strip()
            job_title = row['job title'].strip()

            # check contact details are valid
            if not first_name or not last_name or not unsafe_email_address:
                row['reason']= 'MISSING NAME OR EMAIL'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            # =======================================================
            # get stuff from customer hierarchy
            customer_hierarchy = row['customer_hierarchy']
            contact_agency_account = contact_agency_account_by_unsafe_email[unsafe_email_address]
            agency_account = customer_hierarchy[-1]

            # =======================================================
            # figure out the customer client account
            customer_client_account_name = row['account'].strip()
            if not customer_client_account_name:
                row['reason']= 'MISSING CUSTOMER CLIENT ACCOUNT'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            customer_client_account_name = customer_client_name_mapping.get(customer_client_account_name, {customer_client_account_name})
            customer_client_account_name = list(customer_client_account_name)[0]

            customer_client_account = all_customer_client_accounts_by_name.get(str(customer_client_account_name))
            if not customer_client_account:
                row['reason']= f'UNKNOWN CUSTOMER CLIENT ACCOUNT: {customer_client_account_name}'
                duff_writer.writerow(row)
                duffcount += 1
                continue
            if len(customer_client_account) > 1:
                row['reason']= f'MULTIPLE CUSTOMER CLIENT ACCOUNTS: {customer_client_account_name}'
                duff_writer.writerow(row)
                duffcount += 1
                continue
            customer_client_account = customer_client_account[0]

            # =======================================================
            # figure out the team
            team_name = row['team name'].strip()

            # =======================================================
            # find the contact
            contact_email = safe_email_address if safeemails else unsafe_email_address

            # =======================================================
            # create contact and attach to contact's agency account
            contact_email = safe_email_address if safeemails else unsafe_email_address
            contact = sfimport.get_or_create_cached_contact(contact_agency_account,
                                                            first_name,
                                                            last_name,
                                                            contact_email,
                                                            job_title=job_title)

            # patch the contact and mark as account manager
            if not contact.Id.startswith('Contact') and not contact.PanelManager:
                contacts_to_patch[contact.Id] = {'Id': contact.Id, 'Panel_Manager__c': True}
            contact.PanelManager = True

            # Find the leaf agencies for the top_agency_account
            top_agency_account_root = account_forest_by_id[agency_account.Id]
            leaf_agencies = find_leaf_accounts(top_agency_account_root, account_forest_by_id)

            # find all the child regions of that region
            if sf_market:
                filter_markets = {sf_market.Id: sf_market}
                filter_markets.update(get_market_tree_children(all_markets_by_id, sf_market))
                filter_markets = set(filter_markets.keys())
            else:
                filter_markets = None

            # Now, add the contact to the leaf agencies customer surveys as customer account manager
            match_count = 0
            for leaf_agency_account in leaf_agencies:
                leaf_agency_teams_and_survey_clients = all_survey_clients_by_agency_id_and_team_id_and_customer_client_id.get(leaf_agency_account.Id)
                if not leaf_agency_teams_and_survey_clients:
                    print("No previous customer survey for", leaf_agency_account.Name)
                    continue

                if sf_office_market:
                    if leaf_agency_account.MarketId != sf_office_market.Id:
                        continue

                elif filter_markets:
                    if leaf_agency_account.MarketId not in filter_markets:
                        continue

                team = None
                if team_name:
                    team = all_teams_by_name_and_account.get(f"{team_name}-{leaf_agency_account.Id}")
                    if not team:
                        continue

                for teamid, leaf_agency_survey_clients in leaf_agency_teams_and_survey_clients.items():
                    prior_customer_survey_and_survey_client = leaf_agency_survey_clients.get(customer_client_account.Id)
                    if not prior_customer_survey_and_survey_client:
                        continue
                    prior_customer_survey, prior_survey_client = prior_customer_survey_and_survey_client[WHICH_CUSTOMER_SURVEY]

                    if team and prior_customer_survey.TeamId != team.Id:
                        continue

                    sfimport.get_or_create_cached_survey_panel_manager(prior_survey_client, contact)
                    match_count += 1

            if match_count == 0:
                row['reason'] = 'NO MATCHES DETECTED'
                duff_writer.writerow(row)
                duffcount += 1

    if writeit:
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        if contacts_to_patch:
            for status in sfapi.bulk_update(sf, "Contact", list(contacts_to_patch.values())):
                sfapi.detect_bulk2_errors(status)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_managers, sfapi.SurveyPanelManager)

    else:
        print("Dry run, not writing to Salesforce")
        print(len(sfimport.new_contacts), "new contacts")
        print(len(contacts_to_patch), "contacts to patch")
        print(len(sfimport.new_survey_panel_managers), "new survey panel managers")

    # clean up
    duff_file.flush()
    duff_file.close()
    if duffcount == 0:
        os.unlink('duffrecords-staff.csv')



if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('accounts_filename', help='CSV file to import')
    ap.add_argument('importtype', choices=['advertising', 'manufacturing'], help='Import type')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.accounts_filename, args.importtype, args.safeemails, args.writeit)
