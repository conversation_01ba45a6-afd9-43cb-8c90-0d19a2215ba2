import lib.sfapi as sfapi
import argparse
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    all_ccrs = {}
    for ccr in sfapi.get_all(sf, sfapi.CustomerClientRelationship):
        bk = ccr.business_key2()
        all_ccrs.setdefault(bk, []).append(ccr)

    for bk_ccr_list in all_ccrs.values():
        if len(bk_ccr_list) > 1:
            print()
            print(f"{bk_ccr_list[0].Name} {bk_ccr_list[0].Id}")
            for curccr in bk_ccr_list:
                print(f"   ZAP {curccr.Id} {curccr.Name}")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
