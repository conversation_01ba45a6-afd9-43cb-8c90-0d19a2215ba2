from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
import copy
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts
import datetime


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    all_panel_managers_by_survey_client_id: dict[str, sfapi.SurveyPanelManager] = {}
    for spm in sfapi.get_all(sf, sfapi.SurveyPanelManager):
        all_panel_managers_by_survey_client_id.setdefault(spm.SurveyClientId, []).append(spm)

    all_contacts_by_id: dict[str, sfapi.Contact] = {}
    for c in sfapi.get_all(sf, sfapi.Contact):
        all_contacts_by_id[c.Id] = c

    for in_confirm_panel in confirm_panel_collection.find({}):
        pdb_panel_managers = in_confirm_panel['panel_managers']
        pdb_panel_managers_lookup = {x['panel_manager_id']: x for x in pdb_panel_managers}

        patched = False
        for sf_panel_manager in all_panel_managers_by_survey_client_id.get(in_confirm_panel['Id'], []):
            if sf_panel_manager.ContactId in pdb_panel_managers_lookup:
                continue

            contact = all_contacts_by_id.get(sf_panel_manager.ContactId)

            print("MISSING", sf_panel_manager.ContactId)
            pdb_panel_managers.append({
                'panel_manager_id': contact.Id,
                'panel_manager_email': contact.Email,
                'panel_manager_name': f"{contact.FirstName} {contact.LastName}",
            })
            pdb_panel_managers_lookup[contact.Id] = pdb_panel_managers[-1]
            patched = True

        if patched:
            confirm_panel_collection.update_one({'_id': in_confirm_panel['_id']}, {'$set': {'panel_managers': pdb_panel_managers}})


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
