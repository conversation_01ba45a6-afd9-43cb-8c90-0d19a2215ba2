from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import csv


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    spm_patches = {}
    with open('multipanel.csv', 'w') as csvfile:
        csvwriter = csv.writer(csvfile)
        csvwriter.writerow(['Survey OID', 'Survey Panel Member Link', 'Survey Panel Member Name'])

        for survey in survey_collection.find({'deleted': {'$ne': True}}):
            is_affected = False
            for question in survey['questions']:
                if len(question['panel_members']) > 1:
                    is_affected = True
                    break

            if not is_affected:
                continue

            for question in survey['questions']:
                for panel_member in question['panel_members']:
                    panel_member_id = panel_member["survey_panel_member_id"]
                    panel_member_email = panel_member["survey_panel_member_name"]
                    link = f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Panel_Member__c/{panel_member_id}/view"

                    csvwriter.writerow([survey['_id'], link, panel_member_email])

                    spm_patches[panel_member_id] = {'Id': panel_member_id, 'SurveyJsId__c': '#N/A'}

    # for status in sfapi.bulk_update(sf, "Survey_Panel_Member__c", list(spm_patches.values())):
    #     sfapi.detect_bulk2_errors(status)

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
