import csv
import lib.sfapi as sfapi
import argparse
from lib import sfimport
from lib.settings import settings


def import_csv(accounts_filename, safeemails):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # populate cache
    for objname in ['Account', 'Contact', 'ContactKeyMarket']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))

    # generate account forest
    root_accounts, account_forest_by_id, account_forest_by_name = sfimport.generate_account_forest()

    # load data from the CSV file
    with open(accounts_filename) as file:
        reader = csv.DictReader(file)

        for i, row in enumerate(reader):
            row['row_number'] = i + 1

            contact_unsafe_email_address = row['email_address']
            contact_safe_email_address = sfimport.make_safe_email(contact_unsafe_email_address)

            first_name = row['first_name'].strip()
            last_name = row['last_name'].strip()
            key_account_names = [x.strip() for x in row['account_name'].split(',')]  # e.g. coke (optional)  if there -> key account
            client_name = row['client_name'].strip()  # e.g. dentsu
            reporting_role = row['reporting_role'].strip()
            report_customisation = row['report_customisation'].strip()
            reporting_global_accounts = bool(row['reporting_global_accounts'].strip())

            # find the client (eg dentsu)
            client = sfimport.find_account_by_name_list(account_forest_by_name, account_forest_by_id, client_name.split('/'))
            if not client:
                print("FAILED TO FIND CLIENT", client_name)
                continue

            # find the account (eg coke)
            key_accounts = []
            if key_account_names:
                for key_account_name in key_account_names:
                    if not key_account_name:
                        continue

                    account = sfimport.find_account_by_name_list(account_forest_by_name, account_forest_by_id, key_account_name.split('/'))
                    if account:
                        key_accounts.append(account)
                    else:
                        print("FAILED TO FIND KEY ACCOUNT", key_account_name)

            # create the contact
            if safeemails:
                email_address = contact_safe_email_address
            else:
                email_address = contact_unsafe_email_address
            contact = sfimport.get_or_create_cached_contact(client['account'],
                                                               first_name,
                                                               last_name,
                                                               email_address,
                                                               reporting_role=reporting_role,
                                                               report_customisation=report_customisation,
                                                               reporting_global_accounts=reporting_global_accounts)

            for key_account in key_accounts:
                contact_key_account = sfimport.get_or_create_cached_contact_key_account(contact, key_account['account'])

    sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
    sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_key_accounts, sfapi.ContactKeyAccount)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('accounts_filename', help='CSV file to import')
    args = ap.parse_args()

    import_csv(args.accounts_filename, args.safeemails)
