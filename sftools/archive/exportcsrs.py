from lib import portaldbapi
import argparse
import bson


def main(dirname):
    portaldb = portaldbapi.DocumentDB()
    csr_collection = portaldbapi.get_sf_collection(portaldb, 'surveyrounds')

    for csr in csr_collection.find():
        csr.pop('_id')
        with open(f'{dirname}/{csr["Id"]}.json', 'w') as f:
            f.write(bson.json_util.dumps(csr, indent=2, sort_keys=True))


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('dirname')
    args = ap.parse_args()

    main(args.dirname)
