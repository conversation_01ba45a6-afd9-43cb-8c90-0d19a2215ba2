import lib.sfapi as sfapi
import argparse
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    customer_surveys_by_business_key = {}
    for cs in sfapi.get_all(sf, sfapi.CustomerSurvey):
        bk = cs.business_key2()
        customer_surveys_by_business_key.setdefault(bk, []).append(cs)

    survey_clients_by_customer_survey_id = {}
    for sc in sfapi.get_all(sf, sfapi.SurveyClient):
        survey_clients_by_customer_survey_id.setdefault(sc.CustomerSurveyId, []).append(sc)

    tozap = set()
    for bk_cs_list in customer_surveys_by_business_key.values():
        if len(bk_cs_list) > 1:
            # print()
            # print(f"{bk_cs_list[0].Name} {bk_cs_list[0].Id}")
            for curcs in bk_cs_list:
                scs = survey_clients_by_customer_survey_id.get(curcs.Id, [])
                if not scs:
                    # print(f"   ZAP {curcs.Id} {curcs.Name}")
                    tozap.add(curcs.Id)

    # for x in tozap:
    #     print(x)
    #     sf.Customer_Survey__c.delete(x)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
