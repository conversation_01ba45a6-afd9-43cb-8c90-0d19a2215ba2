import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv
import json
from simple_salesforce import format_soql


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    contacts_to_clean = set()
    with open('DentsuAccManFix2911v2.csv', 'r') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            contacts_to_clean.add(row['Contact__c'])

    soql = format_soql("""
SELECT Id
FROM Survey_Account_Manager__c
WHERE Account_Manager__c IN {contacts_to_clean}
""", contacts_to_clean=list(contacts_to_clean))

    sams_to_zap = set()
    for record in sf.query_all_iter(soql):
        sams_to_zap.add(record['Id'])

    contacts_to_patch = {}
    for c in contacts_to_clean:
        contacts_to_patch[c] = {'Id': c, 'Accounts_Manager__c': False}

    if sams_to_zap:
        print(len(sams_to_zap))
        for record_status in sfapi.bulk_delete(sf, 'Survey_Account_Manager__c', list(sams_to_zap)):
            for row in record_status['failedRecords']:
                print('Failed to delete', row)
            for row in record_status['unprocessedRecords']:
                print('Failed to delete', row)

    if contacts_to_patch:
        print(len(contacts_to_patch))
        for status in sfapi.bulk_update(sf, "Contact", list(contacts_to_patch.values())):
            sfapi.detect_bulk2_errors(status)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
