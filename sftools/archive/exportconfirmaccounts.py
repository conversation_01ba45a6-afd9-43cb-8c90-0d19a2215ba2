import bson.json_util
from lib import portaldbapi
import argparse
import bson


def main(dirname):
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    for confirm_account in confirm_accounts_collection.find():
        # confirm_account = portaldbapi.ConfirmAccounts(**confirm_account)
        # confirm_account.account_managers = sorted(confirm_account.account_managers, key=lambda x: x.account_manager_id)
        # confirm_account.accounts = sorted(confirm_account.accounts, key=lambda x: x.account_id)
        # confirm_account.confirmed_accounts = {}
        # confirm_account.sfsync_date = None
        # confirm_account.confirm_status = False
        # confirm_account.auto_confirmed = False

        with open(f'{dirname}/{confirm_account["Id"]}.json', 'w') as f:
            f.write(bson.json_util.dumps(confirm_account, indent=2, sort_keys=True))
            # f.write(confirm_account.model_dump_json(by_alias=True, indent=2))


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('dirname')
    args = ap.parse_args()

    main(args.dirname)
