import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import re
import openpyxl
import os


CUSTOMER_CLIENT_NAME_MAPPING = {
    'J&<PERSON>': 'Johnson & Johnson',
    'Nestle': 'Nestlé',
    'Honda Canada': 'Honda',
    'LEVI\'S': 'Levi Strauss & Co.',
    'Levi\'s': 'Levi Strauss & Co.',
    'Sainsburys Bank': 'Sainsbury\'s Bank',
}

MISSING_AGENCY_ACCOUNTS = set()
MISSING_CUSTOMER_CLIENTS = set()
MISSING_PANEL_MANAGERS = {}
MISSING_SIGNATORIES = {}
MULTI_MATCH_SIGNATORIES = {}

def import_xlsx(accounts_filename,
                all_markets_by_name,
                all_contacts_by_email,
                all_contacts_by_full_name,
                all_agency_accounts_by_name,
                all_customer_client_accounts_by_name,
                client_group_mapping,
                account_mapping):

    wb = openpyxl.open(accounts_filename, read_only=True)
    if len(wb.worksheets) != 1:
        raise ValueError('Expected exactly one worksheet in the workbook')
    ws = wb.worksheets[0]
    all_rows = list(ws.iter_rows(values_only=True))

    # first of all, find the header row
    header_row = None
    data_row_index = None
    for row_idx, row in enumerate(all_rows):
        if 'Status' in row and 'Agency' in row:
            header_row = {}
            for col_idx, v in enumerate(row):
                if not v:
                    continue
                v = v.lower().strip()
                v = v.split('\n')[0]
                header_row[v] = col_idx
            data_row_index = row_idx + 1
            break

    if not header_row:
        raise ValueError('Could not find header row')

    for i, raw_row in enumerate(all_rows[data_row_index:]):
        row = {k: raw_row[v] for k, v in header_row.items()}

        # filter out rows that are not marked as "Active"
        status = row['status']
        if status:
            status = status.strip().lower()
        if status not in {'active', 'new'}:
            continue

        # also filter things out with 'remove' in the add/remove rationale
        add_remove_rationale = row.get('add/remove rationale')
        if add_remove_rationale:
            if 'remove' in add_remove_rationale.lower():
                continue

        # find the market first
        market_name = row['market'] or ''
        market_name = market_name.strip()
        market = all_markets_by_name.get(market_name.lower())
        if not market:
            print(f"Market {market_name} not found in Salesforce, skipping row {i}")
            continue

        # decode the client group
        client_group_name = row['client group'] or ''
        client_group_name = client_group_name.strip()
        if client_group_name:
            client_group = client_group_mapping.get(client_group_name)
            if not client_group:
                print(f"Client group {client_group_name} not found in mapping, skipping row {i}")
                continue

        # the agency "with market" account
        agency_name = row['agency'].strip()
        agency_with_market_name = f'{agency_name} {market_name}'
        agency_account = all_agency_accounts_by_name.get(agency_with_market_name.lower())
        if not agency_account:
            MISSING_AGENCY_ACCOUNTS.add(agency_with_market_name)
            print(f"Agency account {agency_with_market_name} not found in Salesforce, skipping row {i}")
            continue
        if agency_account.OrganisationalLevel != 'With Market':
            print(f"Agency account {agency_with_market_name} is not a 'With Market' account, skipping row {i}")
            continue

        # the customer client account
        customer_client_name = row['account'].strip()
        customer_client_name = account_mapping.get(customer_client_name.lower(), customer_client_name)
        if len(customer_client_name) > 1:
            print(f"Multiple matches for customer client {customer_client_name}, skipping row {i}")
            continue
        customer_client_name = list(customer_client_name)[0]
        # customer_client_name = CUSTOMER_CLIENT_NAME_MAPPING.get(customer_client_name, customer_client_name)
        customer_client_account = all_customer_client_accounts_by_name.get(customer_client_name.lower())
        if not customer_client_account:
            MISSING_CUSTOMER_CLIENTS.add(customer_client_name)
            # print(f"Customer client account {customer_client_name} not found in Salesforce, skipping row {i}")
            continue

        # the survey name
        survey_name = row['survey name'] or ''
        survey_name = survey_name.strip()

        # the panel managers
        panel_managers = []
        bad_panel_managers = False
        raw_panel_managers = row['database contact'].strip()
        if 'Confirmed Centrally' in raw_panel_managers:
            raw_panel_managers = ''
        elif '@' not in raw_panel_managers:
            print(f"Invalid email address {raw_panel_managers}, skipping row {i}")
            continue
        for panel_manager_email in re.split(r';|\s|\n|,', raw_panel_managers):
            panel_manager_email = panel_manager_email.strip().lower()
            if not panel_manager_email:
                continue
            account_manager_contact = all_contacts_by_email.get(panel_manager_email)
            if not account_manager_contact:
                MISSING_PANEL_MANAGERS.setdefault(panel_manager_email, set()).add(agency_account.Name)
                # print(f"Account manager {panel_manager_email} not found in Salesforce, skipping row {i}")
                bad_panel_managers = True
                break
            panel_managers.append(account_manager_contact)
        if bad_panel_managers:
            continue

        # the signatories
        signatories = []
        bad_signatories = False
        raw_signatories = row['email sender'] or ''
        raw_signatories = raw_signatories.strip()
        if 'confirmed centrally' in raw_signatories.lower():
            raw_signatories = ''
        if 'to be confirmed' in raw_signatories.lower():
            raw_signatories = ''
        if 'we have local trr with mattel' in raw_signatories.lower():
            raw_signatories = ''
        for signatory_full_name in re.split(r';|\n|,', raw_signatories):
            signatory_full_name = signatory_full_name.strip().lower()
            if not signatory_full_name:
                continue
            signatory_full_name = signatory_full_name.split('-')[0].strip()
            signatory_full_name = re.sub('[^a-z]+', '', signatory_full_name)
            signatory_contact = all_contacts_by_full_name.get(signatory_full_name)
            if not signatory_contact:
                MISSING_SIGNATORIES.setdefault(signatory_full_name, set()).add(agency_account.Name)
                # print(f"Signatory {signatory_full_name} not found in Salesforce, skipping row {i}")
                bad_signatories = True
                break
            if len(signatory_contact) > 1:
                MULTI_MATCH_SIGNATORIES.setdefault(signatory_full_name, set()).add(agency_account.Name)
                # print(f"Multiple contacts found for signatory {signatory_full_name}, skipping row {i}")
                bad_signatories = True
                break
            signatories.append(signatory_contact)
        if bad_signatories:
            continue


def load_client_group_mapping():
    wb = openpyxl.open('data/client group mapping.xlsx', read_only=True)
    mapping_sheet = None
    for ws in wb.worksheets:
        if ws.title.lower() == 'mapping + accounts':
            mapping_sheet = ws
            break
    if not mapping_sheet:
        raise ValueError('Could not find the mapping sheet')

    all_rows = list(ws.iter_rows(values_only=True))
    header_row = {}
    for col_idx, v in enumerate(all_rows[0]):
        if not v:
            continue
        v = v.lower().strip()
        v = v.split('\n')[0]
        header_row[v] = col_idx

    client_group_mapping = {}
    for raw_row in all_rows[1:]:
        row = {k: raw_row[v] for k, v in header_row.items()}

        client_group_mapping.setdefault(row['legacy_client_group'], []).append(row)

    return client_group_mapping


def load_account_mapping():
    wb = openpyxl.open('data/account_mappings_master.xlsx', read_only=True)
    mapping_sheet = None
    for ws in wb.worksheets:
        if ws.title.lower() == 'account_mappings_master':
            mapping_sheet = ws
            break
    if not mapping_sheet:
        raise ValueError('Could not find the mapping sheet')

    all_rows = list(ws.iter_rows(values_only=True))
    header_row = {}
    for col_idx, v in enumerate(all_rows[0]):
        if not v:
            continue
        v = v.lower().strip()
        v = v.split('\n')[0]
        header_row[v] = col_idx

    account_mapping = {}
    for raw_row in all_rows[1:]:
        row = {k: raw_row[v] for k, v in header_row.items()}
        legacy_account_name = row['legacy_account_name'] or ''
        legacy_account_name = legacy_account_name.strip().lower()
        account_name = row['account_name'] or ''
        account_name = account_name.strip().lower()

        account_mapping.setdefault(legacy_account_name, set()).add(account_name)

    return account_mapping


def main(dirname: str):
    client_group_mapping = load_client_group_mapping()
    account_mapping = load_account_mapping()

    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    all_markets_by_name: dict[str, sfapi.Market] = {}
    for market in sfapi.get_all(sf, sfapi.Market):
        market_name = market.Name.lower()

        if market_name in all_markets_by_name:
            raise Exception(f"Duplicate market name {market_name} found.")
        all_markets_by_name[market_name] = market

    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    all_contacts_by_full_name: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        contact_email = contact.Email.lower()
        contact_full_name = f"{contact.FirstName} {contact.LastName}".lower()
        contact_full_name = re.sub('[^a-z]+', '', contact_full_name)

        all_contacts_by_email[contact_email] = contact
        all_contacts_by_full_name.setdefault(contact_full_name, []).append(contact)

    all_agency_accounts_by_name: dict[str, sfapi.Account] = {}
    all_customer_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        account_name = account.Name.lower()

        if account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id:
            all_agency_accounts_by_name[account_name] = account
        elif account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            all_customer_client_accounts_by_name[account_name] = account

    for f in os.listdir(dirname):
        filename = os.path.join(dirname, f)
        if not os.path.isfile(filename):
            continue
        print(filename)
        import_xlsx(filename,
                    all_markets_by_name,
                    all_contacts_by_email,
                    all_contacts_by_full_name,
                    all_agency_accounts_by_name,
                    all_customer_client_accounts_by_name,
                    client_group_mapping,
                    account_mapping)

    print("---------------------------------")
    print('Missing agency accounts:')
    for x in MISSING_AGENCY_ACCOUNTS:
        print("  ", x)
    print()
    print('Missing panel managers:')
    for x, accounts in MISSING_PANEL_MANAGERS.items():
        print("  ", x, accounts)
    print()
    print('Missing signatories:')
    for x, accounts in MISSING_SIGNATORIES.items():
        print("  ", x, accounts)
    print()
    print('Multiple match signatories:')
    for x, accounts in MULTI_MATCH_SIGNATORIES.items():
        print("  ", x, accounts)
    print()
    print('Missing customer clients:')
    for x in MISSING_CUSTOMER_CLIENTS:
        print("  ", x)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('dirname', help='Directory to load')
    args = ap.parse_args()

    main(args.dirname)
