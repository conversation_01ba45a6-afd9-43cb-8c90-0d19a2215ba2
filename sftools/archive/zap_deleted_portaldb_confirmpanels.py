from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
import copy
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts
import datetime


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    all_survey_clients: dict[str, sfapi.SurveyClient] = {}
    for sc in sfapi.get_all(sf, sfapi.SurveyClient):
        all_survey_clients[sc.Id] = sc

    tozap = set()
    for in_confirm_panel in confirm_panel_collection.find({}):
        if in_confirm_panel['Id'] in all_survey_clients:
            continue

        tozap.add(in_confirm_panel['_id'])

    if tozap:
        print(len(tozap), "confirm panels to zap")
        confirm_panel_collection.delete_many({'_id': {'$in': list(tozap)}})


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
