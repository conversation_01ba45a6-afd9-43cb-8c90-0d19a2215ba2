import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
from lib import sfimport


def main(filename):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    for objname in ['Market']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("DONE POPULATING CACHE")

    # load data from the CSV file
    with open(filename) as file:
        # we read in all rows, then sort by date descending so the most recent values are the ones take priority
        reader = csv.DictReader(file)
        for row in reader:

            # work out the path of the market
            market_path = []
            for levelname in ['Global', 'Business Regions', 'Key Regions', 'Regions', 'Sub-Regions', 'Countries', 'Cities']:
                value = row.get(levelname, '').strip()
                if not value:
                    continue
                market_path.append((levelname, value))

            # create the market objectsin the correct hierarchy
            parent_market = None
            for market_level, market_name in market_path:
                if market_level == 'Global':
                    market_level = 'Global'
                elif market_level == 'Business Regions':
                    market_level = 'Business Region'
                elif market_level == 'Key Regions':
                    market_level = 'Key Region'
                elif market_level == 'Regions':
                    market_level = 'Region'
                elif market_level == 'Sub-Regions':
                    market_level = 'Sub-Region'
                elif market_level == 'Countries':
                    market_level = 'Country'
                elif market_level == 'Cities':
                    market_level = 'City'
                else:
                    raise ValueError(f"Unknown market level: {market_level}")
                market = sfimport.get_or_create_cached_market(market_name, parent_market, market_level)
                parent_market = market

    # write 'em out!
    sfimport.bulk_write_markets_to_sf(sf, sfimport.new_markets)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    args = ap.parse_args()

    main(args.filename)
