import csv
import base64
import openpyxl
import re
import argparse
import lib.sfapi as sfapi
from collections import namedtuple
from lib.settings import settings



Signature = namedtuple('Signature', ['signature_id', 'html', 'plain', 'stripped_from_name'])

def load_signatures(filename: str):
    wb = openpyxl.open(filename, read_only=True)
    ws = wb.worksheets[0]

    header = None
    all_signatures = []
    for row in ws.iter_rows(values_only=True):
        if not header:
            header = {v:i for i,v in enumerate(row)}
            continue

        signature_id = row[header['signature_id']]
        html = row[header['html']]
        html = html.replace('/agencyLogos/', 'agencyLogos/')
        html = html.replace('{body}', ' ')
        html = html.replace('{opt_out_link}', ' ')
        plain = row[header['plain']]

        stripped_from_name = re.sub(r'[^a-z]', '', row[header['sigature_name']].lower().strip())

        all_signatures.append(Signature(signature_id=signature_id, html=html.lower(), plain=plain.lower(), stripped_from_name=stripped_from_name))


    return all_signatures


def find_signature(all_signatures, contact_name, contact_email, customer_name):
    contact_email = contact_email.lower()
    contact_name = contact_name.lower()
    customer_name = customer_name.lower()

    stripped_contact_name = re.sub(r'[^a-z]', '', contact_name)

    result = []
    for sig in all_signatures:
        if contact_email in sig.html:
            result.append(sig)
        elif contact_name in sig.html:
            result.append(sig)
        elif stripped_contact_name == sig.stripped_from_name:
            result.append(sig)

    return result


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    with open('signatories.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=['signatory name', 'signatory email', 'signatory link', 'has image signature'])
        csvout.writeheader()

        soql = """
        SELECT Id,
                Customers_Client__r.Name,
                Customer_Survey__r.Customer__r.Name,
                Signatory__r.Id,
                Signatory__r.Email,
                Signatory__r.FirstName,
                Signatory__r.LastName,
                Signatory__r.Signature__c
        FROM Survey_Client__c
        WHERE Current_Round__c = TRUE
    """
        seen_contact_ids = set()
        for row in sfapi.bulk_query(sf, soql):
            if not row['Signatory__r.Id']:
                print(f"Skipping row with no signatory id (survey client id: {row['Id']})")
                continue
            if row['Signatory__r.Id'] in seen_contact_ids:
                continue
            seen_contact_ids.add(row['Signatory__r.Id'])

            contact_name = f"{row['Signatory__r.FirstName']} {row['Signatory__r.LastName']}"
            contact_email = row['Signatory__r.Email']
            signature = row['Signatory__r.Signature__c']

            has_image_signature = 'img' in signature

            csvout.writerow({
                'signatory name': contact_name,
                'signatory email': contact_email,
                'signatory link': f"https://clientrelationship.lightning.force.com/lightning/r/Contact/{row['Signatory__r.Id']}/view",
                'has image signature': has_image_signature,
            })


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
