import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy


def get_market_tree_children(markets: dict[str, sfapi.Market], parent: sfapi.Market):
    result = {}

    for item in markets.values():
        if item.ParentMarketId == parent.Id:
            result[item.Id] = item
            result.update(get_market_tree_children(markets, item))

    return result


def get_account_tree_children(accounts: dict[str, sfapi.Account], parent: sfapi.Account):
    result = {}

    for item in accounts.values():
        if item.ParentId == parent.Id:
            result[item.Id] = item
            result.update(get_account_tree_children(accounts, item))

    return result


def load_account_managers(filename, sf, accountRecordTypes, all_markets_by_name, customers_client_accounts_by_name):
    # find child customer client accounts
    child_cc_cc_accounts = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        child_cc_cc_accounts.setdefault(account.ParentId, []).append(account)

    account_managers = []
    with open(filename) as file:
        reader = csv.DictReader(file)

        for row in reader:
            accountmanager = {}
            accountmanager['Email Address'] = row['Email Address'].strip().lower()

            # compute the account hierarchy with the correct names
            for colname in ['Holding Group', 'Network', 'Agency Brand', 'Agency Brand Subsidiary']:
                if colname == 'Holding Group':
                    organisational_level = 'Holding Group'
                elif colname == 'Network':
                    organisational_level = 'Network'
                elif colname == 'Agency Brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'Agency Brand Subsidiary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level")

                v = row[colname].strip()
                if v.upper() == 'ALL':
                    v = 'ALL'
                elif not v:
                    v = 'ALL'
                if v:
                    accountmanager[organisational_level] = v

            if row['Market'].strip().upper() != 'ALL':
                raise Exception("Only 'ALL' is supported for market")
            if row['Region'].strip().upper() != '':
                raise Exception("Region is not supported")
            if row['Team Name'].strip().upper() != '':
                raise Exception("Team Name is not supported")
            if row['Team Market'].strip().upper() != '':
                raise Exception("Team Market is not supported")

            if row['Account'].strip().upper() == 'ALL':
                parent_account_name = row['Parent Account'].strip()
                if parent_account_name:
                    if parent_account_name == 'Nestle':
                        parent_account_name = 'Nestlé'
                    parent_account = customers_client_accounts_by_name.get(parent_account_name)
                    if not parent_account:
                        raise Exception(f"Unknown parent account {parent_account_name}")
                    if len(parent_account) > 1:
                        raise Exception(f"Multiple parent accounts {parent_account_name}")
                    parent_account = parent_account[0]
                    child_accounts = child_cc_cc_accounts.get(parent_account.Id)
                    if not child_accounts:
                        raise Exception(f"Parent account {parent_account_name} has no children")

                    # append parent account
                    accountmanager['Account'] = parent_account_name
                    account_managers.append(accountmanager)

                    # append child accounts
                    for account in child_accounts:
                        cur = copy.copy(accountmanager)
                        cur['Account'] = account.Name
                        account_managers.append(cur)

            else:
                accountmanager['Account'] = row['Account'].strip()
                account_managers.append(accountmanager)

    return account_managers


def import_csv(accounts_filename, accountmanagers_filename, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Contact', 'CustomerClientRelationship', 'AccountGroup', 'Market', 'Team', 'ContactKeyAccount', 'CustomerSurvey', 'SurveyAccountManager', 'CustomerSurveyRound']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")

    all_markets_by_name = {}
    for market in sfapi.get_all(sf, sfapi.Market):
        if market.Name in all_markets_by_name:
            raise Exception(f"Duplicate market name {market.Name} found.")
        all_markets_by_name[market.Name] = market
    SF_CLIENT_ORG_LEVELS = sfapi.get_picklist_values(sf, 'Account', 'Organisation_Level__c', accountRecordTypes[crc_customer_record_type].Id)

    # find all the contacts in salesforce
    all_contacts_by_email = {}
    for contact in sfimport.sfcache.values():
        if not isinstance(contact, sfapi.Contact):
            continue
        all_contacts_by_email[contact.Email.lower()] = contact

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    all_account_managers = load_account_managers(accountmanagers_filename, sf, accountRecordTypes, all_markets_by_name, customers_client_accounts_by_name)

    # hardcoded to the IPG customer survey round
    customer_survey_round: sfapi.CustomerSurveyRound = sfapi.get_by_id(sf, sfapi.CustomerSurveyRound, 'a02WT000003BdasYAC')
    alicia_poole: sfapi.Contact = sfapi.get_by_id(sf, sfapi.Contact, '003WT000005bbNVYAY')

    # load data from the CSV file
    with open(accounts_filename) as file:
        reader = csv.DictReader(file)

        all_rows = []
        for i, row in enumerate(reader):
            row['row_number'] = i + 1
            all_rows.append(row)

        duff_file = open('duffrecords-ipgaccounts.csv', 'w')
        field_names = reader.fieldnames + ['row_number', 'reason', 'customer_hierarchy']
        duff_writer = csv.DictWriter(duff_file, fieldnames=field_names)
        duff_writer.writeheader()

        out_file = open('out-ipgaccounts.csv', 'w')
        field_names = reader.fieldnames + ['row_number', 'reason', 'customer_hierarchy', 'account_managers']
        out_writer = csv.DictWriter(out_file, fieldnames=field_names)
        out_writer.writeheader()

        # preprocess each row and compute various things
        for row in all_rows:
            # compute the account hierarchy with the correct names
            customer_hierarchy = []
            for colname in ['Holding Company', 'Network', 'Agency Brand', 'Agency Brand Subsidary']:
                if colname == 'Holding Company':
                    organisational_level = 'Holding Group'
                elif colname == 'Network':
                    organisational_level = 'Network'
                elif colname == 'Agency Brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'Agency Brand Subsidary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level")

                v = row[colname].strip()
                if v:
                    customer_hierarchy.append((v, organisational_level))
            row['customer_hierarchy'] = customer_hierarchy

            # get market for this row
            market_name = row['Market'].strip()
            row_market = all_markets_by_name.get(market_name)
            if row_market:
                # add on "With market" level
                with_market_account_name = f"{customer_hierarchy[-1][0]} {row_market.Name}"
                customer_hierarchy.append((with_market_account_name, 'With Market'))

        for row in all_rows:
            if row['Holding Company'].strip() == '':
                continue

            # find signatory
            unsafe_signatory_email_address = row['Signatory Email Address'].strip().lower()
            safe_signatory_email_address = sfimport.make_safe_email(unsafe_signatory_email_address)
            if safeemails:
                signatory_email_address = safe_signatory_email_address
            else:
                signatory_email_address = unsafe_signatory_email_address
            if signatory_email_address:
                signatory = all_contacts_by_email.get(signatory_email_address)
                if not signatory:
                    row['reason']= 'UNKNOWN SIGNATORY'
                    duff_writer.writerow(row)
                    continue
            else:
                signatory = None

            # find panel manager
            unsafe_panelmanager_email_address = row['Panel Manager Email Address'].strip().lower()
            safe_panelmanager_email_address = sfimport.make_safe_email(unsafe_panelmanager_email_address)
            if safeemails:
                panelmanager_email_address = safe_panelmanager_email_address
            else:
                panelmanager_email_address = unsafe_panelmanager_email_address
            if panelmanager_email_address:
                panelmanager = all_contacts_by_email.get(panelmanager_email_address)
                if not panelmanager:
                    row['reason']= 'UNKNOWN PANEL MANAGER'
                    duff_writer.writerow(row)
                    continue
            else:
                panelmanager = None

            # =======================================================
            #
            customer_client_account_name = row['IPG Top Tier Account'].strip()
            if customer_client_account_name in {'Nestle'}:
                customer_client_account_name = 'Nestlé'
            if customer_client_account_name.lower() == 'all':
                customer_client_account_name = None

            # =======================================================
            # decode the market information
            market_name = row['Market'].strip()
            market = all_markets_by_name.get(market_name)
            if not market:
                row['reason']= f'INVALID MARKET {market_name}'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # extract the agency hierarchy
            customer_hierarchy = row['customer_hierarchy']
            if not customer_hierarchy:
                row['reason']= 'MISSING CUSTOMER NAME'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # Traverse Account hierarchy to find real objects from salesforce
            agency_account = None
            for i, (hierachy_account_name, organisational_level) in enumerate(customer_hierarchy):
                if organisational_level not in SF_CLIENT_ORG_LEVELS:
                    row['reason']= 'INVALID CLIENT ORGANISATIONAL LEVEL'
                    duff_writer.writerow(row)
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(hierachy_account_name, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    break

            if not agency_account:
                row['reason']= 'MISSING CRC ACCOUNT'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # load the team within the agency
            team_name = row['Team'].strip()
            if team_name:
                team_name = team_name.strip()

            # try and find the team
            team = None
            if team_name:
                team_market_name = row['Team Name'].strip()
                team_market = all_markets_by_name.get(team_market_name)
                if not team_market:
                    row['reason']= 'INVALID TEAM MARKET'
                    duff_writer.writerow(row)
                    continue

                team_business_key_partial = f"Team-{agency_account.Id}-{team_name}-{team_market.Id}-"
                for key in sfimport.sfcache.keys():
                    if key.startswith(team_business_key_partial):
                        team = sfimport.sfcache[key]
                        break

                if not team:
                    row['reason']= 'UNKNOWN TEAM'
                    duff_writer.writerow(row)
                    continue

            # find customer client account
            if customer_client_account_name:
                customers_client_account = customers_client_accounts_by_name.get(customer_client_account_name)
                if not customers_client_account:
                    row['reason']= 'UNKNOWN CUSTOMER CLIENT ACCOUNT'
                    duff_writer.writerow(row)
                    continue
                if len(customers_client_account) > 1:
                    row['reason']= 'MULTIPLE CUSTOMER CLIENT ACCOUNTS'
                    duff_writer.writerow(row)
                    continue
                customers_client_account = customers_client_account[0]

            else:
                customers_client_account = None

            # survey name
            survey_name = row['Survey name'].strip()
            if not survey_name:
                row['reason']= 'MISSING SURVEY NAME'
                duff_writer.writerow(row)
                continue

            # find the customer survey
            csrs = []
            csr = sfimport.get_or_create_cached_customer_survey(customer_survey_round, agency_account, customer_survey_round.RoundDate, 'Survey Setup', None, None)

            if team:
                csrs
                csr = sfimport.get_or_create_cached_customer_survey(customer_survey_round, agency_account, customer_survey_round.RoundDate, csr, team)

            else:
                # FIXME: find all teams
                pass

            # # find the current round customer survey for the specified agency account
            # customer_surveys = customer_survey_by_agency_account_id.get(agency_account.Id)
            # # if not customer_surveys:
            # #     row['reason']= 'MISSING CUSTOMER SURVEYS'
            # #     duff_writer.writerow(row)
            # #     continue

            # # now, handle team filter
            # customer_survey = None
            # if customer_surveys:
            #     team_id = team.Id if team else None
            #     for cs in customer_surveys:
            #         if cs.TeamId == team_id:
            #             customer_survey = cs
            #             break
            #     # if not customer_survey:
            #     #     row['reason']= 'MISSING CUSTOMER SURVEY POST TEAM FILTER'
            #     #     duff_writer.writerow(row)
            #     #     continue

            # if customer_surveys is None:
            #     print("HELLO")
            #     # FIXME
            #     pass

            # find the account managers for this row
            row_account_managers = []
            row_customer_hierarchy = row['customer_hierarchy']
            row_customer_hierarchy_dict = {x[1]: x[0] for x in row_customer_hierarchy}
            for account_manager in all_account_managers:
                match = True
                for colname in ['Holding Group', 'Network', 'Agency Brand', 'Agency Brand (Sub 1)']:
                    row_v = row_customer_hierarchy_dict.get(colname)
                    am_v = account_manager[colname].strip()
                    if am_v != 'ALL' and am_v != row_v:
                        match = False
                        break
                if not match:
                    continue

                row_account = row['IPG Top Tier Account'].strip()
                am_account = account_manager['Account'].strip()
                if am_account != 'ALL' and am_account != row_account:
                    continue

                row_account_managers.append(account_manager)

            # row['account_managers'] = ", ".join(set([x['Email Address'] for x in row_account_managers]))
            # out_writer.writerow(row)

            # FIXME: now find it in the portaldb

    for x in sfimport.new_customer_survey_rounds:
        print(x)

    if writeit:
        for x in sfimport.new_customer_survey_rounds:
            print(x)
        # sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_surveys, sfapi.CustomerSurvey)

        pass # FIXME
        # FIXME: adjust portaldb to add new details

    else:
        print("Dry run, not writing to Salesforce")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('accounts_filename', help='CSV file to import')
    ap.add_argument('accountmanagers_filename', help='CSV file to import')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.accounts_filename, args.accountmanagers_filename, args.safeemails, args.writeit)
