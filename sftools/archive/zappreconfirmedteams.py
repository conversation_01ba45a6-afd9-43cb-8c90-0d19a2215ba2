from typing import Any
from lib import sfapi, portaldbapi
from simple_salesforce import format_soql, Salesforce
import argparse
import datetime
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    f = open('preconfirmed_after.txt', 'r')
    updates_by_csid = {}
    for row in f:
        row = row.strip()
        csid, amid, accid = row.split(' ')

        updates_by_csid.setdefault(csid, {}).setdefault(amid, set()).add(accid)

    f = open('preconfirmed_before.txt', 'r')
    updates_to_remove = {}
    for row in f:
        row = row.strip()
        csid, amid, accid = row.split(' ')

        if not accid in updates_by_csid.get(csid, {}).get(amid, set()):
            updates_to_remove.setdefault(csid, {}).setdefault(amid, set()).add(accid)

    for csid, cs_updates in updates_to_remove.items():
        cs = confirm_accounts_collection.find_one({'Id': csid})
        if not cs:
            print(f'CS not found: {csid}')
            continue

        # accounts_by_id = {}
        # for account in cs['accounts']:
        #     accounts_by_id[account['account_id']] = account

        # updates = {}
        # for amid, accids in cs_updates.items():
        #     confirmed_accounts = {'accounts': [], 'confirm_date': datetime.datetime.now()}
        #     for accid in accids:
        #         account = accounts_by_id[accid]
        #         confirmed_accounts['accounts'].append(account)

        #     updates[f'confirmed_accounts.{amid}'] = confirmed_accounts

        # updates['autoconfirmed_stamp'] = datetime.datetime.now()

        set_updates = {}
        unset_updates = {}
        for amid, accids in cs_updates.items():
            am_confirmed_accounts = cs['confirmed_accounts'].get(amid)
            if am_confirmed_accounts is None:
                print("NOT FOUND")
                continue
            out_accounts = []
            for account in am_confirmed_accounts['accounts']:
                if account['account_id'] not in accids:
                    out_accounts.append(account)
            if out_accounts:
                am_confirmed_accounts['accounts'] = out_accounts
                set_updates[f'confirmed_accounts.{amid}'] = am_confirmed_accounts
            else:
                unset_updates[f'confirmed_accounts.{amid}'] = 1

        update = {}
        if set_updates:
            update['$set'] = set_updates
        if unset_updates:
            update['$unset'] = unset_updates
        print(csid)
        if update:
            confirm_accounts_collection.update_one({'Id': csid}, update)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
