import lib.sfapi as sfapi
import argparse
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    soql =  """
SELECT Id,
        Name,
        Survey_Name__c,
        Customer_Survey__r.Team__r.Name,
        Customer_Survey__r.Customer__r.Name
FROM Survey_Client__c
WHERE Current_Round__c = TRUE
AND Customer_Survey__r.Team__c != NULL
    """

    for row in sfapi.bulk_query(sf, soql):
        if row['Survey_Name__c'] == row['Customer_Survey__r.Customer__r.Name']:
            print(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
