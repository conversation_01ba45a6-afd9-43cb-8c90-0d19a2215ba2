from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
import copy
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts
import datetime


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    all_survey_clients_by_customer_survey_id: dict[str, sfapi.SurveyClient] = {}
    for sc in sfapi.get_all(sf, sfapi.SurveyClient):
        all_survey_clients_by_customer_survey_id.setdefault(sc.CustomerSurveyId, {})[sc.CustomersClientId] = sc

    all_panel_managers_by_survey_client_id: dict[str, sfapi.SurveyPanelManager] = {}
    for spm in sfapi.get_all(sf, sfapi.SurveyPanelManager):
        all_panel_managers_by_survey_client_id.setdefault(spm.SurveyClientId, []).append(spm)

    new_spms = []
    for in_confirm_account in confirm_accounts_collection.find({}):
        survey_clients = all_survey_clients_by_customer_survey_id.get(in_confirm_account['Id'])
        if not survey_clients:
            continue

        account_counts = {}
        for deets in in_confirm_account['confirmed_accounts'].values():
            for confirmed_account in deets['accounts']:
                account_counts.setdefault(confirmed_account['account_id'], 0)
                account_counts[confirmed_account['account_id']] += 1

        for deets in in_confirm_account['confirmed_accounts'].values():
            for confirmed_account in deets['accounts']:
                if account_counts[confirmed_account['account_id']] < 2:
                    continue
                print("GOT ONE", confirmed_account['account_id'], confirmed_account['account_name'], account_counts[confirmed_account['account_id']])

                survey_client = survey_clients.get(confirmed_account['account_id'])
                if not survey_client:
                    continue
                sf_spms = {x.ContactId: x for x in all_panel_managers_by_survey_client_id[survey_client.Id]}

                for pdb_spm in confirmed_account['survey_panel_managers']:
                    if pdb_spm['panel_manager_id'] in sf_spms:
                        continue
                    else:
                        print("MISSING", pdb_spm['panel_manager_id'])

                    new_survey_panel_manager = sfapi.SurveyPanelManager(
                        Id=f"SurveyPanelManager-{len(new_spms)}",
                        Survey_Client__c=survey_client.Id,
                        Contact__c=pdb_spm['panel_manager_id'],
                        First_Name__c=pdb_spm['panel_manager_first_name'],
                        Last_Name__c=pdb_spm['panel_manager_last_name'],
                    )
                    new_spms.append(new_survey_panel_manager)

    if new_spms:
        sfapi.bulk_import_batch_to_sf(sf, None, {}, new_spms, sfapi.SurveyPanelManager)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
