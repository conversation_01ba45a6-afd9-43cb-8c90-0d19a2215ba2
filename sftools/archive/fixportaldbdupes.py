from lib import sfapi, portaldbapi, sfimport
import argparse
from lib.settings import settings
import openpyxl


TEMP_ACCOUNT_MAPPING = {
    'kia': 'Hyundai KIA Automotive Group',
    'sainsbury bank': 'Sainsbury\'s Bank',
    # 'malee enterprise company limited': '',
    # 'cargill': '',
    # 'cattier': '',
    # 'triumph sloggi': '',
    # 'roxy leisure': '',
    # 'native residential': '',
    # 'caja los andes': '',
    # 'pps insurance': '',
    # 'elanco': '',
    # 'viq group co., ltd. (vivo)': '',
    # 'aer lingus': '',
    # 'warburtons': '',
    # 'triumph': '',
    # 'home store + more': '',
    # 'otolift': '',
    # 'gruppo api': '',
}


def load_account_mapping():
    wb = openpyxl.open('data/account_mappings_master.xlsx', read_only=True)
    mapping_sheet = None
    for ws in wb.worksheets:
        if ws.title.lower() == 'account_mappings_master':
            mapping_sheet = ws
            break
    if not mapping_sheet:
        raise ValueError('Could not find the mapping sheet')

    all_rows = list(ws.iter_rows(values_only=True))
    header_row = {}
    for col_idx, v in enumerate(all_rows[0]):
        if not v:
            continue
        v = v.lower().strip()
        v = v.split('\n')[0]
        header_row[v] = col_idx

    account_mapping = {}
    for raw_row in all_rows[1:]:
        row = {k: raw_row[v] for k, v in header_row.items()}
        legacy_account_name = row['legacy_account_name'] or ''
        legacy_account_name = legacy_account_name.strip().lower()
        account_name = row['account_name'] or ''
        account_name = account_name.strip().lower()

        account_mapping.setdefault(legacy_account_name, set()).add(account_name)

    for old, new in TEMP_ACCOUNT_MAPPING.items():
        account_mapping.setdefault(old.lower(), set()).add(new)

    return account_mapping

def main():
    account_mapping = load_account_mapping()

    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        contact_email = contact.Email.lower()
        all_contacts_by_email[contact_email] = contact

    all_customer_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        account_name = account.Name.lower()

        if account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            all_customer_client_accounts_by_name[account_name] = account

    new_panel_managers = {}
    new_customer_clients = set()
    email_dupe_count = 0
    for in_confirm_account in confirm_accounts_collection.find({}):
        if not in_confirm_account['confirmed_accounts']:
            continue

        for am_id, deets in in_confirm_account['confirmed_accounts'].items():
            for account in deets['accounts']:
                if account['account_id'].startswith('NEW'):
                    account_name = account['account_name'].strip().lower()
                    account_name = account_mapping.get(account_name, {account_name})
                    account_name = list(account_name)[0]

                    sf_account = all_customer_client_accounts_by_name.get(account_name.lower())
                    if not sf_account:
                        new_customer_clients.add(account_name)

                for pm in account['survey_panel_managers']:
                    if pm['panel_manager_id'].startswith('NEW'):
                        email = pm['panel_manager_email'].lower()
                        if email in all_contacts_by_email:
                            email_dupe_count += 1
                        else:
                            new_panel_managers.setdefault(email, set()).add(in_confirm_account['client_name'])

        # FIXME: fix survey name field on confirmed_accounts as well

        # FIXME: fix the signatories as well (if not set on confirmed_account, copy from the account)

    # Find new signatories

    # print(email_dupe_count)
    print("New panel managers:")
    for x, accounts in new_panel_managers.items():
        print(x, accounts)

    print()
    print("New customer clients:")
    for x in new_customer_clients:
        print(x)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
