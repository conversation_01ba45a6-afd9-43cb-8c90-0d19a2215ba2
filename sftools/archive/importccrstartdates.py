import csv
import lib.sfapi as sfapi
import copy
import argparse
import datetime
from lib import sfimport
from lib.settings import settings


def import_csv(accounts_filename, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING

    # populate cache
    for objname in ['Account', 'CustomerClientRelationship', 'Market']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))

    # find all the markets in salesforce
    all_markets_by_id: dict[str, sfapi.Market] = {}
    all_markets_by_name: dict[str, sfapi.Market] = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        all_markets_by_id[market.Id] = market
        all_markets_by_name[market.Name.lower()] = market

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    # load data from the CSV file
    with open(accounts_filename) as file:
        reader = csv.reader(file)

        # figure out the header row
        header_row_lookup = {}
        header_row = []
        for col_idx, v in enumerate(next(reader)):
            if not v:
                continue
            v = v.strip().lower()
            header_row.append(v)
            header_row_lookup[v] = col_idx

        # load all the rows into ram and convert them into dicts + enumerate them
        all_rows = []
        for i, row in enumerate(reader):
            row = {k: row[v].strip() for k, v in header_row_lookup.items()}
            row['row_number'] = i + 1
            all_rows.append(row)

        # file to contain duff records
        duff_file = open('duffrecords-ccrdates.csv', 'w')
        duff_header_row = copy.copy(header_row)
        for col in ['row_number', 'reason', 'customer_hierarchy', 'unsafe_email_address', 'sf_market']:
            if col not in duff_header_row:
                duff_header_row.append(col)
        duff_writer = csv.DictWriter(duff_file, fieldnames=duff_header_row)
        duff_writer.writeheader()

        # preprocess each row and compute various things
        for row in all_rows:
            row['reason'] = None

            # ignore rows without proper account names
            if row['account_name'].strip() == '':
                row['reason'] = 'Skipping no account_name'
                continue

            # compute the account hierarchy with the correct names
            agency_account = None
            customer_hierarchy: list[sfapi.Account] = []
            had_agency_account_lookup_error = False
            for colname in ['holding_group', 'network', 'sub_network', 'agency_brand', 'agency_brand_subsidiary']:
                if colname == 'holding_group':
                    organisational_level = 'Holding Group'
                elif colname == 'network':
                    organisational_level = 'Network'
                elif colname == 'sub_network':
                    organisational_level = 'Sub-network'
                elif colname == 'agency_brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'agency_brand_subsidiary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level {colname}")

                v = row[colname].strip()
                if not v:
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(v, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    row['reason'] = f'MISSING AGENCY ACCOUNT {v}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    break
                if agency_account.OrganisationalLevel != organisational_level:
                    row['reason'] = f'UNEXPECTED AGENCY ACCOUNT LEVEL {v} {organisational_level} != {agency_account.OrganisationalLevel}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    break

                customer_hierarchy.append(agency_account)
            if had_agency_account_lookup_error:
                continue

            # find the market if there is one
            market_name = row['market_name'].strip()
            sf_market = all_markets_by_name.get(market_name.lower())
            if not sf_market:
                row['reason'] = f'UNKNOWN MARKET'
                duff_writer.writerow(row)
                continue
            if sf_market.MarketType != 'Country':
                row['reason'] = f'MARKETS MUST BE COUNTRIES'
                duff_writer.writerow(row)
                continue

            # add on "with market" account
            with_market_account_name = f"{customer_hierarchy[-1].Name} {sf_market.Name}"
            agency_account = sfimport.sfcache.get(sfapi.Account.business_key(with_market_account_name, agency_account, accountRecordTypes[crc_customer_record_type]))
            if not agency_account:
                row['reason'] = f'MISSING AGENCY WITH MARKET ACCOUNT {with_market_account_name}'
                duff_writer.writerow(row)
                continue
            customer_hierarchy.append(agency_account)

            # stash stuff for later
            row['sf_market'] = sf_market
            row['customer_hierarchy'] = customer_hierarchy


        # finally, its the main row processing loop!
        ccr_patches = {}
        for row in all_rows:
            # row already has an error of some sort
            if row.get('reason'):
                continue

            # =======================================================
            # extract the market information
            sf_market = row['sf_market']

            # =======================================================
            # get stuff from customer hierarchy
            customer_hierarchy = row['customer_hierarchy']

            # =======================================================
            # find the relevant customer client account
            customer_client_account_name = row['account_name'].strip()
            customer_client_account = customers_client_accounts_by_name.get(customer_client_account_name)
            if not customer_client_account:
                row['reason']= 'UNKNOWN CUSTOMER CLIENT ACCOUNT'
                duff_writer.writerow(row)
                continue
            if len(customer_client_account) > 1:
                row['reason']= 'MULTIPLE CUSTOMER CLIENT ACCOUNTS'
                duff_writer.writerow(row)
                continue
            customer_client_account = customer_client_account[0]

            start_date = datetime.datetime.strptime(row['start_date'], '%Y-%m-%d %H:%M:%S').date()

            ccr = sfimport.get_or_create_cached_customer_client_relationship(customer_hierarchy[-1], customer_client_account, 'Client')
            if ccr.Id.startswith('CustomerClientRelationship-'):
                ccr.StartDate = start_date
            else:
                ccr_patches[ccr.Id] = start_date

    # if writeit:
    #     sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
    #     for ccr_id, patch_record in ccr_patches.items():
    #         sf.Customer_Client_Relationship__c.update(ccr_id, patch_record)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('accounts_filename', help='CSV file to import')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    args = ap.parse_args()

    import_csv(args.accounts_filename, args.writeit)
