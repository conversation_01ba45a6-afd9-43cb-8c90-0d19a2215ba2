from typing import Any
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
import argparse
import datetime
import boto3
from lib.settings import settings
import io
import csv
import uuid
import urllib
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts



def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # populate cache
    print("Populating cache")
    for objname in ['SurveyAccountManager']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")

    for x in sfimport.sfcache.values():
        if not isinstance(x, sfapi.SurveyAccountManager):
            continue
        if x.ContactId != '003WT000005d695YAA':
            continue

        print(x.Id)
        sf.Survey_Account_Manager__c.delete(x.Id)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
