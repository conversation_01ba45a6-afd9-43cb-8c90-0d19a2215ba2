import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import copy


def import_csv(accounts_filename):
    # load data from the CSV file
    with open(accounts_filename) as file:
        reader = csv.DictReader(file)

        csv_fields = ['Holding Group',
                      'Network',
                      'Sub-Network',
                      'Agency Brand',
                      'Agency Brand Subsidiary',
                      'Market',
                      'Account',
                      'Team Name',
                      'Team Market',
                      'Account Manager (yes/no)',
                      'Panel Manager (yes/no)',
                      'Email Sender (yes/no)',
                      'Reporting Role',
                      'Email Address',
                      'First Name',
                      'Last Name']

        f = open('ipg-tt-staff.csv', 'w')
        csv_writer = csv.DictWriter(f, fieldnames=csv_fields)
        csv_writer.writeheader()

        for i, row in enumerate(reader):
            outrow = {}

            outrow['Holding Group'] = row['Holding Company']
            outrow['Network'] = row['Network']
            outrow['Sub-Network'] = ''
            outrow['Agency Brand'] = row['Agency Brand ']
            outrow['Agency Brand Subsidiary'] = row['Agency Brand Subsidary']
            outrow['Market'] = row['Market']
            outrow['Account'] = row['IPG Top Tier Account']
            outrow['Team Name'] = row['Team']
            outrow['Team Market'] = row['Team Name']
            outrow['Account Manager (yes/no)'] = ''
            outrow['Panel Manager (yes/no)'] = ''
            outrow['Email Sender (yes/no)'] = ''
            outrow['Reporting Role'] = ''

            pm_outrow = copy.copy(outrow)
            pm_outrow['Email Address'] = row['Panel Manager Email Address']
            pm_outrow['First Name'] = row['Panel Manager First Name']
            pm_outrow['Last Name'] = row['Panel Manager Last Name']
            pm_outrow['Panel Manager (yes/no)'] = 'yes'
            csv_writer.writerow(pm_outrow)

            signatory_name = row['Signatory'].strip()
            if signatory_name:
                signatory_name = signatory_name.split(' ', 1)
                sig_outrow = copy.copy(outrow)
                sig_outrow['Email Address'] = row['Signatory Email Address']
                sig_outrow['First Name'] = signatory_name[0]
                sig_outrow['Last Name'] = signatory_name[1]
                sig_outrow['Email Sender (yes/no)'] = 'yes'
                csv_writer.writerow(sig_outrow)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('accounts_filename', help='CSV file to import')
    args = ap.parse_args()

    import_csv(args.accounts_filename)
