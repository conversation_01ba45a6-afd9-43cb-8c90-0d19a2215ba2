import csv
import argparse
from lib import portaldbapi


def main():

    good = {}
    with open('goodguy/customer_survey.csv', 'r') as file:
        reader = csv.DictReader(file)
        for row in reader:
            good[row['Id']] = row

    with open('wank/customer_survey.csv', 'r') as file:
        reader = csv.DictReader(file)
        for new_row in reader:
            good_row = good.get(new_row['Id'])
            if not good_row:
                continue

            if new_row['Live_Survey_Start_Date__c'] != good_row['Live_Survey_Start_Date__c']:
                print(good_row['Id'], good_row['Name'], good_row['Live_Survey_Start_Date__c'], new_row['Live_Survey_Start_Date__c'])


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    args = parser.parse_args()

    main()

