import argparse
import datetime
from pymongo import UpdateOne
from lib import portaldbapi
import json


def main():
    portaldb = portaldbapi.DocumentDB()
    confirmaccounts_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE].confirmaccounts

    q = {'customer_survey_round_id': 'a02WT000006MaOIYA0',
         'accounts_confirmed_by_date': {'$lte': datetime.datetime(2024, 12, 19)},
         'confirmed_accounts': {'$eq': {}},
         'account': {'$ne': {}}}

    ids = set()
    for x in confirmaccounts_collection.find(q):
        # skip CHEP as we've already fixed it
        if x['Id'] in {'a03WT000009Yg0yYAC'}:
            continue

        url = f'https://clientrelationship.lightning.force.com/lightning/r/Customer_Survey__c/{x["Id"]}/view'
        print(x['_id'], x['customer_survey_name'], url)

        ids.add(x['_id'])

    q = {'_id': {'$in': list(ids)}}
    u = {'$set': {'sfsync_date': None}}
    print(q)
    print(u)
    confirmaccounts_collection.update_many(q, u)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
