from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import csv
from bson import ObjectId
from pymongo import UpdateOne


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]


    batch = []
    for survey in survey_collection.find({'deleted': {'$ne': True}}):
    # for survey in survey_collection.find({'_id': ObjectId('67127859dbdc85e3062eab62')}):

        patches = {}

        for page_idx, page in enumerate(survey['config']['pages']):
            patches[f'config.pages.{page_idx}.elements.0.isRequired'] = True

        print(survey['_id'], patches)
        # print(survey)
        batch.append(UpdateOne({'_id': survey['_id']}, {'$set': patches}))

    survey_collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
