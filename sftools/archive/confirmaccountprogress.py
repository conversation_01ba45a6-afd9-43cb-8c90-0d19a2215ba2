from typing import Any
from lib import sfapi, portaldbapi
from simple_salesforce import format_soql, Salesforce
import argparse
import datetime
import boto3
import copy
from lib.settings import settings
import io
import csv
import uuid
import urllib
import pprint
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts



def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    f = open('confirm_account_progress.csv', 'w')
    writer = csv.writer(f)
    writer.writerow(['Customer Survey Name',
                     'Customer Survey URL',
                     'Account Manager Count',
                     'Account Managers Confirmed Count',
                     'Has Anyone Confirmed?',
                     'Account Manager Email',
                     'Account Manager URL',
                     'Has This Person Confirmed?',
                     'Confirmed Account Count'])

    for in_confirm_account in confirm_accounts_collection.find({}):
        customer_survey_id = in_confirm_account['Id']
        cs_url = f"https://clientrelationship.lightning.force.com/lightning/r/Customer_Survey__c/{customer_survey_id}/view"
        baserow = [in_confirm_account['customer_survey_name'],
                   cs_url,
                   len(in_confirm_account['account_managers']),
                   len(in_confirm_account['confirmed_accounts']),
                   'yes' if in_confirm_account['confirmed_accounts'] else 'no']

        for account_manager in in_confirm_account['account_managers']:
            account_manager_id = account_manager['account_manager_id']
            am_url = f"https://clientrelationship.lightning.force.com/lightning/r/Contact/{account_manager_id}/view"
            confirmed_accounts = in_confirm_account['confirmed_accounts'].get(account_manager_id)
            if confirmed_accounts is not None:
                confirmed_accounts_names = [x['account_name'] for x in confirmed_accounts['accounts']]
            else:
                confirmed_accounts_names = []

            row = copy.copy(baserow)
            row = baserow + [account_manager['account_manager_email'],
                             am_url,
                             'yes' if confirmed_accounts is not None else 'no',
                             len(confirmed_accounts['accounts']) if confirmed_accounts is not None else '',
                             " ".join(confirmed_accounts_names)
                             ]

            writer.writerow(row)




def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
