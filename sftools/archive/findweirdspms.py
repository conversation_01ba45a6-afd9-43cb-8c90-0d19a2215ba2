import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    patches = []
    with open('weirdspms.csv', 'w') as f:
        csvout = csv.writer(f)
        csvout.writerow(['Survey Panel Member Name', 'Contact Name', 'Contact Email', 'Survey Client Name'])

        soql = """
    SELECT Id,
            Name,
            Contact__r.Name,
            Contact__r.Email,
            Survey_Client__r.Name
    FROM Survey_Panel_Member__c
    WHERE Survey_Client__r.Current_Round__c = TRUE
    """
        for row in sf.query_all_iter(soql):
            if '@' in row['Name']:
                continue

            outrow = [row['Name'], row['Contact__r']['Name'], row['Contact__r']['Email'], row['Survey_Client__r']['Name']]
            csvout.writerow(outrow)

            patches.append({'Id': row['Id'], 'Name': row['Contact__r']['Email']})

    for patch in patches:
        print(patch)
        sf.Survey_Panel_Member__c.update(patch['Id'], {'Name': patch['Name']})


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()

