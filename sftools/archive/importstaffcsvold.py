import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy


def get_market_tree_children(markets: dict[str, sfapi.Market], parent: sfapi.Market):
    result = {}

    for item in markets.values():
        if item.ParentMarketId == parent.Id:
            result[item.Id] = item
            result.update(get_market_tree_children(markets, item))

    return result


def get_account_tree_children(accounts: dict[str, sfapi.Account], parent: sfapi.Account):
    result = {}

    for item in accounts.values():
        if item.ParentId == parent.Id:
            result[item.Id] = item
            result.update(get_account_tree_children(accounts, item))

    return result


def import_csv(accounts_filename, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Contact', 'CustomerClientRelationship', 'AccountGroup', 'Market', 'Team', 'ContactKeyAccount', 'CustomerSurvey', 'SurveyAccountManager']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")

    all_markets_by_name = {}
    for market in sfapi.get_all(sf, sfapi.Market):
        if market.Name in all_markets_by_name:
            raise Exception(f"Duplicate market name {market.Name} found.")
        all_markets_by_name[market.Name] = market
    SF_CLIENT_ORG_LEVELS = sfapi.get_picklist_values(sf, 'Account', 'Organisation_Level__c', accountRecordTypes[crc_customer_record_type].Id)

    # find all the markets in salesforce
    all_markets_by_id = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        all_markets_by_id[market.Id] = market

    # find all the accounts in salesforce
    all_accounts_by_id = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        all_accounts_by_id[account.Id] = account

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    # find the customer surveys by the agency account id
    customer_survey_by_agency_account_id = {}
    for customer_survey in sfimport.sfcache.values():
        if not isinstance(customer_survey, sfapi.CustomerSurvey):
            continue
        if not customer_survey.CurrentRound:
            continue
        customer_survey_by_agency_account_id.setdefault(customer_survey.CustomerId, []).append(customer_survey)

    # load data from the CSV file
    ccr_panelmanagers = {}
    ccr_signatories = {}
    contact_patch_team = {}
    contact_patch_reporting_role = {}
    contact_patch_account_manager = set()
    contact_patch_panel_manager = set()
    contact_patch_signatory = set()
    with open(accounts_filename) as file:
        reader = csv.reader(file)

        # figure out the header row
        header_row_lookup = {}
        header_row = []
        for col_idx, v in enumerate(next(reader)):
            if not v:
                continue
            v = v.strip().lower()
            header_row.append(v)
            header_row_lookup[v] = col_idx

        # load all the rows into ram and convert them into dicts + enumerate them
        all_rows = []
        for i, row in enumerate(reader):
            row = {k: row[v].strip() for k, v in header_row_lookup.items()}
            row['row_number'] = i + 1
            all_rows.append(row)

        # file to contain duff records
        duff_file = open('duffrecords-staff.csv', 'w')
        duff_header_row = copy.copy(header_row)
        for col in ['row_number', 'reason', 'customer_hierarchy']:
            if col not in duff_header_row:
                duff_header_row.append(col)
        duff_writer = csv.DictWriter(duff_file, fieldnames=duff_header_row)
        duff_writer.writeheader()

        # preprocess each row and compute various things
        contact_customer_hierachies = {}
        for row in all_rows:
            # skip empty rows
            if row['holding group'].strip() == '':
                continue

            # compute the account hierarchy with the correct names
            customer_hierarchy = []
            had_all = False
            had_all_error = False
            for colname in ['holding group', 'network', 'sub-network', 'agency brand', 'agency brand subsidiary']:
                if colname == 'holding group':
                    organisational_level = 'Holding Group'
                elif colname == 'network':
                    organisational_level = 'Network'
                elif colname == 'sub-network':
                    organisational_level = 'Sub-network'
                elif colname == 'agency brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'agency brand subsidiary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level {colname}")

                v = row[colname].strip()
                if v and had_all and v.lower() != 'all':
                    row['reason'] = 'ALL WAS NOT THE LAST LEVEL'
                    duff_writer.writerow(row)
                    had_all_error = True
                    customer_hierarchy = []  # invalidate the hierarchy on error
                    break
                if v.lower() == 'all':
                    had_all = True
                    v = None
                if v:
                    customer_hierarchy.append((v, organisational_level))
            row['customer_hierarchy'] = customer_hierarchy

            # we don't want to process this row if it had an error
            if had_all_error:
                continue

            # get market for this row
            market_name = row['market'].strip()
            row_market = all_markets_by_name.get(market_name)
            if row_market and not had_all:
                # add on "With market" level
                with_market_account_name = f"{customer_hierarchy[-1][0]} {row_market.Name}"
                customer_hierarchy.append((with_market_account_name, 'With Market'))

            # get list of hierarchies for each contact/email. convert them into dicts so its easy to compare
            unsafe_email_address = row['email address'].lower().strip().strip(';').strip(',')
            customer_hierarchy_dict = {x[1]: x[0] for x in customer_hierarchy}
            contact_customer_hierachies.setdefault(unsafe_email_address, []).append(customer_hierarchy_dict)

        # for each contact, figure out attachment point within the organisation hierarchy..
        # this is the lowest common level in the hierarchy
        contact_organisation_level = {}
        for unsafe_email_address, email_customer_hierachies in contact_customer_hierachies.items():
            contact_common_level = None
            for colname in sfimport.ORGANISATION_LEVELS:
                level_values = set([h.get(colname) for h in email_customer_hierachies])
                if len(level_values) == 1:
                    if level_values.pop() is not None:
                        contact_common_level = colname
                else:
                    break

            if not contact_common_level:
                print(unsafe_email_address, 'NO COMMON LEVEL')
                exit()
            contact_organisation_level[unsafe_email_address] = contact_common_level

        preconfirmed_customer_survey_accounts = {}
        ccr_patch_survey_names = {}
        for row in all_rows:
            if row['holding group'].strip() == '':
                continue

            # =======================================================
            # contact details
            unsafe_email_address = row['email address'].lower().strip().strip(';').strip(',')
            safe_email_address = sfimport.make_safe_email(unsafe_email_address)

            first_name = row['first name'].strip()
            last_name = row['last name'].strip()

            # check contact details are valid
            if not first_name or not last_name or not unsafe_email_address:
                row['reason']= 'MISSING NAME OR EMAIL'
                duff_writer.writerow(row)
                continue

            # =======================================================
            #
            customer_client_account_name = row['account'].strip()
            if customer_client_account_name in {'Nestle'}:
                customer_client_account_name = 'Nestlé'
            if customer_client_account_name.lower() == 'all':
                customer_client_account_name = None

            # =======================================================
            # decode the market information
            market_name = row['market'].strip()
            was_all_markets = False
            if market_name.lower() == 'all':
                market_name = market = None # special handling for 'All' market
                was_all_markets = True
            else:
                market = all_markets_by_name.get(market_name)
                if not market:
                    row['reason']= f'INVALID MARKET {market_name}'
                    duff_writer.writerow(row)
                    continue

            # =======================================================
            # extract the agency hierarchy
            customer_hierarchy = row['customer_hierarchy']
            if not customer_hierarchy:
                row['reason']= 'MISSING CUSTOMER NAME'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # load the team within the agency
            team_name = row['team name']
            if team_name:
                team_name = team_name.strip()

            # =======================================================
            # what are the person's client area roles?
            is_account_manager = row['account manager (yes/no)'].strip().lower() == 'yes'
            is_panel_manager = row['panel manager (yes/no)'].strip().lower() == 'yes'
            is_signatory = row['email sender (yes/no)'].strip().lower() == 'yes'

            # =======================================================
            # reporting role decoder
            roles = row['reporting role']
            reporting_role = None
            if 'Global Account Lead' in roles:
                reporting_role = 'Global Account View'

            # =======================================================
            # Traverse Account hierarchy to find real objects from salesforce
            contact_attachment_level = contact_organisation_level[unsafe_email_address]
            contact_agency_account = None
            agency_account = None
            agency_account_list = []
            for i, (hierachy_account_name, organisational_level) in enumerate(customer_hierarchy):
                if organisational_level not in SF_CLIENT_ORG_LEVELS:
                    row['reason']= 'INVALID CLIENT ORGANISATIONAL LEVEL'
                    duff_writer.writerow(row)
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(hierachy_account_name, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    break
                agency_account_list.append(agency_account)

                if organisational_level == contact_attachment_level:
                    contact_agency_account = agency_account

            if not agency_account:
                row['reason']= 'MISSING CRC ACCOUNT'
                duff_writer.writerow(row)
                continue
            if not contact_agency_account:
                row['reason']= 'MISSING CONTACT CRC ACCOUNT'
                duff_writer.writerow(row)
                continue

            # try and find the team
            team = None
            if team_name:
                team_market_name = row['team market'].strip()
                team_market = all_markets_by_name.get(team_market_name)
                if not team_market:
                    row['reason']= 'INVALID TEAM MARKET'
                    duff_writer.writerow(row)
                    continue

                team_business_key_partial = f"Team-{agency_account.Id}-{team_name}-{team_market.Id}-"
                for key in sfimport.sfcache.keys():
                    if key.startswith(team_business_key_partial):
                        team = sfimport.sfcache[key]
                        break

                if not team:
                    row['reason']= 'UNKNOWN TEAM'
                    duff_writer.writerow(row)
                    continue

            # find customer client account
            if customer_client_account_name:
                customers_client_account = customers_client_accounts_by_name.get(customer_client_account_name)
                if not customers_client_account:
                    row['reason']= 'UNKNOWN CUSTOMER CLIENT ACCOUNT'
                    duff_writer.writerow(row)
                    continue
                if len(customers_client_account) > 1:
                    row['reason']= 'MULTIPLE CUSTOMER CLIENT ACCOUNTS'
                    duff_writer.writerow(row)
                    continue
                customers_client_account = customers_client_account[0]

            else:
                customers_client_account = None

            # figure out the survey name
            survey_name = row['survey name']
            if not survey_name:
                survey_name = agency_account_list[-2].Name

            # create contact and attach to crc_account
            contact_email = safe_email_address if safeemails else unsafe_email_address
            contact = sfimport.get_or_create_cached_contact(contact_agency_account,
                                                            first_name,
                                                            last_name,
                                                            contact_email,
                                                            accountsmanager=is_account_manager or False,
                                                            panelmanager=is_panel_manager or False,
                                                            signatory=is_signatory or False,
                                                            team=team,
                                                            reporting_role=reporting_role)
            if team and not contact.Id.startswith('Contact-'):
                contact_patch_team[contact.Id] = team
            if reporting_role and not contact.Id.startswith('Contact-'):
                contact_patch_reporting_role[contact.Id] = reporting_role

            # create customer client relationship
            if customers_client_account:
                customer_client_relationship = sfimport.get_or_create_cached_customer_client_relationship(agency_account,
                                                                                                          customers_client_account,
                                                                                                          'Client')
                if not customer_client_relationship.Id.startswith('CustomerClientRelationship-'):
                    customer_client_relationship.SurveyName = survey_name
            else:
                customer_client_relationship = None

            # patch survey name if it is different on a relationship already in SF
            if customer_client_relationship.SurveyName != survey_name:
                ccr_patch_survey_names[customer_client_relationship.Id] = survey_name

            # create contact key accounts
            if reporting_role == 'Global Account View' and customers_client_account:
                sfimport.get_or_create_cached_contact_key_account(contact, customers_client_account)

            if is_panel_manager:
                if not contact.PanelManager and not contact.Id.startswith('Contact-'):
                    contact_patch_panel_manager.add(contact.Id)
                contact.PanelManager = True
                if customer_client_relationship:
                    if customer_client_relationship.Id not in ccr_panelmanagers:
                        ccr_panelmanagers[customer_client_relationship.Id] = contact.Id
                    customer_client_relationship.SurveyPanelManagerId = contact.Id

            if is_signatory:
                if not contact.Signatory and not contact.Id.startswith('Contact-'):
                    contact_patch_signatory.add(contact.Id)
                contact.Signatory = True
                if customer_client_relationship:
                    if customer_client_relationship.Id not in ccr_signatories:
                        ccr_signatories[customer_client_relationship.Id] = contact.Id
                    customer_client_relationship.SignatoryId = contact.Id

            if is_account_manager is True:
                if not contact.AccountsManager and not contact.Id.startswith('Contact-'):
                    contact_patch_account_manager.add(contact.Id)
                contact.AccountsManager = True

                # ok, any sort of market filtering going on?
                filter_markets = None
                if row['region'] or not was_all_markets:
                    region_name = row['region'].strip()
                    if not region_name:
                        region_name = row['market'].strip()
                    filter_markets = None
                    region = None
                    if region_name:
                        # find the region
                        for item in sfimport.sfcache.values():
                            if not isinstance(item, sfapi.Market):
                                continue
                            if item.Name != region_name:
                                continue
                            region = item
                            break
                        if not region:
                            row['reason']= 'INVALID REGION'
                            duff_writer.writerow(row)
                            continue

                        # find all the child regions of that region
                        filter_markets = {region.Id: region}
                        filter_markets.update(get_market_tree_children(all_markets_by_id, region))

                # find all accounts underneath the agency_account
                all_child_accounts = {agency_account.Id: agency_account}
                all_child_accounts.update(get_account_tree_children(all_accounts_by_id, agency_account))

                # now apply the filter markets
                crc_accounts_managed = {}
                for account in all_child_accounts.values():
                    if filter_markets is not None and account.MarketId not in filter_markets:
                        continue

                    crc_accounts_managed[account.Id] = account

                # setup the survey account managers
                for customer_id in crc_accounts_managed:
                    for customer_survey in customer_survey_by_agency_account_id.get(customer_id, []):

                        # filter for teams
                        if team is not None and customer_survey.TeamId != team.Id:
                            continue

                        sfimport.get_or_create_cached_survey_account_manager(customer_survey, contact)

                        preconfirmed_customer_survey_accounts.setdefault(customer_survey.Id, {})
                        preconfirmed_customer_survey_accounts[customer_survey.Id]['cs'] = customer_survey
                        preconfirmed_customer_survey_accounts[customer_survey.Id].setdefault('am', {})
                        preconfirmed_customer_survey_accounts[customer_survey.Id]['am'].setdefault(contact.Id, {})
                        preconfirmed_customer_survey_accounts[customer_survey.Id]['am'][contact.Id]['contact'] = contact
                        preconfirmed_customer_survey_accounts[customer_survey.Id]['am'][contact.Id].setdefault('accounts', {})
                        preconfirmed_customer_survey_accounts[customer_survey.Id]['am'][contact.Id]['accounts'][customers_client_account.Id] = customers_client_account

    # output CSV of preconfirmed accounts
    filename = os.path.split(accounts_filename)[1]
    base_filename = os.path.splitext(filename)[0]
    preconfirmed_filename = f'{base_filename}.preconfirmed_accounts.csv'
    with open(preconfirmed_filename, 'w') as f:
        csvout = csv.writer(f)
        csvout.writerow(['CustomerSurveyId', 'AccountManagerContactId', 'CustomerClientAccountId'])
        for cs in preconfirmed_customer_survey_accounts.values():
            for am in cs['am'].values():
                for acc in am['accounts'].values():
                    csvout.writerow([cs['cs'].Id, am['contact'].Id, acc.Id])

    if writeit:
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        for contact_id in contact_patch_account_manager:
            sfapi.Contact.set_accounts_manager(sf, contact_id, True)
        for contact_id in contact_patch_panel_manager:
            sfapi.Contact.set_panel_manager(sf, contact_id, True)
        for contact_id in contact_patch_signatory:
            sfapi.Contact.set_signatory(sf, contact_id, True)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_key_accounts, sfapi.ContactKeyAccount)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_account_managers, sfapi.SurveyAccountManager)

        for ccr_id, contact_id in ccr_panelmanagers.items():
            ccr_id = sfimport.sfid_mapping[ccr_id]
            contact_id = sfimport.sfid_mapping[contact_id]
            sfapi.CustomerClientRelationship.set_panel_manager(sf, ccr_id, contact_id)
        for ccr_id, signatory_id in ccr_signatories.items():
            ccr_id = sfimport.sfid_mapping[ccr_id]
            signatory_id = sfimport.sfid_mapping[signatory_id]
            sfapi.CustomerClientRelationship.set_signatory(sf, ccr_id, signatory_id)
        for ccr_id, survey_name in ccr_patch_survey_names.items():
            sf.Customer_Client_Relationship__c.update(ccr_id, {'Survey_Name__c': survey_name})
        for contact_id, team in contact_patch_team.items():
            sf.Contact.update(contact_id, {'Team__c': team.Id})
        for contact_id, role in contact_patch_reporting_role.items():
            sf.Contact.update(contact_id, {'Reporting_Role__c': role})
    else:
        print(len(sfimport.new_contacts), "new contacts")
        print(len(sfimport.new_customer_client_relationships), "new customer client relationships")
        print(len(sfimport.new_contact_key_accounts), "new contact key accounts")
        print(len(sfimport.new_survey_account_managers), "new survey account managers")

        print("Dry run, not writing to Salesforce")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('accounts_filename', help='CSV file to import')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.accounts_filename, args.safeemails, args.writeit)
