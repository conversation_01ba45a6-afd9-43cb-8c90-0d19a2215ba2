from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import datetime


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    all_contacts_by_id: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        contact_email = contact.Email.lower()
        all_contacts_by_email[contact_email] = contact
        all_contacts_by_id[contact.Id] = contact

    all_survey_clients_by_id: dict[str, sfapi.SurveyClient] = {}
    for sc in sfapi.get_all(sf, sfapi.SurveyClient, bulk=True, query_suffix='WHERE Customer_Survey__r.Current_Round__c = true'):
        all_survey_clients_by_id[sc.Id] = sc

    all_panel_managers_by_survey_client_id: dict[str, sfapi.SurveyPanelManager] = {}
    for spm in sfapi.get_all(sf, sfapi.SurveyPanelManager, bulk=True, query_suffix='WHERE Survey_Client__r.Customer_Survey__r.Current_Round__c = true'):
        all_panel_managers_by_survey_client_id.setdefault(spm.SurveyClientId, {})[spm.ContactId] = spm

    zap_confirm_panels = set()
    for pdb_confirm_panel in confirm_panel_collection.find({}):
        set_updates = {}
        sf_survey_client = all_survey_clients_by_id.get(pdb_confirm_panel['Id'])
        if not sf_survey_client:
            zap_confirm_panels.add(pdb_confirm_panel['Id'])
            continue

        # update the survey name to match salesforce if there is one in salesforce
        if pdb_confirm_panel['survey_name'] != sf_survey_client.SurveyName and sf_survey_client.SurveyName:
            set_updates['survey_name'] = sf_survey_client.SurveyName
            pdb_confirm_panel['survey_name'] = sf_survey_client.SurveyName
        survey_name = pdb_confirm_panel['survey_name']

        # now update the panel managers to match salesforce
        sf_survey_panel_managers_by_contact_id = all_panel_managers_by_survey_client_id.get(pdb_confirm_panel['Id'], {})
        out_survey_panel_managers = []
        seen_sf_spms = set()
        out_survey_panel_managers_changed = False

        # handle existing SPMs or SPMs removed from salesforce
        for pdb_spm in pdb_confirm_panel['panel_managers']:
            sf_spm = sf_survey_panel_managers_by_contact_id.get(pdb_spm['panel_manager_id'])

            if sf_spm:
                out_survey_panel_managers.append(pdb_spm)  # SPM as expected
                seen_sf_spms.add(sf_spm.ContactId)
            else:
                # SPM was removed from SF - drop it from this list
                # FIXME: check with robbie if this is correct wrt delegation
                out_survey_panel_managers_changed = True

        # now handle new panel managers
        for sf_spm in sf_survey_panel_managers_by_contact_id.values():
            if sf_spm.ContactId in seen_sf_spms:
                continue
            contact = all_contacts_by_id.get(sf_spm.ContactId)
            if not contact:
                continue

            # OK, a new SPM has appeared in SF
            out_survey_panel_managers.append({
                'panel_manager_id': contact.Id,
                'panel_manager_name': f"{contact.FirstName} {contact.LastName}",
                'panel_manager_email': contact.Email,
            })
            seen_sf_spms.add(sf_spm.ContactId)
            out_survey_panel_managers_changed = True
        if out_survey_panel_managers_changed:
            set_updates['panel_managers'] = out_survey_panel_managers

        # fixup the panel members survey name
        for idx, potential_panel_member in enumerate(pdb_confirm_panel['panel']):
            if survey_name and potential_panel_member['survey_name'] != survey_name:
                set_updates[f'panel.{idx}.survey_name'] = survey_name

        # patch any panel members that are "new", but actually exist in salesforce already
        for panel_manager_id, deets in pdb_confirm_panel['confirmed_panel'].items():
            for idx, panel_member in enumerate(deets['panel']):
                if panel_member['contact_id'].startswith('NEW-'):
                    contact = all_contacts_by_email.get(panel_member['contact_email'].lower())
                    if not contact:
                        print("New contact: ", panel_member['contact_email'], panel_member['contact_name'])
                        continue

                    patched_panel_member = {
                        'contact_id': contact.Id,
                        'contact_name': f"{contact.FirstName} {contact.LastName}",
                        'contact_email': contact.Email,
                        'contact_type': contact.ContactType,
                        'contact_seniority': contact.Seniority,
                        'contact_job_function': contact.JobFunction,
                        'contact_division': contact.Division,
                        'account_id': contact.AccountId,
                        'serial_non_responder': False,
                        'in_round': True,
                    }
                    set_updates[f'confirmed_panel.{panel_manager_id}.panel.{idx}'] = patched_panel_member

        # FIXME: fix email sender how?

        # if no change -> don't do anything
        if not set_updates:
            continue

        print("Updating Confirm Panel: ", pdb_confirm_panel['Id'], pdb_confirm_panel['customer_survey_name'], pdb_confirm_panel['client_name'])
        print(set_updates)
        set_updates['fixconfirmpanel_stamp'] = datetime.datetime.now()
        update = {
            '$set': set_updates
        }

        if writeit:
            confirm_panel_collection.update_one({'Id': pdb_confirm_panel['Id']}, update, upsert=False)

    if zap_confirm_panels:
        print(len(zap_confirm_panels), "defunct confirm panels to zap")
        if writeit:
            confirm_panel_collection.delete_many({'Id': {'$in': list(zap_confirm_panels)}})


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true')
    args = ap.parse_args()

    main(args.writeit)
