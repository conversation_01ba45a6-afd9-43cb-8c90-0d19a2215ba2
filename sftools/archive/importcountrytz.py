import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings
from lib import sfimport



def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    country_tz = {}
    with open('countries_timezones_unix.csv', 'r') as filein:
        csvin = csv.DictReader(filein)
        for row in csvin:
            country_tz[row['Country']] = row['Timezone']


    patch = []
    for item in sfapi.get_all(sf, sfapi.Market, bulk=True):
        if item.MarketType != 'Country':
            continue
        if item.Timezone:
            continue
        name = item.Name
        if name == 'SriLanka':
            name = 'Sri Lanka'
        tz = country_tz.get(name)
        if not tz:
            print(name)
            continue

        patch.append({
            'Id': item.Id,
            'Timezone__c': tz
        })

    for record in sfapi.bulk_update(sf, "Market__c", patch):
        sfapi.detect_bulk2_errors(record)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
