import csv
import lib.sfapi as sfapi
import lib.portaldbapi as portaldbapi
import argparse
import os
from lib.settings import settings
from lib import sfimport
import datetime


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    all_accounts = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        all_accounts[account.Id] = account

    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    for confirm_account in list(collection.find()):

        dupes = 0
        missing = 0
        seenaccounts = {}
        output_accounts = []
        input_accounts = confirm_account['accounts']
        for account in input_accounts:
            account_id = account['account_id']

            # account is no longer in salesforce -> remove it
            if account_id not in all_accounts:
                missing += 1
                continue

            # we've already seen this account, so merge the survey_panel_managers
            if account_id in seenaccounts:
                output_account = seenaccounts[account_id]
                output_account['survey_panel_managers'] += account['survey_panel_managers']
                dupes += 1
                continue

            seenaccounts[account_id] = account
            output_accounts.append(account)

        if dupes or missing:
            update = {
                '$set': {
                    'accounts': output_accounts,
                    'old_accounts': input_accounts,
                    'fixzappedaccounts_stamp': datetime.datetime.now(),
                }
            }
            # print(update)
            collection.update_one({'_id': confirm_account['_id']}, update)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
