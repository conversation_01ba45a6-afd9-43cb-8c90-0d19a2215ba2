import pymongo.collection
import pymongo
from lib import sfapi, portaldbapi
from simple_salesforce import format_soql, Salesforce
import bson
import datetime
from lib.settings import settings
import time


SF_BATCH_SIZE = 1   # 000


def sync_batch_contacts_to_sf(sf: Salesforce,
                              sf_mapping: dict[str, str],
                              sf_contacts_to_create: list[sfapi.Account],
                              confirm_accounts_collection: pymongo.collection.Collection,
                              account_oids: set) -> None:

    for c in sf_contacts_to_create:
        sf_mapping[c.AccountId] = c.AccountId
    sfapi.bulk_import_batch_to_sf(sf, sf_mapping, {}, sf_contacts_to_create, sfapi.Contact)

    # update the confirm accounts with the new contact ids
    query = {
        '_id': {'$in': list(account_oids)}
    }
    confirm_accounts = confirm_accounts_collection.find(query)

    for confirm_account in confirm_accounts:
        for account in confirm_account['accounts']:
            for survey_panel_manager in account['survey_panel_managers']:
                new_contact_id = sf_mapping.get(survey_panel_manager['panel_manager_id'])
                if new_contact_id:
                    survey_panel_manager['panel_manager_id'] = new_contact_id

        confirm_accounts_collection.update_one({'_id': confirm_account['_id']}, {'$set': {'accounts': confirm_account['accounts']}})


def sync_batch_accounts_to_sf(sf: Salesforce,
                              sf_mapping: dict[str, str],
                              sf_accounts_to_create: list[sfapi.Account],
                              sf_relationships_to_create: list[sfapi.CustomerClientRelationship],
                              confirm_accounts_collection: pymongo.collection.Collection,
                              account_oids: set) -> None:

    sfapi.bulk_import_batch_to_sf(sf, sf_mapping, {}, sf_accounts_to_create, sfapi.Account)

    # create the customer client relationship
    for relationship in sf_relationships_to_create:
        new_account_id = sf_mapping.get(relationship.CustomersClientAccountId)
        relationship.CustomersClientAccountId = new_account_id

    for r in sf_relationships_to_create:
        sf_mapping[r.CustomerAccountId] = r.CustomerAccountId
        sf_mapping[r.CustomersClientAccountId] = r.CustomersClientAccountId

    sfapi.bulk_import_batch_to_sf(sf, sf_mapping, {}, sf_relationships_to_create, sfapi.CustomerClientRelationship)

    # update the confirm accounts with the new account ids
    query = {
        '_id': {'$in': list(account_oids)}
    }
    confirm_accounts = confirm_accounts_collection.find(query)

    for confirm_account in confirm_accounts:
        for account in confirm_account['accounts']:
            new_account_id = sf_mapping.get(account['account_id'])
            if new_account_id:
                account['account_id'] = new_account_id

        confirm_accounts_collection.update_one({'_id': confirm_account['_id']}, {'$set': {'accounts': confirm_account['accounts']}})


def sync_survey_clients_batch_to_sf(sf: Salesforce,
                     sf_survey_clients_to_create: list[sfapi.SurveyClient],
                     sf_survey_clients_to_delete: set[str],
                     sf_survey_panel_managers_to_create: list[sfapi.SurveyPanelManager]) -> None:

    # make up an sfid mapping so the automatic code will work when inserting new SurveyClients
    sfid_mapping = {}
    for sc in sf_survey_clients_to_create:
        sfid_mapping[sc.CustomerSurveyId] = sc.CustomerSurveyId
        sfid_mapping[sc.CustomersClientId] = sc.CustomersClientId
    for spm in sf_survey_panel_managers_to_create:
        sfid_mapping[spm.ContactId] = spm.ContactId

    created_survey_clients: dict[str, sfapi.SurveyClient] = {}
    sfapi.bulk_import_batch_to_sf(sf, sfid_mapping, created_survey_clients, sf_survey_clients_to_create, sfapi.SurveyClient)

    created_survey_panel_managers: dict[str, sfapi.SurveyPanelManager] = {}
    sfapi.bulk_import_batch_to_sf(sf, sfid_mapping, created_survey_panel_managers, sf_survey_panel_managers_to_create, sfapi.SurveyPanelManager)

    # delete any old survey clients from prior runs
    if sf_survey_clients_to_delete:
        statuses = sfapi.bulk_delete(sf, "Survey_Client__c", sf_survey_clients_to_delete)
        for status in statuses:
            sfapi.detect_bulk2_errors(status)


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    # # find all customersurveys that have been confirmed or need auto-confirmed and unsynced
    # today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)
    # pipeline = [
    #     {
    #         "$match" : {
    #             "sfsync_date" : None,
    #             "account_managers.0" : { "$exists" : True },
    #         }
    #     },
    #     {
    #         "$addFields" : {
    #             "confirmed_accounts_count" : {
    #                 "$size" : {
    #                     "$objectToArray" : { "$ifNull": [ "$confirmed_accounts", {} ] }
    #                 }
    #             },
    #             "account_managers_count" : {
    #                 "$size" : {
    #                     "$ifNull": [ "$account_managers", [] ]
    #                 }
    #             }
    #         }
    #     },
    #     {
    #         "$redact" : {
    #             "$cond" : {
    #                 "if" : {
    #                     "$or" : [
    #                         { "$eq": [ "$confirmed_accounts_count", "$account_managers_count" ] },
    #                         { "$lt": [ "$accounts_confirmed_by_date", today ] }
    #                     ]
    #                 },
    #                 "then": "$$KEEP",
    #                 "else": "$$PRUNE"
    #             }
    #         }
    #     }
    # ]
    # portaldb_customer_surveys = list(confirm_accounts_collection.aggregate(pipeline))


    # q = {
    #     "sfsync_date" : None,
    #     "account_managers.0" : { "$exists" : True },
    # }
    # portaldb_customer_surveys = list(confirm_accounts_collection.find(q))

    # # sync them
    # account_oids = set()
    # sf_accounts_to_create = []
    # sf_relationships_to_create = []
    # sf_contacts_to_create = []
    # sf_survey_clients_to_create = []
    # sf_survey_clients_to_delete = set()
    # sf_survey_panel_managers_to_create = []
    # sc_idx = 0
    # spm_idx = 0
    # sf_mapping = {
    #     None: None
    # }

    # for portaldb_customer_survey in portaldb_customer_surveys:
    #     oid = portaldb_customer_survey['_id']
    #     portaldb_customer_survey = portaldbapi.ConfirmAccounts(**portaldb_customer_survey)

    #     # determine if there have been any records created
    #     for portaldb_survey_clients in portaldb_customer_survey.confirmed_accounts.values():
    #         for portaldb_survey_client in portaldb_survey_clients.accounts:
    #             if not portaldb_survey_client.in_round:
    #                 continue

    #             customers_client_id = portaldb_survey_client.account_id
    #             customers_client_name = portaldb_survey_client.account_name

    #             # check if there have been new accounts created
    #             if customers_client_id.startswith('NEW-'):
    #                 print("HAD NEW ACCOUNT")
    #                 exit()
    #     #             # new_account = sfapi.Account(
    #     #             #     Id=customers_client_id,
    #     #             #     Name=customers_client_name,
    #     #             #     ParentId=None,
    #     #             #     RecordTypeId='0128e000000S0k5AAC',
    #     #             #     Created_In_Client_Area__c=True,
    #     #             #     CurrencyIsoCode="GBP"
    #     #             # )

    #     #             # new_relationship = sfapi.CustomerClientRelationship(
    #     #             #     Id=f"CustomerClientRelationship-{portaldb_customer_survey.client_name}-{customers_client_name}",
    #     #             #     Name=f"{portaldb_customer_survey.client_name}-{customers_client_name}",
    #     #             #     Customer_Account__c=portaldb_customer_survey.client_id,
    #     #             #     Customers_Client__c=customers_client_id,
    #     #             #     Relationship_Type__c='Client',
    #     #             # )

    #     #             # sf_accounts_to_create.append(new_account)
    #     #             # sf_relationships_to_create.append(new_relationship)
    #     #             # account_oids.add(oid)

    #     #     # if len(sf_accounts_to_create) > SF_BATCH_SIZE:
    #     #     #     sync_batch_accounts_to_sf(sf, sf_mapping, sf_accounts_to_create, sf_relationships_to_create, confirm_accounts_collection, account_oids)
    #     #     #     sf_accounts_to_create.clear()
    #     #     #     sf_relationships_to_create.clear()
    #     #     #     account_oids.clear()

    #     # determine if there have been any panel managers created
    #     for portaldb_survey_clients in portaldb_customer_survey.confirmed_accounts.values():
    #         for portaldb_survey_client in portaldb_survey_clients.accounts:
    #             if not portaldb_survey_client.in_round:
    #                 continue

    #             for survey_panel_manager in portaldb_survey_client.survey_panel_managers:
    #                 panel_manager_id = survey_panel_manager.panel_manager_id

    #                 if panel_manager_id.startswith('NEW-'):
    #                     print("HAD NEW PANEL MANAGER")
    #                     exit()
    #     #                 new_contact = sfapi.Contact(
    #     #                     Id=panel_manager_id,
    #     #                     FirstName=survey_panel_manager.panel_manager_first_name,
    #     #                     LastName=survey_panel_manager.panel_manager_last_name,
    #     #                     Email=survey_panel_manager.panel_manager_email,
    #     #                     AccountId=portaldb_customer_survey.client_id,
    #     #                     Panel_Manager__c=True,
    #     #                     Created_In_Client_Area__c=True,
    #     #                 )

    #     #                 sf_contacts_to_create.append(new_contact)
    #     #                 account_oids.add(oid)

    #     #     if len(sf_contacts_to_create):
    #     #         sync_batch_contacts_to_sf(sf, sf_mapping, sf_contacts_to_create, confirm_accounts_collection, account_oids)
    #     #         sf_contacts_to_create.clear()

    #     # if len(sf_accounts_to_create):
    #     #     sync_batch_accounts_to_sf(sf, sf_mapping, sf_accounts_to_create, sf_relationships_to_create, confirm_accounts_collection, account_oids)
    #     #     sf_accounts_to_create.clear()
    #     #     sf_relationships_to_create.clear()
    #     #     account_oids.clear()

    #     # if len(sf_contacts_to_create):
    #     #     sync_batch_contacts_to_sf(sf, sf_mapping, sf_contacts_to_create, confirm_accounts_collection, account_oids)
    #     #     sf_contacts_to_create.clear()

    # # add new survey clients to batch
    # # NOTE: fetching customer surveys again as they may have been updated in the previous loop

    q = {
        "sfsync_date" : None,
        "account_managers.0" : { "$exists" : True },
    }
    portaldb_customer_surveys = list(confirm_accounts_collection.find(q))

    # portaldb_customer_surveys = list(confirm_accounts_collection.aggregate(pipeline))



    # sf_accounts_to_create = []
    # sf_relationships_to_create = []
    # sf_contacts_to_create = []
    sf_survey_clients_to_create = []
    sf_survey_clients_to_delete = set()
    sf_survey_panel_managers_to_create = []
    sf_customer_survey_updates: list = []
    portaldb_customer_survey_updates: list = []
    sc_idx = 0
    spm_idx = 0
    count = 0
    for portaldb_customer_survey in portaldb_customer_surveys:
        oid = portaldb_customer_survey['_id']
        print(f"Syncing {oid}")
        portaldb_customer_survey = portaldbapi.ConfirmAccounts(**portaldb_customer_survey)

        # merge and dedupe accounts across all account managers confirmations
        merged_deduped_account_lookup: dict[str, list[portaldbapi.ConfirmAccountsAccount]] = {}
        merged_deduped_accounts: list[portaldbapi.ConfirmAccountsAccount] = []
        for survey_account_manager_id, account_confirmation_details in portaldb_customer_survey.confirmed_accounts.items():
            for account in account_confirmation_details.accounts:
                account.account_manager_id = survey_account_manager_id
                if account.account_id in merged_deduped_account_lookup:
                    account.duplicate_account = True
                merged_deduped_account_lookup.setdefault(account.account_id, {}).update(account)

        merged_deduped_accounts = [accounts for accounts in merged_deduped_account_lookup.values()]

        # create the survey clients
        for portaldb_survey_client in merged_deduped_accounts:
            if not portaldb_survey_client['in_round']:
                continue

            customers_client_id = portaldb_survey_client['account_id']
            customers_client_name = portaldb_survey_client['account_name']
            survey_panel_managers = portaldb_survey_client['survey_panel_managers']
            signatory_id = portaldb_survey_client['signatory_id']
            duplicate_account = portaldb_survey_client['duplicate_account']

            # we create a clean new object for salesforce insertion
            new_survey_client = sfapi.SurveyClient(
                Id=f"SurveyClient-{sc_idx}",
                Name=f"{portaldb_customer_survey.customer_survey_name} {customers_client_name}"[:79],
                State__c="Panel Pending",
                Customer_Survey__c=portaldb_customer_survey.Id,
                Customers_Client__c=customers_client_id,
                Customers_Client_Name__c=customers_client_name,
                Survey_Name__c=portaldb_customer_survey.client_name,
                Signatory__c=signatory_id,
                Duplicate_Accounts_Detected__c=duplicate_account,
            )
            sf_survey_clients_to_create.append(new_survey_client)

            for survey_panel_manager in survey_panel_managers:
                new_survey_panel_manager = sfapi.SurveyPanelManager(
                    Id=f"SurveyPanelManager-{spm_idx}",
                    Survey_Client__c=new_survey_client.Id,
                    Contact__c=survey_panel_manager.panel_manager_id,
                    First_Name__c=survey_panel_manager.panel_manager_first_name,
                    Last_Name__c=survey_panel_manager.panel_manager_last_name,
                )
                sf_survey_panel_managers_to_create.append(new_survey_panel_manager)

                spm_idx += 1

            sc_idx += 1

        sf_customer_survey_updates.append({
            "Id": portaldb_customer_survey.Id,
            "State__c": "Survey Clients Agreed",
            "Accounts_Auto_Confirmed__c": bool(len(portaldb_customer_survey.confirmed_accounts.keys()) < len(portaldb_customer_survey.account_managers)),
        })
        portaldb_customer_survey_updates.append({
            '_id': oid,
            'sfsync_date': datetime.datetime.now(datetime.UTC),
            'confirm_status': True,
            'auto_confirmed': bool(len(portaldb_customer_survey.confirmed_accounts.keys()) < len(portaldb_customer_survey.account_managers)),
            'confirm_date': datetime.datetime.now(datetime.UTC)
        })

        # get list of existing survey clients in salesforce
        query = format_soql(f"SELECT Id from Survey_Client__c WHERE Customer_Survey__c = '{portaldb_customer_survey.Id}'")
        for row in sf.query_all_iter(query):
            sf_survey_clients_to_delete.add(row['Id'])

        # if len(sf_survey_clients_to_create) > SF_BATCH_SIZE:
        #     print("Create survey clients batch")
        #     sync_survey_clients_batch_to_sf(sf, sf_survey_clients_to_create, sf_survey_clients_to_delete, sf_survey_panel_managers_to_create)
        #     sf_survey_clients_to_create.clear()
        #     sf_survey_panel_managers_to_create.clear()

        # exit loop after 1 cycle
        count += 1
        if count > 10:
            break

    if len(sf_survey_clients_to_create):
        print("Create survey clients batch")
        print(sf_survey_clients_to_create)
        sync_survey_clients_batch_to_sf(sf, sf_survey_clients_to_create, sf_survey_clients_to_delete, sf_survey_panel_managers_to_create)
        sf_survey_clients_to_create.clear()
        sf_survey_panel_managers_to_create.clear()

    # update the status on the SF Customer Survey objects we retrieved above
    if len(sf_customer_survey_updates):
        print("update CS status")
        # print(sf_customer_survey_updates)
        statuses = sfapi.bulk_update(sf, "Customer_Survey__c", sf_customer_survey_updates)
        for status in statuses:
            sfapi.detect_bulk2_errors(status)

    # reflect that back onto the portaldb confirm accounts now its written to sf
    print("UDATE PORTALDB status")
    for portaldb_customer_survey_update in portaldb_customer_survey_updates:
        # print(portaldb_customer_survey_update)
        oid = portaldb_customer_survey_update.pop('_id')
        confirm_accounts_collection.update_one(
            {'_id': oid, 'sfsync_date': None},
            {'$set': portaldb_customer_survey_update
            }
        )

    return len(portaldb_customer_survey_updates) > 0


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    while True:
        if not main():
            break

        time.sleep(1)
        # input("Next")
