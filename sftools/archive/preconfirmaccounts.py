from lib import sfapi, portaldbapi
import argparse
import datetime
from lib.settings import settings
import csv


def main(filename, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    updates_by_csid = {}
    with open(filename, 'r') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            updates_by_csid.setdefault(row['CustomerSurveyId'], {}).setdefault(row['AccountManagerContactId'], set()).add(row['CustomerClientAccountId'])

    for csid, cs_updates in updates_by_csid.items():
        cs = confirm_accounts_collection.find_one({'Id': csid})
        if not cs:
            print(f'CS not found: {csid}')
            continue

        accounts_by_id = {}
        for account in cs['accounts']:
            accounts_by_id[account['account_id']] = account

        updates = {}
        for amid, accids in cs_updates.items():
            confirmed_accounts = {'accounts': [], 'confirm_date': datetime.datetime.now()}
            for accid in accids:
                account = accounts_by_id[accid]
                confirmed_accounts['accounts'].append(account)

            updates[f'confirmed_accounts.{amid}'] = confirmed_accounts

        updates['autoconfirmed_stamp'] = datetime.datetime.now()

        print(csid)
        if writeit:
            confirm_accounts_collection.update_one({'Id': csid}, {'$set': updates})
        else:
            print(updates)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('filename')
    ap.add_argument('--writeit', action='store_true')
    args = ap.parse_args()

    main(args.filename, args.writeit)
