import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import datetime


def import_csv(metadata_filename, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # populate cache
    print("Populating cache")
    for objname in ['Contact']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")
    SF_TIMEZONES = sfapi.get_picklist_values(sf, 'Contact', 'Timezone__c')

    # load/sort/dedupe everything
    all_rows = []
    with open(metadata_filename) as f:
        csvin = csv.DictReader(f)

        # get all rows and sort by last_modified descending so we get the latest version of each row
        salutations = set()
        for row in csvin:
            row['last_modified'] = datetime.datetime.fromisoformat(row['last_modified'])
            all_rows.append(row)
            salutations.add(row['legacy_title'])
        all_rows.sort(key=lambda x: x['last_modified'], reverse=True)

        # dedupe by email address
        contact_metadata_by_email = {}
        for row in all_rows:
            email = row['legacy_email'].lower().strip()
            if email not in contact_metadata_by_email:
                contact_metadata_by_email[email] = row

        # figure out patches
        patch_timezones = []
        for contact in sfimport.sfcache.values():
            if not isinstance(contact, sfapi.Contact):
                continue

            email = contact.Email
            if not contact.Email:
                continue
            contact_metadata = contact_metadata_by_email.get(email.lower())
            if not contact_metadata:
                continue

            tz = contact_metadata['legacy_timezone']
            if contact.Timezone or not tz or not tz in SF_TIMEZONES:
                continue

            patch_timezones.append({
                'Id': contact.Id,
                'Timezone__c': tz
            })

    if writeit:
        for record in sfapi.bulk_update(sf, "Contact", patch_timezones):
            sfapi.detect_bulk2_errors(record)

    else:
        print("Dry run, not writing to Salesforce")
        print(len(patch_timezones), "contacts to update")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('metadata_filename', help='CSV file to import')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    args = ap.parse_args()

    import_csv(args.metadata_filename, args.writeit)
