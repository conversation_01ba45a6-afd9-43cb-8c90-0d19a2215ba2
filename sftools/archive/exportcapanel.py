import lib.portaldbapi as portaldbapi
import argparse
import csv
import datetime



def main():
    portaldb = portaldbapi.DocumentDB()

    with open('panelstatus.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=['panel_manager_name', 'panel_manager_email', 'client_name', 'account_name', 'team_name', 'survey_client_link', 'panel_member_name', 'panel_member_email', 'confirmed_date', 'was_in_last_round', 'auto_confirmed', 'in_round'])
        csvout.writeheader()

        for confirm_panel in portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION].find():
            panel_managers_by_id = {x['panel_manager_id']: x for x in confirm_panel.get('panel_managers', [])}
            panel_members = confirm_panel['panel']
            panel_members_by_email = {x['contact_email']: x for x in panel_members}
            confirmed_panel = confirm_panel['confirmed_panel']
            auto_confirm_date = confirm_panel['panel_confirmed_by_date'] + datetime.timedelta(days=1)

            if not confirmed_panel and datetime.datetime.now() < confirm_panel['panel_confirmed_by_date'] + datetime.timedelta(days=1):
                continue

            for panel_manager_id, deets  in confirmed_panel.items():
                confirmed_date = deets['confirm_date'].replace(microsecond=0).isoformat()
                panel_manager = panel_managers_by_id.get(panel_manager_id, {'panel_manager_name': panel_manager_id, 'panel_manager_email': panel_manager_id})

                seen_panel_members = set()
                for panel_member in deets['panel']:
                    csvout.writerow({'panel_manager_name': panel_manager['panel_manager_name'],
                                'panel_manager_email': panel_manager['panel_manager_email'],
                                'client_name': confirm_panel['client_name'],
                                'account_name': confirm_panel['account_name'],
                                'team_name': confirm_panel['team_name'],
                                'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                                'panel_member_name': panel_member['contact_name'],
                                'panel_member_email': panel_member['contact_email'],
                                'confirmed_date': confirmed_date,
                                'was_in_last_round': panel_members_by_email.get(panel_member['contact_email'], {}).get('in_round', False),
                                'auto_confirmed': False,
                                'in_round': True,
                                })
                    seen_panel_members.add(panel_member['contact_email'].lower())

                for panel_member in panel_members:
                    if panel_member['contact_email'].lower() in seen_panel_members:
                        continue
                    seen_panel_members.add(panel_member['contact_email'].lower())

                    csvout.writerow({'panel_manager_name': panel_manager['panel_manager_name'],
                                'panel_manager_email': panel_manager['panel_manager_email'],
                                'client_name': confirm_panel['client_name'],
                                'account_name': confirm_panel['account_name'],
                                'team_name': confirm_panel['team_name'],
                                'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                                'panel_member_name': panel_member['contact_name'],
                                'panel_member_email': panel_member['contact_email'],
                                'confirmed_date': confirmed_date,
                                'was_in_last_round': panel_members_by_email.get(panel_member['contact_email'], {}).get('in_round', False),
                                'auto_confirmed': False,
                                'in_round': False,
                                })

            for panel_manager_id, panel_manager in panel_managers_by_id.items():
                if panel_manager_id in confirmed_panel:
                    continue

                for panel_member in panel_members:
                    csvout.writerow({'panel_manager_name': panel_manager['panel_manager_name'],
                                'panel_manager_email': panel_manager['panel_manager_email'],
                                'client_name': confirm_panel['client_name'],
                                'account_name': confirm_panel['account_name'],
                                'team_name': confirm_panel['team_name'],
                                'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                                'panel_member_name': panel_member['contact_name'],
                                'panel_member_email': panel_member['contact_email'],
                                'confirmed_date': auto_confirm_date.isoformat(),
                                'was_in_last_round': panel_member['in_round'],
                                'auto_confirmed': True,
                                'in_round': panel_member['in_round'],
                                })


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
