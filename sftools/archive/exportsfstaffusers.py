from lib import sfapi
import argparse
from lib.settings import settings
import csv


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # get key accounts/markets
    key_accounts_by_contact_id = {}
    for row in sfapi.bulk_query(sf, "SELECT Contact__c, Account__r.Name FROM Contact_Key_Account__c"):
        key_accounts_by_contact_id.setdefault(row['Contact__c'], []).append(row['Account__r.Name'])
    key_markets_by_contact_id = {}
    for row in sfapi.bulk_query(sf, "SELECT Contact__c, Market__r.Name FROM Contact_Key_Market__c"):
        key_markets_by_contact_id.setdefault(row['Contact__c'], []).append(row['Market__r.Name'])

    with open('staffcontacts.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=['contact_id', 'first_name', 'last_name', 'email',
                                               'is_signatory', 'is_accounts_manager', 'is_panel_manager',
                                               'agency_name', 'agency_id', 'key_accounts', 'key_markets'])
        csvout.writeheader()

        # get the list of contacts
        soql = """
            SELECT Id,
                FirstName,
                LastName,
                Email,
                Signatory__c,
                Accounts_Manager__c,
                Panel_Manager__c,
                Account.Id,
                Account.Name
            FROM Contact
            WHERE Account.RecordType.Name = 'CRC\\'s Customer - Advertising'
"""
        for row in sfapi.bulk_query(sf, soql):
            csvout.writerow({
                'contact_id': row['Id'],
                'first_name': row['FirstName'],
                'last_name': row['LastName'],
                'email': row['Email'].lower(),
                'is_signatory': row['Signatory__c'],
                'is_accounts_manager': row['Accounts_Manager__c'],
                'is_panel_manager': row['Panel_Manager__c'],
                'agency_name': row['Account.Name'],
                'agency_id': row['Account.Id'],
                'key_accounts': ', '.join(key_accounts_by_contact_id.get(row['Id'], [])),
                'key_markets': ', '.join(key_markets_by_contact_id.get(row['Id'], [])),
            })


def lambda_handler(event, _):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
