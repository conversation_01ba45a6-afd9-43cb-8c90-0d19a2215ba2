import argparse
from datetime import date, datetime, timezone
from bson import ObjectId
from pymongo import UpdateOne
from lib import portaldbapi


SURVEY_PANEL_MEMBER_IDS = []

'''
SURVEY_PANEL_MEMBER_IDS = [
'a07WT00000Ae1RUYAZ',
'a07WT00000Ae1RVYAZ',
'a07WT00000Ae1RWYAZ',
'a07WT00000Ae1RXYAZ',
'a07WT00000Ae1RYYAZ',
'a07WT00000Ae1RZYAZ',
'a07WT00000Ae1RaYAJ',
'a07WT00000Ae1RbYAJ',
'a07WT00000Ae1RhYAJ',
'a07WT00000Ae1RrYAJ',
'a07WT00000Ae1RuYAJ',
'a07WT00000Ae1RvYAJ',
'a07WT00000Ae1RxYAJ',
'a07WT00000Ae1S0YAJ',
'a07WT00000Ae1S3YAJ'
]
'''


def main():
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    new_survey_end_date = datetime(2024, 11, 10, tzinfo=timezone.utc)
    total_count = 0
    updated_count = 0
    batch = []

    for survey in survey_collection.find({'deleted': {'$ne': True}, 'response': None, 'sfsync_date': None}):
    #for survey in survey_collection.find({'_id': ObjectId('6712782fdbdc85e3062ea973')}):
        total_count += 1
        valid_survey = False
        panel_member_ids = [
            member["survey_panel_member_id"]
            for question in survey["questions"]
            for member in question["panel_members"]
        ]

        if any(item in SURVEY_PANEL_MEMBER_IDS for item in panel_member_ids) and survey['end_date'].date() == date(2024, 11, 13):
            valid_survey = True

        if valid_survey:
            updated_count += 1
            batch.append(UpdateOne({'_id': survey['_id']}, {'$set': {'end_date': new_survey_end_date}})) 
        
        print(f'{survey['_id']}: {valid_survey}')

    if batch:
        survey_collection.bulk_write(batch)

    print(f'Total surveys: {total_count}')
    print(f'Updated: {updated_count}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
