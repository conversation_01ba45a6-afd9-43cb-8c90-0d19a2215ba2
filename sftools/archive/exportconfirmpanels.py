import bson.json_util
from lib import portaldbapi
import argparse
import bson


def main(dirname):
    portaldb = portaldbapi.DocumentDB()
    confirm_panels_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    for confirm_panel in confirm_panels_collection.find():
        # confirm_panel = portaldbapi.ConfirmPanel(**confirm_panel)

        # confirm_panel.account_managers = filter(lambda x: x, confirm_panel.account_managers)
        # confirm_panel.account_managers = sorted(confirm_panel.account_managers)
        # confirm_panel.panel = sorted(confirm_panel.panel, key=lambda x: x.contact_id)
        # confirm_panel.sfsync_date = None
        # confirm_panel.confirm_status = False
        # confirm_panel.confirmed_panel = {}

        with open(f'{dirname}/{confirm_panel["Id"]}.json', 'w') as f:
            f.write(bson.json_util.dumps(confirm_panel, indent=2, sort_keys=True))
            # f.write(confirm_panel.model_dump_json(by_alias=True, indent=2))


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('dirname')
    args = ap.parse_args()

    main(args.dirname)
