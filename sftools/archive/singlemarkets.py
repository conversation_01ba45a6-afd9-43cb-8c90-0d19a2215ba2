import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings
from lib import sfimport


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    f = open("singlemarkets.csv", "w")
    c = csv.writer(f)

    accounts = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id:
            continue
        accounts[account.Id] = account

    for account in accounts.values():
        if account.OrganisationalLevel != 'With Market' and account.MarketId:
            account_tree = []
            cur_account = account
            while cur_account:
                account_tree.append(cur_account.Name)
                cur_account = accounts.get(cur_account.ParentId)
            account_tree.reverse()

            c.writerow([account.Id, "/".join(account_tree), account.OrganisationalLevel])

    # print('contacts')
    # for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
    #     account = accounts.get(contact.AccountId)
    #     if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
    #         continue
    #     if not contact.TeamId:
    #         continue

    #     row = [contact.Id, contact.FirstName, contact.LastName, contact.Email, contact.AccountId, account.Name]
    #     c.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
