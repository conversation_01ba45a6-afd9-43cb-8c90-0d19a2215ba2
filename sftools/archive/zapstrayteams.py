import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings
from lib import sfimport


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    print('accounts')
    accounts = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        accounts[account.Id] = account

    print('contacts')
    updates = []
    for contact in sfapi.get_all(sf, sfapi.Contact):
        account = accounts.get(contact.AccountId)
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        if not contact.TeamId:
            continue

        updates.append({'Id': contact.Id, 'Team__c': '#N/A'})

    for record in sfapi.bulk_update(sf, 'Contact', updates):
        sfapi.detect_bulk2_errors(record)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
