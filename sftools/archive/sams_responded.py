from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
from lib.settings import settings


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # load everything outta salesforce into local ram cache
    sams_by_customer_survey_id: dict[str, sfapi.SurveyAccountManager] = {}
    for sam in sfapi.get_all(sf, sfapi.SurveyAccountManager, query_suffix="WHERE Customer_Survey__r.Current_Round__c = true"):
        sams_by_customer_survey_id.setdefault(sam.CustomerSurveyId, []).append(sam)

    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    patch_sams = []
    for in_confirm_account in confirm_accounts_collection.find({}):
        customer_survey_id = in_confirm_account['Id']
        all_responded_sam_contact_ids = set(in_confirm_account['confirmed_accounts'].keys())
        all_sams = sams_by_customer_survey_id.get(customer_survey_id)
        if not all_sams:
            continue

        for sam in all_sams:
            if sam.ContactId in all_responded_sam_contact_ids:
                patch_sams.append({'Id': sam.Id, 'Has_Responded__c': True})


    if writeit:
        sfapi.bulk_update(sf, 'Survey_Account_Manager__c', patch_sams)
    else:
        print(len(patch_sams))


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true')
    args = ap.parse_args()

    main(args.writeit)
