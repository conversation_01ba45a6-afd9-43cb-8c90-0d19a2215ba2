import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings
from lib import sfimport


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    customer_survey_updates = []
    for cs in sfapi.get_all(sf, sfapi.CustomerSurvey):
        if not cs.CurrentRound:
            continue
        customer_survey_updates.append(
            {'Id': cs.Id,
             'Last_Sync_Date__c': '#N/A',
             'Requires_Portaldb_Sync__c': True})

    for record in sfapi.bulk_update(sf, 'Customer_Survey__c', customer_survey_updates):
        sfapi.detect_bulk2_errors(record)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
