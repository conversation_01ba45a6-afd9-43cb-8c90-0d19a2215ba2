import lib.sfapi as sfapi
import argparse
import csv
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    tozap = set()
    with open('zappyzap.csv', 'w') as f:
        csvout = csv.writer(f)
        csvout.writerow(['Id', 'Name', 'CustomerAccountId', 'CustomersClientAccountId'])

        for ccr in sfapi.get_all(sf, sfapi.CustomerClientRelationship, query_suffix=" WHERE Customer_Account__c ='' or Customers_Client__c = ''"):
            if ccr.CustomerAccountId and ccr.CustomersClientAccountId:
                continue
            tozap.add(ccr.Id)
            csvout.writerow([ccr.Id, ccr.Name, ccr.CustomerAccountId, ccr.CustomersClientAccountId])
            # print(ccr.Id, ccr.Name, ccr.CustomerAccountId, ccr.CustomersClientAccountId)


    for x in tozap:
        print(x)
        sf.Customer_Client_Relationship__c.delete(x)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()

