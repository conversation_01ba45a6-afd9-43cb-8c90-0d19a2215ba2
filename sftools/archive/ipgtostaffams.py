import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import copy


def load_account_managers(filename, sf, accountRecordTypes, all_markets_by_name, customers_client_accounts_by_name):
    # find child customer client accounts
    child_cc_cc_accounts = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        child_cc_cc_accounts.setdefault(account.ParentId, []).append(account)

    account_managers = []
    with open(filename) as file:
        reader = csv.DictReader(file)

        for row in reader:
            accountmanager = {}
            accountmanager['Email Address'] = row['Email Address'].strip().lower()

            # compute the account hierarchy with the correct names
            for colname in ['Holding Group', 'Network', 'Agency Brand', 'Agency Brand Subsidiary']:
                if colname == 'Holding Group':
                    organisational_level = 'Holding Group'
                elif colname == 'Network':
                    organisational_level = 'Network'
                elif colname == 'Agency Brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'Agency Brand Subsidiary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level")

                v = row[colname].strip()
                if v.upper() == 'ALL':
                    v = 'ALL'
                elif not v:
                    v = 'ALL'
                if v:
                    accountmanager[organisational_level] = v

            if row['Market'].strip().upper() != 'ALL':
                raise Exception("Only 'ALL' is supported for market")
            if row['Region'].strip().upper() != '':
                raise Exception("Region is not supported")
            if row['Team Name'].strip().upper() != '':
                raise Exception("Team Name is not supported")
            if row['Team Market'].strip().upper() != '':
                raise Exception("Team Market is not supported")

            if row['Account'].strip().upper() == 'ALL':
                parent_account_name = row['Parent Account'].strip()
                if parent_account_name:
                    if parent_account_name == 'Nestle':
                        parent_account_name = 'Nestlé'
                    parent_account = customers_client_accounts_by_name.get(parent_account_name)
                    if not parent_account:
                        raise Exception(f"Unknown parent account {parent_account_name}")
                    if len(parent_account) > 1:
                        raise Exception(f"Multiple parent accounts {parent_account_name}")
                    parent_account = parent_account[0]
                    child_accounts = child_cc_cc_accounts.get(parent_account.Id)
                    if not child_accounts:
                        raise Exception(f"Parent account {parent_account_name} has no children")

                    # append parent account
                    accountmanager['Account'] = parent_account_name
                    account_managers.append(accountmanager)

                    # append child accounts
                    for account in child_accounts:
                        cur = copy.copy(accountmanager)
                        cur['Account'] = account.Name
                        account_managers.append(cur)

            else:
                accountmanager['Account'] = row['Account'].strip()
                account_managers.append(accountmanager)

    return account_managers


def import_csv(accounts_filename, accountmanagers_filename):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Contact', 'CustomerClientRelationship', 'AccountGroup', 'Market', 'Team', 'ContactKeyAccount', 'CustomerSurvey', 'SurveyAccountManager', 'CustomerSurveyRound']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")

    all_markets_by_name = {}
    for market in sfapi.get_all(sf, sfapi.Market):
        if market.Name in all_markets_by_name:
            raise Exception(f"Duplicate market name {market.Name} found.")
        all_markets_by_name[market.Name] = market
    SF_CLIENT_ORG_LEVELS = sfapi.get_picklist_values(sf, 'Account', 'Organisation_Level__c', accountRecordTypes[crc_customer_record_type].Id)

    # find all the contacts in salesforce
    all_contacts_by_email = {}
    for contact in sfimport.sfcache.values():
        if not isinstance(contact, sfapi.Contact):
            continue
        all_contacts_by_email[contact.Email.lower()] = contact

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    all_account_managers = load_account_managers(accountmanagers_filename, sf, accountRecordTypes, all_markets_by_name, customers_client_accounts_by_name)

    # load data from the CSV file
    with open(accounts_filename) as file:
        reader = csv.DictReader(file)

        csv_fields = ['Holding Group',
                      'Network',
                      'Sub-Network',
                      'Agency Brand',
                      'Agency Brand Subsidiary',
                      'Market',
                      'Region',
                      'Account',
                      'Team Name',
                      'Team Market',
                      'Account Manager (yes/no)',
                      'Panel Manager (yes/no)',
                      'Email Sender (yes/no)',
                      'Reporting Role',
                      'Email Address',
                      'First Name',
                      'Last Name']

        f = open('ipg-tt-am.csv', 'w')
        csv_writer = csv.DictWriter(f, fieldnames=csv_fields)
        csv_writer.writeheader()

        # load data from the CSV file
        with open(accounts_filename) as file:
            reader = csv.DictReader(file)

            all_rows = []
            for i, row in enumerate(reader):
                row['row_number'] = i + 1
                all_rows.append(row)

        # preprocess each row and compute various things
        for row in all_rows:
            # compute the account hierarchy with the correct names
            customer_hierarchy = []
            for colname in ['Holding Company', 'Network', 'Agency Brand', 'Agency Brand Subsidary']:
                if colname == 'Holding Company':
                    organisational_level = 'Holding Group'
                elif colname == 'Network':
                    organisational_level = 'Network'
                elif colname == 'Agency Brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'Agency Brand Subsidary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level")

                v = row[colname].strip()
                if v:
                    customer_hierarchy.append((v, organisational_level))
            row['customer_hierarchy'] = customer_hierarchy

            # get market for this row
            market_name = row['Market'].strip()
            row_market = all_markets_by_name.get(market_name)
            if row_market:
                # add on "With market" level
                with_market_account_name = f"{customer_hierarchy[-1][0]} {row_market.Name}"
                customer_hierarchy.append((with_market_account_name, 'With Market'))

        for row in all_rows:
            outrow = {}

            outrow['Holding Group'] = row['Holding Company']
            outrow['Network'] = row['Network']
            outrow['Sub-Network'] = ''
            outrow['Agency Brand'] = row['Agency Brand']
            outrow['Agency Brand Subsidiary'] = row['Agency Brand Subsidary']
            outrow['Region'] = ''
            outrow['Market'] = row['Market']
            outrow['Account'] = row['IPG Top Tier Account']
            outrow['Team Name'] = row['Team']
            outrow['Team Market'] = row['Team Name']
            outrow['Account Manager (yes/no)'] = 'yes'
            outrow['Panel Manager (yes/no)'] = 'no'
            outrow['Email Sender (yes/no)'] = 'no'
            outrow['Reporting Role'] = ''

            # find the account managers for this row
            row_account_managers = []
            row_customer_hierarchy = row['customer_hierarchy']
            row_customer_hierarchy_dict = {x[1]: x[0] for x in row_customer_hierarchy}
            for account_manager in all_account_managers:
                match = True
                for colname in ['Holding Group', 'Network', 'Agency Brand', 'Agency Brand (Sub 1)']:
                    row_v = row_customer_hierarchy_dict.get(colname)
                    am_v = account_manager[colname].strip()
                    if am_v != 'ALL' and am_v != row_v:
                        match = False
                        break
                if not match:
                    continue

                row_account = row['IPG Top Tier Account'].strip()
                am_account = account_manager['Account'].strip()
                if am_account != 'ALL' and am_account != row_account:
                    continue

                am_outrow = copy.copy(outrow)
                am_outrow['Email Address'] = account_manager['Email Address']
                am_outrow['First Name'] = 'first'
                am_outrow['Last Name'] = 'last'
                csv_writer.writerow(am_outrow)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('accounts_filename', help='CSV file to import')
    ap.add_argument('accountmanagers_filename', help='CSV file to import')
    args = ap.parse_args()

    import_csv(args.accounts_filename, args.accountmanagers_filename)
