import lib.sfapi as sfapi
import argparse
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    tofix = set()
    for cs in sfapi.get_all(sf, sfapi.CustomerSurvey):
        if not cs.CurrentRound:
            continue
        if cs.Stage == 'Survey Setup':
            continue

        tofix.add(cs.Id)

    for id in tofix:
        print(id)
        sf.Customer_Survey__c.update(id, {'Stage__c': 'Survey Setup'})


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
