import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings

import string


def get_bit(char) -> str:
    return '1' if str(char).isupper() else '0'


class Converter:
    """
    Convert 15 character Salesforce id into a 18 character id.

    Algorithm is explained here:
    http://salesforce.stackexchange.com/questions/1653/what-are-salesforce-ids-composed-of
    """

    def __init__(self, short):
        self.short = short

    def _convert_chunk_to_bit(self, chunk):
        binary_repr = ''.join(reversed([get_bit(char) for char in chunk]))
        return str(int(binary_repr, 2))

    def convert(self):
        suffix = ''
        # split id into three chunks
        chunks = [self.short[i:i+5] for i in range(0, len(self.short), 5)]
        # construct an array of A-Z and 0-5
        array = string.ascii_uppercase + string.digits[:6]

        for chunk in chunks:
            bit_char = self._convert_chunk_to_bit(chunk)
            suffix += array[int(bit_char)]

        return self.short + suffix


def import_csv(filename):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # populate cache
    accounts_by_id = {}
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True):
        accounts_by_id[account.Id] = account

    # load data from the CSV file
    with open(filename) as file:
        reader = csv.DictReader(file)

        patches = []
        for i, row in enumerate(reader):
            row['row_number'] = i + 1

            account_id = Converter(row['Account ID']).convert()
            account_name = row['Account Name']
            legacy_sector = row['Legacy Sector']

            account = accounts_by_id.get(account_id)
            if not account:
                print(f"Could not find account with ID {account_id} {account_name}")
                continue
            if account.Name != account_name:
                print("Account name mismatch", account.Name, account_name)
                continue

            patches.append({'Id': account.Id, 'Legacy_Sector__c': legacy_sector})

    for status in sfapi.bulk_update(sf, "Account", patches):
        sfapi.detect_bulk2_errors(status)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    args = ap.parse_args()

    import_csv(args.filename)
