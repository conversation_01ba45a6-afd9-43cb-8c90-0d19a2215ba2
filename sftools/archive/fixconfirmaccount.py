from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
import copy
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts
import datetime


ACCOUNT_NAME_MAPPING = {
    'sainsbury bank': 'Sainsbury\'s Bank',
    'viq group co., ltd. (vivo)': 'VIVO',
}

def add_ccrs():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    for objname in ['CustomerClientRelationship', 'Account']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("DONE POPULATING CACHE")

    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        contact_email = contact.Email.lower()
        all_contacts_by_email[contact_email] = contact

    all_accounts_by_id: dict[str, sfapi.Account] = {}
    all_customer_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        account_name = account.Name.lower()

        all_accounts_by_id[account.Id] = account
        if account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            all_customer_client_accounts_by_name[account_name] = account

    # now sort out the CCRs
    for in_confirm_account in confirm_accounts_collection.find({}):
        # find the agency account id
        agency_account = all_accounts_by_id[in_confirm_account['client_id']]

        for contact_id, contact_confirmed_accounts in in_confirm_account['confirmed_accounts'].items():
            for confirmed_account_idx, confirmed_account in enumerate(contact_confirmed_accounts['accounts']):
                if confirmed_account['account_id'].startswith('NEW'):
                    print("NEW ACCOUNT: ", confirmed_account['account_name'])
                    continue

                customers_client_account = all_accounts_by_id[confirmed_account['account_id']]

                # print(agency_account)
                # print(customers_client_account)

                ccr = sfimport.get_or_create_cached_customer_client_relationship(agency_account, customers_client_account, 'Client')
                print(ccr.Id)

    sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)


def main(quick, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    all_contacts_by_email: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        contact_email = contact.Email.lower()
        all_contacts_by_email[contact_email] = contact

    all_customer_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfapi.get_all(sf, sfapi.Account):
        account_name = account.Name.lower()

        if account.RecordTypeId == accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            all_customer_client_accounts_by_name[account_name] = account

    for in_confirm_account in confirm_accounts_collection.find({}):
        if not quick:
            try:
                out_confirm_account = get_confirm_accounts(sf, in_confirm_account['Id'])
                if not out_confirm_account:
                    continue
            except Exception as e:
                print("Error getting confirm account: ", in_confirm_account['Id'], e)
                continue
        else:
            out_confirm_account = None

        set_updates = {}
        old_confirmed_accounts = in_confirm_account['confirmed_accounts']

        if not quick:
            out_confirm_account_dict = out_confirm_account.model_dump(by_alias=True)
            set_updates['accounts'] = new_accounts = out_confirm_account_dict['accounts']
            set_updates['account_managers'] = out_confirm_account_dict['account_managers']
            set_updates['old_accounts'] = in_confirm_account['accounts']
            set_updates['old_account_managers'] = in_confirm_account['account_managers']
            new_accounts_by_id = {account['account_id']: account for account in new_accounts}
        else:
            new_accounts = None
            new_accounts_by_id = {}

        new_confirmed_accounts = copy.deepcopy(old_confirmed_accounts)
        for contact_id, contact_confirmed_accounts in new_confirmed_accounts.items():
            for confirmed_account_idx, confirmed_account in enumerate(contact_confirmed_accounts['accounts']):
                if confirmed_account['account_id'].startswith('NEW'):
                    account_name = confirmed_account['account_name'].strip().lower()
                    account_name = ACCOUNT_NAME_MAPPING.get(account_name, account_name).lower()
                    sf_account = all_customer_client_accounts_by_name.get(account_name)
                    if sf_account:
                        confirmed_account['account_id'] = account.Id
                        confirmed_account['account_name'] = account.Name

                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.account_id"] = sf_account.Id
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.account_name"] = sf_account.Name

                    else:
                        print("New customer client account: ", confirmed_account['account_name'], '--', in_confirm_account['customer_survey_name'])

                # try and fix stuff from an actual account from sf if we have it
                pdb_account = new_accounts_by_id.get(confirmed_account['account_id'])
                if pdb_account:
                    # use any signatories from the account if not set on the confirmed account
                    if not confirmed_account['signatory_id']:
                        confirmed_account['signatory_id'] = pdb_account['signatory_id']
                        confirmed_account['signatory_email'] = pdb_account['signatory_email']
                        confirmed_account['signatory_name'] = pdb_account['signatory_name']

                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.signatory_id"] = pdb_account['signatory_id']
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.signatory_email"] = pdb_account['signatory_email']
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.signatory_name"] = pdb_account['signatory_name']

                    # if the survey name is not set on the confirmed account, use the account's survey name
                    if not confirmed_account['survey_name']:
                        confirmed_account['survey_name'] = account['survey_name']

                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_name"] = pdb_account['survey_name']

                # if the signatory is a new signatory, fix it up to match salesforce
                # if not confirmed_account['signatory_id']:
                #     print("NOSIGNATORY")
                # if confirmed_account['signatory_id'] and confirmed_account['signatory_id'].startswith('NEW'):
                #     print("NEWSIGNATORY")

                for panel_manager_idx, panel_manager in enumerate(confirmed_account['survey_panel_managers']):
                    # if its a salesforce contact -> fine
                    if not panel_manager['panel_manager_id'].startswith('NEW'):
                        continue

                    # try and find it in salesforce
                    email = panel_manager['panel_manager_email'].lower()
                    sf_panel_manager = all_contacts_by_email.get(email)
                    if sf_panel_manager:
                        panel_manager['panel_manager_id'] = sf_panel_manager.Id
                        panel_manager['panel_manager_name'] = f"{sf_panel_manager.FirstName} {sf_panel_manager.LastName}"
                        panel_manager['panel_manager_email'] = sf_panel_manager.Email
                        panel_manager['panel_manager_first_name'] = sf_panel_manager.FirstName
                        panel_manager['panel_manager_last_name'] = sf_panel_manager.LastName

                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_panel_managers.{panel_manager_idx}.panel_manager_id"] = sf_panel_manager.Id
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_panel_managers.{panel_manager_idx}.panel_manager_name"] = f"{sf_panel_manager.FirstName} {sf_panel_manager.LastName}"
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_panel_managers.{panel_manager_idx}.panel_manager_email"] = sf_panel_manager.Email
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_panel_managers.{panel_manager_idx}.panel_manager_first_name"] = sf_panel_manager.FirstName
                        set_updates[f"confirmed_accounts.{contact_id}.accounts.{confirmed_account_idx}.survey_panel_managers.{panel_manager_idx}.panel_manager_last_name"] = sf_panel_manager.LastName

                    else:
                        print("New panel manager: ", email, panel_manager['panel_manager_name'], in_confirm_account['customer_survey_name'])

        if not set_updates:
            continue
        if quick:
            continue

        print("Updating Confirm Account: ", in_confirm_account['Id'], in_confirm_account['customer_survey_name'])

        set_updates['fixconfirmaccount_stamp'] = datetime.datetime.now()
        update = {
            '$set': set_updates
        }

        if writeit:
            confirm_accounts_collection.update_one({'Id': in_confirm_account['Id']}, update, upsert=False)


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--quick', action='store_true')
    ap.add_argument('--writeit', action='store_true')
    args = ap.parse_args()

    # add_ccrs()
    main(args.quick, args.writeit)
