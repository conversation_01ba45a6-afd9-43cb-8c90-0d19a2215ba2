import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv
from lib import sfimport
import json


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # all_srs = {}
    # for sr in sfapi.get_all(sf, sfapi.SurveyResponse, bulk=True):
    #     all_srs[sr.Id] = spm

    all_spms = {}
    for spm in sfapi.get_all(sf, sfapi.SurveyPanelMember, bulk=True):
        all_spms[spm.Id] = spm

    all_scs = {}
    for sc in sfapi.get_all(sf, sfapi.SurveyClient, bulk=True):
        all_scs[sc.Id] = sc

    all_contacts = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        all_contacts[contact.Id] = contact

    all_csv_scs = {}
    with open('csv/survey_client.csv') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            all_csv_scs[row['Id']] = row

    restored_spms = {}
    with open('csv/survey_panel_member.csv') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            if row['Id'] in all_spms:
                continue

            contact = all_contacts.get(row['Contact__c'])
            if not contact:
                print(f'Contact {row["Contact__c"]} not found')
                continue

            survey_client = all_scs.get(row['Survey_Client__c'])
            if not survey_client:
                # ignore missing survey clients
                continue

            spm = sfimport.get_or_create_cached_survey_panel_member(survey_client, contact, row['Hub_Row_Key__c'])
            spm.ContactEmail = row['Contact_Email__c']
            spm.ContactTitle = row['Contact_Title__c']
            spm.ContactDivision = row['Contact_Division__c']
            spm.ContactJobFunction = row['Contact_Job_Function__c']
            spm.ContactJobFunction2 = row['Contact_Job_Function_2__c']
            spm.ContactType = row['Contact_Type__c']

            sfimport.sfid_mapping[survey_client.Id] = survey_client.Id
            sfimport.sfid_mapping[contact.Id] = contact.Id

            restored_spms[row['Id']] = spm

    with open('csv/survey_response.csv') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            if row['Survey_Panel_Member__c'] not in restored_spms:
                continue

            spm = restored_spms[row['Survey_Panel_Member__c']]

            sr = sfimport.get_or_create_cached_survey_response(spm,
                                                               score_question=row['Score_Question__c'],
                                                               score=row['Score__c'],
                                                               feedback_question=row['Feedback_Question__c'],
                                                               feedback=row['Feedback__c'],
                                                               feedback_translated=row['Feedback_Translated__c'],
                                                               themes=row['Themes__c'],
                                                               is_extra_question=row['Is_Extra_Question__c'],
                                                               response_date_time=row['Response_Date_Time__c'])

    print("WRITING")
    sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_members, sfapi.SurveyPanelMember)
    sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_responses, sfapi.SurveyResponse)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
