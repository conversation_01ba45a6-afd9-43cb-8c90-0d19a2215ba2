import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
from lib import sfimport


def main(filename):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    for objname in ['Market']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("DONE POPULATING CACHE")

    markets_by_name = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        markets_by_name[market.Name] = market

    # load data from the CSV file
    patches = []
    with open(filename, 'r', encoding='ISO-8859-1') as file:
        # we read in all rows, then sort by date descending so the most recent values are the ones take priority
        reader = csv.DictReader(file)
        for row in reader:
            market_name = row['Name']
            language = row['DefaultLanguage']

            market = markets_by_name[market_name]
            if market.Language:
                continue

            patches.append({'Id': market.Id, 'Language__c': language})

    statuses = sfapi.bulk_update(sf, "Market__c", patches)
    for status in statuses:
        sfapi.detect_bulk2_errors(status)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    args = ap.parse_args()

    main(args.filename)
