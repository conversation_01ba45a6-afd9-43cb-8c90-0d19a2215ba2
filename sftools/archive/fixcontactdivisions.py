from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import csv


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    division_by_contact_id = {}
    all_contacts_by_email = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        division_by_contact_id[contact.Id] = contact.LegacyDivision
        all_contacts_by_email[contact.Email.lower()] = contact.Id

    for confirmpanel in confirm_panel_collection.find({'deleted': {'$ne': True}}):
        confirmpanel = portaldbapi.ConfirmPanel(**confirmpanel)
        for account_manager_id, panel in confirmpanel.confirmed_panel.items():
            for panel_member in panel.panel:
                if panel_member.contact_division:
                    contact_id = panel_member.contact_id
                    if contact_id.startswith('NEW'):
                        contact_id = all_contacts_by_email.get(panel_member.contact_email.lower())

                    division_by_contact_id[contact_id] = panel_member.contact_division

    patch_spms = []
    soql = """
SELECT  Id,
        Contact_Division__c,
        Contact__c,
        Contact__r.Legacy_Division__c,
        Contact__r.Email
FROM Survey_Panel_Member__c
WHERE Survey_Client__r.Current_Round__c = True
"""
    for row in sfapi.bulk_query(sf, soql):
        division = division_by_contact_id.get(row['Contact__c'])
        if row['Contact_Division__c'] != division and division:
            patch_spms.append({'Id': row['Id'], 'Contact_Division__c': division})

    for status in sfapi.bulk_update(sf, "Survey_Panel_Member__c", patch_spms):
        sfapi.detect_bulk2_errors(status)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
