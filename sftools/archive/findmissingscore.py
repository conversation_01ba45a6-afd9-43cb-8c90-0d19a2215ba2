from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import csv
from bson import ObjectId
from pymongo import UpdateOne


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    for survey in survey_collection.find({'deleted': {'$ne': True}}):
        response = survey['response']
        if response is None:
            continue

        for q in survey['questions']:
            if q['score_question_id'] not in response:
                print("AWOOGA", survey['_id'])


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
