import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import copy


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    soql = """
SELECT  Id,
        Contact_Title__c,
        Contact_Type__c,
        Contact_Seniority__c,
        Contact_Division__c,
        Contact__c,
        Contact__r.Title,
        Contact__r.Contact_Type__c,
        Contact__r.Seniority__c,
        Contact__r.Legacy_Division__c,
        Contact__r.Email
FROM Survey_Panel_Member__c
WHERE Survey_Client__r.Current_Round__c = True
"""
    patches = []
    seen_contacts = {}
    problem_contacts = set()
    for row in sfapi.bulk_query(sf, soql):
        topatch = {}
        if row['Contact__r.Title'] != row['Contact_Title__c'] and row['Contact_Title__c']:
            topatch['Title'] = row['Contact_Title__c']

        if row['Contact_Type__c'] == 'Primary':
            row['Contact_Type__c'] = 'Primary Contact'
        elif row['Contact_Type__c'] == 'Influencer':
            row['Contact_Type__c'] = ''

        if row['Contact__r.Contact_Type__c'] != row['Contact_Type__c'] and row['Contact_Type__c']:
            topatch['Contact_Type__c'] = row['Contact_Type__c']

        if row['Contact__r.Seniority__c'] != row['Contact_Seniority__c'] and row['Contact_Seniority__c']:
            topatch['Seniority__c'] = row['Contact_Seniority__c']

        if row['Contact__r.Legacy_Division__c'] != row['Contact_Division__c'] and row['Contact_Division__c']:
            topatch['Legacy_Division__c'] = row['Contact_Division__c']

        if topatch:
            old = seen_contacts.get(row['Contact__c'], {})
            if not old:
                seen_contacts[row['Contact__c']] = copy.copy(topatch)

                topatch['Id'] = row['Contact__c']
                patches.append(topatch)

            else:
                for k, v in topatch.items():
                    if k not in old:
                        old[k] = v

                    else:
                        if old[k] != v:
                            problem_contacts.add(row['Contact__r.Email'])

    for status in sfapi.bulk_update(sf, "Contact", patches):
        sfapi.detect_bulk2_errors(status)

    print(problem_contacts)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
