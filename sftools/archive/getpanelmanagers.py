from typing import Any
from lib import sfapi, portaldbapi, sfimport
import argparse
import csv
from lib.settings import settings
from agents.sync_confirmaccounts_to_portaldb import get_confirm_accounts
import datetime


def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    panel_managers = {}
    for in_confirm_account in confirm_accounts_collection.find({}):
        for amid, confirmed_account in in_confirm_account['confirmed_accounts'].items():
            for account in confirmed_account['accounts']:
                for panel_manager in account['survey_panel_managers']:
                    panel_managers[panel_manager['panel_manager_email'].lower()] = panel_manager['panel_manager_name']

    f = open('panel_managers.txt', 'w')
    csvout = csv.writer(f)
    for email, name in panel_managers.items():
        csvout.writerow([email, name])


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
