import lib.sfapi as sfapi
import argparse
import csv
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    tozap = set()
    with open('zappyzap.csv', 'w') as f:
        csvout = csv.writer(f)
        csvout.writerow(['Id', 'Name', 'SurveyClientId'])

        for spm in sfapi.get_all(sf, sfapi.SurveyPanelMember, query_suffix=" WHERE Survey_Client__c =''"):
            if spm.SurveyClientId:
                continue
            tozap.add(spm.Id)
            csvout.writerow([spm.Id, spm.Name, spm.SurveyClientId])

    for status in sfapi.bulk_delete(sf, "Survey_Panel_Member__c", list(tozap)):
        sfapi.detect_bulk2_errors(status)


    # for x in tozap:
    #     print(x)
    #     sf.Survey_Panel_Member__c.delete(x)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
