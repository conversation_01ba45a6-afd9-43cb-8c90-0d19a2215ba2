import csv
import os
import lib.sfapi as sfapi
import datetime
import argparse
from lib import sfimport
from lib.settings import settings
import openpyxl


def load_customer_client_name_mapping():
    wb = openpyxl.open('data/account_mappings_master.xlsx', read_only=True)
    mapping_sheet = None
    for ws in wb.worksheets:
        if ws.title.lower() == 'account_mappings_master':
            mapping_sheet = ws
            break
    if not mapping_sheet:
        raise ValueError('Could not find the mapping sheet')

    all_rows = list(ws.iter_rows(values_only=True))
    header_row = {}
    for col_idx, v in enumerate(all_rows[0]):
        if not v:
            continue
        v = v.lower().strip()
        v = v.split('\n')[0]
        header_row[v] = col_idx

    account_mapping = {}
    for raw_row in all_rows[1:]:
        row = {k: raw_row[v] if v < len(raw_row) else None for k, v in header_row.items()}
        legacy_account_name = row['legacy_account_name'] or ''
        legacy_account_name = str(legacy_account_name).strip()
        account_name = row['account_name'] or ''
        account_name = str(account_name).strip()

        account_mapping.setdefault(legacy_account_name, set()).add(account_name)

    for k, v in account_mapping.items():
        if len(v) > 1:
            print(f'WARNING: Multiple mappings for {k}: {v}')

    return account_mapping

def parse_date(d):
    try:
        return datetime.date.fromisoformat(d.split(' ')[0])
    except ValueError:
        return datetime.datetime.strptime(d.split(' ')[0], '%d/%m/%Y').date()

def parse_datetime(d):
    try:
        return datetime.datetime.fromisoformat(d)
    except ValueError:
        return datetime.datetime.strptime(d, '%d/%m/%Y %H:%M')


def import_csv(filename, importtype, targetsurveytype, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    customer_client_name_mapping = load_customer_client_name_mapping()
    agency_dev_prefix = os.environ.get('AGENCY_DEV_PREFIX')
    if agency_dev_prefix:
        agency_dev_prefix = agency_dev_prefix.strip()

    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    if importtype == 'advertising':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_ADVERTISING_SURVEYCSV
    elif importtype == 'manufacturing':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_MANUFACTURING_SURVEYCSV
    else:
        raise Exception(f'Invalid import type {importtype}')

    market_by_name_and_type = {}
    for market in sfapi.get_all(sf, sfapi.Market):
        key = f'{market.Name}-{market.MarketType}'
        if key in market_by_name_and_type:
            raise Exception(f"Duplicate market name/type {market.Name}/{market.MarketType} found.")
        market_by_name_and_type[key] = market

    SF_TEAM_TYPES = sfapi.get_picklist_values(sf, 'Team__c', 'Type__c')

    SF_CLIENT_ORG_LEVELS = sfapi.get_picklist_values(sf, 'Account', 'Organisation_Level__c', accountRecordTypes[crc_customer_record_type].Id)

    SF_CONTACT_TYPE = sfapi.get_picklist_values(sf, 'Contact', 'Contact_Type__c')
    SF_CONTACT_SENIORITY = sfapi.get_picklist_values(sf, 'Contact', 'Seniority__c')
    SF_CONTACT_LANGUAGE = sfapi.get_picklist_values(sf, 'Contact', 'Language__c')
    SF_CONTACT_JOB_FUNCTION = sfapi.get_picklist_values(sf, 'Contact', 'Job_Function__c')
    SF_CONTACT_JOB_FUNCTION_LEVEL_2 = sfapi.get_picklist_values(sf, 'Contact', 'Job_Function_Level_2__c')

    # load everything outta salesforce into local ram cache
    print("POPULATING CACHE")
    for objname in sfapi.SFAPI_OBJECT_NAMES + ['Market']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("DONE POPULATING CACHE")

    trr_response_count_by_panel_member_id = {}
    for sr in sfimport.sfcache.values():
        if not isinstance(sr, sfapi.SurveyResponse):
            continue
        if sr.IsExtraQuestion:
            continue
        trr_response_count_by_panel_member_id.setdefault(sr.SurveyPanelMemberId, 0)
        trr_response_count_by_panel_member_id[sr.SurveyPanelMemberId] += 1

    customers_client_accounts_by_account_name = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_account_name.setdefault(account.Name, []).append(account)

    # load data from the CSV file
    emaildomains_by_client_account = {}
    with open(filename, encoding='utf-8-sig') as file:
        # we read in all rows, then sort by date descending so the most recent values are the ones take priority
        reader = csv.DictReader(file)

        duff_file = open(f'{os.path.basename(filename)}.duffrecords-surveycsv.csv', 'w')
        field_names = reader.fieldnames + ['row_number', 'reason']
        duff_writer = csv.DictWriter(duff_file, fieldnames=field_names)
        duff_writer.writeheader()

        all_rows = []
        for i, row in enumerate(reader):
            row['row_number'] = i + 1

            if not row['survey_start_date']:
                row['reason']= 'MISSING SURVEY START DATE'
                duff_writer.writerow(row)
                continue

            if targetsurveytype == 'trr' and row['survey_type'] not in {'trr', 'eqs'}:
                row['reason']= f'INVALID SURVEY TYPE: TARGET {targetsurveytype} GOT {row['survey_type']}'
                duff_writer.writerow(row)
                continue
            elif targetsurveytype == 'barometer' and row['survey_type'] != 'barometer':
                row['reason']= f'INVALID SURVEY TYPE: TARGET {targetsurveytype} GOT {row['survey_type']}'
                duff_writer.writerow(row)
                continue

            row['survey_start_date'] = parse_date(row['survey_start_date'])
            row['survey_end_date'] = parse_date(row['survey_end_date'])

            all_rows.append(row)
        all_rows.sort(key=lambda x: x['survey_start_date'], reverse=True)

        # now, process each row and generate the salesforce objects
        for row in all_rows:
            # =======================================================
            # figure out dates of bits of the survey
            survey_start_date = row['survey_start_date']
            survey_end_date = row['survey_end_date']
            quarter_start_date = survey_start_date.replace(day=1, month=((survey_start_date.month - 1) // 3) * 3 + 1)

            survey_round_date = quarter_start_date
            survey_round_end_date = quarter_start_date + datetime.timedelta(days=30) # we need this value, but dunno what to set it to!

            # =======================================================
            # Skip rows where the hub_flag_inclusion is 0
            hub_flag_inclusion = row['hub_flag_inclusion']
            if not hub_flag_inclusion:
                hub_flag_inclusion = '0'
            hub_flag_inclusion = int(float(hub_flag_inclusion))
            if not hub_flag_inclusion:
                continue

            # =======================================================
            # decode the market information
            market_name = row['market_name'].strip()
            if not market_name:
                row['reason']= 'MISSING MARKET'
                duff_writer.writerow(row)
                continue

            market = market_by_name_and_type.get(f'{market_name}-Country')
            if not market:
                row['reason']= 'INVALID MARKET'
                duff_writer.writerow(row)
                continue
            if market.MarketType != 'Country':
                row['reason']= f'INVALID MARKET TYPE {market.MarketType} (NOT COUNTRY)'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # decode the office market information
            office_market_name = row['office_name'].strip()
            office_market_name = sfimport.MARKET_MAP.get(f'{office_market_name}-City', office_market_name)

            # advertising surveys must have an office market
            if importtype == 'advertising' and not office_market_name:
                row['reason']= 'MISSING OFFICE MARKET'
                duff_writer.writerow(row)
                continue

            if office_market_name:
                office_market = market_by_name_and_type.get(f'{office_market_name}-City')
                if not office_market:
                    row['reason']= 'INVALID OFFICE MARKET'
                    duff_writer.writerow(row)
                    continue
                if office_market.MarketType != 'City':
                    row['reason']= f'INVALID OFFICE MARKET TYPE {office_market.MarketType} (NOT CITY)'
                    duff_writer.writerow(row)
                    continue
            else:
                office_market = None

            # =======================================================
            # load the team within the agency
            team_name = row['team_name'].strip()
            if not team_name:
                team_market = team_type = None

            else:
                team_market_name = row['team_market_name'].strip()
                if not team_market_name:
                    team_market_name = market_name
                for market_type in {'Global', 'Region', 'Business Region', 'Country', 'Sub-Region', 'Key Region'}:
                    team_market = market_by_name_and_type.get(f'{team_market_name}-{market_type}')
                    if team_market:
                        break
                if not team_market:
                    row['reason']= 'UNKNOWN/INVALID TEAM MARKET'
                    duff_writer.writerow(row)
                    continue

                team_type = row['team_type'].strip()
                if team_type not in SF_TEAM_TYPES:
                    row['reason']= 'INVALID TEAM TYPE'
                    duff_writer.writerow(row)
                    continue

            # =======================================================
            # extract the office label if supplied
            office_label = None
            if importtype == 'manufacturing':
                office_label = row['office_label'].strip()

            # =======================================================
            # extract the agency hierarchy
            customer_hierarchy = []
            for colname, organisational_level in organisational_level_fields:
                v = row[colname]
                if v is None:
                    continue
                v = v.strip()
                if v and agency_dev_prefix:
                    v = f'{agency_dev_prefix} {v}'
                if v:
                    customer_hierarchy.append((v, organisational_level))

            # do we have a valid agency name?
            if not customer_hierarchy:
                row['reason']= 'MISSING CUSTOMER NAME'
                duff_writer.writerow(row)
                continue
            leaf_agency_name = customer_hierarchy[-1][0]

            # make up the with market level
            if market:
                if importtype == 'advertising':
                    level_name = 'With Market'
                elif importtype == 'manufacturing':
                    level_name = 'Country Market'
                else:
                    raise Exception(f'Invalid import type {importtype}')
                customer_hierarchy.append((f"{leaf_agency_name} {market.Name}", level_name))

            # finally, add on the office level
            if office_market:
                if importtype == 'advertising':
                    level_name = 'Office'
                elif importtype == 'manufacturing':
                    level_name = 'Site'
                else:
                    raise Exception(f'Invalid import type {importtype}')

                if not office_label:
                    office_label = f"{leaf_agency_name} {office_market.Name}"
                customer_hierarchy.append((office_label, level_name))

            # =======================================================
            # norm sectors
            legacy_customer_sectors = row['legacy_norm_sectors']

            # =======================================================
            # get the customer's client name
            account_name = row['account_name'].strip()
            if not account_name:
                row['reason']= 'MISSING CUSTOMER CLIENT NAME'
                duff_writer.writerow(row)
                continue

            # =======================================================
            account_global = row.get('global_account')
            account_global = True if account_global else False
            account_tier = row.get('account_tier')

            # =======================================================
            account_group_name = row['account_group_name']

            # =======================================================
            # decode contact type
            if row['contact_type'] == 'pc':
                contact_type = 'Primary Contact'
            elif row['contact_type'] == 'cc':
                contact_type = 'Critical Contact'
            elif row['contact_type'] == 'influencer':
                contact_type = None
            elif row['contact_type'] in {None, ''}:
                contact_type = None
            else:
                row['reason']= 'UNKNOWN CONTACT TYPE'
                duff_writer.writerow(row)
                continue

            if contact_type and contact_type not in SF_CONTACT_TYPE:
                row['reason']= 'INVALID CONTACT TYPE'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # job title
            contact_job_title = row['job_title'].strip()
            if contact_job_title in {'No Title Provided'}:
                contact_job_title = None
            if contact_job_title:
                contact_job_title = contact_job_title[:127] # truncate madness - field length is 128 chars

            # =======================================================
            # name
            contact_first_name = row['first_name'].strip()
            contact_last_name = row['last_name'].strip()

            # =======================================================
            # email address
            contact_unsafe_email_address = row['email_address'].strip().replace('\u200b', '')
            contact_safe_email_address = sfimport.make_safe_email(contact_unsafe_email_address)
            contact_emaildomain = contact_unsafe_email_address.split('@')[-1].lower()

            # validate emails
            if contact_unsafe_email_address == '' or contact_first_name == '' or contact_last_name == '' or ' ' in contact_unsafe_email_address:
                row['reason']= 'MISSING EMAIL ADDRESS/ FIRST NAME/ LAST NAME'
                duff_writer.writerow(row)
                continue

            email_invalid = '@' not in contact_unsafe_email_address
            if email_invalid:
                row['reason']= 'INVALID EMAIL ADDRESS'
                duff_writer.writerow(row)
                continue
            email_chars_len = len(contact_unsafe_email_address)
            email_bytes_len = len(contact_unsafe_email_address.encode('utf-8'))
            if email_chars_len != email_bytes_len:
                row['reason']= 'INVALID EMAIL ADDRESS WITH NON-ASCII CHARACTERS'
                duff_writer.writerow(row)
                continue

            # =======================================================
            contact_seniority = row['seniority_name'].strip()
            if contact_seniority == 'senior':
                contact_seniority = 'Senior'
            elif contact_seniority == 'junior':
                contact_seniority = 'Junior'
            elif contact_seniority == 'middle':
                contact_seniority = 'Middle'
            elif not contact_seniority:
                contact_seniority = None
            else:
                row['reason']= 'UNKNOWN SENIORITY'
                duff_writer.writerow(row)
                continue
            if contact_seniority and contact_seniority not in SF_CONTACT_SENIORITY:
                row['reason']= 'INVALID CONTACT SENIORITY'
                duff_writer.writerow(row)
                continue

            # =======================================================
            contact_job_function = contact_job_function_level2 = None
            if importtype == 'manufacturing':
                if row['job_role_1'].strip():
                    contact_job_function = row['job_role_1'].strip()
                    if contact_job_function and contact_job_function not in SF_CONTACT_JOB_FUNCTION:
                        row['reason']= f'INVALID JOB FUNCTION {contact_job_function}'
                        duff_writer.writerow(row)
                        continue
                if row['job_role_2'].strip():
                    contact_job_function_level2 = row['job_role_2'].strip()
                    if contact_job_function_level2 and contact_job_function_level2 not in SF_CONTACT_JOB_FUNCTION_LEVEL_2:
                        row['reason']= f'INVALID JOB FUNCTION LEVEL 2 {contact_job_function_level2}'
                        duff_writer.writerow(row)
                        continue

            # =======================================================
            contact_hub_contact_id = row['contact_id']
            customer_external_contact_id = row.get('customer_external_contact_id')
            if customer_external_contact_id:
                customer_external_contact_id = customer_external_contact_id.strip()

            # =======================================================
            contact_disable_external_comms = False
            if importtype == "manufacturing":
                disable_contact_comms = row['disable_contact_comms']
                if disable_contact_comms is not None:
                    disable_contact_comms = disable_contact_comms.strip().lower()
                contact_disable_external_comms = disable_contact_comms in {'true', 't', 'yes', '1'}

            # =======================================================
            contact_hub_flag_opt_out = row['hub_flag_opt_out']
            if not contact_hub_flag_opt_out:
                contact_hub_flag_opt_out = '0'
            contact_hub_flag_opt_out = True if int(float(contact_hub_flag_opt_out)) else False
            contact_hub_flag_opt_out_timestamp = None
            if contact_hub_flag_opt_out and row['opt_out_timestamp']:
                contact_hub_flag_opt_out_timestamp = parse_datetime(row['opt_out_timestamp'])

            # =======================================================
            contact_legacy_division = row['legacy_contact_division'].strip()
            if contact_legacy_division in sfimport.EXCLUDED_LEGACY_DIVISIONS:
                contact_legacy_division = None

            # =======================================================
            survey_language = row['survey_language'].strip()
            survey_language = sfimport.LANGUAGE_MAP.get(survey_language, survey_language)
            if survey_language and survey_language not in SF_CONTACT_LANGUAGE:
                row['reason']= 'INVALID SURVEY LANGUAGE'
                duff_writer.writerow(row)
                continue

            # =======================================================
            contact_market = None
            if importtype == 'manufacturing':
                contact_market_name = row['contact_market']
                if contact_market_name is not None:
                    contact_market_name = contact_market_name.strip()
                if contact_market_name:
                    contact_market = market_by_name_and_type.get(f'{contact_market_name}-Country')
                    if not contact_market:
                        row['reason']= 'INVALID CONTACT MARKET'
                        duff_writer.writerow(row)
                        continue
                    if contact_market.MarketType != 'Country':
                        row['reason']= f'INVALID CONTACT MARKET TYPE {contact_market.MarketType} (NOT COUNTRY)'
                        duff_writer.writerow(row)
                        continue

            # =======================================================
            contact_account_site = None
            if importtype == 'manufacturing':
                contact_account_site_name = row['account_site']
                if contact_account_site_name is not None:
                    contact_account_site_name = contact_account_site_name.strip()
                if contact_account_site_name:
                    contact_account_site = market_by_name_and_type.get(f'{contact_account_site_name}-City')
                    if not contact_account_site:
                        row['reason']= 'INVALID CONTACT ACCOUNT SITE'
                        duff_writer.writerow(row)
                        continue
                    if contact_account_site.MarketType != 'City':
                        row['reason']= f'INVALID CONTACT ACCOUNT SITE {contact_account_site.MarketType} (NOT CITY)'
                        duff_writer.writerow(row)
                        continue

            # =======================================================
            # Survey response
            score_question = row['rating_question_text']
            try:
                score = int(float(row['rating']))
            except ValueError:
                score = None
            feedback_question = row['feedback_question_text']
            feedback = row['feedback']
            feedback_translated = row['feedback_translation']
            has_responded = score is not None
            themes = row['feedback_attributes']
            response_datetime = row['response_timestamp']
            if response_datetime in {'nan'}:
                response_datetime = None
            if response_datetime:
                response_datetime = parse_datetime(response_datetime)
            hub_row_key = row['hub_row_key']

            is_extra_question = False
            survey_type = 'TRR'
            if row['survey_type'] in {'trr'}:
                is_extra_question = False
            elif row['survey_type'] in {'barometer'}:
                is_extra_question = False
                survey_type = 'Barometer'
            elif row['survey_type'] == 'eqs':
                is_extra_question = True
            else:
                row['reason']= 'INVALID SURVEY TYPE'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # Create CRC Customer Account hierarchy
            parent_crc_account = customer_holding_group_account = customer_agency_leaf_account = None
            for i, (hierachy_account_name, organisational_level) in enumerate(customer_hierarchy):
                if organisational_level not in SF_CLIENT_ORG_LEVELS:
                    row['reason']= 'INVALID CLIENT ORGANISATIONAL LEVEL'
                    duff_writer.writerow(row)
                    continue

                hierarchy_level_account = sfimport.get_or_create_cached_account(hierachy_account_name,
                                                                                parent_crc_account,
                                                                                accountRecordTypes[crc_customer_record_type],
                                                                                organisational_level=organisational_level)
                if hierarchy_level_account.OrganisationalLevel != organisational_level:
                    row['reason'] = f'EXPECTED {organisational_level} FOR AGENCY {hierachy_account_name}'
                    duff_writer.writerow(row)
                    continue

                if organisational_level in {'With Market', 'Country Market'}:
                    if parent_crc_account:
                        parent_crc_account.LegacySector = legacy_customer_sectors
                    hierarchy_level_account.LegacySector = legacy_customer_sectors
                    hierarchy_level_account.MarketId = market.Id

                elif organisational_level in {'Office', 'Site'}:
                    if parent_crc_account:
                        parent_crc_account.LegacySector = legacy_customer_sectors
                    hierarchy_level_account.LegacySector = legacy_customer_sectors
                    hierarchy_level_account.MarketId = office_market.Id

                parent_crc_account = hierarchy_level_account
                customer_agency_leaf_account = hierarchy_level_account
                if not customer_holding_group_account:
                    customer_holding_group_account = hierarchy_level_account

            # =======================================================
            # Create the team if supplied
            team = None
            if team_name:
                team = sfimport.get_or_create_cached_team(team_name,
                                                          customer_agency_leaf_account,
                                                          market=team_market,
                                                          type=team_type)

            # =======================================================
            # get the survey name
            survey_name = row['survey_agency_name'].strip()
            if not survey_name:
                survey_name = leaf_agency_name

            # =======================================================
            # get the survey name
            account_label = None
            if importtype == 'manufacturing':
                account_label = row['account_label']
                if account_label is not None:
                    account_label = account_label.strip()

            # =======================================================
            # CRC Customer's Client Account

            # lookup the customer client account BY NAME
            customers_client_account = None
            if account_name:
                account_name = customer_client_name_mapping.get(account_name, {account_name})
                account_name = list(account_name)[0]

                customers_client_account = customers_client_accounts_by_account_name.get(account_name)
                if customers_client_account and len(customers_client_account) > 1:
                    row['reason']= 'MULTIPLE MATCHED CLIENT ACCOUNTS'
                    duff_writer.writerow(row)
                    continue

                if customers_client_account:
                    customers_client_account = customers_client_account[0]
            if not customers_client_account:
                row['reason']= 'MISSING CUSTOMER CLIENT ACCOUNT'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # populate the account group for the thing
            if account_group_name:
                account_group = sfimport.get_or_create_cached_account_group(customer_agency_leaf_account, account_group_name)
            else:
                account_group = None

            if account_tier:
                account_tier = sfimport.get_or_create_cached_account_tier(customer_agency_leaf_account, account_tier)
            else:
                account_tier = None

            customer_segment = row.get('customer_segment')
            if customer_segment is not None:
                customer_segment = customer_segment.strip()
                

            # =======================================================
            # Customer Client Relationship between the two
            customer_client_relationship = sfimport.get_or_create_cached_customer_client_relationship(customer_agency_leaf_account,
                                                                                                      customers_client_account,
                                                                                                      'Client',
                                                                                                      account_group=account_group,
                                                                                                      account_tier=account_tier,
                                                                                                      customers_client_segment=customer_segment)
            # If we've not set the survey name already, set it now
            if not customer_client_relationship.SurveyName:
                customer_client_relationship.SurveyName = survey_name
            if not customer_client_relationship.AccountLabel:
                customer_client_relationship.AccountLabel = account_label

            # =======================================================
            # Survey Contact
            if safeemails and not contact_unsafe_email_address.endswith('@clientrelationship.com'):
                contact_email_to_write = contact_safe_email_address
            else:
                contact_email_to_write = contact_unsafe_email_address

            contact = sfimport.get_or_create_cached_contact(customers_client_account,
                                                                contact_first_name,
                                                                contact_last_name,
                                                                contact_email_to_write,
                                                                contact_type=contact_type,
                                                                seniority=contact_seniority,
                                                                job_title=contact_job_title,
                                                                language=survey_language,
                                                                job_function=contact_job_function,
                                                                job_function_level2=contact_job_function_level2,
                                                                hub_contact_id=contact_hub_contact_id,
                                                                legacy_division=contact_legacy_division,
                                                                )
            contact.Location = contact_market.Id if contact_market else None
            contact.AccountSite = contact_account_site.Id if contact_account_site else None
            contact.DisableExternalCommunications = contact_disable_external_comms

            # add relation between contact/customer_client_accouunt and the customer which surveys them
            sfimport.get_or_create_cached_account_contact_relation(customer_agency_leaf_account,
                                                                   contact,
                                                                   customer_external_contact_id=customer_external_contact_id)

            # =======================================================
            # calculate the email domains for the client account (note this won't update if the account already existed in salesforce)
            emaildomains_by_client_account.setdefault(customers_client_account.Id, set()).add(contact_emaildomain)
            emaildomains_by_client_account.setdefault(customers_client_account.Id, set()).add('clientrelationship.com')
            customers_client_account.ValidEmailDomains = ' '.join(emaildomains_by_client_account[customers_client_account.Id])[:254]

            # =======================================================
            # import survey data
            survey_round = sfimport.get_or_create_cached_survey_round(survey_round_date, survey_round_end_date, '', survey_type, is_imported=True)

            customer_survey_round = sfimport.get_or_create_cached_customer_survey_round(survey_round, customer_holding_group_account, 'Insights')
            customer_survey_round.LiveSurveyStartDate = survey_start_date
            customer_survey_round.LiveSurveyEndDate = survey_end_date
            customer_survey_round.DisableExternalCommunication = True

            customer_survey = sfimport.get_or_create_cached_customer_survey(customer_survey_round,
                                                                            customer_agency_leaf_account,
                                                                            'Insights')
            customer_survey.LiveSurveyStartDate = survey_start_date
            customer_survey.LiveSurveyEndDate = survey_end_date

            if team:
                customer_survey = sfimport.get_or_create_cached_customer_survey(customer_survey_round,
                                                                                customer_agency_leaf_account,
                                                                                'Insights',
                                                                                parent_customer_survey=customer_survey,
                                                                                team=team)
                customer_survey.LiveSurveyStartDate = survey_start_date
                customer_survey.LiveSurveyEndDate = survey_end_date

            survey_client = sfimport.get_or_create_cached_survey_client(customer_survey,
                                                                        customers_client_account,
                                                                        'Survey Complete',
                                                                        survey_name=survey_name)
            
            product_segments = row.get('product_segment')
            if product_segments:
                # split comma seperated values to support multi-value attribution
                segment_names = [segment.strip() for segment in product_segments.split(',')]
                
                for segment_name in segment_names:
                    if not segment_name:
                        continue
                        
                    product_segment = sfimport.get_or_create_cached_product_segment(
                        customer_agency_leaf_account, 
                        segment_name
                    )
                    
                    sfimport.get_or_create_cached_survey_client_product_segment(
                        survey_client,
                        product_segment
                    )


            survey_panel_member = sfimport.get_or_create_cached_survey_panel_member(survey_client,
                                                                                    contact,
                                                                                    hub_row_key=hub_row_key)
            survey_panel_member.OptOut = contact_hub_flag_opt_out
            survey_panel_member.OptOutDateTime = contact_hub_flag_opt_out_timestamp
            survey_panel_member.ContactEmail = contact_email_to_write
            survey_panel_member.ContactType = contact_type
            survey_panel_member.ContactSeniority = contact_seniority
            survey_panel_member.ContactTitle = contact_job_title
            survey_panel_member.ContactJobFunction = contact_job_function
            survey_panel_member.ContactJobFunction2 = contact_job_function_level2
            survey_panel_member.ContactDivision = contact_legacy_division

            if has_responded:
                sr = sfimport.get_or_create_cached_survey_response(survey_panel_member,
                                                                    score_question=score_question,
                                                                    score=score,
                                                                    feedback_question=feedback_question,
                                                                    feedback=feedback,
                                                                    feedback_translated=feedback_translated,
                                                                    themes=themes,
                                                                    is_extra_question=is_extra_question,
                                                                    response_date_time=response_datetime)

                if not is_extra_question:
                    if sr.Id.startswith('SurveyResponse-'):  # ie it is a new response we're gonna create with this run of the importer
                        trr_response_count_by_panel_member_id.setdefault(survey_panel_member.Id, 0)
                        trr_response_count_by_panel_member_id[survey_panel_member.Id] += 1

                        if trr_response_count_by_panel_member_id[survey_panel_member.Id] > 1:
                            row['reason']= 'MULTIPLE TRR RESPONSES FOR PANEL MEMBER'
                            duff_writer.writerow(row)

    # =======================================================
    # aaand load it all into salesforce
    if writeit:
        pass
        sfimport.bulk_write_accounts_to_sf(sf, sfimport.new_accounts)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_teams, sfapi.Team)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_groups, sfapi.AccountGroup)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_tiers, sfapi.AccountTier)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_divisions, sfapi.Division)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_contact_relations, sfapi.AccountContactRelation)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_rounds, sfapi.SurveyRound)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_survey_rounds, sfapi.CustomerSurveyRound)
        sfimport.bulk_write_customer_surveys_to_sf(sf, sfimport.new_customer_surveys)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_clients, sfapi.SurveyClient)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_product_segments, sfapi.ProductSegment)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_client_product_segments, sfapi.SurveyClientProductSegment)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_members, sfapi.SurveyPanelMember)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_responses, sfapi.SurveyResponse)
    else:
        print("Dry run, not writing to Salesforce")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    ap.add_argument('importtype', choices=['advertising', 'manufacturing'], help='Import type')
    ap.add_argument('targetsurveytype', choices=['trr', 'barometer'], help='The type of survey being imported')
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.filename, args.importtype, args.targetsurveytype, args.safeemails, args.writeit)
