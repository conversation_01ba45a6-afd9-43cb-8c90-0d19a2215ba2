import csv
import argparse
import datetime
from pymongo import UpdateOne
from lib import portaldbapi


def main(csr_id: str, writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)
    query = {
        'customer_survey_round_id': csr_id,
        'accounts_confirmed_by_date': { '$lt': today }
    }
    projection = {
        'Id': 1,
        'client_id': 1,
        'client_name': 1,
        'customer_survey_round_id': 1,
        'customer_survey_name': 1,
        'confirmed_accounts': 1,
    }
    batch = []

    for confirm_account in collection.find(query, projection):
        confirmed_account_ids = set()
        client_id = confirm_account['client_id']
        client_name = confirm_account['client_name']
        customer_survey_round_id = confirm_account['customer_survey_round_id']
        customer_survey_id = confirm_account['Id']
        customer_survey_name = confirm_account['customer_survey_name']

        for confirmed_account in confirm_account.get('confirmed_accounts',{}).values():
            for account in confirmed_account.get('accounts', []):
                account_id = account['account_id']
                account_name = account['account_name']
                panel_managers = [pm['panel_manager_email'] for pm in account.get('survey_panel_managers', [])]
                signatory = account['signatory_email']

                if account_id in confirmed_account_ids:
                    continue

                confirmed_account_ids.add(account_id)

                batch.append(dict(
                    salesforce_customer_survey_round_id=customer_survey_round_id,
                    salesforce_customer_survey_id=customer_survey_id,
                    customer_survey_name=customer_survey_name,
                    salesforce_client_id=client_id,
                    client_name=client_name,
                    salesforce_account_id=account_id,
                    account_name=account_name,
                    panel_managers='|'.join(panel_managers),
                    signatory=signatory,
                ))

    if writeit:
        with open('confirmed_accounts.csv', 'w') as f:
            csvout = csv.DictWriter(f, fieldnames=['salesforce_customer_survey_round_id', 'salesforce_customer_survey_id', 'customer_survey_name', 'salesforce_client_id', 'client_name', 'salesforce_account_id', 'account_name', 'panel_managers', 'signatory'])
            csvout.writeheader()

            for row in batch:
                csvout.writerow(row)

    print(f'{len(batch)} confirmed accounts found')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_round_id', help='ID of the CSR to process')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the CSV')
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.writeit)