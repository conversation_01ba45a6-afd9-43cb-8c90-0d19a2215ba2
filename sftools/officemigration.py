import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    header = [
        'survey_round_id',
        'survey_round_name',

        'customer_survey_round_id',
        'customer_survey_round_name',

        'customer_survey_id',
        'customer_survey_name',
        'customer_survey_customer_id',
        'customer_survey_customer_name',
        'customer_survey_customer_parent_id',
        'customer_survey_customer_parent_name',
        'customer_survey_parent_customer_survey_id',
        'customer_survey_parent_customer_survey_name',
        'customer_survey_team_id',
        'customer_survey_team_name',
        'customer_survey_team_type',

        'survey_client_id',
        'survey_client_name',
        'survey_client_customers_client_id',
        'survey_client_customers_client_name',
    ]

    soql = """
SELECT Id,
        Name,
        Customers_Client__r.Id,
        Customers_Client__r.Name,
        Customer_Survey__r.Id,
        Customer_Survey__r.Name,
        Customer_Survey__r.Customer__r.Id,
        Customer_Survey__r.Customer__r.Name,
        Customer_Survey__r.Customer__r.ParentId,
        Customer_Survey__r.Customer__r.Parent.Name,
        Customer_Survey__r.Parent_Customer_Survey__r.Id,
        Customer_Survey__r.Parent_Customer_Survey__r.Name,
        Customer_Survey__r.Team__r.Id,
        Customer_Survey__r.Team__r.Name,
        Customer_Survey__r.Team__r.Type__c,
        Customer_Survey__r.Customer_Survey_Round__r.Id,
        Customer_Survey__r.Customer_Survey_Round__r.Name,
        Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
        Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name
From Survey_Client__c
"""

    with open('officemigration.csv', 'w') as f:
        csvout = csv.DictWriter(f, fieldnames=header)
        csvout.writeheader()


        for row in sfapi.bulk_query(sf, soql):
            rowdict = {
                'survey_client_id': row['Id'],
                'survey_client_name': row['Name'],
                'survey_client_customers_client_id': row['Customers_Client__r.Id'],
                'survey_client_customers_client_name': row['Customers_Client__r.Name'],
                'customer_survey_id': row['Customer_Survey__r.Id'],
                'customer_survey_name': row['Customer_Survey__r.Name'],
                'customer_survey_customer_id': row['Customer_Survey__r.Customer__r.Id'],
                'customer_survey_customer_name': row['Customer_Survey__r.Customer__r.Name'],
                'customer_survey_customer_parent_id': row['Customer_Survey__r.Customer__r.ParentId'],
                'customer_survey_customer_parent_name': row['Customer_Survey__r.Customer__r.Parent.Name'],
                'customer_survey_parent_customer_survey_id': row['Customer_Survey__r.Parent_Customer_Survey__r.Id'],
                'customer_survey_parent_customer_survey_name': row['Customer_Survey__r.Parent_Customer_Survey__r.Name'],
                'customer_survey_team_id': row['Customer_Survey__r.Team__r.Id'],
                'customer_survey_team_name': row['Customer_Survey__r.Team__r.Name'],
                'customer_survey_team_type': row['Customer_Survey__r.Team__r.Type__c'],
                'customer_survey_round_id': row['Customer_Survey__r.Customer_Survey_Round__r.Id'],
                'customer_survey_round_name': row['Customer_Survey__r.Customer_Survey_Round__r.Name'],
                'survey_round_id': row['Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id'],
                'survey_round_name': row['Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name']
            }
            csvout.writerow(rowdict)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
