import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    soql = """
SELECT Id,
        Account_Manager__c,
        Customer_Survey__c,
        Account_Manager__r.Email
FROM Survey_Account_Manager__c
WHERE Customer_Survey__r.Customer_Survey_Round__c = 'a02WT00000676VhYAI'
"""
    deduper = {}
    for row in sfapi.bulk_query(sf, soql):
        bk = f"SurveyAccountManager-{row['Account_Manager__c']}-{row['Customer_Survey__c']}"

        # if row['Customer_Survey__c'] != 'a03WT000009JoCmYAK':
        #     continue

        deduper.setdefault(bk, []).append(row)

    tozap = set()
    emails = set()
    for k,v in deduper.items():
        emails.add(v[0]['Account_Manager__r.Email'])

        # if there's only one, it's not a dupe
        if len(v) == 1:
            continue

        # if there's more than one, zap any subsequent ones
        for i in range(1, len(v)):
            row = v[i]
            print(f"Dupe sam {row['Id']} Customer Survey {row['Customer_Survey__c']} Duplicate SAM Contact: {row['Account_Manager__c']}")
            tozap.add(row['Id'])

    # for x in sorted(emails):
    #     print(x)


    for record_status in sfapi.bulk_delete(sf, 'Survey_Account_Manager__c', list(tozap)):
        for row in record_status['failedRecords']:
            print('Failed to delete', row)
        for row in record_status['unprocessedRecords']:
            print('Failed to delete', row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
