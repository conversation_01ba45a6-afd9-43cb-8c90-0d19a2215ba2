import lib.sfapi as sfapi
from lib.settings import settings
from lib.settingsapi import stack_name
import time


def bulk_delete_batch_to_sf(sf, batch, sf_object_name):
    # don't bother if there's nothing to do
    if not batch:
        return

    for record_status in sfapi.bulk_delete(sf, sf_object_name, batch):
        for row in record_status['failedRecords']:
            print('Failed to delete', row)
        for row in record_status['unprocessedRecords']:
            print('Failed to delete', row)

def zap_sf():
    if stack_name not in {'dev'}:
        print(f"REFUSING TO RUN ON STACK {stack_name}")
        exit(1)
    if '.sandbox.' not in settings.SF_INSTANCE:
        print(f"REFUSING TO RUN ON INSTANCE {settings.SF_INSTANCE}")
        exit(1)
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    print(settings.SF_INSTANCE)
    time.sleep(5)

    for objtozap in sfapi.SFAPI_OBJECT_NAMES:
        print("Wiping", objtozap)
        objtozap = getattr(sfapi, objtozap)
        ids = [item.Id for item in sfapi.get_all(sf, objtozap)]
        bulk_delete_batch_to_sf(sf, ids, objtozap.sfapi_name())


if __name__ == '__main__':
    zap_sf()
