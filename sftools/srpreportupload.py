import os
import argparse
import base64
from lib import sfapi
from lib.settings import settings 


REPORTS_PATH = '/Users/<USER>/Desktop/srpreports'


def upload_report_to_sf(sf, file_path, contact_id, filename):

    with open(file_path, 'rb') as f:
        encoded_file = base64.b64encode(f.read()).decode('utf-8')

    # create the ContentVersion object
    content_version = {
        'Title': filename,
        'Share_in_Client_Area__c': True,
        'Report_Type__c': 'Mass Release',
        'PathOnClient': filename,
        'VersionData': encoded_file
    }
    content_version_result = sf.ContentVersion.create(content_version)
    content_version_id = content_version_result['id']

    # get the ContentDocument ID from ContentVersion
    content_document_id = sf.query(
        f"SELECT ContentDocumentId FROM ContentVersion WHERE Id = '{content_version_id}'"
    )['records'][0]['ContentDocumentId']

    # link the ContentDocument to the Contact
    content_document_link = {
        'ContentDocumentId': content_document_id,
        'LinkedEntityId': contact_id, 
        'ShareType': 'V', 
        'Visibility': 'AllUsers'
    }
    sf.ContentDocumentLink.create(content_document_link)
    print(f"Uploaded {filename} and linked to Contact {contact_id}")


def main(writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    for file_name in os.listdir(REPORTS_PATH):
        print(f"Processing {file_name}")
        if file_name.endswith('.pdf'):
            contact_id, filename = file_name.split('_', 1)
            if filename.endswith(".pdf"):
                filename = filename[:-4]
            print(f"  >> Uploading {filename} for Contact {contact_id}")
            file_path = os.path.join(REPORTS_PATH, file_name)
            if writeit:
                try:
                    upload_report_to_sf(sf, file_path, contact_id, filename)
                except Exception as e:
                    print(f"Failed to upload {file_name}: {e}")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to Salesforce")
    args = ap.parse_args()

    main(writeit=args.writeit)