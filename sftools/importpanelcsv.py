import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy
import openpyxl


def import_csv(filename, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Market', 'Contact', 'CustomerClientRelationship', 'AccountContactRelation']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject))
    print("Cache populated")

    # find all the markets in salesforce
    all_markets_by_name: dict[str, sfapi.Market] = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        all_markets_by_name[market.Name.lower()] = market

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    # load data from the CSV file
    with open(filename) as file:
        reader = csv.reader(file)

        # figure out the header row
        header_row_lookup = {}
        header_row = []
        for col_idx, v in enumerate(next(reader)):
            if not v:
                continue
            v = v.strip().lower()
            header_row.append(v)
            header_row_lookup[v] = col_idx

        # load all the rows into ram and convert them into dicts + enumerate them
        all_rows = []
        for i, row in enumerate(reader):
            row = {k: row[v].strip() for k, v in header_row_lookup.items()}
            row['row_number'] = i + 1
            all_rows.append(row)

        # file to contain duff records
        duff_file = open('duffrecords-panel.csv', 'w')
        duff_header_row = copy.copy(header_row)
        for col in ['row_number', 'reason', 'customer_hierarchy', 'unsafe_email_address', 'sf_market']:
            if col not in duff_header_row:
                duff_header_row.append(col)
        duff_writer = csv.DictWriter(duff_file, fieldnames=duff_header_row)
        duff_writer.writeheader()

        # preprocess each row and compute various things
        # contact_customer_hierachies = {}
        for row in all_rows:
            row['reason'] = None

            # compute the account hierarchy with the correct names
            agency_account = None
            customer_hierarchy: list[sfapi.Account] = []
            had_agency_account_lookup_error = False
            for colname in ['holding group', 'network', 'sub-network', 'agency brand', 'agency brand subsidiary']:
                if colname == 'holding group':
                    organisational_level = 'Holding Group'
                elif colname == 'network':
                    organisational_level = 'Network'
                elif colname == 'sub-network':
                    organisational_level = 'Sub-network'
                elif colname == 'agency brand':
                    organisational_level = 'Agency Brand'
                elif colname == 'agency brand subsidiary':
                    organisational_level = 'Agency Brand (Sub 1)'
                else:
                    raise Exception(f"Unknown organisational level {colname}")

                v = row[colname].strip()
                if not v:
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(v, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    row['reason'] = f'MISSING AGENCY ACCOUNT {v}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    break
                if agency_account.OrganisationalLevel != organisational_level:
                    row['reason'] = f'UNEXPECTED AGENCY ACCOUNT LEVEL {v} {organisational_level} != {agency_account.OrganisationalLevel}'
                    duff_writer.writerow(row)
                    had_agency_account_lookup_error = True
                    break

                customer_hierarchy.append(agency_account)
            if had_agency_account_lookup_error:
                continue

            # find the market if there is one
            market_name = row['market'].strip()
            sf_market = all_markets_by_name.get(market_name.lower())
            if not sf_market:
                row['reason'] = f'UNKNOWN MARKET'
                duff_writer.writerow(row)
                continue
            if sf_market.MarketType != 'Country':
                row['reason'] = f'MARKETS MUST BE COUNTRIES'
                duff_writer.writerow(row)
                continue

            # add on "with market" account
            with_market_account_name = f"{customer_hierarchy[-1].Name} {sf_market.Name}"
            agency_account = sfimport.sfcache.get(sfapi.Account.business_key(with_market_account_name, agency_account, accountRecordTypes[crc_customer_record_type]))
            if not agency_account:
                row['reason'] = f'MISSING AGENCY WITH MARKET ACCOUNT {with_market_account_name}'
                duff_writer.writerow(row)
                continue
            customer_hierarchy.append(agency_account)

            # stash stuff for later
            row['sf_market'] = sf_market
            row['customer_hierarchy'] = customer_hierarchy
            row['unsafe_email_address'] = unsafe_email_address = row['email address'].lower().strip().strip(';').strip(',')

        # finally, its the main row processing loop!
        for row in all_rows:
            # row already has an error of some sort
            if row.get('reason'):
                continue

            # =======================================================
            # contact details
            unsafe_email_address = row['unsafe_email_address']
            safe_email_address = sfimport.make_safe_email(unsafe_email_address)
            first_name = row['first name'].strip()
            last_name = row['last name'].strip()
            job_title = row['job title'].strip()
            division = row['brand division'].strip()
            contact_type = row['contact type'].strip()
            seniority = row['seniority'].strip()
            language = row['language'].strip()
            timezone = row['timezone'].strip()
            job_function = row['job function'].strip()

            # check contact details are valid
            if not first_name or not last_name or not unsafe_email_address:
                row['reason']= 'MISSING NAME OR EMAIL'
                duff_writer.writerow(row)
                continue

            # =======================================================
            # extract the market information
            sf_market = row['sf_market']

            # =======================================================
            # get stuff from customer hierarchy
            customer_hierarchy = row['customer_hierarchy']

            # =======================================================
            # find the relevant customer client account
            customer_client_account_name = row['account'].strip()
            customer_client_account = customers_client_accounts_by_name.get(customer_client_account_name)
            if not customer_client_account:
                row['reason']= 'UNKNOWN CUSTOMER CLIENT ACCOUNT'
                duff_writer.writerow(row)
                continue
            if len(customer_client_account) > 1:
                row['reason']= 'MULTIPLE CUSTOMER CLIENT ACCOUNTS'
                duff_writer.writerow(row)
                continue
            customer_client_account = customer_client_account[0]

            # setup the CCR between agency and customer client
            customer_client_relationship = sfimport.get_or_create_cached_customer_client_relationship(agency_account,
                                                                                                      customer_client_account,
                                                                                                      'Client')

            # create contact and attach to customer client account
            contact_email = safe_email_address if safeemails else unsafe_email_address
            contact = sfimport.get_or_create_cached_contact(customer_client_account,
                                                            first_name,
                                                            last_name,
                                                            contact_email)
            contact.JobTitle = job_title
            contact.LegacyDivision = division
            contact.ContactType = contact_type
            contact.Seniority = seniority
            contact.Language = language
            contact.Timezone = timezone
            contact.JobFunction = job_function

            # create account contact relation between agency market and customer client
            sfimport.get_or_create_cached_account_contact_relation(agency_account, contact)

    if writeit:
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_contact_relations, sfapi.AccountContactRelation)

    else:
        print("Dry run, not writing to Salesforce")
        print(len(sfimport.new_contacts), "new contacts")
        print(len(sfimport.new_customer_client_relationships), "new customer client relationships")
        print(len(sfimport.new_account_contact_relations), "new account contact relations")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('filename', help='CSV file to import')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.filename, args.safeemails, args.writeit)
