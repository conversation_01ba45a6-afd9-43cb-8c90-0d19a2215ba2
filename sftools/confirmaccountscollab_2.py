import argparse
from lib import portaldbapi


def main(writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    # update the existing (old) confirmed_accounts to confirmed_accounts_archive so we still have a copy of it (for now)
    print('Updating confirmed_accounts to confirmed_accounts_archive')
    if writeit:
        result = collection.update_many(
            {},
            {
                "$rename": {
                    "confirmed_accounts": "confirmed_accounts_archive"
                }
            }
        )
        print("Matched count:", result.matched_count)
        print("Modified count:", result.modified_count)

    # update the new structure to be confirmed_accounts
    print('Updating confirmed_accounts_new to confirmed_accounts')
    if writeit:
        result = collection.update_many(
            {}, 
            {
                "$rename": {
                    "confirmed_accounts_new": "confirmed_accounts"
                }
            }
        )
        print("Matched count:", result.matched_count)
        print("Modified count:", result.modified_count)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.writeit)