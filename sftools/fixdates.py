import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy


def import_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    csr = sfapi.get_by_id(sf, sfapi.CustomerSurveyRound, 'a02WT00000676VhYAI')
    print(csr)

    soql = """
SELECT Id, Name
FROM Customer_Survey__c
WHERE Customer_Survey_Round__c = 'a02WT00000676VhYAI' and Live_Survey_Start_Date__c = null
"""

    patch = []
    for cs in sf.query_all_iter(soql):
        patch.append({'Id': cs['Id'],
                      'Account_Updates_Start_Date__c': csr.AccountUpdatesStartDate.isoformat(),
                      'Account_Updates_End_Date__c': csr.AccountUpdatesEndDate.isoformat(),
                       'Panel_Updates_Start_Date__c': csr.PanelUpdatesStartDate.isoformat(),
                       'Panel_Updates_End_Date__c': csr.PanelUpdatesEndDate.isoformat(),
                       'Live_Survey_Start_Date__c': csr.LiveSurveyStartDate.isoformat(),
                       'Live_Survey_First_Request__c': csr.LiveSurveyFirstRequest.isoformat(),
                       'Live_Survey_Second_Request__c': csr.LiveSurveySecondRequest.isoformat(),
                       'Live_Survey_Third_Request__c': csr.LiveSurveyThirdRequest.isoformat(),
                       'Live_Survey_Fourth_Request__c': csr.LiveSurveyFourthRequest.isoformat(),
                       'Live_Survey_End_Date__c': csr.LiveSurveyEndDate.isoformat()})

    for status in sfapi.bulk_update(sf, 'Customer_Survey__c', patch):
        sfapi.detect_bulk2_errors(status)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    import_csv()
