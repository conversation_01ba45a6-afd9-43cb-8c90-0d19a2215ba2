""" Export list of confirmed accounts for account confirm stage that has started but not yet completed (as of today)
"""
import lib.portaldbapi as portaldbapi
import argparse
import csv
import datetime


def main():
    portaldb = portaldbapi.DocumentDB()

    today = datetime.datetime(2024, 12, 12)
    # today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    filename = f"/tmp/confirmed_accounts_{today.strftime('%Y-%m-%d')}.csv"
    with open(filename, 'w') as f:
        fieldnames = [
            'account_manager_id', 'account_manager_email', 'client_name', 
            'account_id', 'account_name', 'signatory_email', 'signatory_name', 'survey_name',
            # survey_panel_managers
            'panel_manager_name', 'panel_manager_email', 'panel_manager_id',
            'account_confirmation_start_date', 'account_confirmation_end_date', 'survey_client_link', 
            'confirmed_date', 'auto_confirmed', 'in_round'
        ]
        csvout = csv.DictWriter(f, fieldnames=fieldnames)
        csvout.writeheader()

        query = {'deleted': {'$ne': True}, 'accounts_confirmed_by_date': {'$gte': today}, 'accounts_confirmed_start_date': {'$lte': today}}

        for confirm_acc in portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION].find(query):
            acc_managers_by_id = {x['account_manager_id']: x for x in confirm_acc.get('account_managers', [])}
            # panel_members = confirm_panel['panel']
            # panel_members_by_email = {x['contact_email']: x for x in panel_members}
            confirmed_accounts = confirm_acc['confirmed_accounts']
            auto_confirm_date = confirm_acc['accounts_confirmed_by_date'] + datetime.timedelta(days=1)

            if confirmed_accounts:
                # One or more AMs have confirmed accounts
                # List them all by PM
                # seen_panel_members = set()
                for acc_manager_id, details  in confirmed_accounts.items():
                    confirmed_date = details['confirm_date'].replace(microsecond=0).strftime('%Y-%m-%d')
                    acct_manager = acc_managers_by_id.get(acc_manager_id, {'account_manager_id': acc_manager_id, 'account_manager_email': acc_manager_id})

                    for account in details['accounts']:
                        # seen_panel_members.add(panel_member['contact_email'].lower())
                        for survery_panel_manager in account['survey_panel_managers']:

                            row = {
                                'account_manager_id': acct_manager['account_manager_id'],
                                'account_manager_email': acct_manager['account_manager_email'],
                                'client_name': confirm_acc['client_name'],
                                'account_id': account['account_id'],
                                'account_name': account['account_name'],
                                'signatory_email': account['signatory_email'],
                                'signatory_name': account['signatory_name'],
                                'survey_name': confirm_acc['customer_survey_name'],
                                'panel_manager_name': survery_panel_manager['panel_manager_name'],
                                'panel_manager_email': survery_panel_manager['panel_manager_email'],
                                'panel_manager_id': survery_panel_manager['panel_manager_id'],
                                'account_confirmation_start_date': confirm_acc['accounts_confirmed_start_date'].strftime('%Y-%m-%d'),
                                'account_confirmation_end_date': confirm_acc['accounts_confirmed_by_date'].strftime('%Y-%m-%d'),
                                'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_acc['Id']}/view",
                                'confirmed_date': confirmed_date,
                                'auto_confirmed': False,
                                'in_round': account['in_round'],
                            }
                            csvout.writerow(row)

                # List any previous panel members that have not been confirmed and would therefore be excluded
                # for panel_member in panel_members:
                #     if panel_member['contact_email'].lower() in seen_panel_members:
                #         continue
                #     seen_panel_members.add(panel_member['contact_email'].lower())
                #     row = {
                #         'panel_manager_name': '',
                #         'panel_manager_email': '',
                #         'client_name': confirm_panel['client_name'],
                #         'account_name': confirm_panel['account_name'],
                #         'panel_confirmation_start_date': confirm_panel['panel_confirmed_start_date'].strftime('%Y-%m-%d'),
                #         'panel_confirmation_end_date': confirm_panel['panel_confirmed_by_date'].strftime('%Y-%m-%d'),
                #         'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                #         'panel_member_name': panel_member['contact_name'],
                #         'panel_member_email': panel_member['contact_email'],
                #         'confirmed_date': confirmed_date,
                #         'was_in_last_round': panel_members_by_email.get(panel_member['contact_email'], {}).get('in_round', False),
                #         'auto_confirmed': False,
                #         'in_round': False,
                #     }
                #     csvout.writerow(row)
            else:
                # No-one confirmed yet, would auto-confirm all if it stays that way by panel close date
                pass
                # for panel_member in panel_members:
                #     row = {
                #         'panel_manager_name': '',
                #         'panel_manager_email': '',
                #         'client_name': confirm_panel['client_name'],
                #         'account_name': confirm_panel['account_name'],
                #         'panel_confirmation_start_date': confirm_panel['panel_confirmed_start_date'].strftime('%Y-%m-%d'),
                #         'panel_confirmation_end_date': confirm_panel['panel_confirmed_by_date'].strftime('%Y-%m-%d'),
                #         'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                #         'panel_member_name': panel_member['contact_name'],
                #         'panel_member_email': panel_member['contact_email'],
                #         'confirmed_date': auto_confirm_date.strftime('%Y-%m-%d'),
                #         'was_in_last_round': panel_member['in_round'],
                #         'auto_confirmed': True,
                #         'in_round': panel_member['in_round'],
                #     }
                #     csvout.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()
    main()
