import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport
import os
import copy
from sftools.importsurveycsv import load_customer_client_name_mapping


def import_csv(accounts_filename, importtype, safeemails, writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    if importtype == 'advertising':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_ADVERTISING_STAFF
        organisational_levels = sfimport.ORGANISATION_LEVELS_ADVERTISING
        market_column_name = 'market'
        office_market_column_name = 'office'
    elif importtype == 'manufacturing':
        crc_customer_record_type = sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING
        organisational_level_fields = sfimport.ORGANISATION_LEVEL_FIELDS_MANUFACTURING_STAFF
        organisational_levels = sfimport.ORGANISATION_LEVELS_MANUFACTURING
        market_column_name = 'country market'
        office_market_column_name = 'site'
    else:
        raise Exception(f'Invalid import type {importtype}')
    customer_client_name_mapping = load_customer_client_name_mapping()

    agency_dev_prefix = os.environ.get('AGENCY_DEV_PREFIX')
    if agency_dev_prefix:
        agency_dev_prefix = agency_dev_prefix.strip()

    # populate cache
    print("Populating cache")
    for objname in ['Account', 'Contact', 'Market', 'ContactKeyAccount', 'ContactKeyMarket', 'ContactKeyCustomer']:
        sfobject = getattr(sfapi, objname)
        sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfobject, bulk=False))

    # find all the markets in salesforce
    all_markets_by_name_and_type: dict[str, sfapi.Market] = {}
    all_markets_by_id: dict[str, sfapi.Market] = {}
    for market in sfimport.sfcache.values():
        if not isinstance(market, sfapi.Market):
            continue
        all_markets_by_name_and_type[f'{market.Name}-{market.MarketType}'] = market
        all_markets_by_id[market.Id] = market

    # find all the contacts in salesforce
    all_contacts_by_id: dict[str, sfapi.Contact] = {}
    for contact in sfapi.get_all(sf, sfapi.Contact):
        if not isinstance(contact, sfapi.Contact):
            continue
        all_contacts_by_id[contact.Id] = contact

    # find all the customer client accounts as lists by name
    customers_client_accounts_by_name: dict[str, sfapi.Account] = {}
    for account in sfimport.sfcache.values():
        if not isinstance(account, sfapi.Account):
            continue
        if account.RecordTypeId != accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT].Id:
            continue
        customers_client_accounts_by_name.setdefault(account.Name, []).append(account)

    contact_report_view_bits = {}
    for v in sfimport.sfcache.values():
        if isinstance(v, sfapi.ContactKeyAccount):
            contact_report_view_bits.setdefault(v.ContactReportViewId, []).append(v.Id)
        elif isinstance(v, sfapi.ContactKeyMarket):
            contact_report_view_bits.setdefault(v.ContactReportViewId, []).append(v.Id)
        elif isinstance(v, sfapi.ContactKeyCustomer):
            contact_report_view_bits.setdefault(v.ContactReportViewId, []).append(v.Id)

    # special loading needed for ContactReportView
    for item in sfapi.get_all(sf, sfapi.ContactReportView):
        sfimport.sfid_mapping[item.Id] = item.Id
        contact = all_contacts_by_id[item.ContactId]
        bits = contact_report_view_bits.get(item.Id, [])

        business_key = sfapi.ContactReportView.business_key(contact, item.Role, bits)
        sfimport.sfcache[business_key] = item
        sfimport.sfid_mapping[item.Id] = item.Id
    print("Cache populated")

    # load data from the CSV file
    duffcount = 0
    with open(accounts_filename) as file:
        reader = csv.reader(file)

        # figure out the header row
        header_row_lookup = {}
        header_row = []
        for col_idx, v in enumerate(next(reader)):
            if not v:
                continue
            v = v.strip().lower()
            header_row.append(v)
            header_row_lookup[v] = col_idx

        # load all the rows into ram and convert them into dicts + enumerate them
        all_rows = []
        for i, row in enumerate(reader):
            outrow = {}
            emptycount = 0
            for k, colidx in header_row_lookup.items():
                if colidx >= len(row):
                    v = ''
                else:
                    v = row[colidx]

                outrow[k] = v
                if not v:
                    emptycount += 1

            if emptycount >= len(outrow):
                continue

            outrow['row_number'] = i + 1
            all_rows.append(outrow)

        # file to contain duff records
        duff_file = open('duffrecords-staff.csv', 'w')
        duff_header_row = copy.copy(header_row)
        for col in ['row_number', 'reason', 'customer_hierarchy', 'unsafe_email_address', 'sf_market', 'sf_office_market']:
            if col not in duff_header_row:
                duff_header_row.append(col)
        duff_writer = csv.DictWriter(duff_file, fieldnames=duff_header_row)
        duff_writer.writeheader()

        # preprocess each row and compute various things
        contact_customer_hierachies = {}
        for row in all_rows:
            row['reason'] = None

            # =======================================================
            # find the market if there is one
            market_name = row[market_column_name].strip()
            if market_name:
                sf_market = all_markets_by_name_and_type.get(f'{market_name}-Country')
                if not sf_market:
                    row['reason'] = f'UNKNOWN MARKET'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                if sf_market.MarketType != 'Country':
                    row['reason'] = f'MARKETS MUST BE COUNTRIES'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
            else:
                sf_market = None

            # =======================================================
            # decode the office market information
            office_market_name = row[office_market_column_name].strip()
            if office_market_name:
                office_market_name = sfimport.MARKET_MAP.get(f'{office_market_name}-City', office_market_name)

                sf_office_market = all_markets_by_name_and_type.get(f'{office_market_name}-City')
                if not sf_office_market:
                    row['reason']= 'INVALID OFFICE MARKET'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                if sf_office_market.MarketType != 'City':
                    row['reason']= f'INVALID OFFICE MARKET TYPE {sf_office_market.MarketType} (NOT CITY)'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
            else:
                sf_office_market = None

            if sf_office_market and not sf_market:
                row['reason'] = 'OFFICE MARKET WITHOUT A MARKET'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            # compute the account hierarchy with the correct names
            agency_account = None
            customer_hierarchy: list[sfapi.Account] = []
            had_agency_account_lookup_error = False
            for colname, organisational_level in organisational_level_fields:
                v = row[colname].strip()
                if v and agency_dev_prefix:
                    v = f'{agency_dev_prefix} {v}'
                if not v:
                    continue

                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(v, agency_account, accountRecordTypes[crc_customer_record_type]))
                if not agency_account:
                    row['reason'] = f'MISSING AGENCY ACCOUNT {v}'
                    duff_writer.writerow(row)
                    duffcount += 1
                    had_agency_account_lookup_error = True
                    break
                if agency_account.OrganisationalLevel != organisational_level:
                    row['reason'] = f'UNEXPECTED AGENCY ACCOUNT LEVEL {v} {organisational_level} != {agency_account.OrganisationalLevel}'
                    duff_writer.writerow(row)
                    duffcount += 1
                    had_agency_account_lookup_error = True
                    break

                customer_hierarchy.append(agency_account)
            if had_agency_account_lookup_error:
                continue

            if not customer_hierarchy:
                row['reason'] = 'NO AGENCY ACCOUNTS'
                duff_writer.writerow(row)
                duffcount += 1
                continue
            leaf_agency_name = customer_hierarchy[-1].Name

            # FIXME: not sure this is correct - what if we have a market value, but its not meant to be an agency with market?

            # make up the with market level
            if sf_market:
                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(f"{leaf_agency_name} {sf_market.Name}", agency_account, accountRecordTypes[crc_customer_record_type]))
                if agency_account:
                    customer_hierarchy.append(agency_account)

            # office level
            if sf_office_market:
                agency_account = sfimport.sfcache.get(sfapi.Account.business_key(f"{leaf_agency_name} {sf_office_market.Name}", agency_account, accountRecordTypes[crc_customer_record_type]))
                if agency_account:
                    customer_hierarchy.append(agency_account)

            # stash stuff for later
            row['sf_market'] = sf_market
            row['sf_office_market'] = sf_office_market
            row['customer_hierarchy'] = customer_hierarchy
            row['unsafe_email_address'] = unsafe_email_address = row['email address'].lower().strip().strip(';').strip(',')

            # get list of hierarchies for each contact/email. convert them into dicts so its easy to compare
            customer_hierarchy_dict = {x.OrganisationalLevel: x for x in customer_hierarchy}
            contact_customer_hierachies.setdefault(unsafe_email_address, []).append(customer_hierarchy_dict)

        # for each contact, figure out attachment point within the organisation hierarchy..
        # this is the lowest common level in the hierarchy
        contact_agency_account_by_unsafe_email = {}
        for unsafe_email_address, email_customer_hierachies in contact_customer_hierachies.items():
            contact_common_level = None
            for colname in organisational_levels:
                level_values = set([h.get(colname).Name if h.get(colname) else None for h in email_customer_hierachies])
                if len(level_values) == 1:
                    if level_values.pop() is not None:
                        contact_common_level = colname
                else:
                    break

            if not contact_common_level:
                print(unsafe_email_address, 'NO COMMON LEVEL')
                exit()
            contact_agency_account_by_unsafe_email[unsafe_email_address] = email_customer_hierachies[0][contact_common_level]

        # finally, its the main row processing loop!
        contacts_to_patch = {}
        for row in all_rows:
            # row already has an error of some sort
            if row.get('reason'):
                continue

            # =======================================================
            # contact details
            unsafe_email_address = row['unsafe_email_address']
            safe_email_address = sfimport.make_safe_email(unsafe_email_address)
            first_name = row['first name'].strip()
            last_name = row['last name'].strip()
            job_title = row['job title'].strip()

            # check contact details are valid
            if not first_name or not last_name or not unsafe_email_address:
                row['reason']= 'MISSING NAME OR EMAIL'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            is_market_lead = row['market lead (yes/no)'].strip().lower() == 'yes'
            is_account_lead = row['account lead (yes/no)'].strip().lower() == 'yes'
            is_global_lead = row['global lead (yes/no)'].strip().lower() == 'yes'
            is_regional_lead = row['regional lead (yes/no)'].strip().lower() == 'yes'

            region_market_name = row['regional lead region'].strip()
            if region_market_name:
                for market_type in {'Region', 'Business Region', 'Sub-Region', 'Key Region'}:
                    sf_region = all_markets_by_name_and_type.get(f'{region_market_name}-{market_type}')
                    if sf_region:
                        break
                if not sf_region:
                    row['reason'] = f'UNKNOWN/INVALID REGION'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
            else:
                sf_region = None

            # =======================================================
            # extract the market information
            sf_market = row['sf_market']

            if sf_market and sf_region:
                row['reason'] = f'MARKET AND REGION SET'
                duff_writer.writerow(row)
                duffcount += 1
                continue

            # =======================================================
            # get stuff from customer hierarchy
            customer_hierarchy = row['customer_hierarchy']
            contact_agency_account = contact_agency_account_by_unsafe_email[unsafe_email_address]
            agency_account = customer_hierarchy[-1]

            # =======================================================
            # find the relevant customer client account
            customer_client_account_name = row['account'].strip()
            if customer_client_account_name:
                customer_client_account_name = customer_client_name_mapping.get(customer_client_account_name, {customer_client_account_name})
                customer_client_account_name = list(customer_client_account_name)[0]

                customer_client_account = customers_client_accounts_by_name.get(customer_client_account_name)
                if not customer_client_account:
                    row['reason']= 'UNKNOWN CUSTOMER CLIENT ACCOUNT'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                if len(customer_client_account) > 1:
                    row['reason']= 'MULTIPLE CUSTOMER CLIENT ACCOUNTS'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                customer_client_account = customer_client_account[0]
            else:
                customer_client_account = None

            # =======================================================
            # create contact and attach to contact's agency account
            contact_email = safe_email_address if safeemails else unsafe_email_address
            contact = sfimport.get_or_create_cached_contact(contact_agency_account,
                                                            first_name,
                                                            last_name,
                                                            contact_email,
                                                            job_title=job_title)
            # patch the contact and mark as account manager
            if not contact.Id.startswith('Contact') and not contact.InsightsViewer:
                contacts_to_patch[contact.Id] = {'Id': contact.Id, 'Insights_Viewer__c': True}
            contact.InsightsViewer = True

            # now set up contact report views
            ckr_ids = []
            if contact_agency_account != agency_account:
                key_customer = agency_account
                ckr_ids.append(key_customer.Id)
            else:
                key_customer = None

            if is_global_lead:
                ckr = sfimport.get_or_create_cached_contact_report_view(contact, 'Enterprise', ckr_ids)
                if key_customer:
                    sfimport.get_or_create_cached_contact_key_customer(ckr, key_customer)

            if is_market_lead:
                if sf_market is None:
                    row['reason'] = 'MARKET LEAD WITHOUT A MARKET'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                ckr_ids.append(sf_market.Id)
                ckr = sfimport.get_or_create_cached_contact_report_view(contact, 'Market', ckr_ids)

                sfimport.get_or_create_cached_contact_key_market(ckr, sf_market)
                if key_customer:
                    sfimport.get_or_create_cached_contact_key_customer(ckr, key_customer)

            if is_account_lead:
                if not customer_client_account:
                    row['reason']= 'ACCOUNT LEAD WITHOUT AN ACCOUNT'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue

                ckr_ids.append(customer_client_account.Id)
                if sf_market:
                    ckr_ids.append(sf_market.Id)
                if sf_region:
                    ckr_ids.append(sf_region.Id)
                ckr = sfimport.get_or_create_cached_contact_report_view(contact, 'Account', ckr_ids)
                sfimport.get_or_create_cached_contact_key_account(ckr, customer_client_account)
                if sf_market:
                    sfimport.get_or_create_cached_contact_key_market(ckr, sf_market)
                if sf_region:
                    sfimport.get_or_create_cached_contact_key_market(ckr, sf_region)
                if key_customer:
                    sfimport.get_or_create_cached_contact_key_customer(ckr, key_customer)

            if is_regional_lead:
                if sf_region is None:
                    row['reason'] = 'REGIONAL LEAD WITHOUT A REGION'
                    duff_writer.writerow(row)
                    duffcount += 1
                    continue
                ckr_ids.append(sf_region.Id)
                ckr = sfimport.get_or_create_cached_contact_report_view(contact, 'Regional', ckr_ids)
                sfimport.get_or_create_cached_contact_key_market(ckr, sf_region)
                if key_customer:
                    sfimport.get_or_create_cached_contact_key_customer(ckr, key_customer)


    if writeit:
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        if contacts_to_patch:
            for status in sfapi.bulk_update(sf, "Contact", list(contacts_to_patch.values())):
                sfapi.detect_bulk2_errors(status)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_report_views, sfapi.ContactReportView)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_key_accounts, sfapi.ContactKeyAccount)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_key_markets, sfapi.ContactKeyMarket)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contact_key_customers, sfapi.ContactKeyCustomer)

    else:
        print("Dry run, not writing to Salesforce")
        print(len(sfimport.new_contacts), "new contacts")
        print(len(contacts_to_patch), "patched contacts")
        print(len(sfimport.new_contact_report_views), "new contact report views")
        print(len(sfimport.new_contact_key_accounts), "new contact key accounts")
        print(len(sfimport.new_contact_key_markets), "new contact key markets")
        print(len(sfimport.new_contact_key_customers), "new contact key customers")

    # clean up
    duff_file.flush()
    duff_file.close()
    if duffcount == 0:
        os.unlink('duffrecords-staff.csv')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--nosafeemails', action='store_false', default=True, dest='safeemails', help='Do not make email addresses safe')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    ap.add_argument('accounts_filename', help='CSV file to import')
    ap.add_argument('importtype', choices=['advertising', 'manufacturing'], help='Import type')
    args = ap.parse_args()

    if os.environ.get('STACK') in {'prod'} and args.safeemails:
        raise Exception("Refusing to run in production with safe emails")
    if os.environ.get('STACK') not in {'prod'} and not args.safeemails:
        raise Exception("Refusing to run without safe emails")

    import_csv(args.accounts_filename, args.importtype, args.safeemails, args.writeit)
