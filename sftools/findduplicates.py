from lib import sfapi
import argparse
from lib.settings import settings
import csv

OBJNAMES = sfapi.SFAPI_OBJECT_NAMES + sfapi.SFAPI_KEY_OBJECT_NAMES


def main(snakecase):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    duff_file = open('duffrecords-dupes.csv', 'w')
    duff_writer = csv.DictWriter(duff_file, fieldnames=['objname', 'bk', 'id1', 'id2'])
    duff_writer.writeheader()

    items = {}
    for objname in OBJNAMES:
        for item in sfapi.get_all(sf, getattr(sfapi, objname), bulk=True):
            if not hasattr(item, 'business_key2'):
                continue
            bk = item.business_key2()

            if bk in items:
                duffrow = {'objname': objname, 'bk': bk, 'id1': items[bk].Id, 'id2': item.Id}
                print(duffrow)
                duff_writer.writerow(duffrow)
            else:
                items[bk] = item


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    print(main(True))
