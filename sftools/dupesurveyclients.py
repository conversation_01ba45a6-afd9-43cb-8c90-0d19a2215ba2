from lib import sfapi
import argparse
from lib.settings import settings
import csv


def main(writeit: bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    dupes_file = open('dupes.csv', 'w')
    dupes_writer = csv.DictWriter(dupes_file, fieldnames=['key', 'keep_id', 'delete_id'])
    dupes_writer.writeheader()

    items = {}
    to_delete = set()
    for item in sfapi.get_all(sf, getattr(sfapi, 'SurveyClient'), bulk=True):
        if not hasattr(item, 'business_key2'):
            continue
        if not item.CurrentRound:
            continue
        
        key = item.business_key2()

        if key in items:
            row = {'key': key, 'keep_id': items[key].Id, 'delete_id': item.Id}
            dupes_writer.writerow(row)
            to_delete.add(item.Id)
        else:
            items[key] = item

    print(len(to_delete), 'duplicates found')

    if writeit:
        for record_status in sfapi.bulk_delete(sf, 'Survey_Client__c', list(to_delete)):
            for row in record_status['failedRecords']:
                print('Failed to delete', row)
            for row in record_status['unprocessedRecords']:
                print('Failed to delete', row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to SF")
    args = ap.parse_args()

    main(args.writeit)
