import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import csv
from lib import sfimport
import json


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    all_srs = {}
    for sr in sfapi.get_all(sf, sfapi.SurveyResponse, bulk=True):
        all_srs[sr.Id] = sr

    with open('csv/survey_response.csv') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            if row['Id'] not in all_srs:
                print('Missing SR:', row['Id'])
                continue


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
