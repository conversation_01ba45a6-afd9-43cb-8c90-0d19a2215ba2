import argparse
from lib import portaldbapi


def main(writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    # update the existing (old) confirmed_panel to confirmed_panel_archive so we still have a copy of it (for now)
    print('Updating confirmed_panel to confirmed_panel_archive')
    if writeit:
        result = collection.update_many(
            {},
            {
                "$rename": {
                    "confirmed_panel": "confirmed_panel_archive"
                }
            }
        )
        print("Matched count:", result.matched_count)
        print("Modified count:", result.modified_count)

    # update the new structure to be confirmed_panel
    print('Updating confirmed_panel_new to confirmed_panel')
    if writeit:
        result = collection.update_many(
            {}, 
            {
                "$rename": {
                    "confirmed_panel_new": "confirmed_panel"
                }
            }
        )
        print("Matched count:", result.matched_count)
        print("Modified count:", result.modified_count)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.writeit)