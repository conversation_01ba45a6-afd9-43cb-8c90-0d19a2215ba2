import argparse
from lib import portaldbapi


def main(writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    projection = {
        'Id': 1,
        'account_managers': 1,
        'confirmed_accounts': 1,
    }

    confirmed_accounts_result = collection.find({}, projection)

    for ca in confirmed_accounts_result:
        print(f'>> Processing confirmed account {ca["Id"]}')

        new_confirmed_account = []
        new_accounts = {}
        confirm_data = []
        confirmed_accounts_last_save_date = None
        confirmed_accounts_last_save_user_id = None
        confirmed_accounts_last_save_user_name = None
        confirmed_accounts_last_save_user_email = None
        
        # generate a lookup of account manager data by account manager id
        account_managers = {
            am['account_manager_id']: am
            for am in ca.get('account_managers', [])
        }

        # grab the existing confirmed accounts data
        confirmed_accounts = ca.get('confirmed_accounts', [])

        # iterate over the existing confirmed accounts data
        for account_manager_id, data in confirmed_accounts.items():
            # add confirm date to list (for this confirm account)
            confirm_data.append({
                'confirm_date': data['confirm_date'],
                'confirm_user_id': account_manager_id,
            })

            # iterate over accounts
            for account in data['accounts']:
                account_id = account['account_id']
                account_panel_managers = account.get('survey_panel_managers', [])

                # if account has already been added in new structure, merge the panel managers
                if account_id in new_accounts:
                    prior_account = new_accounts[account_id]
                    prior_panel_managers = prior_account['survey_panel_managers']
                    prior_panel_managers_ids = set([x['panel_manager_id'] for x in prior_account['survey_panel_managers']])
                    updated_panel_managers = False
                                               
                    for panel_manager in account_panel_managers:
                        panel_manager_id = panel_manager['panel_manager_id']
                        if panel_manager_id not in prior_panel_managers_ids:
                            updated_panel_managers = True
                            prior_panel_managers.append(panel_manager)

                    # update panel managers
                    if updated_panel_managers:
                        account['survey_panel_managers'] = prior_panel_managers

                # add account to new structure
                new_accounts[account_id] = account

        # flatten new_accounts
        new_accounts = list(new_accounts.values())

        # set confirm data 
        if len(confirm_data) > 0:
            # sort the confirm data so the most recent saved accounts is first
            confirm_data.sort(key=lambda d: d['confirm_date'], reverse=True)

            for cd in confirm_data:
                # grab the account data of the user
                confirm_user_data = account_managers.get(cd['confirm_user_id'], {})

                new_confirmed_account.append({
                    'accounts': [],
                    'confirm_date': cd['confirm_date'],
                    'confirm_user_id': cd['confirm_user_id'],
                    'confirm_user_email': confirm_user_data.get('account_manager_email'),
                    'confirm_user_name': confirm_user_data.get('account_manager_name'),
                })
            
            # assign the accounts to latest confirmation
            if len(new_confirmed_account) > 0:
                new_confirmed_account[0]['accounts'] = new_accounts
                confirmed_accounts_last_save_date = new_confirmed_account[0]['confirm_date']
                confirmed_accounts_last_save_user_id = new_confirmed_account[0]['confirm_user_id']
                confirmed_accounts_last_save_user_name = new_confirmed_account[0]['confirm_user_name']
                confirmed_accounts_last_save_user_email = new_confirmed_account[0]['confirm_user_email']

        # update mongo if we have accounts
        if writeit:
            collection.update_one(
                {'_id': ca['_id']},
                {
                    '$set': {
                        'confirmed_accounts_new': new_confirmed_account,
                        'confirmed_accounts_last_save_date': confirmed_accounts_last_save_date,
                        'confirmed_accounts_last_save_user_id': confirmed_accounts_last_save_user_id,
                        'confirmed_accounts_last_save_user_name': confirmed_accounts_last_save_user_name,
                        'confirmed_accounts_last_save_user_email': confirmed_accounts_last_save_user_email
                    }
                }
            )

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.writeit)