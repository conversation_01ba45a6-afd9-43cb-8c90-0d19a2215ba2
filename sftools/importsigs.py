import csv
import base64
import re
import os
import json
import argparse
import lib.sfapi as sfapi
from collections import namedtuple
from lib.settings import settings


Signature = namedtuple('Signature', ['signature_id', 'html', 'plain', 'stripped_from_name'])

def load_signatures():
    all_signatures = []
    for item in os.listdir('sigs'):
        if not item.endswith('.json'):
            continue

        with open(f'sigs/{item}') as f:
            sig = Signature(**json.load(f))
            all_signatures.append(sig)

    all_signatures = sorted(all_signatures, key=lambda x: x.signature_id, reverse=True)
    return all_signatures


def find_signature(all_signatures, contact_name, contact_email):
    contact_email = contact_email.lower()
    contact_name = contact_name.lower()
    stripped_contact_name = re.sub(r'[^a-z]', '', contact_name)

    result = []
    for sig in all_signatures:
        if contact_email in sig.html:
            result.append(sig)
        elif contact_name in sig.html:
            result.append(sig)
        elif stripped_contact_name == sig.stripped_from_name:
            result.append(sig)

    return result


def main(writeit: bool):
    all_signatures = load_signatures()

    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    CSVS = {}

    soql = """
    SELECT  Id,
            Email,
            FirstName,
            LastName,
            Signature__c,
            Account.Ultimate_Parent__r.Name,
            Account.Name
    FROM Contact
    WHERE Signatory__c = TRUE
"""
    seen_contact_ids = set()
    total_contact_count = 0
    matched_contact_count = 0
    for row in sfapi.bulk_query(sf, soql):
        if row['Id'] in seen_contact_ids:
            continue
        if row['Signature__c']:
            continue
        seen_contact_ids.add(row['Id'])
        ultimate_parent = row['Account.Ultimate_Parent__r.Name']
        if not ultimate_parent:
            ultimate_parent = row['Account.Name']

        contact_name = f"{row['FirstName']} {row['LastName']}"
        contact_email = row['Email']
        matched_sigs = find_signature(all_signatures, contact_name, contact_email)
        if matched_sigs:
            signature_filename = "\n".join([f"sigs/{sig.signature_id}.png" for sig in matched_sigs])
            matched_contact_count += 1
        else:
            signature_filename = ''

        if matched_sigs:
            image = open(f'sigs/{matched_sigs[0].signature_id}.png', 'rb').read()
            image = base64.b64encode(image).decode('utf-8')
            html = "<img src='data:image/png;base64," + image + '>'

            if writeit:
                sf.Contact.update(row['Id'], {'Signature__c': html})

        if ultimate_parent not in CSVS:
            f = open(f'matched_signatories_{ultimate_parent}.csv', 'w')
            csvout = csv.DictWriter(f, fieldnames=['signatory name', 'signatory email', 'signatory link', 'ultimate parent', 'match detected', 'detected filename'])
            csvout.writeheader()
            CSVS[ultimate_parent] = csvout

        total_contact_count += 1
        CSVS[ultimate_parent].writerow({
            'signatory name': contact_name,
            'signatory email': contact_email,
            'signatory link': f"https://clientrelationship.lightning.force.com/lightning/r/Contact/{row['Id']}/view",
            'match detected': 'yes' if signature_filename else '',
            'detected filename': signature_filename,
            'ultimate parent': ultimate_parent,
        })

    print(f"matched {(matched_contact_count / total_contact_count) * 100:.2f}% of contacts")
    print(matched_contact_count, total_contact_count)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False)
    args = ap.parse_args()

    main(args.writeit)
