from lib import sfapi, portaldbapi
import argparse
from lib.settings import settings
import csv
from bson import ObjectId
from pymongo import UpdateOne


def export_csv(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    batch = []
    for survey in survey_collection.find({'deleted': {'$ne': True}}):
    # for survey in survey_collection.find({'_id': ObjectId('679b4d0d2e7b19514ece5663')}):

        patches = {}

        for page_idx, page in enumerate(survey['config']['pages']):
            for element_idx, element in enumerate(page['elements']):
                if element.get('type') == 'rating' and not element.get('isRequired'):
                    patches[f'config.pages.{page_idx}.elements.{element_idx}.isRequired'] = True

        if patches:
            print(survey['_id'], patches)
            batch.append(UpdateOne({'_id': survey['_id']}, {'$set': patches}))

    if writeit:
        if batch:
            survey_collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    export_csv(args.writeit)
