import argparse
import csv
from lib import portaldbapi


query = {
    'account_id': {'$in': [
    "001WT000022PAwUYAW",
    "001WT000022PAwVYAW",
    "001WT000022PAwWYAW",
    "001WT000022OwaUYAS",
    "001WT000022PAwXYAW",
    "001WT000022OwaVYAS",
    "001WT000022OwaXYAS",
    "001WT000022PAwaYAG",
    "001WT000022PAfYYAW",
    "001WT000022PAfbYAG",
    "001WT000022PAfdYAG",
    "001WT000022PAfeYAG",
    "001WT000022PAhdYAG",
    "001WT000022PAhfYAG",
    "001WT000022PAyAYAW",
    "001WT000022OwbTYAS",
    "001WT000022PAhgYAG",
    "001WT000022PAhjYAG",
    "001WT000022<PERSON>hkYAG",
    "001WT000022PAisYAG",
    "001WT000022PAitYAG",
    "001WT000022PAixYAG",
    "001WT000022PAbXYAW",
    "001WT000022PAYKYA4",
    "001WT000022PAxHYAW",
    "001WT000022PAxOYAW",
    "001WT000022PAj0YAG",
    "001WT000022PAj1YAG",
    "001WT000022PAj2YAG",
    "001WT000022PAyLYAW",
    "001WT000022OwaTYAS",
    "001WT000022PAjyYAG",
    "001WT000022PAjzYAG",
    "001WT000022PAk0YAG",
    "001WT000022PAWsYAO",
    "001WT000022PAk1YAG",
    "001WT000022PAyMYAW",
    "001WT000022PAk3YAG",
    "001WT000022PAyNYAW",
    "001WT000022PAxxYAG",
    "001WT000022PAi6YAG",
    "001WT000022PAlLYAW",
    "001WT000022PAlMYAW",
    "001WT000022PAlNYAW",
    "001WT000022PAgwYAG",
    "001WT000022PAlRYAW",
    "001WT000022PAlSYAW",
    "001WT000022PAlUYAW",
    "001WT000022PAlVYAW",
    "001WT000022PAxZYAW",
    "001WT000022PAVLYA4",
    "001WT000022PAmUYAW",
    "001WT000022PAxzYAG",
    "001WT000022PAWNYA4",
    "001WT000022PAmWYAW",
    "001WT000022PAmXYAW",
    "001WT000022PAYqYAO",
    "001WT000022PAmZYAW",
    "001WT000022PAmdYAG",
    "001WT000022PAmeYAG",
    "001WT000022PAnOYAW",
    "001WT000022PAnQYAW",
    "001WT000022PAnSYAW",
    "001WT000022PAnTYAW",
    "001WT000022PAnaYAG",
    "001WT000022PAy2YAG",
    "001WT000022PAkQYAW",
    "001WT000022PAncYAG",
    "001WT000022PAXKYA4",
    "001WT000022PAnhYAG",
    "001WT000022PAhhYAG",
    "001WT000022PAnmYAG",
    "001WT000022PAyfYAG",
    "001WT000022PAoVYAW",
    "001WT000022PAoWYAW",
    "001WT000022PAoXYAW",
    "001WT000022PAoYYAW",
    "001WT000022PAoZYAW",
    "001WT000022PAoaYAG",
    "001WT000022PAobYAG",
    "001WT000022PAocYAG",
    "001WT000022PAodYAG",
    "001WT000022PAnLYAW",
    "001WT000022PAggYAG",
    "001WT000022PAoeYAG",
    "001WT000022PAofYAG",
    "001WT000022PAWZYA4",
    "001WT000022PAZSYA4",
    "001WT000023MjLVYA0",
    "001WT000022PAirYAG",
    "001WT000022PAhiYAG",
    "001WT000022PAizYAG",
    "001WT000022PAmaYAG",
    "001WT000022PAfWYAW",
    "001WT000022PAiuYAG",
    "001WT000022Owb5YAC",
    "001WT000022PAhlYAG",
    "001WT000022PAiyYAG",
    "001WT000023MjzpYAC",
    "001WT000022OwbYYAS",
    "001WT000022PAneYAG",
    "001WT000022PAiwYAG",
    "001WT000022PAfZYAW",
    "001WT000022OwabYAC",
    "001WT000022OwaaYAC",
    "001WT000022PAkzYAG",
    "001WT000024ZCPuYAO",
    "001WT000022PAlOYAW",
    "001WT000022PAlWYAW",
    "001WT000022PAlXYAW",
    "001WT000022PAlYYAW",
    "001WT000022PAmYYAW",
    "001WT000022PAVhYAO",
    "001WT000022PAmbYAG",
    "001WT000022PAnNYAW",
    "001WT000022PAnPYAW",
    "001WT000022PAnRYAW",
    "001WT000022PAnVYAW",
    "001WT000022PAnZYAW",
    "001WT000022PAnfYAG",
    "001WT000022PAniYAG",
    "001WT000022PAnjYAG",
    "001WT000022PAybYAG",
    "001WT000022PAf8YAG",
    "001WT000022PAlPYAW",
    "001WT000022PAlTYAW",
    "001WT000022PAnMYAW",
    "001WT000022PAngYAG",
    "001WT000022PAnkYAG",
    "001WT000022PAnlYAG",
    "001WT000022PAoUYAW",
    "001WT000022PAwYYAW",
    "001WT000022PAwZYAW",
    "001WT000022PAwbYAG",
    "001WT000022PAwcYAG",
    "001WT000022PAwdYAG",
    "001WT000022OwacYAC",
    "001WT000022OwadYAC",
    "001WT000022PAxwYAG",
    "001WT000022PAxyYAG",
    "001WT000022PAfXYAW",
    "001WT000022PAfaYAG",
    "001WT000022PAfcYAG",
    "001WT000022PAheYAG",
    "001WT000022OwazYAC",
    "001WT000022PAivYAG",
    "001WT000022PAi0YAG",
    "001WT000022PAlQYAW",
    "001WT000022PAyYYAW",
    "001WT000022PAmVYAW",
    "001WT000022PAmcYAG",
    "001WT000022PAhtYAG",
    "001WT000022PAnUYAW",
    "001WT000022PAnWYAW",
    "001WT000022PAnXYAW",
    "001WT000022PAnYYAW",
    "001WT000022PAnbYAG",
    "001WT000022PAndYAG",
    "001WT000022PAnnYAG",
    "001WT000022OwaMYAS",
    "001WT000022OwaNYAS",
    "001WT000022OwaOYAS",
    "001WT000022OwaPYAS",
    "001WT000022OwaQYAS",
    "001WT000022PAfVYAW",
    "001WT000022PAgeYAG",
    "001WT000022PAVoYAO",
    "001WT000022PAiqYAG",
    "001WT000022PAlJYAW",
    "001WT000022PAyUYAW",
    "001WT000022PAViYAO",
    "001WT000022PAlKYAW",
    "001WT000022PAnIYAW",
    "001WT000022PApMYAW",
    "001WT000022PAq3YAG",
    "001WT000022PAxIYAW",
    "001WT000022OwatYAC",
    "001WT000022PAxLYAW",
    "001WT000022PAfDYAW",
    "001WT000022pUV5YAM",
    "001WT000022PApbYAG",
    "001WT000022PAwfYAG",
    "001WT000022PAV4YAO",
    "001WT000022PAfgYAG",
    "001WT000022PAhTYAW",
    "001WT000022PAx2YAG",
    "001WT000022PAwmYAG",
    "001WT000022OwauYAC",
    "001WT000022Owb0YAC",
    "001WT000022Owb2YAC",
    "001WT000022Owb4YAC",
    "001WT000022Owb8YAC",
    "001WT000022OwbBYAS",
    "001WT000022OwbIYAS",
    "001WT000022OwbLYAS",
    "001WT000022OwbOYAS",
    "001WT000022OwbUYAS",
    "001WT000022OwbbYAC",
    "001WT000022OwbdYAC",
    "001WT000022OwbfYAC",
    "001WT000022OwbjYAC",
    "001WT000022PAV8YAO",
    "001WT000022PAV9YAO",
    "001WT000022PAVCYA4",
    "001WT000022PAVFYA4",
    "001WT000022PAVUYA4",
    "001WT000022PAVWYA4",
    "001WT000022PAVZYA4",
    "001WT000022PAVkYAO",
    "001WT000022PAVrYAO",
    "001WT000022PAW0YAO",
    "001WT000022PAW5YAO",
    "001WT000022PAW8YAO",
    "001WT000022PAWBYA4",
    "001WT000022PAWHYA4",
    "001WT000022PAxAYAW",
    "001WT000022PAWSYA4",
    "001WT000022PAWYYA4",
    "001WT000022PAWaYAO",
    "001WT000022PAWbYAO",
    "001WT000022PAWdYAO",
    "001WT000022PAWjYAO",
    "001WT000022PAWlYAO",
    "001WT000022PAffYAG",
    "001WT000022PAVGYA4",
    "001WT000022PAfsYAG",
    "001WT000022PAg5YAG",
    "001WT000022PAg6YAG",
    "001WT000022PAg9YAG",
    "001WT000022PAgAYAW",
    "001WT000022PAgBYAW",
    "001WT000022PAgDYAW",
    "001WT000022PAy8YAG",
    "001WT000022PAwnYAG",
    "001WT000022PAhYYAW",
    "001WT000022PAy9YAG",
    "001WT000022PAhaYAG",
    "001WT000022PAhrYAG",
    "001WT000022PAhDYAW",
    "001WT000022PAyGYAW",
    "001WT000022PAyJYAW",
    "001WT000022PAjsYAG",
    "001WT000022PAjtYAG",
    "001WT000022PAVfYAO",
    "001WT000022PAjvYAG",
    "001WT000022PAgCYAW",
    "001WT000022PAk7YAG",
    "001WT000022PAk8YAG",
    "001WT000022PAk9YAG",
    "001WT000022PAmPYAW",
    "001WT000022PAmRYAW",
    "001WT000022PAmSYAW",
    "001WT000022PAmTYAW",
    "001WT000022PAnJYAW",
    "001WT000022PAnKYAW",
    "001WT000022PAyeYAG",
    "001WT000022PApNYAW",
    "001WT000022PApOYAW",
    "001WT000022PApPYAW",
    "001WT000022PApQYAW",
    "001WT000022PAq4YAG",
    "001WT000022OwafYAC",
    "001WT000022PAWUYA4",
    "001WT000022PAx5YAG",
    "001WT000022PAxDYAW",
    "001WT000022PAwiYAG",
    "001WT000022PAwgYAG",
    "001WT000022PAy3YAG",
    "001WT000022PAxBYAW",
    "001WT000022PAwxYAG",
    "001WT0000236SiQYAU",
    "001WT0000233gm0YAA",
    "001WT0000233k2EYAQ",
    "001WT0000233eeJYAQ",
    "001WT0000233lsYYAQ",
    "001WT0000233pY7YAI",
    "001WT0000233gRTYAY",
    "001WT000022OwaiYAC",
    "001WT000022PAwhYAG",
    "001WT000022PAVNYA4",
    "001WT000022PAW4YAO",
    "001WT000022PAwlYAG",
    "001WT000022OwalYAC",
    "001WT000022OwaoYAC",
    "001WT000022OwayYAC",
    "001WT000022OwbPYAS",
    "001WT000022OwbQYAS",
    "001WT000022OwbXYAS",
    "001WT000022OwbeYAC",
    "001WT000022PAVEYA4",
    "001WT000022PAVHYA4",
    "001WT000022PAVTYA4",
    "001WT000022PAVaYAO",
    "001WT000022PAVbYAO",
    "001WT000022PAwzYAG",
    "001WT000022PAVtYAO",
    "001WT000022PAWLYA4",
    "001WT000022PAhPYAW",
    "001WT000022PAwoYAG",
    "001WT000022PAyBYAW",
    "001WT000022PAhnYAG",
    "001WT000022PAhpYAG",
    "001WT000022PAb4YAG",
    "001WT000022PAjHYAW",
    "001WT000022PAjrYAG",
    "001WT000022PAwsYAG",
    "001WT000022PAk6YAG",
    "001WT000022PAmQYAW",
    "001WT000022PAoSYAW",
    "001WT000022PAoTYAW",
    "001WT000022PAdTYAW",
    "001WT000022OwawYAC",
    "001WT000022PAwjYAG",
    "001WT000023n2XZYAY",
    "001WT000022PAwpYAG",
    "001WT000022PAY9YAO",
    "001WT000022PAx6YAG",
    "001WT000022OwaqYAC",
    "001WT000022PAVBYA4",
    "001WT000022PAVDYA4",
    "001WT000022PAVQYA4",
    "001WT000022PAVVYA4",
    "001WT000022PAW6YAO",
    "001WT000022Owb1YAC",
    "001WT000022PAwkYAG",
    "001WT000022OwarYAC",
    "001WT000022PAwqYAG",
    "001WT000022PAwuYAG",
    "001WT000022PAwwYAG",
    "001WT000022PAx1YAG",
    "001WT000022PAV5YAO",
    "001WT000022PAx3YAG",
    "001WT000022PAxEYAW",
    "001WT000022PAgqYAG",
    "001WT000022PAhUYAW",
    "001WT000022PAx0YAG",
    "001WT000023B8M8YAK",
    "001WT000022Owb3YAC",
    "001WT000022OwbFYAS",
    "001WT000022OwbWYAS",
    "001WT000022PAVgYAO",
    "001WT000022PAVjYAO",
    "001WT000022PAVmYAO",
    "001WT000022PAVpYAO",
    "001WT000022PAVzYAO",
    "001WT000022PAW1YAO",
    "001WT000022PAj5YAG",
    "001WT000022OwbEYAS",
    "001WT000022PAW2YAO",
    "001WT000022PAWGYA4",
    "001WT000022PAWXYA4",
    "001WT000022PAfxYAG",
    "001WT000022PAW7YAO",
    "001WT000022U8vwYAC",
    "001WT000022PAV3YAO",
    "001WT000022PAWPYA4",
    "001WT000022PAWTYA4",
    "001WT000022PAWVYA4",
    "001WT000022PAWcYAO",
    "001WT000022PAg1YAG",
    "001WT000022PAg2YAG",
    "001WT000022PAkAYAW",
    "001WT000022PApcYAG",
    "001WT000023B2sAYAS",
    "001WT000023BEi8YAG",
    "001WT000023BGOsYAO",
    "001WT000022PAx7YAG",
    "001WT000022PAWMYA4",
    "001WT000022PAWRYA4",
    "001WT000022PAWkYAO",
    "001WT000022PAWpYAO",
    "001WT000022PAhXYAW",
    "001WT000022PAhbYAG",
    "001WT000022PAWmYAO",
    "001WT000022PAjGYAW",
    "001WT000022PAkJYAW",
    "001WT000022OwaxYAC",
    "001WT000022PAwtYAG",
    "001WT000022PAVvYAO",
    "001WT000022PAhRYAW",
    "001WT000022PAy0YAG",
    "001WT0000233dweYAA",
    "001WT000022OwamYAC",
    "001WT000022OwbKYAS",
    "001WT000022PAx8YAG",
    "001WT000022PAg8YAG",
    "001WT000022PAhWYAW",
    "001WT000022nofsYAA",
    "001WT000022OwapYAC",
    "001WT000022OwbCYAS",
    "001WT000022OwbHYAS",
    "001WT000022OwbMYAS",
    "001WT000022OwbhYAC",
    "001WT000022OwbiYAC",
    "001WT000022PAVJYA4",
    "001WT000022PAVMYA4",
    "001WT000022PAVRYA4",
    "001WT000022PAVeYAO",
    "001WT000022PAVlYAO",
    "001WT000022PAVyYAO",
    "001WT000022PAWCYA4",
    "001WT000022PAWEYA4",
    "001WT000022PAWFYA4",
    "001WT000022PAWIYA4",
    "001WT000022PAWeYAO",
    "001WT000022PAWiYAO",
    "001WT000022PAWnYAO",
    "001WT000022PAWoYAO",
    "001WT000022PAfhYAG",
    "001WT000022PAfrYAG",
    "001WT000022PAftYAG",
    "001WT000022PAg3YAG",
    "001WT000022PAg4YAG",
    "001WT000022PAg7YAG",
    "001WT000022PAhQYAW",
    "001WT000022PAhVYAW",
    "001WT000022PAhmYAG",
    "001WT000022PAhoYAG",
    "001WT000022PAhqYAG",
    "001WT000022PAj6YAG",
    "001WT000022PAjDYAW",
    "001WT000022PAjEYAW",
    "001WT000022PAxJYAW",
    "001WT000022PAVIYA4",
    "001WT000022PAx9YAG",
    "001WT000022PAwyYAG",
    "001WT000022PAWJYA4",
    "001WT000022PAWKYA4",
    "001WT000022PFTBYA4",
    "001WT000022PFW7YAO",
    "001WT000022PAVOYA4",
    "001WT000022OwasYAC",
    "001WT000022PAV6YAO",
    "001WT000022PAVXYA4",
    "001WT000022PAVsYAO",
    "001WT000022PAVwYAO",
    "001WT000022PAWfYAO",
    "001WT000022PAZuYAO",
    "001WT000022OwbcYAC",
    "001WT000022PAWOYA4",
    "001WT000022PAhsYAG",
    "001WT000022OwbAYAS",
    "001WT000022OwbRYAS",
    "001WT000022PAVPYA4",
    "001WT000022OwbgYAC",
    "001WT0000233qiTYAQ",
    "001WT000022OwbGYAS",
    "001WT000022OwbNYAS",
    "001WT000022PAW3YAO",
    "001WT000022PAWWYA4",
    "001WT000022PAdwYAG",
    "001WT000022PAjuYAG",
    "001WT000022PAk5YAG",
    "001WT000022PAXgYAO",
    "001WT000022PAxFYAW",
    "001WT0000233mGmYAI",
    "001WT0000233g9mYAA",
    "001WT0000233iA2YAI",
    "001WT0000233hc8YAA",
    "001WT000022OwbJYAS",
    "001WT000022PAVqYAO",
    "001WT000022PAhcYAG",
    "001WT000022OwbkYAC",
    "001WT000022PAV7YAO",
    "001WT000022PAVuYAO",
    "001WT000022PAWgYAO",
    "001WT000022PAx4YAG",
    "001WT000022PAVYYA4",
    "001WT000022PAflYAG",
    "001WT000022PAVAYA4",
    "001WT000022PAVnYAO",
    "001WT000022PAW9YAO",
    "001WT000022PAwrYAG",
    "001WT000022Owb6YAC",
    "001WT000022OwbSYAS",
    "001WT000022PAVdYAO",
    "001WT000022PAxKYAW",
    "001WT000022PAXDYA4",
    "001WT000022PAXNYA4",
    "001WT000022PAWqYAO",
    "001WT000022PAY3YAO",
    "001WT000022PAWwYAO",
    "001WT000022PAXkYAO",
    "001WT000022PAa9YAG",
    "001WT000022PAcDYAW",
    "001WT000022PAX5YAO",
    "001WT000022PAXCYA4",
    "001WT000022PAYwYAO",
    "001WT000022PAgoYAG",
    "001WT000022PAgiYAG",
    "001WT000022PAj3YAG",
    "001WT000022PAeeYAG",
    "001WT000022PAjTYAW",
    "001WT000022PApUYAW",
    "001WT000022PAXLYA4",
    "001WT000022PAWyYAO",
    "001WT000022PAWzYAO",
    "001WT000022PAXGYA4",
    "001WT000022PAXUYA4",
    "001WT000022PAZOYA4",
    "001WT000022PAZRYA4",
    "001WT000022PAZYYA4",
    "001WT000022PAZhYAO",
    "001WT000022PAxYYAW",
    "001WT000022PAa7YAG",
    "001WT000022PAe0YAG",
    "001WT000022PAajYAG",
    "001WT000022PAhuYAG",
    "001WT000022PAhwYAG",
    "001WT000022PAknYAG",
    "001WT000022PAmOYAW",
    "001WT000022PAyaYAG",
    "001WT000022PAhAYAW",
    "001WT000022PAXpYAO",
    "001WT000022PApiYAG",
    "001WT000022PAiCYAW",
    "001WT000022gXwmYAE",
    "001WT000022PAepYAG",
    "001WT000022PApeYAG",
    "001WT000022PAxSYAW",
    "001WT000022PAxMYAW",
    "001WT000022PAxNYAW",
    "001WT000022PAXQYA4",
    "001WT000022PAatYAG",
    "001WT000022PAeJYAW",
    "001WT000022PAfqYAG",
    "001WT000022PAy5YAG",
    "001WT000022PAhMYAW",
    "001WT000022PAiYYAW",
    "001WT000022PAj4YAG",
    "001WT000022PAl0YAG",
    "001WT000022PAn2YAG",
    "001WT000022PAo3YAG",
    "001WT000022PAoqYAG",
    "001WT000022PAX6YAO",
    "001WT000022PAdDYAW",
    "001WT000022PAeOYAW",
    "001WT000022PAgNYAW",
    "001WT000022PAhCYAW",
    "001WT0000234Kf1YAE",
    "001WT000022PAYsYAO",
    "001WT000022PAbZYAW",
    "001WT000022PAcJYAW",
    "001WT000022PAccYAG",
    "001WT000022PAdOYAW",
    "001WT000022PAcmYAG",
    "001WT000022PAf5YAG",
    "001WT000022PAgWYAW",
    "001WT000022PAgzYAG",
    "001WT000022PAiDYAW",
    "001WT000022PAoOYAW",
    "001WT000022PAbKYAW",
    "001WT000022PAX8YAO",
    "001WT000022PAXSYA4",
    "001WT000022PAXbYAO",
    "001WT000022PAXeYAO",
    "001WT000022PAXqYAO",
    "001WT000022PAXsYAO",
    "001WT000022PAXuYAO",
    "001WT000022PAXxYAO",
    "001WT000022PAY0YAO",
    "001WT000022PAYTYA4",
    "001WT000022PAZ9YAO",
    "001WT000022PAZCYA4",
    "001WT000022PAapYAG",
    "001WT000022PAdbYAG",
    "001WT000022PAfjYAG",
    "001WT000022PAYAYA4",
    "001WT000022PAj8YAG",
    "001WT000022PAksYAG",
    "001WT000022PAl4YAG",
    "001WT000022PAlAYAW",
    "001WT000022PAyVYAW",
    "001WT000022PAljYAG",
    "001WT000022PAn4YAG",
    "001WT000022PAycYAG",
    "001WT000022PAoJYAW",
    "001WT000022PAdLYAW",
    "001WT000022PAX9YAO",
    "001WT000022PAxQYAW",
    "001WT000022PAYNYA4",
    "001WT000022PAxaYAG",
    "001WT000022PAxgYAG",
    "001WT000022PAZAYA4",
    "001WT000022PAZWYA4",
    "001WT000022PAZdYAO",
    "001WT000022PAaCYAW",
    "001WT000022PAbHYAW",
    "001WT000022PAbJYAW",
    "001WT000022PAYDYA4",
    "001WT000022PAbmYAG",
    "001WT000022PAeaYAG",
    "001WT000022PAhFYAW",
    "001WT000022PAm5YAG",
    "001WT000022PAaBYAW",
    "001WT000022PAbuYAG",
    "001WT000022PAc2YAG",
    "001WT000022PAdrYAG",
    "001WT000022PAe4YAG",
    "001WT000022gGxdYAE",
    "001WT000022PAYWYA4",
    "001WT000022PAa1YAG",
    "001WT000022PAaXYAW",
    "001WT000022PAbLYAW",
    "001WT000022PAxnYAG",
    "001WT000022PAdBYAW",
    "001WT000022PAdpYAG",
    "001WT000022PAdzYAG",
    "001WT000022PAeMYAW",
    "001WT000022PAecYAG",
    "001WT000022PAgpYAG",
    "001WT000022PAglYAG",
    "001WT000022PAjQYAW",
    "001WT000022PAjZYAW",
    "001WT000022PAoFYAW",
    "001WT000022PAplYAG",
    "001WT0000233epjYAA",
    "001WT0000234EuVYAU",
    "001WT000022PAZaYAO",
    "001WT000022PAbDYAW",
    "001WT000022PAc3YAG",
    "001WT000022PAc5YAG",
    "001WT000022PAebYAG",
    "001WT000022PAghYAG",
    "001WT000022PAh8YAG",
    "001WT000022PAhzYAG",
    "001WT000022PAjdYAG",
    "001WT000022PAilYAG",
    "001WT000022PAa5YAG",
    "001WT000022PAfzYAG",
    "001WT000022PAXJYA4",
    "001WT000022PAaGYAW",
    "001WT000022PAcKYAW",
    "001WT000022PAcnYAG",
    "001WT000022PAdHYAW",
    "001WT000022PAgrYAG",
    "001WT000022PAgGYAW",
    "001WT000022PAh9YAG",
    "001WT000022PAmBYAW",
    "001WT000022PAmKYAW",
    "001WT000022PAoDYAW",
    "001WT000022PAp0YAG",
    "001WT000022PApfYAG",
    "001WT0000234DnGYAU",
    "001WT000022PAaHYAW",
    "001WT000022PAc4YAG",
    "001WT000022PAcBYAW",
    "001WT000022PAaeYAG",
    "001WT000022PAcfYAG",
    "001WT000022PAd3YAG",
    "001WT000022PAdjYAG",
    "001WT000022PAfnYAG",
    "001WT000022PAiXYAW",
    "001WT000022PAh6YAG",
    "001WT000022PAjJYAW",
    "001WT000022PAl1YAG",
    "001WT000022PAlpYAG",
    "001WT000022PAmAYAW",
    "001WT000022PAfuYAG",
    "001WT000022PApaYAG",
    "001WT000022PAXZYA4",
    "001WT000022PAZPYA4",
    "001WT000022PAaAYAW",
    "001WT000022PAZLYA4",
    "001WT000022PAYkYAO",
    "001WT000022PAf3YAG",
    "001WT000022PAjBYAW",
    "001WT000022PAkrYAG",
    "001WT000022PAnqYAG",
    "001WT000022gO5pYAE",
    "001WT000022PAYFYA4",
    "001WT000022PAYcYAO",
    "001WT000022PAZUYA4",
    "001WT000022PAakYAG",
    "001WT000022PAcyYAG",
    "001WT000022PAdAYAW",
    "001WT000022PAdkYAG",
    "001WT000022PAeDYAW",
    "001WT000022PAeKYAW",
    "001WT000022PAefYAG",
    "001WT000022PAesYAG",
    "001WT000022PAiBYAW",
    "001WT000022PAhEYAW",
    "001WT000022PAjUYAW",
    "001WT000022PAlDYAW",
    "001WT000022PAloYAG",
    "001WT000022PAm7YAG",
    "001WT000022PAiOYAW",
    "001WT000022PAojYAG",
    "001WT000022PAorYAG",
    "001WT000022PAotYAG",
    "001WT000022PAouYAG",
    "001WT000022PAoxYAG",
    "001WT000022PAp4YAG",
    "001WT000022gbNaYAI",
    "001WT000022PAZtYAO",
    "001WT000022PAckYAG",
    "001WT000022PAg0YAG",
    "001WT000022PAjnYAG",
    "001WT000022PAY8YAO",
    "001WT000022PAYRYA4",
    "001WT000022PAaDYAW",
    "001WT000022PAbQYAW",
    "001WT000022PAc0YAG",
    "001WT000022PAcsYAG",
    "001WT0000234jjnYAA",
    "001WT000022PAYGYA4",
    "001WT000022PAZzYAO",
    "001WT000022PAaNYAW",
    "001WT000022PAbcYAG",
    "001WT000022PAaxYAG",
    "001WT000022PAdQYAW",
    "001WT000022PAdXYAW",
    "001WT000022PAdoYAG",
    "001WT000022PAahYAG",
    "001WT000022PAgHYAW",
    "001WT000022PAopYAG",
    "001WT0000233yBSYAY",
    "001WT0000234Ba3YAE",
    "001WT0000234FSLYA2",
    "001WT00002340y9YAA",
    "001WT000023rsU9YAI",
    "001WT000022PAZTYA4",
    "001WT000022PAcNYAW",
    "001WT000022PAYOYA4",
    "001WT000022PAbqYAG",
    "001WT000022PAYSYA4",
    "001WT000022PAYiYAO",
    "001WT000022PAZZYA4",
    "001WT000022PAeIYAW",
    "001WT000022PAieYAG",
    "001WT000022PAkxYAG",
    "001WT000022PAYUYA4",
    "001WT000022PAZoYAO",
    "001WT000022PAe5YAG",
    "001WT000022PAidYAG",
    "001WT000022PAjVYAW",
    "001WT000022PApWYAW",
    "001WT0000232VujYAE",
    "001WT0000232ZNHYA2",
    "001WT000022PAYeYAO",
    "001WT000022PAYmYAO",
    "001WT000022PAYvYAO",
    "001WT000022PAbdYAG",
    "001WT000022PApTYAW",
    "001WT000022PAYnYAO",
    "001WT000022PAauYAG",
    "001WT000022PAcRYAW",
    "001WT000022PAe3YAG",
    "001WT000022PAmJYAW",
    "001WT000022PAn6YAG",
    "001WT000022PAbnYAG",
    "001WT000022PAbbYAG",
    "001WT000022PAYdYAO",
    "001WT000022PAnEYAW",
    "001WT000022PAYoYAO",
    "001WT000022PAZIYA4",
    "001WT000022PAdcYAG",
    "001WT000022PAh0YAG",
    "001WT000022PAi1YAG",
    "001WT000022PAiPYAW",
    "001WT000022PAliYAG",
    "001WT000022PAohYAG",
    "001WT000022PAoiYAG",
    "001WT000022gapkYAA",
    "001WT0000231XC6YAM",
    "001WT000022PAxVYAW",
    "001WT000022PAenYAG",
    "001WT000022PAWrYAO",
    "001WT000022PAl6YAG",
    "001WT000022PAaoYAG",
    "001WT000022PAavYAG",
    "001WT000022PAb3YAG",
    "001WT000022PAbsYAG",
    "001WT000022PAd6YAG",
    "001WT000022PAeWYAW",
    "001WT000022PAegYAG",
    "001WT000022PAf4YAG",
    "001WT000022PAgyYAG",
    "001WT000022PAh1YAG",
    "001WT000022PAnDYAW",
    "001WT000022PApnYAG",
    "001WT000022PAb6YAG",
    "001WT000022PAbpYAG",
    "001WT000022PAeNYAW",
    "001WT000022PAeqYAG",
    "001WT0000233mF4YAI",
    "001WT0000233krlYAA",
    "001WT000022PAZrYAO",
    "001WT000022PAeSYAW",
    "001WT000022PAaMYAW",
    "001WT000022PAeEYAW",
    "001WT000022PAetYAG",
    "001WT000022PAlCYAW",
    "001WT000022PAmtYAG",
    "001WT000022PApdYAG",
    "001WT000022PApkYAG",
    "001WT000022PAbaYAG",
    "001WT000022PAeBYAW",
    "001WT000022PAltYAG",
    "001WT000022PAf0YAG",
    "001WT000022PAgPYAW",
    "001WT000022PAmxYAG",
    "001WT000022PAn9YAG",
    "001WT000022PAnAYAW",
    "001WT000022PAo1YAG",
    "001WT000022PAoPYAW",
    "001WT000022PAXOYA4",
    "001WT000022PAXRYA4",
    "001WT000022PAXlYAO",
    "001WT000022PAYYYA4",
    "001WT000022PAYtYAO",
    "001WT000022PAYyYAO",
    "001WT000022PAZFYA4",
    "001WT000022PAaFYAW",
    "001WT000022PAaIYAW",
    "001WT000022PAaPYAW",
    "001WT000022PAarYAG",
    "001WT000022PAbRYAW",
    "001WT000022PAcWYAW",
    "001WT000022PAdIYAW",
    "001WT000022PAfmYAG",
    "001WT000022PAgjYAG",
    "001WT000023BHsjYAG",
    "001WT000023BDPJYA4",
    "001WT000023B90sYAC",
    "001WT000023B4NtYAK",
    "001WT000023BI5UYAW",
    "001WT000022PAX4YAO",
    "001WT000022PAb9YAG",
    "001WT000022PAdPYAW",
    "001WT0000236HTJYA2",
    "001WT000022PAWtYAO",
    "001WT000022PAX3YAO",
    "001WT000022PAXIYA4",
    "001WT000022PAXVYA4",
    "001WT000022PAXXYA4",
    "001WT000022PAXaYAO",
    "001WT000022PAXiYAO",
    "001WT000022PAXnYAO",
    "001WT000022PAXrYAO",
    "001WT000022PAXvYAO",
    "001WT000022PAXwYAO",
    "001WT000022PAZ3YAO",
    "001WT000022PAZBYA4",
    "001WT000022PAY2YAO",
    "001WT000022PAZkYAO",
    "001WT000022PAZwYAO",
    "001WT000022PAaSYAW",
    "001WT000022PAaYYAW",
    "001WT000022PAbwYAG",
    "001WT000022PAgkYAG",
    "001WT000022PAgvYAG",
    "001WT000022PAihYAG",
    "001WT000022PAXPYA4",
    "001WT000022PAldYAG",
    "001WT000022PAnuYAG",
    "001WT0000233hIbYAI",
    "001WT000022PAXAYA4",
    "001WT000022PAYLYA4",
    "001WT000022PAZDYA4",
    "001WT000022PAZeYAO",
    "001WT000022PAaUYAW",
    "001WT000022PAawYAG",
    "001WT000022PAb2YAG",
    "001WT000022PAcUYAW",
    "001WT000022PAdJYAW",
    "001WT000022PAdKYAW",
    "001WT000022PAbeYAG",
    "001WT000022PAekYAG",
    "001WT000022PAgKYAW",
    "001WT000022PAiNYAW",
    "001WT000022PAjWYAW",
    "001WT000022PAmEYAW",
    "001WT000022PAeRYAW",
    "001WT000022PAoMYAW",
    "001WT000022PAosYAG",
    "001WT0000233pePYAQ",
    "001WT0000233il7YAA",
    "001WT0000233fs1YAA",
    "001WT000022PAX2YAO",
    "001WT000022PAXjYAO",
    "001WT000022PAXzYAO",
    "001WT000022PAaaYAG",
    "001WT000022PAYgYAO",
    "001WT000022PAhIYAW",
    "001WT000022PAkoYAG",
    "001WT000022PApSYAW",
    "001WT000022PAxTYAW",
    "001WT000022PAxUYAW",
    "001WT000022PAZ2YAO",
    "001WT000022PAxeYAG",
    "001WT000022PAxbYAG",
    "001WT000022PAxkYAG",
    "001WT000022PAxlYAG",
    "001WT000022PAxrYAG",
    "001WT000022PAy6YAG",
    "001WT000022PAyTYAW",
    "001WT000022PAyDYAW",
    "001WT000022PAZ5YAO",
    "001WT000022PAYEYA4",
    "001WT000022PAYuYAO",
    "001WT000022PAa2YAG",
    "001WT000022PAagYAG",
    "001WT000022PActYAG",
    "001WT000022PAcvYAG",
    "001WT000022PAdMYAW",
    "001WT000022PAe2YAG",
    "001WT000022PAe8YAG",
    "001WT000022PAewYAG",
    "001WT000022PAYJYA4",
    "001WT000022PAZGYA4",
    "001WT000022PAa4YAG",
    "001WT000022PAfpYAG",
    "001WT000022PAjqYAG",
    "001WT000022PAXtYAO",
    "001WT000022PAaOYAW",
    "001WT000022PAcIYAW",
    "001WT000022PAcZYAW",
    "001WT000022PAexYAG",
    "001WT000022PAkyYAG",
    "001WT000022PAXMYA4",
    "001WT000022PAXyYAO",
    "001WT000022PAYIYA4",
    "001WT000022PAYjYAO",
    "001WT000022PAYpYAO",
    "001WT000022PAZ6YAO",
    "001WT000022PAZjYAO",
    "001WT000022PAa6YAG",
    "001WT000022PAa8YAG",
    "001WT000022PAbkYAG",
    "001WT000022PAbtYAG",
    "001WT000022PAdCYAW",
    "001WT000022PAdFYAW",
    "001WT000022PAdyYAG",
    "001WT000022PAeHYAW",
    "001WT000022PAeUYAW",
    "001WT000022PAfkYAG",
    "001WT000022PAh2YAG",
    "001WT000022PAhBYAW",
    "001WT000022PAhyYAG",
    "001WT000022PAfSYAW",
    "001WT000022PAyIYAW",
    "001WT000022PAyXYAW",
    "001WT000022PAmwYAG",
    "001WT000022PAn8YAG",
    "001WT000022PAogYAG",
    "001WT000022PAonYAG",
    "001WT000022PAowYAG",
    "001WT000022PApRYAW",
    "001WT000022PApYYAW",
    "001WT000022PAYXYA4",
    "001WT000022PAZgYAO",
    "001WT000022PAZnYAO",
    "001WT000022PAZsYAO",
    "001WT000022PAaWYAW",
    "001WT000022PAaqYAG",
    "001WT000022PAY7YAO",
    "001WT000022PAcOYAW",
    "001WT000022PAd4YAG",
    "001WT000022PAd5YAG",
    "001WT000022PAd7YAG",
    "001WT000022PAdNYAW",
    "001WT000022PAdWYAW",
    "001WT000022PAd8YAG",
    "001WT000022PAfvYAG",
    "001WT000022PAZ0YAO",
    "001WT000022PAo4YAG",
    "001WT000022PAoEYAW",
    "001WT000022PAWvYAO",
    "001WT000022PAYBYA4",
    "001WT000022PAZyYAO",
    "001WT000022PAaJYAW",
    "001WT000022PAayYAG",
    "001WT000022PAl5YAG",
    "001WT000022gbozYAA",
    "001WT000022gRuZYAU",
    "001WT000022PAY1YAO",
    "001WT000022PAY5YAO",
    "001WT000022PAYzYAO",
    "001WT000022PAZ1YAO",
    "001WT000022PAZMYA4",
    "001WT000022PAaKYAW",
    "001WT000022PAgnYAG",
    "001WT000022PAjFYAW",
    "001WT000022PAm6YAG",
    "001WT000022PAn5YAG",
    "001WT000022PAa3YAG",
    "001WT000022PAb0YAG",
    "001WT000022PAb5YAG",
    "001WT000022PAbYYAW",
    "001WT000022PAcgYAG",
    "001WT000022PAchYAG",
    "001WT000022PAazYAG",
    "001WT000022PAePYAW",
    "001WT000022PAeQYAW",
    "001WT000022PAeoYAG",
    "001WT000022PAgQYAW",
    "001WT000022PAi7YAG",
    "001WT000022PAktYAG",
    "001WT000022PAlbYAG",
    "001WT000022PAasYAG",
    "001WT000022PAe9YAG",
    "001WT000022PAfwYAG",
    "001WT000022PAiZYAW",
    "001WT000022PAjKYAW",
    "001WT000022PAleYAG",
    "001WT000022PAlfYAG",
    "001WT000022gPXzYAM",
    "001WT000022PAmlYAG",
    "001WT000022PAYPYA4",
    "001WT000022PAcMYAW",
    "001WT000022PAgxYAG",
    "001WT000022PAkvYAG",
    "001WT000022PAmyYAG",
    "001WT000022PAXBYA4",
    "001WT000022PAYMYA4",
    "001WT000022PAZKYA4",
    "001WT000022PAbTYAW",
    "001WT000022PAcPYAW",
    "001WT000022PAXHYA4",
    "001WT000022PAiGYAW",
    "001WT000022PAjOYAW",
    "001WT000022PAo0YAG",
    "001WT000022PAXEYA4",
    "001WT000022PAXFYA4",
    "001WT000022PAYZYA4",
    "001WT000022PAZQYA4",
    "001WT000022PAbEYAW",
    "001WT000022PAxjYAG",
    "001WT000022PAeAYAW",
    "001WT000022PAxsYAG",
    "001WT000022PAgMYAW",
    "001WT000022PAYxYAO",
    "001WT000022PAZqYAO",
    "001WT000022PAaVYAW",
    "001WT000022PAafYAG",
    "001WT000022PAbyYAG",
    "001WT000022PAczYAG",
    "001WT000022PAmuYAG",
    "001WT000022PAmzYAG",
    "001WT000022PAomYAG",
    "001WT000022PAduYAG",
    "001WT000022PAy1YAG",
    "001WT000022PApgYAG",
    "001WT000022hTHbYAM",
    "001WT000022PAZXYA4",
    "001WT000022PAb1YAG",
    "001WT000022PAbIYAW",
    "001WT000022PAbVYAW",
    "001WT000022PAbhYAG",
    "001WT000022PAc8YAG",
    "001WT000022PAcLYAW",
    "001WT000022PAcSYAW",
    "001WT000022PAcTYAW",
    "001WT000022PAe1YAG",
    "001WT000022PAgFYAW",
    "001WT000022PAl3YAG",
    "001WT000022PAmHYAW",
    "001WT000022PAokYAG",
    "001WT000022PAYVYA4",
    "001WT000022PAbOYAW",
    "001WT000022PAc7YAG",
    "001WT000022PAdEYAW",
    "001WT000022PAe7YAG",
    "001WT000022PAYfYAO",
    "001WT000022PAejYAG",
    "001WT000022PApjYAG",
    "001WT000022PAZ4YAO",
    "001WT000022PAbBYAW",
    "001WT000022PAbiYAG",
    "001WT000022PAemYAG",
    "001WT000022PAgLYAW",
    "001WT000022PAnsYAG",
    "001WT000022PAXfYAO",
    "001WT000022PAZxYAO",
    "001WT000022PAaLYAW",
    "001WT000022PAaTYAW",
    "001WT000022PAbCYAW",
    "001WT000022PAcGYAW",
    "001WT000022PAcHYAW",
    "001WT000022PAdeYAG",
    "001WT000022PAeFYAW",
    "001WT000022PAeGYAW",
    "001WT000022PAeyYAG",
    "001WT000022PAiaYAG",
    "001WT000022PAdiYAG",
    "001WT000022PAjPYAW",
    "001WT000022PAmDYAW",
    "001WT000022gYsmYAE",
    "001WT000022PAXhYAO",
    "001WT000022PAxfYAG",
    "001WT000022PAbzYAG",
    "001WT000022PAfiYAG",
    "001WT000022PAgIYAW",
    "001WT000022PAgJYAW",
    "001WT000022PAinYAG",
    "001WT000022PAj7YAG",
    "001WT000022PAjLYAW",
    "001WT000022PAnvYAG",
    "001WT0000231CFyYAM",
    "001WT0000231TBnYAM",
    "001WT000022PAbFYAW",
    "001WT000022PAc6YAG",
    "001WT000022PAcjYAG",
    "001WT000022PAcoYAG",
    "001WT000022PAcxYAG",
    "001WT000022PAjmYAG",
    "001WT000022PAlFYAW",
    "001WT000022PAlHYAW",
    "001WT000022PAnCYAW",
    "001WT000022PAoGYAW",
    "001WT000022PApZYAW",
    "001WT000022gPOAYA2",
    "001WT000022gPuPYAU",
    "001WT000022PAcbYAG",
    "001WT000022PAddYAG",
    "001WT000022PAeXYAW",
    "001WT000022PAeYYAW",
    "001WT000022PAxdYAG",
    "001WT000022PAxmYAG",
    "001WT000022PAcXYAW",
    "001WT000022PAcYYAW",
    "001WT000022PAciYAG",
    "001WT000022PAcrYAG",
    "001WT000022PAeiYAG",
    "001WT000022PAgSYAW",
    "001WT000022PAh7YAG",
    "001WT000022PAkwYAG",
    "001WT0000233fJ6YAI",
    "001WT000022PAZVYA4",
    "001WT000022PAamYAG",
    "001WT000022PAanYAG",
    "001WT000022PAZfYAO",
    "001WT000022PAb7YAG",
    "001WT000022PAbWYAW",
    "001WT000022PAboYAG",
    "001WT000022PAceYAG",
    "001WT000022PAcuYAG",
    "001WT000022PAdgYAG",
    "001WT000022PAl7YAG",
    "001WT000022PAl9YAG",
    "001WT000022PAovYAG",
    "001WT000022PAZHYA4",
    "001WT000022PAaQYAW",
    "001WT000022PAc9YAG",
    "001WT000022PAcQYAW",
    "001WT000022PAcdYAG",
    "001WT000022PAclYAG",
    "001WT000022PAdtYAG",
    "001WT000022PAeZYAW",
    "001WT000022PAedYAG",
    "001WT000022PAgUYAW",
    "001WT000022PAgtYAG",
    "001WT000022PAiVYAW",
    "001WT000022PAjcYAG",
    "001WT000022PAmFYAW",
    "001WT000022PAmIYAW",
    "001WT000022PAoQYAW",
    "001WT000022PAp3YAG",
    "001WT000022PApoYAG",
    "001WT000022gc9zYAA",
    "001WT000022PAbGYAW",
    "001WT000022gXFBYA2",
    "001WT000022gUqyYAE",
    "001WT000022gPwEYAU",
    "001WT000022PAaiYAG",
    "001WT000022PAbUYAW",
    "001WT000022PAc1YAG",
    "001WT000022PAcpYAG",
    "001WT000022PAf2YAG",
    "001WT000022gVRwYAM",
    "001WT000022PAcEYAW",
    "001WT000022PAe6YAG",
    "001WT000022PAhHYAW",
    "001WT000022PAn7YAG",
    "001WT000022PAnGYAW",
    "001WT000022PAo5YAG",
    "001WT000022PAoIYAW",
    "001WT000022PAozYAG",
    "001WT000022gTDKYA2",
    "001WT000022PAacYAG",
    "001WT000022PAxhYAG",
    "001WT000022PAfoYAG",
    "001WT000022PAfyYAG",
    "001WT000022PAiFYAW",
    "001WT000022PAkpYAG",
    "001WT000022PAl2YAG",
    "001WT000022PAdvYAG",
    "001WT000022PAmrYAG",
    "001WT000022PAh5YAG",
    "001WT000022PAn0YAG",
    "001WT000022PAn1YAG",
    "001WT000022PAn3YAG",
    "001WT000022PApVYAW",
    "001WT000022PAYrYAO",
    "001WT000022PAZmYAO",
    "001WT000022PAZpYAO",
    "001WT000022PAbgYAG",
    "001WT000022PAYbYAO",
    "001WT000022PAYhYAO",
    "001WT000022PAZ7YAO",
    "001WT000022PAxcYAG",
    "001WT000022PAeTYAW",
    "001WT000022PAhvYAG",
    "001WT000022PAp2YAG",
    "001WT000022PFXCYA4",
    "001WT000022wupIYAQ",
    "001WT000022PAX0YAO",
    "001WT000022PAZlYAO",
    "001WT000022PAa0YAG",
    "001WT000022PAdUYAW",
    "001WT000022PAdhYAG",
    "001WT000022PAgOYAW",
    "001WT000022PAgfYAG",
    "001WT000022PAgmYAG",
    "001WT000022PAjeYAG",
    "001WT000022PAyZYAW",
    "001WT000022PAp1YAG",
    "001WT000022PAXTYA4",
    "001WT000022PAXYYA4",
    "001WT000022PAXoYAO",
    "001WT000022PAYCYA4",
    "001WT000022PAxRYAW",
    "001WT000022PAZJYA4",
    "001WT000022PAxWYAW",
    "001WT000022PAxXYAW",
    "001WT000022PAbAYAW",
    "001WT000022PAblYAG",
    "001WT000022PAxiYAG",
    "001WT000022PAcqYAG",
    "001WT000022PAxpYAG",
    "001WT000022PAxqYAG",
    "001WT000022PAd9YAG",
    "001WT000022PAdlYAG",
    "001WT000022PAeLYAW",
    "001WT000022PAevYAG",
    "001WT000022PAezYAG",
    "001WT000022PAf6YAG",
    "001WT000022PAgRYAW",
    "001WT000022PAgTYAW",
    "001WT000022PAgVYAW",
    "001WT000022PAhJYAW",
    "001WT000022PAhOYAW",
    "001WT000022PAibYAG",
    "001WT000022PAigYAG",
    "001WT000022PAmGYAW",
    "001WT000022PAyhYAG",
    "001WT000022hU7BYAU",
    "001WT0000234ixVYAQ",
    "001WT0000234mMoYAI",
    "001WT0000234oJdYAI",
    "001WT0000234od1YAA",
    "001WT0000234rfhYAA",
    "001WT000022PAbPYAW",
    "001WT000022PAbfYAG",
    "001WT000022PAj9YAG",
    "001WT000022PAjXYAW",
    "001WT000022PAygYAG",
    "001WT000022PAgsYAG",
    "001WT000022PAi2YAG",
    "001WT000022PAijYAG",
    "001WT000022PAmsYAG",
    "001WT000022PAnpYAG",
    "001WT000022PAo2YAG",
    "001WT000022PAiHYAW",
    "001WT000022PAiJYAW",
    "001WT000022PAntYAG",
    "001WT000022PAXmYAO",
    "001WT000022PAiLYAW",
    "001WT000022PAadYAG",
    "001WT000022PAalYAG",
    "001WT000022PAdSYAW",
    "001WT000022PAguYAG",
    "001WT000022PAi3YAG",
    "001WT000022PAZEYA4",
    "001WT000022PAbjYAG",
    "001WT000022wgftYAA",
    "001WT0000233lhJYAQ",
    "001WT000022PAhNYAW",
    "001WT000022PAyHYAW",
    "001WT000022PAjpYAG",
    "001WT000022PAnzYAG",
    "001WT000022PAo7YAG",
    "001WT000022PAoyYAG",
    "001WT000022PAd0YAG",
    "001WT000022PAeCYAW",
    "001WT000022PAh3YAG",
    "001WT000022PAh4YAG",
    "001WT000022PAhGYAW",
    "001WT000022PAhKYAW",
    "001WT000022PAhLYAW",
    "001WT000022PAjYYAW",
    "001WT000022PAjfYAG",
    "001WT000022PAjlYAG",
    "001WT000022PAjoYAG",
    "001WT000022PAlEYAW",
    "001WT000022PAlIYAW",
    "001WT000022PAm9YAG",
    "001WT000022PAnHYAW",
    "001WT000022PAoHYAW",
    "001WT000022PAoKYAW",
    "001WT000022PAoNYAW",
    "001WT000022PApmYAG",
    "001WT000022PAerYAG",
    "001WT000022PAyKYAW",
    "001WT000022PAyEYAW",
    "001WT000022PAicYAG",
    "001WT000022PAifYAG",
    "001WT000022PAimYAG",
    "001WT000022PAmCYAW",
    "001WT000022PAjNYAW",
    "001WT000022PAjhYAG",
    "001WT000022PAjiYAG",
    "001WT000022PAjjYAG",
    "001WT000022PAjkYAG",
    "001WT000022PAmLYAW",
    "001WT000022PAphYAG",
    "001WT000022PAiWYAW",
    "001WT000022PAX1YAO",
    "001WT000022PAxoYAG",
    "001WT000022PAY6YAO",
    "001WT000022PAaRYAW",
    "001WT000022PAbNYAW",
    "001WT000022PAcwYAG",
    "001WT000022PAd1YAG",
    "001WT000022PAd2YAG",
    "001WT000022PAdGYAW",
    "001WT000022PAdYYAW",
    "001WT000022PAdaYAG",
    "001WT000022PAdfYAG",
    "001WT000022PAdqYAG",
    "001WT000022PAeVYAW",
    "001WT000022PAehYAG",
    "001WT000022PAeuYAG",
    "001WT000022PAf1YAG",
    "001WT000022PAgEYAW",
    "001WT000022PAjbYAG",
    "001WT000022PAjgYAG",
    "001WT000022PAl8YAG",
    "001WT000022PAlGYAW",
    "001WT000022PAm8YAG",
    "001WT000022PAmMYAW",
    "001WT000022PAnBYAW",
    "001WT000022PAoLYAW",
    "001WT000022ikkvYAA",
    "001WT00002347bOYAQ",
    "001WT000022PAxPYAW",
    "001WT000022PAdZYAW",
    "001WT000022PAelYAG",
    "001WT000022PAmNYAW",
    "001WT000022PAXWYA4",
    "001WT000022PAYlYAO",
    "001WT000022PAZ8YAO",
    "001WT000022PAZcYAO",
    "001WT000022PAZvYAO",
    "001WT000022PAaZYAW",
    "001WT000022PAb8YAG",
    "001WT000022PAbMYAW",
    "001WT000022PAbSYAW",
    "001WT000022PAbvYAG",
    "001WT000022PAcAYAW",
    "001WT000022PAcFYAW",
    "001WT000022PAdRYAW",
    "001WT000022PAdVYAW",
    "001WT000022PAdmYAG",
    "001WT000022PAdnYAG",
    "001WT000022PAdsYAG",
    "001WT000022PAdxYAG",
    "001WT000022PAiiYAG",
    "001WT000022PAikYAG",
    "001WT000022PAjaYAG",
    "001WT000022PAkqYAG",
    "001WT000022PAkuYAG",
    "001WT000022PAnFYAW",
    "001WT000022PAo6YAG",
    "001WT000022PAolYAG",
    "001WT000022PAooYAG",
    "001WT000022PAp5YAG",
    "001WT000022PApXYAW",
    "001WT000022gMALYA2",
    "001WT000022PAlBYAW",
    "001WT000022PAfGYAW",
    "001WT000022PAfHYAW",
    "001WT000022PAfKYAW",
    "001WT000022PAfNYAW",
    "001WT000022PAgaYAG",
    "001WT000022PAgcYAG",
    "001WT000022PAiUYAW",
    "001WT000022PAjMYAW",
    "001WT000022PAkEYAW",
    "001WT000022PAkKYAW",
    "001WT000022PAyRYAW",
    "001WT000022PAlsYAG",
    "001WT000022PAlxYAG",
    "001WT000022PAm1YAG",
    "001WT000022PAmgYAG",
    "001WT000022PAmnYAG",
    "001WT000022PAp6YAG",
    "001WT000022PApGYAW",
    "001WT000022PApuYAG",
    "001WT000022PApwYAG",
    "001WT000022PAkWYAW",
    "001WT000022PAf7YAG",
    "001WT000022PAfEYAW",
    "001WT000022PAfPYAW",
    "001WT000022PAyFYAW",
    "001WT000022PAyOYAW",
    "001WT000022PAyQYAW",
    "001WT000022PAkRYAW",
    "001WT000022PAkdYAG",
    "001WT000022PAlwYAG",
    "001WT000022PAmkYAG",
    "001WT000022PAfIYAW",
    "001WT000022PAxuYAG",
    "001WT000022PAfQYAW",
    "001WT000022PAfRYAW",
    "001WT000022PAgXYAW",
    "001WT000022PAiMYAW",
    "001WT000022PAkLYAW",
    "001WT000022PAkMYAW",
    "001WT000022PAkSYAW",
    "001WT000022PAkTYAW",
    "001WT000022PAkbYAG",
    "001WT000022PAkgYAG",
    "001WT000022PAlzYAG",
    "001WT000022PAmjYAG",
    "001WT000022PAnyYAG",
    "001WT000022PApqYAG",
    "001WT000022PApxYAG",
    "001WT000022PApyYAG",
    "001WT000022PAfMYAW",
    "001WT000022PAiKYAW",
    "001WT000022PAkGYAW",
    "001WT000022PAkOYAW",
    "001WT000022PAkkYAG",
    "001WT000022PAlaYAG",
    "001WT000022PAlnYAG",
    "001WT000022PAi4YAG",
    "001WT000022PAnrYAG",
    "001WT000022PAnxYAG",
    "001WT000022PAf9YAG",
    "001WT000022PAfAYAW",
    "001WT000022PAfCYAW",
    "001WT000022PAfOYAW",
    "001WT000022PAxvYAG",
    "001WT000022PAfTYAW",
    "001WT000022PAfUYAW",
    "001WT000022PAgYYAW",
    "001WT000022PAgbYAG",
    "001WT000022PAy7YAG",
    "001WT000022PAhxYAG",
    "001WT000022PAiAYAW",
    "001WT000022PAiQYAW",
    "001WT000022PAiSYAW",
    "001WT000022PAiTYAW",
    "001WT000022PAjCYAW",
    "001WT000022PAjRYAW",
    "001WT000022PAjSYAW",
    "001WT000022PAkCYAW",
    "001WT000022PAkDYAW",
    "001WT000022PAyPYAW",
    "001WT000022PAkFYAW",
    "001WT000022PAkNYAW",
    "001WT000022PAkUYAW",
    "001WT000022PAkXYAW",
    "001WT000022PAkYYAW",
    "001WT000022PAkcYAG",
    "001WT000022PAkeYAG",
    "001WT000022PAkfYAG",
    "001WT000022PAkhYAG",
    "001WT000022PAkiYAG",
    "001WT000022PAkjYAG",
    "001WT000022PAySYAW",
    "001WT000022PAklYAG",
    "001WT000022PAkmYAG",
    "001WT000022PAlZYAW",
    "001WT000022PAlcYAG",
    "001WT000022PAlhYAG",
    "001WT000022PAllYAG",
    "001WT000022PAlmYAG",
    "001WT000022PAlrYAG",
    "001WT000022PAluYAG",
    "001WT000022PAlvYAG",
    "001WT000022PAlqYAG",
    "001WT000022PAm3YAG",
    "001WT000022PAm4YAG",
    "001WT000022PAmfYAG",
    "001WT000022PAmiYAG",
    "001WT000022PAmmYAG",
    "001WT000022PAnoYAG",
    "001WT000022PAnwYAG",
    "001WT000022PAo8YAG",
    "001WT000022PAo9YAG",
    "001WT000022PAoAYAW",
    "001WT000022PAoBYAW",
    "001WT000022PAoCYAW",
    "001WT000022PAp8YAG",
    "001WT000022PApAYAW",
    "001WT000022PApBYAW",
    "001WT000022PApHYAW",
    "001WT000022PApIYAW",
    "001WT000022PApJYAW",
    "001WT000022PApKYAW",
    "001WT000022PApLYAW",
    "001WT000022PAprYAG",
    "001WT000022PApsYAG",
    "001WT000022PApvYAG",
    "001WT000022PAyiYAG",
    "001WT000022PApzYAG",
    "001WT000022PAq0YAG",
    "001WT000022PAfFYAW",
    "001WT000022PAfLYAW",
    "001WT000022PAgZYAW",
    "001WT000022PAgdYAG",
    "001WT000022PAiRYAW",
    "001WT000022PAjIYAW",
    "001WT000022PAlgYAG",
    "001WT000022PAlyYAG",
    "001WT000022PAmoYAG",
    "001WT000022PAmpYAG",
    "001WT000022PAp9YAG",
    "001WT000022PAkHYAW",
    "001WT000022PAkZYAW",
    "001WT000022PAkaYAG",
    "001WT000022PApCYAW",
    "001WT000022PApEYAW",
    "001WT000022PAfBYAW",
    "001WT000022PAkIYAW",
    "001WT000022PAkPYAW",
    "001WT000022PAm0YAG",
    "001WT000022PAm2YAG",
    "001WT000022PAp7YAG",
    "001WT000022PApDYAW",
    "001WT000022PApFYAW",
    "001WT000022PAptYAG",
    "001WT000023IL50YAG",
    "001WT000022PAkVYAW",
    "001WT000022PAppYAG",
    "001WT000022PAi5YAG",
    "001WT000022PAi8YAG",
    "001WT000022PAi9YAG",
    "001WT000022PAiIYAW",
    "001WT000022PAioYAG",
    "001WT000022PAipYAG",
    "001WT000022PAjwYAG",
    "001WT000022PAjxYAG",
    "001WT000022PAkBYAW",
    "001WT000022PAlkYAG",
    "001WT000022PAmhYAG",
    "001WT000022PAmqYAG",
    "001WT000022PAoRYAW",
    "001WT000022PAydYAG",
    "001WT000022PAq1YAG",
    "001WT000022PAq2YAG"
]},
'client_id': {'$in': [
    "001WT000022OwaLYAS",
    "001WT000022PAwTYAW",
    "001WT000022OzMpYAK",
    "001WT000022P0sPYAS",
    "001WT000022P0sQYAS",
    "001WT000022PB3GYAW",
    "001WT000022PB3KYAW",
    "001WT000022PB3LYAW",
    "001WT000022mYLbYAM",
    "001WT000022P0sRYAS",
    "001WT000022mmBcYAI",
    "001WT000022OzMoYAK",
    "001WT000022nLfgYAE",
    "001WT000022nXlSYAU",
    "001WT000022o22xYAA",
    "001WT000022o540YAA",
    "001WT000022o3WtYAI",
    "001WT000022o2O4YAI",
    "001WT000022o74GYAQ",
    "001WT000022oBSjYAM",
    "001WT000022o7QmYAI",
    "001WT000022o7InYAI",
    "001WT000022o7YoYAI",
    "001WT000022o9sLYAQ",
    "001WT000022o7kGYAQ",
    "001WT000022oAJlYAM",
    "001WT000022o8GPYAY",
    "001WT000022o6XwYAI",
    "001WT000022oA21YAE",
    "001WT000022o6McYAI",
    "001WT000022oAbWYAU",
    "001WT000022o7KMYAY",
    "001WT000022oAdAYAU",
    "001WT000022o5IXYAY",
    "001WT000022oB2wYAE",
    "001WT000022o99CYAQ",
    "001WT000022oBULYA2",
    "001WT000022o9fUYAQ",
    "001WT000022oBe1YAE",
    "001WT000022o9CTYAY",
    "001WT000022oBirYAE",
    "001WT000022o6hgYAA",
    "001WT000022oBnhYAE",
    "001WT000022oAutYAE",
    "001WT000022PAweYAG",
    "001WT000022OzMqYAK",
    "001WT000022P0sSYAS",
    "001WT000022P0sTYAS",
    "001WT000022P0sXYAS",
    "001WT000022P0saYAC",
    "001WT000022P0sbYAC",
    "001WT000022Oz1qYAC",
    "001WT000022Oz1rYAC",
    "001WT000022P0sfYAC",
    "001WT000022P0sgYAC",
    "001WT000022P0shYAC",
    "001WT000022P0sjYAC",
    "001WT000022P0smYAC",
    "001WT000022P0soYAC",
    "001WT000022P0sqYAC",
    "001WT000022P0suYAC",
    "001WT000022P0svYAC",
    "001WT000022P0t3YAC",
    "001WT000022P0t5YAC",
    "001WT000022P0t6YAC",
    "001WT000022P0t9YAC",
    "001WT000022PB2vYAG",
    "001WT000022PB2wYAG",
    "001WT000022mxJmYAI",
    "001WT000022PB35YAG",
    "001WT000022mz0dYAA",
    "001WT000022P0t1YAC",
    "001WT000022mzjlYAA",
    "001WT000022P0slYAC",
    "001WT000022mzwfYAA",
    "001WT000022P0sUYAS",
    "001WT000022nheNYAQ",
    "001WT000022nawOYAQ",
    "001WT000022nxzJYAQ",
    "001WT000022nzZjYAI",
    "001WT000022OzMrYAK",
    "001WT000022P0sVYAS",
    "001WT000022P0sWYAS",
    "001WT000022P0seYAC",
    "001WT000022P0spYAC",
    "001WT000022P0t2YAC",
    "001WT000022f9jcYAA",
    "001WT000022fHkQYAU",
    "001WT000022mRbqYAE",
    "001WT000022P0t0YAC",
    "001WT000022OzMsYAK",
    "001WT000022P0sYYAS",
    "001WT000022P0sZYAS",
    "001WT000022P0siYAC",
    "001WT000022P0snYAC",
    "001WT000022P0ssYAC",
    "001WT000022P0swYAC",
    "001WT000022P0sxYAC",
    "001WT000022P0syYAC",
    "001WT000022P0t4YAC",
    "001WT000022P0t7YAC",
    "001WT000022PB2xYAG",
    "001WT000022nt2rYAA",
    "001WT000023s3FzYAI",
    "001WT000024E1ysYAC",
    "001WT000024E4q8YAC",
    "001WT000022OzMtYAK",
    "001WT000022P0scYAC",
    "001WT000022P0sdYAC",
    "001WT000022P0skYAC",
    "001WT000022P0srYAC",
    "001WT000022P0stYAC",
    "001WT000022P0szYAC",
    "001WT000022P0t8YAC",
    "001WT000022P0tAYAS",
    "001WT000022PB2yYAG",
    "001WT000022OzN9YAK",
    "001WT000022PB2nYAG",
    "001WT000022PB34YAG",
    "001WT000022nGeCYAU",
    "001WT000022n0KsYAI",
    "001WT000022nAKcYAM",
    "001WT000022nB0QYAU",
    "001WT000022nIeZYAU",
    "001WT000022nK4sYAE",
    "001WT000022nK74YAE",
    "001WT000022nRmaYAE",
    "001WT000022nUPTYA2",
    "001WT000022nWxRYAU",
    "001WT000022nX0gYAE",
    "001WT000022nXS5YAM",
    "001WT000022nrigYAA",
    "001WT000022njjiYAA",
    "001WT000022nl76YAA",
    "001WT000022np2dYAA",
    "001WT000022o0FoYAI",
    "001WT000022np2aYAA",
    "001WT000022nsIHYAY",
    "001WT000022ntqsYAA",
    "001WT000022nuyBYAQ",
    "001WT000022njjqYAA",
    "001WT000022nxg4YAA",
    "001WT000022nyqYYAQ",
    "001WT000022o1I9YAI",
    "001WT000022o1WeYAI",
    "001WT000022o1zdYAA",
    "001WT000022nsBjYAI",
    "001WT000022nysAYAQ",
    "001WT000022PAxGYAW",
    "001WT000022OzMuYAK",
    "001WT000022P0tBYAS",
    "001WT000022P0tDYAS",
    "001WT000022P0tHYAS",
    "001WT000022P0tQYAS",
    "001WT000022P0tRYAS",
    "001WT000022P0tSYAS",
    "001WT000022P0tVYAS",
    "001WT000022P0tWYAS",
    "001WT000022P0tcYAC",
    "001WT000022P0tdYAC",
    "001WT000022P0thYAC",
    "001WT000022P0tjYAC",
    "001WT000022P0tkYAC",
    "001WT000022P0tnYAC",
    "001WT000022P0tqYAC",
    "001WT000022PB1KYAW",
    "001WT000022PB1LYAW",
    "001WT000022PB1NYAW",
    "001WT000022PB1PYAW",
    "001WT000022PB1QYAW",
    "001WT000022PB1RYAW",
    "001WT000022PB1XYAW",
    "001WT000022PB1cYAG",
    "001WT000022PB1dYAG",
    "001WT000022PB1eYAG",
    "001WT000022PB1fYAG",
    "001WT000022PB1gYAG",
    "001WT000022PB1hYAG",
    "001WT000022PB1iYAG",
    "001WT000022PB1jYAG",
    "001WT000022PB1lYAG",
    "001WT000022PB1mYAG",
    "001WT000022PB1nYAG",
    "001WT000022PB1oYAG",
    "001WT000022PB1qYAG",
    "001WT000022PB1uYAG",
    "001WT000022PB20YAG",
    "001WT000022PB25YAG",
    "001WT000022PB29YAG",
    "001WT000022PB2BYAW",
    "001WT000022PB2FYAW",
    "001WT000022PB2HYAW",
    "001WT000022PB2JYAW",
    "001WT000022PB2MYAW",
    "001WT000022PB2PYAW",
    "001WT000022PB2QYAW",
    "001WT000022PB2RYAW",
    "001WT000022PB2VYAW",
    "001WT000022PB2XYAW",
    "001WT000022PB2YYAW",
    "001WT000022PB2ZYAW",
    "001WT000022PB2cYAG",
    "001WT000022PB2mYAG",
    "001WT000022PB2tYAG",
    "001WT000022PB38YAG",
    "001WT000022PB39YAG",
    "001WT000022PB3EYAW",
    "001WT000022PB3JYAW",
    "001WT000022fkYAYAY",
    "001WT000022fqbjYAA",
    "001WT000022fwh4YAA",
    "001WT000022gSR3YAM",
    "001WT000022n9RcYAI",
    "001WT000022nK5GYAU",
    "001WT000022nNSwYAM",
    "001WT000022OzMvYAK",
    "001WT000022P0tCYAS",
    "001WT000022P0tbYAC",
    "001WT000022P0toYAC",
    "001WT000022PB1pYAG",
    "001WT000022PB2DYAW",
    "001WT000022PB2TYAW",
    "001WT000022PB2rYAG",
    "001WT000022PB2sYAG",
    "001WT0000234lTpYAI",
    "001WT000022OzMwYAK",
    "001WT000022P0tEYAS",
    "001WT000022P0tGYAS",
    "001WT000022P0tIYAS",
    "001WT000022P0tJYAS",
    "001WT000022P0tKYAS",
    "001WT000022P0tLYAS",
    "001WT000022P0tMYAS",
    "001WT000022P0tNYAS",
    "001WT000022P0tOYAS",
    "001WT000022P0tPYAS",
    "001WT000022P0tUYAS",
    "001WT000022P0tXYAS",
    "001WT000022P0tZYAS",
    "001WT000022P0taYAC",
    "001WT000022P0teYAC",
    "001WT000022P0tfYAC",
    "001WT000022P0tgYAC",
    "001WT000022P0tiYAC",
    "001WT000022P0tlYAC",
    "001WT000022P0tmYAC",
    "001WT000022P0tpYAC",
    "001WT000022P0trYAC",
    "001WT000022P0tsYAC",
    "001WT000022PB1JYAW",
    "001WT000022PB1MYAW",
    "001WT000022PB1SYAW",
    "001WT000022PB1TYAW",
    "001WT000022PB1UYAW",
    "001WT000022PB1VYAW",
    "001WT000022PB1WYAW",
    "001WT000022PB1ZYAW",
    "001WT000022PB1aYAG",
    "001WT000022PB1bYAG",
    "001WT000022PB1kYAG",
    "001WT000022PB1sYAG",
    "001WT000022PB1wYAG",
    "001WT000022PB1xYAG",
    "001WT000022PB1zYAG",
    "001WT000022PB21YAG",
    "001WT000022PB22YAG",
    "001WT000022PB23YAG",
    "001WT000022PB24YAG",
    "001WT000022PB26YAG",
    "001WT000022PB27YAG",
    "001WT000022PB28YAG",
    "001WT000022PB2AYAW",
    "001WT000022PB2CYAW",
    "001WT000022PB2EYAW",
    "001WT000022PB2IYAW",
    "001WT000022PB2NYAW",
    "001WT000022PB2SYAW",
    "001WT000022PB2WYAW",
    "001WT000022PB2aYAG",
    "001WT000022PB2bYAG",
    "001WT000022PB2fYAG",
    "001WT000022PB2oYAG",
    "001WT000022PB31YAG",
    "001WT000022PB32YAG",
    "001WT000022PB33YAG",
    "001WT000022PB36YAG",
    "001WT000022PB3IYAW",
    "001WT000022fqVYYAY",
    "001WT000022fsgnYAA",
    "001WT000022fv1qYAA",
    "001WT000022mjTsYAI",
    "001WT000022P0tFYAS",
    "001WT000022myFqYAI",
    "001WT000022PB1tYAG",
    "001WT000022n1nBYAQ",
    "001WT000022PB1vYAG",
    "001WT000022OzMxYAK",
    "001WT000022P0tTYAS",
    "001WT000022P0tYYAS",
    "001WT000022PB1OYAW",
    "001WT000022PB2LYAW",
    "001WT000022PB2dYAG",
    "001WT000022PB2pYAG",
    "001WT000022PB2qYAG",
    "001WT000022PB30YAG",
    "001WT000022PB3MYAW",
    "001WT000022OzN2YAK",
    "001WT000022PB1YYAW",
    "001WT000022PB1rYAG",
    "001WT000022PB1yYAG",
    "001WT000022PB2OYAW",
    "001WT000022PB3HYAW",
    "001WT000022OzN5YAK",
    "001WT000022PB2GYAW",
    "001WT000022PB2uYAG",
    "001WT000022OzN6YAK",
    "001WT000022PB2KYAW",
    "001WT000022PB2eYAG",
    "001WT000022OzNCYA0",
    "001WT000022PB2zYAG",
    "001WT000022PB37YAG",
    "001WT000022PB3AYAW",
    "001WT000022PB3FYAW",
    "001WT000022PB3NYAW",
    "001WT000022mfyOYAQ",
    "001WT000022OzN1YAK",
    "001WT000022miCzYAI",
    "001WT000022OzN4YAK",
    "001WT000022mib9YAA",
    "001WT000022OzNDYA0",
    "001WT000022mjwxYAA",
    "001WT000022OzMyYAK",
    "001WT000022mlaZYAQ",
    "001WT000022OzN3YAK",
    "001WT000022mn5yYAA",
    "001WT000022OzN0YAK",
    "001WT000022mrUUYAY",
    "001WT000022OzMzYAK",
    "001WT000022mwdwYAA",
    "001WT000022OzNEYA0",
    "001WT000022PAxtYAG",
    "001WT000022OzN7YAK",
    "001WT000022PB2gYAG",
    "001WT000022PB2hYAG",
    "001WT000022PB2iYAG",
    "001WT000022PB2jYAG",
    "001WT000022PB2kYAG",
    "001WT000022PB2lYAG",
    "001WT000022PB3BYAW",
    "001WT000022PB3CYAW",
    "001WT000022PB3DYAW",
    "001WT000022mbwKYAQ",
    "001WT000022OzNAYA0",
    "001WT000022nxMiYAI",
    "001WT000022nyVdYAI",
    "001WT000022o0Z2YAI",
    "001WT000022nzY6YAI",
    "001WT000022o1rbYAA",
    "001WT000022PAy4YAG",
    "001WT000022my2vYAA",
    "001WT000022OzN8YAK",
    "001WT000022nrsHYAQ",
    "001WT000022nvO0YAI",
    "001WT000022nwv7YAA",
    "001WT000022nSCqYAM",
    "001WT000022nj29YAA",
    "001WT000022nrIjYAI",
    "001WT000022nm6FYAQ",
    "001WT000022nxHhYAI",
    "001WT000022nr4DYAQ",
    "001WT000022o05yYAA",
    "001WT000022nrNaYAI",
    "001WT000022nmSwYAI",
    "001WT000022nu5JYAQ",
    "001WT000022PAyCYAW",
    "001WT000022mzQPYAY",
    "001WT000022OzNBYA0",
    "001WT000022nJSmYAM",
    "001WT000022nq8CYAQ",
    "001WT000022nwGvYAI",
    "001WT000022o3CIYAY",
    "001WT000022o9PQYAY",
    "001WT000022o4xoYAA",
    "001WT000022o6oMYAQ",
    "001WT000022oEs2YAE",
    "001WT000022o8MvYAI",
    "001WT000022oAq4YAE",
    "001WT000022nnVIYAY",
    "001WT000022oB1cYAE",
    "001WT000022oBVzYAM",
    "001WT000022oCIMYA2",
    "001WT000022oCezYAE",
    "001WT000022oDfrYAE",
    "001WT000022oDsoYAE",
    "001WT000022oDuNYAU",
    "001WT000022njq7YAA",
    "001WT000022oFbEYAU",
    "001WT000022o6pmYAA",
    "001WT000022oFbFYAU",
    "001WT000022oGNZYA2",
    "001WT000022nSXUYA2",
    "001WT000022nd6BYAQ",
    "001WT000022npgjYAA",
    "001WT000022nksoYAA",
    "001WT000022ntR6YAI",
    "001WT000022nlaZYAQ",
    "001WT000022nljaYAA",
    "001WT000022nmXiYAI",
    "001WT000022npQdYAI",
    "001WT000022nqD3YAI",
    "001WT000022nzI3YAI",
    "001WT000022nwDbYAI",
    "001WT000022nwdPYAQ",
    "001WT000022nxfyYAA",
    "001WT000022nxpbYAA",
    "001WT000022nz3RYAQ",
    "001WT000022nzg7YAA",
    "001WT000022o0InYAI",
    "001WT000022o3gUYAQ",
    "001WT000022o3IIYAY",
    "001WT000022pcVhYAI"
]},
'market_id': {'$in':[
    "a0aWT000003OKhuYAG",
    "a0aWT000003OKoHYAW",
    "a0aWT000003OKh1YAG",
    "a0aWT000003OKhyYAG",
    "a0aWT000003OKiAYAW",
    "a0aWT000003OKhnYAG",
    "a0aWT000003OKkIYAW",
    "a0aWT000003OKhrYAG",
    "a0aWT000003OKh3YAG",
    "a0aWT000003OKkRYAW",
    "a0aWT000003OKi9YAG",
    "a0aWT000003OKhjYAG",
    "a0aWT000003OKhtYAG",
    "a0aWT000003OKhoYAG",
    "a0aWT000003OKiBYAW",
    "a0aWT000003OKi8YAG",
    "a0aWT000003OKgnYAG",
    "a0aWT000003OKk6YAG",
    "a0aWT000003OKhvYAG",
    "a0aWT000003OKgkYAG",
    "a0aWT000003OKgzYAG",
    "a0aWT000003OKhpYAG",
    "a0aWT000003OKgmYAG",
    "a0aWT000003OKhJYAW",
    "a0aWT000003OKhxYAG",
    "a0aWT000003OKhKYAW",
    "a0aWT000003OKh7YAG",
    "a0aWT000003OKh4YAG",
    "a0aWT000003OKkDYAW",
    "a0aWT000003OKi7YAG",
    "a0aWT000003OKkNYAW",
    "a0aWT000003OKhqYAG",
    "a0aWT000003OKgtYAG",
    "a0aWT000003OKnvYAG",
    "a0aWT000003OKkOYAW",
    "a0aWT000003OKoFYAW",
    "a0aWT000003OKhwYAG",
    "a0aWT000003OKoIYAW",
    "a0aWT000003OKoGYAW",
    "a0aWT000003OKi3YAG",
    "a0aWT000003OKkMYAW",
    "a0aWT000003OKnwYAG",
    "a0aWT000003uxWjYAI",
    "a0aWT000003OKh6YAG",
    "a0aWT000003OKk9YAG",
    "a0aWT000003OKh5YAG",
    "a0aWT000003OKkQYAW",
    "a0aWT000003OKnsYAG",
    "a0aWT000003OKi2YAG",
    "a0aWT000003OKi4YAG",
    "a0aWT000003OKlSYAW",
    "a0aWT000003OKh2YAG",
    "a0aWT000003OKk8YAG",
    "a0aWT000003OKhMYAW",
    "a0aWT000003OKkKYAW",
    "a0aWT000003OKi0YAG",
    "a0aWT000003unkVYAQ",
    "a0aWT000003OKgiYAG",
    "a0aWT000003OKhAYAW",
    "a0aWT000003OKntYAG",
    "a0aWT000003OKhkYAG",
    "a0aWT000003OKk3YAG",
    "a0aWT000003OKk5YAG",
    "a0aWT000003OKheYAG",
    "a0aWT000003OKgxYAG",
    "a0aWT000003OKhZYAW",
    "a0aWT000003OKgrYAG",
    "a0aWT000003OKhaYAG",
    "a0aWT000003OKoDYAW",
    "a0aWT000003OKhGYAW",
    "a0aWT000003OKi1YAG",
    "a0aWT000003OKgjYAG",
    "a0aWT000003OKhlYAG"
]},
'round_id': 'a08WT000004scduYAA',
'holding_group_round_index': 0
}
projection = {'_id': 1}


def main():
    portaldb = portaldbapi.DocumentDB()
    responses_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_RESPONSES_COLLECTION]

    response_count = 0
    responses = set()

    for response in responses_collection.find(query, projection):
        response_count += 1
        responses.add(response['_id'])
    
    with open('responses_2.csv', mode='w', newline='') as file:
        writer = csv.writer(file)
    
        # Writing each string as a separate row
        for item in list(responses):
            writer.writerow([item])

    print(f'Found {response_count} responses')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()