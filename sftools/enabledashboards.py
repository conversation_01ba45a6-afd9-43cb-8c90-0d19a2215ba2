import argparse
import datetime
from pymongo import UpdateOne
from lib import portaldbapi


def main():
    portaldb = portaldbapi.DocumentDB()
    contacts_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CUSTOMER_CONTACTS_COLLECTION]
    features_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_FEATURES_COLLECTION]

    audit_date = datetime.datetime.now(tz=datetime.UTC)
    updated_count = 0
    batch = []

    for contact in contacts_collection.find({'insights_viewer': True}):
        contact_id = contact['Id']

        updated_count += 1
        batch.append(UpdateOne({'contact_id': contact_id}, {'$set': {'contact_id': contact_id, 'features.dashboards': True, 'audit.dashboards': audit_date}}, upsert=True)) 

    if batch:
        features_collection.bulk_write(batch)

    print(f'Updated {updated_count} contact features')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()