import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings
from lib import sfimport


def bulk_delete_batch_to_sf(sf, batch, sf_object_name):
    # don't bother if there's nothing to do
    if not batch:
        return

    for record_status in sfapi.bulk_delete(sf, sf_object_name, batch):
        for row in record_status['failedRecords']:
            print('Failed to delete', row)
        for row in record_status['unprocessedRecords']:
            print('Failed to delete', row)


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    ids = [item.Id for item in sfapi.get_all(sf, sfapi.Market)]
    bulk_delete_batch_to_sf(sf, ids, sfapi.Market.sfapi_name())

    with open(os.path.join('data', f"Market.csv"), 'r') as filein:
        csvin = csv.DictReader(filein)

        all_markets = []
        for row in csvin:
            all_markets.append(sfapi.Market(**row))

        while all_markets:
            market_batch = []
            for cur_market in all_markets:
                try:
                    cur_market.ParentMarketId = sfimport.sfid_mapping[cur_market.ParentMarketId]
                    market_batch.append(cur_market)
                    all_markets.remove(cur_market)
                except KeyError:
                    pass

            if not market_batch:
                raise Exception("Error: batch is empty")

            sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, market_batch, sfapi.Market)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
