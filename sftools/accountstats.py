import argparse
from pymongo import UpdateOne
from lib import portaldbapi
from pprint import pprint


def extract_account_ids(accounts):
    return set(account['account_id'] for account in accounts)


def main(customer_survey_id: str|None = None, writeit: bool = False):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    batch = []

    query = {}
    if customer_survey_id:
        query = {'Id': customer_survey_id}

    projection = {
        '_id': 1,
        'Id': 1,
        'client_name': 1,
        'confirmed_accounts': 1,
    }

    # get all confirm accounts from Mongo
    confirm_accounts = collection.find(query, projection)

    for ca in confirm_accounts:
        confirmed_accounts = ca.get('confirmed_accounts', [])

        # if this confirm accounts has no user confirmations, just skip it - we have nothing to do
        if len(confirmed_accounts) == 0:
            print(f'>> Skipping confirmed accounts {ca["Id"]} as it has no confirmed account data')
            continue

        for i in range(len(confirmed_accounts)):
            current_accounts = confirmed_accounts[i].get('accounts', [])
            current_ids = extract_account_ids(current_accounts)

            # Compare with previous accounts if exists
            if i + 1 < len(confirmed_accounts):
                previous_accounts = confirmed_accounts[i + 1].get('accounts', [])
                previous_ids = extract_account_ids(previous_accounts)

                added_ids = current_ids - previous_ids
                removed_ids = previous_ids - current_ids

                # Get full dicts for added and removed
                added = [
                    {'id': c['account_id'], 'name': c['account_name'], 'client': ca['client_name']}
                    for c in current_accounts 
                    if c['account_id'] in added_ids
                ]
                removed = [
                    {'id': c['account_id'], 'name': c['account_name'], 'client': ca['client_name']}
                    for c in previous_accounts 
                    if c['account_id'] in removed_ids
                ]
            else:
                # No previous panel to compare to
                added = [
                    {'id': c['account_id'], 'name': c['account_name'], 'client': ca['client_name']}
                    for c in current_accounts
                ]
                removed = []

            confirmed_accounts[i]['stats'] = {
                'added': added,
                'removed': removed
            }
        
        # add to batch for updating
        batch.append(UpdateOne(
            {'_id': ca['_id']}, 
            {'$set': {'confirmed_accounts': confirmed_accounts}}
        ))
    
    # write to Mongo
    if writeit:
        collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_id', default=None, help='single customer survey to run for')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.customer_survey_id, args.writeit)