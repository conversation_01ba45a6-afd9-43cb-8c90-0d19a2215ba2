import argparse
from enum import Enum
from pymongo import UpdateOne
from simple_salesforce import Salesforce
from typing import Optional
from lib import sfapi
from lib.settings import settings
from lib.portaldbapi import DocumentDB
from pprint import pprint
from api.clientarea.src.handlers.panel.get_panel_handler import ContactJobFunctionMfv, ContactJobFunction


# Convert enum values to a set for quick lookup
VALID_JOB_FUNCTIONS_ADV = {e.value for e in ContactJobFunction}
VALID_JOB_FUNCTIONS_MFV = {e.value for e in ContactJobFunctionMfv}


def fetch_customer_survey_round_ids(sf: Salesforce) -> list[str]:
    all_customer_survey_round_ids:set[str] = set()
    soql = '''
        WHERE Current_Round__c = True 
    '''
    for csr in sfapi.get_all(sf, sfapi.CustomerSurveyRound, bulk=True, query_suffix=soql):
        all_customer_survey_round_ids.add(csr.Id)

    return list(all_customer_survey_round_ids)


def fetch_confirm_panels(db: DocumentDB, customer_survey_round_ids:list[str], confirm_panel_id:Optional[str] = None) -> list[str]:
    confirm_panels:list = []

    query:dict = {
        'customer_survey_round_id': {'$in': customer_survey_round_ids},
        'sfsync_date': None
    }
    projection:dict = {
        'Id': 1,
        'confirmed_panel': 1,
        'survey_name': 1,
    }

    if confirm_panel_id:
        query['Id'] = confirm_panel_id

    for cp in db.confirmpanel.find(query, projection):
        confirm_panels.append(cp)

    return confirm_panels


def main(confirm_panel_id:Optional[str] = None, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:DocumentDB = DocumentDB()

    batch:list[UpdateOne] = []

    # get the customer survey round ids for the current round
    customer_survey_round_ids:list[str] = fetch_customer_survey_round_ids(sf)

    # get all confirmpanel records for the current round
    confirm_panel_records:list[dict] = fetch_confirm_panels(db, customer_survey_round_ids, confirm_panel_id)
    
    for cp in confirm_panel_records:
        updated = False
        valid_job_functions = VALID_JOB_FUNCTIONS_ADV
        survey_name = cp.get('survey_name', '')
        if survey_name == 'Johnson Matthey':
            # TODO how to determine vertical?
            valid_job_functions = VALID_JOB_FUNCTIONS_MFV

        confirmed_panel = cp.get('confirmed_panel', [])
        if len(confirmed_panel) > 0:
            confirmed = confirmed_panel[0]

            for contact in confirmed.get('panel', []):
                contact_id = contact.get('contact_id')
                contact_name = contact.get('contact_name')
                job_title = contact.get('contact_job_title', '')
                job_function = contact.get('contact_job_function', '')
                if job_function == '' or job_function is None:
                    continue
                if job_function in valid_job_functions:
                    continue

                print(f"{survey_name} // {cp.get('Id')} // {contact_id} // {contact_name} // {job_title} // {job_function} // invalid")

                if (not job_title or job_title in valid_job_functions) and job_function not in valid_job_functions:
                    print('Changed to:')
                    contact['contact_job_title'] = job_function
                    contact['contact_job_function'] = job_title
                    # print(f'{contact['contact_job_title']} // {contact['contact_job_function']}')
                    print(f"{survey_name} // {cp.get('Id')} // {contact_id} // {contact_name} // {contact['contact_job_title']} // {contact['contact_job_function']}")
                    updated = True
                print('========')

        #pprint(cp['confirmed_panel'])
        
        if updated:
            batch.append(
                UpdateOne({
                    '_id': cp['_id']
                }, 
                {'$set': {
                    'confirmed_panel': cp['confirmed_panel']
                }
            }))

    if batch and writeit:
        db.confirmpanel.bulk_write(batch)
                    
    print(f"Updated {len(batch)} confirm panel docs")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--confirm_panel_id", type=str, help="The ID of the confirm panel to update")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.confirm_panel_id, args.writeit)
