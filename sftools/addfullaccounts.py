import argparse
from pymongo import UpdateOne
from lib import portaldbapi


def main(customer_survey_id: str|None = None, writeit: bool = False):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    batch = []

    query = {}
    if customer_survey_id:
        query = {'Id': customer_survey_id}

    projection = {
        '_id': 1,
        'Id': 1,
        'accounts': 1,
        'confirmed_accounts': 1,
    }

    # get all confirm accounts from Mongo
    confirm_accounts = collection.find(query, projection)

    for ca in confirm_accounts:
        ca_id = ca['_id']
        last_round_accounts = ca.get('accounts', [])
        confirmed_accounts = ca.get('confirmed_accounts', [])

        # if this confirm accounts has no user confirmations, just skip it - we have nothing to do
        if len(confirmed_accounts) == 0:
            print(f'>> Skipping confirmed accounts {ca["Id"]} as it has no confirmed account data')
            continue

        # we'll only be updating the latest (most recent) save, so let's grab its accounts
        latest_confirmed_accounts = confirmed_accounts[0].get('accounts', [])

        # re-jig to get a list a account ids of the latest confirmed accounts
        confirmed_accounts_ids = [account['account_id'] for account in latest_confirmed_accounts]

        # get all accounts who in the last round but have not been confirmed in the latest save
        # these are the accounts we need to add in
        in_rounds_accounts = [
            account
            for account in last_round_accounts
            if account['in_round'] and account['account_id'] not in confirmed_accounts_ids
        ]

        # combine both the latest confirmed accounts and the last round "in-round" accounts m
        new_combined_accounts = latest_confirmed_accounts + in_rounds_accounts

        # if there was any last round "in-round" accounts, we need to update the confirmed accounts
        if len(new_combined_accounts) > 0:
            batch.append(UpdateOne(
                {'_id': ca_id},
                {
                    '$set': {
                        'confirmed_accounts.0.all_accounts': new_combined_accounts,
                    }
                }
            ))

    # write to Mongo
    if writeit and batch:
        collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_id', default=None, help='single customer survey to run for')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.customer_survey_id, args.writeit)