import argparse
from pymongo import UpdateOne
from lib import portaldbapi


def main(survey_client_id: str|None = None, writeit: bool = False):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    batch = []

    query = {}
    if survey_client_id:
        query = {'Id': survey_client_id}

    projection = {
        '_id': 1,
        'Id': 1,
        'panel': 1,
        'confirmed_panel': 1,
    }

    # get all confirm panels from Mongo
    confirm_panels = collection.find(query, projection)

    for cp in confirm_panels:
        cp_id = cp['_id']
        panel = cp.get('panel', [])
        confirmed_panels = cp.get('confirmed_panel', [])

        # if this confirm panel has no user confirmations, just skip it - we have nothing to do
        if len(confirmed_panels) == 0:
            print(f'>> Skipping confirmed panel {cp["Id"]} as it has no confirmed panel data')
            continue

        # we'll only be updating the latest (most recent) save, so let's grab its panel
        latest_confirmed_panel = confirmed_panels[0].get('panel', [])

        # re-jig to get a list a contact_ids of the latest confirmed panel
        confirmed_panel_ids = [p['contact_id'] for p in latest_confirmed_panel]

        # get all panel members who in the last round but have not been confirmed in the latest save
        # these are the panel members we need to add in
        in_rounds_panel_members = [
            panel_member
            for panel_member in panel
            if panel_member['in_round'] and panel_member['contact_id'] not in confirmed_panel_ids
        ]

        # combine both the latest confirmed panel and the last round "in-round" panel members
        new_combined_panel = latest_confirmed_panel + in_rounds_panel_members

        # if there was any last round "in-round" panel members, we need to update the confirmed panel
        if len(new_combined_panel) > 0:
            batch.append(UpdateOne(
                {'_id': cp_id},
                {
                    '$set': {
                        'confirmed_panel.0.all_panel': new_combined_panel,
                    }
                }
            ))

    # write to Mongo
    if writeit and batch:
        collection.bulk_write(batch)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_client_id', default=None, help='single survey client to run for')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.survey_client_id, args.writeit)