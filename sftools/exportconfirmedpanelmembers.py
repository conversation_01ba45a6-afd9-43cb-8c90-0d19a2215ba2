""" Export list of confirmed panel members for panel confirm stage that has started but not yet completed (as of today)
"""
import lib.portaldbapi as portaldbapi
import argparse
import csv
import datetime


def main(round_id=None):
    portaldb = portaldbapi.DocumentDB()

    # today = datetime.datetime(2024, 12, 17)
    today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    if round_id:
        filename = f"/tmp/panel_members_{round_id}_{today.strftime('%Y-%m-%d')}.csv"
    else:
        filename = f"/tmp/panel_members_{today.strftime('%Y-%m-%d')}.csv"

    with open(filename, 'w') as f:
        fieldnames = [
            'panel_manager_name', 
            'panel_manager_email', 
            'client_name', 
            'account_name', 
            'panel_confirmation_start_date', 
            'panel_confirmation_end_date', 
            'survey_client_link', 
            'panel_member_name', 
            'panel_member_email', 
            'confirmed_date', 
            'was_in_last_round', 
            'auto_confirmed', 
            'in_round'
        ]
        csvout = csv.DictWriter(f, fieldnames=fieldnames)
        csvout.writeheader()

        query = {'deleted': {'$ne': True}, 'panel_confirmed_by_date': {'$gte': today}, 'panel_confirmed_start_date': {'$lte': today}}
        if round_id:
            query['customer_survey_round_id'] = round_id

        for confirm_panel in portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION].find(query):
            panel_members = confirm_panel['panel']
            panel_members_by_email = {x['contact_email']: x for x in panel_members}
            confirmed_panel = confirm_panel['confirmed_panel']
            auto_confirm_date = confirm_panel['panel_confirmed_by_date'] + datetime.timedelta(days=1)

            if confirmed_panel:
                # One or more PMs have confirmed panel members
                # List them all by PM
                seen_panel_members = set()
                for details in confirmed_panel:
                    confirmed_date = details['confirm_date'].replace(microsecond=0).strftime('%Y-%m-%d')

                    for panel_member in details['panel']:
                        seen_panel_members.add(panel_member['contact_email'].lower())
                        row = {
                            'panel_manager_name': details['confirm_user_name'],
                            'panel_manager_email': details['confirm_user_email'],
                            'client_name': confirm_panel['client_name'],
                            'account_name': confirm_panel['account_name'],
                            'panel_confirmation_start_date': confirm_panel['panel_confirmed_start_date'].strftime('%Y-%m-%d'),
                            'panel_confirmation_end_date': confirm_panel['panel_confirmed_by_date'].strftime('%Y-%m-%d'),
                            'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                            'panel_member_name': panel_member['contact_name'],
                            'panel_member_email': panel_member['contact_email'],
                            'confirmed_date': confirmed_date,
                            'was_in_last_round': panel_members_by_email.get(panel_member['contact_email'], {}).get('in_round', False),
                            'auto_confirmed': False,
                            'in_round': True,
                        }
                        csvout.writerow(row)

                # List any previous panel members that have not been confirmed and would therefore be excluded
                for panel_member in panel_members:
                    if panel_member['contact_email'].lower() in seen_panel_members:
                        continue
                    seen_panel_members.add(panel_member['contact_email'].lower())
                    row = {
                        'panel_manager_name': '',
                        'panel_manager_email': '',
                        'client_name': confirm_panel['client_name'],
                        'account_name': confirm_panel['account_name'],
                        'panel_confirmation_start_date': confirm_panel['panel_confirmed_start_date'].strftime('%Y-%m-%d'),
                        'panel_confirmation_end_date': confirm_panel['panel_confirmed_by_date'].strftime('%Y-%m-%d'),
                        'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                        'panel_member_name': panel_member['contact_name'],
                        'panel_member_email': panel_member['contact_email'],
                        'confirmed_date': confirmed_date,
                        'was_in_last_round': panel_members_by_email.get(panel_member['contact_email'], {}).get('in_round', False),
                        'auto_confirmed': False,
                        'in_round': False,
                    }
                    csvout.writerow(row)
            else:
                # No-one confirmed yet, would auto-confirm all if it stays that way by panel close date
                for panel_member in panel_members:
                    row = {
                        'panel_manager_name': '',
                        'panel_manager_email': '',
                        'client_name': confirm_panel['client_name'],
                        'account_name': confirm_panel['account_name'],
                        'panel_confirmation_start_date': confirm_panel['panel_confirmed_start_date'].strftime('%Y-%m-%d'),
                        'panel_confirmation_end_date': confirm_panel['panel_confirmed_by_date'].strftime('%Y-%m-%d'),
                        'survey_client_link': f"https://clientrelationship.lightning.force.com/lightning/r/Survey_Client__c/{confirm_panel['Id']}/view",
                        'panel_member_name': panel_member['contact_name'],
                        'panel_member_email': panel_member['contact_email'],
                        'confirmed_date': auto_confirm_date.strftime('%Y-%m-%d'),
                        'was_in_last_round': panel_member['in_round'],
                        'auto_confirmed': True,
                        'in_round': panel_member['in_round'],
                    }
                    csvout.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--round_id', default=None, help='single survey round to run for')
    args = ap.parse_args()
    main(round_id=args.round_id)
