import io
import boto3
import datetime
import re
import argparse
import csv
import sys
import json
from sftools.sfdumpgrep import find_files_in_range, find_matching_files


def prepare_row(row, key_metdata_fields, key_changed_fields):
    outrow = {
        'Id': row['Id'],
    }
    for fieldname in key_metdata_fields:
        outrow[fieldname] = row.get(fieldname)
    for fieldname in key_changed_fields:
        outrow[fieldname] = row.get(fieldname)
    return outrow


def main(filename_for, startdate, enddate, key_metadata_fields, key_changed_fields, csvfile, grep_for):
    key_changed_fields = set(key_changed_fields)
    key_metadata_fields = set(key_metadata_fields)
    current_state = {}
    datetime_re = re.compile(".*/crc-sf-(.*)-(.*).zip$")
    grep_for_re = re.compile(grep_for) if grep_for else None

    outstream = open(csvfile, 'w') if csvfile else sys.stdout
    csvout = csv.DictWriter(outstream, fieldnames=['Id', 'action', 'stamp', 'fields_changed'] + list(key_metadata_fields) + list(key_changed_fields))
    csvout.writeheader()

    client = boto3.client('s3')
    for s3_key in find_files_in_range(client, startdate, enddate):
        dtbits = datetime_re.match(s3_key)
        if dtbits:
            d = dtbits.group(1)
            t = dtbits.group(2)
            s3_key_stamp = datetime.datetime.strptime(f"{d} {t}", "%Y%m%d %H%M%S")
        else:
            print(f"Failed to parse datetime from s3 key {s3_key}")
            continue

        for zip_filename, content in find_matching_files(client, s3_key, filename_for):
            with io.TextIOWrapper(content, encoding="utf-8") as f:
                csv_reader = csv.DictReader(f)
                for row in csv_reader:
                    if grep_for_re and not grep_for_re.search(json.dumps(row)):
                        continue

                    outrow = None
                    if row['Id'] not in current_state:
                        outrow = prepare_row(row, key_metadata_fields, key_changed_fields)
                        outrow['action'] = 'insert'
                        outrow['fields_changed'] = ''
                        outrow['stamp'] = s3_key_stamp.isoformat()
                        current_state[row['Id']] = row

                    else:
                        oldrecord = current_state[row['Id']]
                        fields_which_changed = set()
                        for fieldname in key_changed_fields:
                            if row.get(fieldname) != oldrecord.get(fieldname):
                                fields_which_changed.add(fieldname)
                        current_state[row['Id']] = row

                        if fields_which_changed:
                            outrow = prepare_row(row, key_metadata_fields, key_changed_fields)
                            outrow['action'] = 'update'
                            outrow['fields_changed'] = ','.join(fields_which_changed)
                            outrow['stamp'] = s3_key_stamp.isoformat()

                    if outrow:
                        csvout.writerow(outrow)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename_for', help="The filename to look for in the zip files")
    ap.add_argument('startdate', help="The start date to look for")
    ap.add_argument('enddate', help="The end date to look for (inclusive)")
    ap.add_argument('metadata_fields', help="The fields to include in the output (comma separated)")
    ap.add_argument('changed_fields', help="The fields to include in the output, but with change tracking (comma separated)")
    ap.add_argument('--csvoutfile', help="File to write to, default stdout")
    ap.add_argument('--grepfor', help="Row must contain this string to be included in the output")
    args = ap.parse_args()

    startdate = datetime.datetime.strptime(args.startdate, "%Y-%m-%d")
    enddate = datetime.datetime.strptime(args.enddate, "%Y-%m-%d")

    main(args.filename_for,
         startdate, enddate,
         args.metadata_fields.split(','),
         args.changed_fields.split(','),
         args.csvoutfile,
         args.grepfor)
