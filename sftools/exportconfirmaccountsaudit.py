""" Export list of confirmed accounts for account confirm stage for specific customer survey round
"""
import lib.portaldbapi as portaldbapi
import argparse
import csv
import datetime


def main(csr_id: str, writeit: bool = False):
    portaldb = portaldbapi.DocumentDB()

    today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    filename = f"/tmp/ipg_confirmed_accounts_{today.strftime('%Y-%m-%d')}.csv"

    with open(filename, 'w') as f:
        fieldnames = [
            'survey_round_name',
            'customer_survey_round_name', 'customer_survey_round_id',
            'customer_survey_name', 'customer_survey_id',
            'account_confirmation_start_date', 'account_confirmation_end_date',
            'client_name', 'client_id',
            'team_name', 'team_id',
            'account_confirmation_saved_date', 'account_confirmation_saved_by',
            'account_name', 'account_id', 
            'signatory', 'panel_managers'
        ]
        if writeit:
            csvout = csv.DictWriter(f, fieldnames=fieldnames)
            csvout.writeheader()

        query = {'deleted': {'$ne': True}, 'customer_survey_round_id': csr_id}

        for confirm_acc in portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION].find(query):
            confirmed_accounts = confirm_acc['confirmed_accounts']

            if confirmed_accounts:
                for details  in confirmed_accounts:
                    row = {}
                    confirmed_date = details['confirm_date'].replace(microsecond=0).strftime('%Y-%m-%d %H:%M')
                    accounts = details['accounts']

                    row = {
                        'survey_round_name': confirm_acc['survey_round'],
                        'customer_survey_round_name': confirm_acc['customer_survey_round_name'],
                        'customer_survey_round_id': confirm_acc['customer_survey_round_id'],
                        'customer_survey_name': confirm_acc['customer_survey_name'],
                        'customer_survey_id': confirm_acc['Id'],
                        'account_confirmation_start_date': confirm_acc['accounts_confirmed_start_date'].strftime('%Y-%m-%d'),
                        'account_confirmation_end_date': confirm_acc['accounts_confirmed_by_date'].strftime('%Y-%m-%d'),
                        'client_name': confirm_acc['client_name'],
                        'client_id': confirm_acc['client_id'],
                        'team_name': confirm_acc['team_name'],
                        'team_id': confirm_acc['team_id'],
                        'account_confirmation_saved_date': confirmed_date,
                        'account_confirmation_saved_by': details['confirm_user_email'],
                        'account_name': None,
                        'account_id': None,
                        'signatory': None,
                        'panel_managers': None,
                    }

                    if accounts:
                        for account in accounts:
                            panel_managers = ', '.join([x['panel_manager_email'] for x in account['survey_panel_managers']])

                            row['account_name'] = account['account_name']
                            row['account_id'] = account['account_id']
                            row['signatory'] = account['signatory_email']
                            row['panel_managers'] = panel_managers

                            if writeit:
                                csvout.writerow(row)
                    else:
                        if writeit:
                            csvout.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_round_id', help='ID of the CSR to process')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the CSV')
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.writeit)
