import argparse
from lib import portaldbapi


def main(writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    projection = {
        'Id': 1,
        'panel_managers': 1,
        'confirmed_panel': 1,
    }

    confirm_panels = collection.find({}, projection)

    for cp in confirm_panels:
        print(f'>> Processing confirmed panel {cp["Id"]}')

        new_confirmed_panel = []
        new_panel_members = []
        panel_members_seen = set()
        confirm_data = []
        confirmed_panel_last_save_date = None
        confirmed_panel_last_save_user_id = None
        confirmed_panel_last_save_user_name = None
        confirmed_panel_last_save_user_email = None
        
        # generate a lookup of panel manager data by panel manager id
        panel_managers = {
            pm['panel_manager_id']: pm
            for pm in cp.get('panel_managers', [])
        }

        # grab the existing confirmed panel data
        confirmed_panel = cp.get('confirmed_panel', [])

        # iterate over the existing confirmed panel data
        for panel_manager_id, data in confirmed_panel.items():
            # add confirm date to list (for this confirm panel)
            confirm_data.append({
                'confirm_date': data['confirm_date'],
                'confirm_user_id': panel_manager_id,
            })

            # iterate over panel members
            for panel_member in data['panel']:
                panel_member_id = panel_member['contact_id']

                # if panel member has already been added in new structure, skip
                if panel_member_id in panel_members_seen:
                    continue

                # add panel member to new structure
                new_panel_members.append(panel_member)

                # mark this panel member as been seen
                panel_members_seen.add(panel_member_id)

        # set confirm data 
        if len(confirm_data) > 0:
            # sort the confirm data so the most recent saved panel is first
            confirm_data.sort(key=lambda d: d['confirm_date'], reverse=True)

            for cd in confirm_data:
                # grab the panel manager data of the user
                confirm_user_data = panel_managers.get(cd['confirm_user_id'], {})

                new_confirmed_panel.append({
                    'panel': [],
                    'confirm_date': cd['confirm_date'],
                    'confirm_user_id': cd['confirm_user_id'],
                    'confirm_user_email': confirm_user_data.get('panel_manager_email'),
                    'confirm_user_name': confirm_user_data.get('panel_manager_name'),
                })
            
            # assign the panel to latest confirmation
            if len(new_confirmed_panel) > 0:
                new_confirmed_panel[0]['panel'] = new_panel_members
                confirmed_panel_last_save_date = new_confirmed_panel[0]['confirm_date']
                confirmed_panel_last_save_user_id = new_confirmed_panel[0]['confirm_user_id']
                confirmed_panel_last_save_user_name = new_confirmed_panel[0]['confirm_user_name']
                confirmed_panel_last_save_user_email = new_confirmed_panel[0]['confirm_user_email']

        # update mongo if we have panel members
        if writeit:
            collection.update_one(
                {'_id': cp['_id']},
                {
                    '$set': {
                        'confirmed_panel_new': new_confirmed_panel,
                        'confirmed_panel_last_save_date': confirmed_panel_last_save_date,
                        'confirmed_panel_last_save_user_id': confirmed_panel_last_save_user_id,
                        'confirmed_panel_last_save_user_name': confirmed_panel_last_save_user_name,
                        'confirmed_panel_last_save_user_email': confirmed_panel_last_save_user_email
                    }
                }
            )

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.writeit)