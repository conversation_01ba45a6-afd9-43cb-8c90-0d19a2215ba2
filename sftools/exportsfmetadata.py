import csv
import lib.sfapi as sfapi
import argparse
import os
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    dirname = os.path.join(f"data")
    os.makedirs(dirname, exist_ok=True)

    objnames = ['Market', 'SurveyTemplateDefinition']

    for objname in objnames:
        sfobject = getattr(sfapi, objname)
        fieldnames = sfobject.model_json_schema()['properties'].keys()
        filename = f"{objname}.csv"

        with open(os.path.join(dirname, filename), 'w') as fileout:
            csvout = csv.DictWriter(fileout, fieldnames=fieldnames)
            csvout.writeheader()

            for item in sfapi.get_all(sf, sfobject, bulk=True):
                row = item.model_dump(by_alias=True)
                csvout.writerow(row)

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
