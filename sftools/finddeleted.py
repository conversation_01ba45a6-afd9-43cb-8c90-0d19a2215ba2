import lib.sfapi as sfapi
import argparse
from lib.settings import settings


def export_csv():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    for row in sf.query_all_iter("SELECT Id, Name FROM Contact WHERE IsDeleted=True", include_deleted=True):
        print(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    export_csv()
