import csv
import lib.sfapi as sfapi
import argparse
from lib.settings import settings
import lib.sfimport as sfimport


def import_csv(filename, writeit: bool):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    accountRecordTypes = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    account_record_type = accountRecordTypes[sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT]

    # get all existing accounts from salesforce
    all_accounts = {}
    all_accounts_by_name = {}
    for account in sfapi.get_all(sf, sfapi.Account, bulk=False, query_suffix="WHERE Account.RecordType.Name = 'Customer\\'s Client Account'"):
        all_accounts[account.Id] = account
        all_accounts_by_name[account.Name] = account
        sfimport.sfid_mapping[account.Id] = account.Id
        sfimport.sfcache[account.business_key2()] = account

    with open(filename, 'r') as f:
        reader = csv.DictReader(f)
        for row in reader:
            account_name = row['account_name'].strip()
            if row['parent_account_name']:
                parent_account_name = row['parent_account_name'].strip()
            else:
                parent_account_name = None

            # skip empty rows entirely
            emptycount = sum([1 for v in row.values() if not v])
            if emptycount == len(row):
                continue

            if not account_name:
                print("MISSING ACCOUNT NAME", row)
                continue
            if account_name == parent_account_name:
                print("PARENT IS CHILD", row)
                continue

            if account_name not in all_accounts_by_name:
                new_account = sfimport.get_or_create_cached_account(account_name, None, account_record_type)
                all_accounts[new_account.Id] = new_account
                all_accounts_by_name[account_name] = new_account
            account = all_accounts_by_name[account_name]

            if parent_account_name and parent_account_name not in all_accounts_by_name:
                new_account = sfimport.get_or_create_cached_account(parent_account_name, None, account_record_type)
                all_accounts[new_account.Id] = new_account
                all_accounts_by_name[parent_account_name] = new_account
            parent_account = all_accounts_by_name.get(parent_account_name)

            if parent_account and account.ParentId != parent_account.Id:
                if account.ParentId is None:
                    account.ParentId = parent_account.Id
                    if not account.Id.startswith('Account-'):
                        print("SF MANUAL MOVE ORPHAN", account.Name, '-->', parent_account.Id, parent_account.Name)

                else:
                    print("SF MANUAL MOVE EXISTING", account.Name, account.ParentId, all_accounts[account.ParentId].Name, '-->', parent_account.Id, parent_account.Name)

    # now create any new accounts in salesforce
    if writeit:
        sfimport.bulk_write_accounts_to_sf(sf, sfimport.new_accounts)
    else:
        for x in sfimport.new_accounts:
            print(x)

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to Salesforce')
    args = ap.parse_args()

    import_csv(args.filename, args.writeit)
