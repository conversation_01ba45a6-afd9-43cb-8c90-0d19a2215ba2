import argparse
from datetime import datetime
from pymongo import UpdateOne
from lib import portaldbapi
from lib import sfapi
from simple_salesforce import format_soql
from lib.settings import settings


def main(customer_survey_round_id:str, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    total_count = 0
    updated_count = 0
    batch = []

    # get the survey panel member ids and their end dates
    soql = """
        SELECT  Id,
                Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM    Survey_Panel_Member__c
        WHERE   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {customer_survey_round_id}
    """
    soql = format_soql(soql, customer_survey_round_id=customer_survey_round_id)
    all_survey_panel_member_ids = set()
    survey_panel_member_dates = {}
    for row in sfapi.bulk_query(sf, soql):
        all_survey_panel_member_ids.add(row['Id'])
        survey_panel_member_end_date = datetime.fromisoformat(row['Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c'])
        survey_panel_member_dates[row['Id']] = survey_panel_member_end_date

    for survey in survey_collection.find({'deleted': {'$ne': True}, 'response': None, 'sfsync_date': None}):
        total_count += 1
        panel_member_ids = set([
            member["survey_panel_member_id"]
            for question in survey["questions"]
            for member in question["panel_members"]
        ])

        # ok, is this survey relevant?
        if not panel_member_ids & all_survey_panel_member_ids:
            continue

        # figure out the max end date based on the panel members
        end_dates = []
        for panel_member_id in panel_member_ids:
            if panel_member_id in survey_panel_member_dates:
                end_dates.append(survey_panel_member_dates[panel_member_id])
        end_date = max(end_dates) if end_dates else None
        if not end_date:
            print("End date was unknown for survey", survey["_id"])
            continue

        # if the end date is already set to the correct end date, skip
        if survey['end_date'] == end_date:
            continue

        # ok, add the update to the batch
        updated_count += 1
        batch.append(UpdateOne({'_id': survey['_id']}, {'$set': {'end_date': end_date}}))
        print(f'Updating {survey['_id']}')

    if batch and writeit:
        survey_collection.bulk_write(batch)

    print(f'Total surveys: {total_count}')
    print(f'Updated: {updated_count}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_round_id')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.writeit)
