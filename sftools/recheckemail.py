import argparse
import datetime
from bson import ObjectId
from simple_salesforce import Salesforce
from typing import Optional
from lib import sfapi
from lib.settings import settings
from lib.portaldbapi import DocumentDB


TARGET_DATE = datetime.datetime(2025, 2, 11).date()
SG_M4_TEMPLATES = [
    "d-dc9244b3bf9846bb881a77f3d29c2e29",
    "d-4d013372725a4060b3bb384786516363",
    "d-c204e96d3f6646f7b2fce6e76ced76a4",
    "d-a3ffd76843cb40b8bfebbaa80a65eac0",
    "d-cbe8f7e713004821950a772ba4589476",
    "d-9f684896dd4b44199ef1fcfcb68191cb",
    "d-cf2fc980c4834d2a81a82fd028ab755c",
    "d-64e6cb6e25d74490af28d17420638cf8",
    "d-b57af634f42a4b8b82673ed0109fe517",
    "d-fb1d37af71024048b9ddfa754e650af6",
    "d-5560111b4d9949baa09c65ef56a9eda6",
    "d-c05b8dac3e1944ca9e144df31bb04f6f",
    "d-666787ff6e554394b351209a7e55bd61",
    "d-a3f9dac2161e41d9a431e132a112237d",
    "d-c6c243a64d7b42769c2ba958ad774d21",
    "d-29f5644f30854f4db32ba3675f1f350d",
    "d-0d2c5edd95934ed496f73a57065296e1",
    "d-ce84b17bf91d4158b29c786e495b6457",
    "d-ff14d5db6f8b4bb3b4d2772eb949e40a",
    "d-0863f4d8eff849b088bf181a11cdaad2",
    "d-980e48b9551147e3be0c2bee9e8520e7",
]
SF_SPM_M4_SENT_EMAIL_FIELD = 'Survey_Email_Reminder_3__c'


def update_sf_survey_panel_member(sf:Salesforce, survey_panel_members_updates:list[dict]) -> None:
    statuses = sfapi.bulk_update(sf, "Survey_Panel_Member__c", survey_panel_members_updates)
    for status in statuses:
        sfapi.detect_bulk2_errors(status)


def fetch_email_logs(db: DocumentDB, survey_panel_member_id:Optional[str] = None) -> dict[str, dict]:
    logs_by_panel_member:set = set()

    # we only want logs for the M4 templates (will be multiple due to the languages)
    query:dict = {
        'template': {'$in': SG_M4_TEMPLATES},
        'deleted': {'$ne': True},
    }
    # if we're only running for a single panel member, add that to the query
    if survey_panel_member_id:
        query['event_data.survey_panel_member_id'] = survey_panel_member_id

    # grab the logs
    email_logs = db.emaillog.find(query)

    # iterate over the logs and add the panel member ID to the set
    for log in email_logs:
        record_oid:ObjectId = log.get('_id')
        panel_member_id:str = log.get('event_data', {}).get('survey_panel_member_id')

        # make sure we only add the panel members that were sent the email on our target date
        record_timestamp:datetime = record_oid.generation_time
        if panel_member_id and record_timestamp.date() == TARGET_DATE:
            logs_by_panel_member.add(panel_member_id)

    return list(logs_by_panel_member)


def main(survey_panel_member_id:Optional[str] = None, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:DocumentDB = DocumentDB()

    batch:list[dict] = []

    # get list of survey panel members that had been sent an M4 template
    panel_member_email_logs:list[str] = fetch_email_logs(db, survey_panel_member_id)

    # iterate over the survey panel members and update the M4 Email Scheduled field
    for survey_panel_member_id in panel_member_email_logs:
        # add the survey panel member to the batch for updating
        batch.append({
            'Id': survey_panel_member_id,
            SF_SPM_M4_SENT_EMAIL_FIELD: True
        })

    #print(batch)

    # if we have any, update SF
    if batch and writeit:
        update_sf_survey_panel_member(sf, batch)

    print(f"Updated {len(panel_member_email_logs)} survey panel members")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--survey_panel_member_id", type=str, help="The ID of the survey panel member to update")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.writeit)
