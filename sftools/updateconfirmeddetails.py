import argparse
from lib import portaldbapi


def main(writeit: bool):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    count = 0
    confirm_panels = collection.find({})

    for cp in confirm_panels:
        confirmed_panels = cp.get('confirmed_panel', [])
        latest_confirmed_panel_save_date = cp.get('confirmed_panel_last_save_date', None)

        latest_confirmed_panel = None
        confirmed_date = None
        confirmed_user_id = None
        confirmed_user_email = None
        confirmed_user_name = None

        if len(confirmed_panels) > 0 and latest_confirmed_panel_save_date is None:
            count += 1
            latest_confirmed_panel = confirmed_panels[0]
            confirmed_date = latest_confirmed_panel['confirm_date']
            confirmed_user_id = latest_confirmed_panel['confirm_user_id']
            confirmed_user_email = latest_confirmed_panel['confirm_user_email']
            confirmed_user_name = latest_confirmed_panel['confirm_user_name']

        if latest_confirmed_panel and confirmed_date and confirmed_user_id and confirmed_user_email and confirmed_user_name:
            print(f'>> Updating confirmed panel {cp["Id"]}')
            if writeit:
                collection.update_one(
                    {'_id': cp['_id']},
                    {
                        '$set': {
                            'confirmed_panel_last_save_date': confirmed_date,
                            'confirmed_panel_last_save_user_id': confirmed_user_id,
                            'confirmed_panel_last_save_user_name': confirmed_user_email,
                            'confirmed_panel_last_save_user_email': confirmed_user_name
                        }
                    }
                )
    
    print(f'{count} confirmed panels affected')

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--writeit', action='store_true', default=False, dest='writeit', help='Actually write to the Mongo')
    args = ap.parse_args()

    main(args.writeit)