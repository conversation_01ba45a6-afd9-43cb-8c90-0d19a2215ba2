name: Deployment
run-name: ${{ github.event_name == 'workflow_dispatch' && format('Manual deploy {0} to {1}', github.event.inputs.component, github.event.inputs.environment) || format('Auto deploy all to {0}', github.ref_name) }}

on:
  push:
    branches:
      - dev
      - stage
  
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target Environment'
        required: true
        type: choice
        options:
          - dev
          - stage
          - prod
      component:
        description: 'Component to Deploy'
        required: true
        type: choice
        options:
          - apis
          - agents
          - all
        default: 'all'

# Needed by aws-actions/configure-aws-credentials
permissions:
  id-token: write
  contents: read

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.set-env.outputs.environment }}
      component: ${{ steps.set-env.outputs.component }}
    steps:
      - name: Set environment and component
        id: set-env
        run: |
          # For workflow_dispatch (manual trigger)
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
            echo "component=${{ github.event.inputs.component }}" >> $GITHUB_OUTPUT
          # For push events (automatic trigger)
          elif [[ "${{ github.event_name }}" == "push" ]]; then
            echo "environment=${{ github.ref_name }}" >> $GITHUB_OUTPUT
            echo "component=all" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: [setup]
    runs-on: ubuntu-latest
    environment: ${{ needs.setup.outputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.7'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.0

      - name: Install dependencies
        run: |
          poetry config virtualenvs.create false
          poetry install --no-interaction --no-ansi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-region: ${{ secrets.AWS_REGION }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Build APIs
        if: ${{ needs.setup.outputs.component == 'apis' || needs.setup.outputs.component == 'all' }}
        run: ./build-apis ${{ needs.setup.outputs.environment }}

      - name: Deploy APIs
        if: ${{ needs.setup.outputs.component == 'apis' || needs.setup.outputs.component == 'all' }}
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: ${{ needs.setup.outputs.environment }}
          work-dir: ./api
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}

      - name: Build Agents
        if: ${{ needs.setup.outputs.component == 'agents' || needs.setup.outputs.component == 'all' }}
        run: ./build-agents ${{ needs.setup.outputs.environment }}

      - name: Deploy Agents
        if: ${{ needs.setup.outputs.component == 'agents' || needs.setup.outputs.component == 'all' }}
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: ${{ needs.setup.outputs.environment }}
          work-dir: ./agents
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
