name: Test

on:
  pull_request:


jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.7'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.0

      - name: Install dependencies
        run: |
          poetry config virtualenvs.create false
          poetry install --no-interaction --no-ansi

      - name: Run unit tests
        run: poe test
