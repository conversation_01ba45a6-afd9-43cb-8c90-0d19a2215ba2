One Time Setup
-------

Checkout this repo to your local machine.

*Setup AWS*

Add the following to your ~/.bashrc (or other shell init script):
```
export AWS_PROFILE=vaul-crc-admin
export PULUMI_CONFIG_PASSPHRASE="<PULMI PASSPHRASE>"
```

Restart your terminal

Install aws CLI v2: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html

Non-Vaul (Access Key) Users
----
If you are not using SSO (generally everyone who does not have an @vaullabs.com email address right now).

Add the following AWS profile to ~/.aws/config:

```
[profile vaul-crc-admin]
region = eu-west-1
```

You'll also need to setup your Access keys: just edit ~/.aws/credentials as follows:
```
[vaul-crc-admin]
aws_access_key_id = <YOURACCESSKEY>
aws_secret_access_key = <YOURSECRETACCESSKEY>
```

You should now be able to run the following to check your access key works:
```
aws s3 ls
```
(it'll just list the names of the S3 buckets on success)

V<PERSON> (SSO) Users
----

Add the following AWS profile to ~/.aws/config:
```
[profile vaul-crc-admin]
sso_start_url = https://vaullabs.awsapps.com/start
sso_region = us-east-1
sso_account_id = ************
sso_role_name = AdministratorAccess
region = eu-west-1
```

Just run the following to check your SSO works:
```
export AWS_PROFILE=vaul-crc-admin
aws sso login
```

Setup poetry/python
----

Install poetry
```
pip install poetry==1.8
```

Run the following in the crc-backend folder
```
poetry install
```

Follow the install for the shell plugin: https://github.com/python-poetry/poetry-plugin-shell

Day to Day Development Process
-------

*Daily setup*

You'll end up running this every day
```
poetry shell
export AWS_PROFILE=vaul-crc-admin
```
If using sso, also do `aws sso login`

*Running an agent or other command line tool*

(By default, the `dev` stack is used)

All of the agents/etc can be run from the command line, eg:
```
python3 -m agents.sync_sf_to_s3
```

*Running an API*
```
LITESTAR_DEBUG=1 litestar --app api.clientarea.app:app run
```

*Running against prod (for debug/ data loading etc)*

```
STACK=prod python3 -m agents.sync_sf_to_s3
```
You will be asked to confirm that you definitely mean to run against prod! (the same mechanism works for APIs as well, which can be very useful for investigation!)

-------
**Deployment**

This is done with pulumi. There are three pulumi deploys in this repo. Both have stacks `dev` and `prod`.

Prior to running these commands, you'll need to run this:
```
poetry shell
export AWS_PROFILE=vaul-crc-admin
```
If using sso, also do `aws sso login`

Finally, run this to "login" to the pulumi S3 bucket:
```
pulumi login s3://crc-pulumi/crc
```

------
*Core infrastructure*
I strongly recommend using extreme care when running this as it provisions the VPCs and databases. Deployment of this is very very very rare.

```
cd crc-backend
pulumi up -s <stack>
```

------
*Agents*
To build the agents image and deploy it:
```
cd crc-backend
./build-agents <stack>
cd agents
pulumi up -s <stack>
````

------
*APIs*
To build the API image and deploy it:
```
cd crc-backend
./build-apis <stack>
cd apis
pulumi up -s <stack>
````


## Profiling with Austin and Flamegraph (CPU & memory)

1. Install the [flamegraph](https://github.com/brendangregg/FlameGraph) tool
   ```shell
   brew install flamegraph
   ```
2. Start the API in the "profiling" mode.
   ```shell
   poe api_austin
   ```
3. Call the endpoints that you would like to include in the profiling report, have a look at the [examples](./examples) directory for inspiration.
4. Generate flamegraph
   ```shell
   poe austin_flamegraph
   ```
5. Open [austin_flamegraph.svg](.profiling/austin_flamegraph.svg) in your browser.
