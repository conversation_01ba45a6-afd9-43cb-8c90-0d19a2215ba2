#!/usr/bin/env python
import argparse
import subprocess
import os

ap = argparse.ArgumentParser()
ap.add_argument('stack')
args = ap.parse_args()

env = os.environ.copy()
env['AWS_PROFILE'] = 'AWS_PROFILE'

subprocess.check_call(['pulumi', 'login', 's3://crc-pulumi/crc'], env=env)
subprocess.check_call(['python', 'build-agents', args.stack], env=env)
subprocess.check_call(['python', 'build-apis', args.stack], env=env)
subprocess.check_call(['pulumi', 'up', '-s', args.stack], env=env, cwd='agents')
subprocess.check_call(['pulumi', 'up', '-s', args.stack], env=env, cwd='api')
