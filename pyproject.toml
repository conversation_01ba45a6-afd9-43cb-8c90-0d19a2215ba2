[tool.poetry]
name = "crc-backend"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
package-mode = false

[tool.poetry.dependencies]
python = "~3.12"
simple-salesforce = "^1.12.6"
pydantic = {extras = ["email"], version = "^2.8.2"}
pymongo = "^4.8.0"
litestar = {extras = ["standard"], version = "^2.9.1"}
boto3 = "^1.34.145"
mangum = "^0.17.0"
pydantic-settings = "^2.3.4"
brotli = "^1.1.0"
sendgrid = "^6.11.0"
python-jose = "^3.3.0"
aws-encryption-sdk = "^3.3.0"
msgpack = "^1.1.0"
pillow = "^10.4.0"
sentry-sdk = "^2.34.1"
motor = "^3.6.0"
pandas = "^2.2.3"
requests = "^2.32.3"
bleach = "^6.2.0"

[tool.poetry.group.dev.dependencies]
pulumi = "^3.113.3"
pulumi-aws = "^6.32.0"
nltk = "^3.8.1"
sentence-transformers = "^3.0.1"
openpyxl = "^3.1.5"
pandas = "^2.2.2"
deepl = "^1.18.0"
langdetect = "^1.0.9"
selenium = "^4.25.0"
poethepoet = "^0.36.0"
austin-dist = "^3.7.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks.test]
cmd = "python -m unittest"

[tool.poe.tasks.austin]
help = "Run Austin profiler on the API"
shell = '''
# Create profiling directory if it doesn't exist
mkdir -p .profiling

# Export environment variable
export STACK=stage

# MacOS requires root, see https://github.com/P403n1x87/austin#on-macos-1
if [[ "$(uname)" == "Darwin" ]]; then
    sudo -E austin -o .profiling/austin_report.austin litestar --app api.clientarea.app:app run
else
    austin -E -o .profiling/austin_report.austin litestar --app api.clientarea.app:app run
fi
'''

[tool.poe.tasks.austin_flamegraph]
help = "Generate API flamegraph from Austin profiling"
shell = "flamegraph.pl .profiling/austin_report.austin > .profiling/austin_flamegraph.svg"

