from typing import List, Dict
from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.handlers.overview.get_report_handler import GetReportHandler
from api.clientarea.src.handlers.overview.get_report_definitions_handler import GetReportDefinitionsHandler


class ReportsRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
         cache=False,
         status_code=HTTP_200_OK,
         sync_to_thread=False,
    )
    def get_report_definitions(self, user: User, documentdb: DocumentDB) -> List:
        return GetReportDefinitionsHandler(user, documentdb).handler()


    @get(
        '{report_id:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_report(self, report_id: str, user: User, documentdb: DocumentDB) -> Dict:
        return GetReportHandler(user, documentdb, report_id).handler()
