from urllib.parse import urlencode
from typing import Dict, Optional, Any
from litestar import Request, Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDBAsync
from api.clientarea.src.dependencies import User
from api.clientarea.src.handlers.dashboard.get_export_handler import GetExportHandler

class ExportRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDBAsync, sync_to_thread=False),
    }

    @get(
        '{dashboard:str}',
        cache=False,
        status_code=HTTP_200_OK,
    )
    async def get_export(
        self,
        dashboard: str,
        user: User,
        documentdb: DocumentDBAsync,
        gf_round: Optional[str] = None,
        gf_theme: Optional[str] = None,
        gf_agency: Optional[str] = None,
        gf_account: Optional[str] = None,
        gf_survey: Optional[str] = None,
        gf_market: Optional[str] = None,
        gf_sector: Optional[str] = None,
        gf_contact_type: Optional[str] = None,
        gf_contact_id: Optional[str] = None,
        gf_contact_division: Optional[str] = None,
        gf_team: Optional[str] = None,
        gf_team_type: Optional[str] = None,
        gf_team_market: Optional[str] = None,
        gf_team_reach: Optional[str] = None,
        gf_key_account: Optional[str] = None,
        gf_market_country: Optional[str] = None,
        lf: Optional[str] = None) -> Dict:
        global_filters = {
            '__round_index': int(gf_round) if gf_round else None,
            'theme_categories': gf_theme.split(',') if gf_theme else None,
            'agencies': gf_agency.split(',') if gf_agency else None,
            'account_id': gf_account.split(',') if gf_account else None,
            'survey_name': gf_survey.split(',') if gf_survey else None,
            'market_id': gf_market.split(',') if gf_market else None,
            'market_country_id': gf_market_country.split(',') if gf_market_country else None,
            'sector': gf_sector.split(',') if gf_sector else None,
            'contact_type': gf_contact_type.split(',') if gf_contact_type else None,
            'contact_id': gf_contact_id.split(',') if gf_contact_id else None,
            'contact_division': gf_contact_division.split(',') if gf_contact_division else None,
            'team_name': gf_team.split(',') if gf_team else None,
            'team_type': gf_team_type.split(',') if gf_team_type else None,
            'team_market': gf_team_market.split(',') if gf_team_market else None,
            'team_market_group': gf_team_reach.split(',') if gf_team_reach else None,
            'key_account': gf_key_account.split(',') if gf_key_account else None,
        }
        local_filters = {}
        parsed_lf = lf.split(',') if lf else []
        for plf in parsed_lf:
            card_id, filter = plf.split(':')
            filters = filter.split(';')
            for filter in filters:
                key, value = filter.split('=')
                local_filters.setdefault(card_id, {})[key] = value
        return GetExportHandler(dashboard, global_filters, local_filters, user, documentdb).handler()
