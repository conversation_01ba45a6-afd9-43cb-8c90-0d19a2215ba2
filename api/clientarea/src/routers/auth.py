from typing import Dict
from litestar import Controller, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from lib.cognitoapi import Cognito
from api.clientarea.src.handlers.auth.register_handler import RegisterHandler
from api.clientarea.src.handlers.auth.login_handler import LoginHandler
from api.clientarea.src.handlers.auth.okta_authentication_handler import Okta<PERSON><PERSON>enticationHandler
from api.clientarea.src.handlers.auth.okta_issuer_handler import OktaIssuerHandler
from api.clientarea.src.handlers.auth.refresh_token_handler import RefreshTokenHandler
from api.clientarea.src.handlers.auth.logout_handler import LogoutHandler
from api.clientarea.src.handlers.auth.reset_password_handler import ResetPasswordHandler
from api.clientarea.src.handlers.auth.reset_password_verification_handler import ResetPasswordVerificationHandler
from api.clientarea.src.handlers.auth.registration_verification_handler import RegistrationVerificationHandler
from api.clientarea.src.handlers.auth.registration_verification_resend_handler import RegistrationVerificationResendHandler
from api.clientarea.src.models.requests import RegisterRequest, LoginRequest, OktaIssuerRequest, OktaAuthenticationRequest, RefreshTokenRequest, LogoutRequest, ResetPasswordRequest, ResetPasswordVerificationRequest, RegistrationVerificationRequest, RegistrationVerificationResendRequest


class AuthRouter(Controller):
    dependencies = {
        'cognito': Provide(Cognito, sync_to_thread=False),
    }

    @post(
        '/register',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
        dependencies={'documentdb': Provide(DocumentDB, sync_to_thread=False)}
    )
    def register_user(self, data: RegisterRequest, documentdb: DocumentDB, cognito: Cognito) -> Dict:
        return RegisterHandler(data, documentdb, cognito).handler()

    @post(
        '/registration_verification',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def registration_verification(self, data: RegistrationVerificationRequest, cognito: Cognito) -> Dict:
        return RegistrationVerificationHandler(data, cognito).handler()

    @post(
        '/registration_verification_resend',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def registration_verification_resend(self, data: RegistrationVerificationResendRequest, cognito: Cognito) -> Dict:
        return RegistrationVerificationResendHandler(data, cognito).handler()

    @post(
        '/login',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def login_user(self, data: LoginRequest, cognito: Cognito) -> Dict:
        return LoginHandler(data, cognito).handler()

    @post(
        '/login/okta/iss',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def issuer_okta(self, data: OktaIssuerRequest, cognito: Cognito) -> Dict:
        return OktaIssuerHandler(data, cognito).handler()

    @post(
        '/login/okta',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def authenticate_okta(self, data: OktaAuthenticationRequest, cognito: Cognito) -> Dict:
        return OktaAuthenticationHandler(data, cognito).handler()

    @post(
        '/reset_password',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def reset_password(self, data: ResetPasswordRequest, cognito: Cognito) -> Dict:
        return ResetPasswordHandler(data, cognito).handler()

    @post(
        '/reset_password_verification',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def reset_password_verification(self, data: ResetPasswordVerificationRequest, cognito: Cognito) -> Dict:
        return ResetPasswordVerificationHandler(data, cognito).handler()

    @post(
        '/logout',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def logout(self, data: LogoutRequest, cognito: Cognito) -> Dict:
        return LogoutHandler(data, cognito).handler()

    @post(
        '/refresh_token',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False, 
    )
    def refresh_token(self, data: RefreshTokenRequest, cognito: Cognito) -> Dict:
        return RefreshTokenHandler(data, cognito).handler()
