from typing import Dict
from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.handlers.config.get_navigation_handler import GetNavigationHandler


class ConfigRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '/navigation',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_navigation(self, user: User, documentdb: DocumentDB) -> Dict:
        return GetNavigationHandler(user, documentdb).handler()