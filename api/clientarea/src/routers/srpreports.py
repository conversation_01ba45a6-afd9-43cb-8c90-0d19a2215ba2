from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.handlers.reports.get_reports_handler import GetReportsHandler
from api.clientarea.src.handlers.reports.get_report_handler import GetReportHandler


class SrpReportsRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
         cache=False,
         status_code=HTTP_200_OK,
         sync_to_thread=False,
    )
    def get_reports(self, user: User, documentdb: DocumentDB) -> list:
        return GetReportsHandler(user, documentdb).handler()

    @get(
        '{report_id:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_report(self, report_id: str, user: User, documentdb: DocumentDB) -> dict:
        return GetReportHandler(user, documentdb, report_id).handler()