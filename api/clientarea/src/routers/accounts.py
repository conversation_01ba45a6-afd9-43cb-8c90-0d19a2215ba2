from typing import Optional
from litestar import Controller, get, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from lib.sqsapi import SQS
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.requests import DelegateSurveyClientRequest, ConfirmCustomerSurveyRequest
from api.clientarea.src.models.responses import ConfirmAccountsResponse
from api.clientarea.src.handlers.accounts.get_accounts_handler import GetAccountsHandler
from api.clientarea.src.handlers.accounts.get_accounts_search_handler import GetAccountsSearchHandler
from api.clientarea.src.handlers.accounts.confirm_accounts_handler import ConfirmAccountsHandler
from api.clientarea.src.handlers.accounts.delegate_account_handler import DelegateAccountHandler


class AccountsRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '{survey_id:str}/accounts',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_accounts(self, survey_id: str, user: User, documentdb: DocumentDB) -> ConfirmAccountsResponse:
        return GetAccountsHandler(survey_id, user, documentdb).handler()


    @get(
        '{survey_id:str}/search/accounts',
        cache=True,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_accounts_search(self, user: User, documentdb: DocumentDB, survey_id: str, q: Optional[str], limit: int = 50) -> list[dict]:
        return GetAccountsSearchHandler(survey_id, q, limit, user, documentdb).handler()


    @post(
        '{survey_id:str}/accounts',
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def confirm_accounts(self, data: ConfirmCustomerSurveyRequest, survey_id: str, user: User, documentdb: DocumentDB) -> dict:
        return ConfirmAccountsHandler(survey_id, data, user, documentdb).handler()


    @post(
        '{survey_id:str}/accounts/delegate',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
        dependencies={'sqs': Provide(SQS, sync_to_thread=False)}
    )
    def delegate_survey_clients(self, data: DelegateSurveyClientRequest, survey_id: str, user: User, documentdb: DocumentDB) -> ConfirmAccountsResponse:
        return DelegateAccountHandler(data, survey_id, user, documentdb).handler()
