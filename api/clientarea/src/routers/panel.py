from typing import List
from litestar import Controller, get, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.collections import SfSurveyClients
from api.clientarea.src.handlers.panel.get_panel_handler import GetPanelHandler
from api.clientarea.src.handlers.panel.confirm_panel_handler import ConfirmPanelHandler
from api.clientarea.src.models.requests import ConfirmPanelRequest


class PanelRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '{survey_id:str}/panel',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_panel(self, survey_id: str, user: User, documentdb: DocumentDB) -> List[SfSurveyClients]:
        return GetPanelHandler(survey_id, user, documentdb).handler()

    @post(
        '{survey_id:str}/panel',
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def confirm_panel(self, data: ConfirmPanelRequest, survey_id: str, user: User, documentdb: DocumentDB) -> None:
        return ConfirmPanelHandler(survey_id, data, user, documentdb).handler()
