from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.responses import SurveyProgressResponse
from api.clientarea.src.handlers.surveys.get_survey_rounds_handler import GetSurveyRoundsHandler
from api.clientarea.src.handlers.surveys.get_progress_handler import GetProgressHandler


class SurveysRouter(Controller):
    dependencies = {
        'user': Provide(User, sync_to_thread=False),
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_survey_rounds(self, user: User, documentdb: DocumentDB) -> dict:
        return GetSurveyRoundsHandler(user, documentdb).handler()


    @get(
        '/progress',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_progress(self, user: User, documentdb: DocumentDB) -> SurveyProgressResponse:
        return GetProgressHandler(user, documentdb).handler()