import datetime
from api.clientarea.src.utils.cards.Card import Card


class ContactFeedback(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        feedback = []
        
        for record in data:
            count += 1
            themes = []

            for theme in record.get('themes', []):
                word = theme['l3']
                pn = theme['pn']

                if not word:
                    continue

                themes.append({
                    'word': word,
                    'pn': pn,
                })

            response_date = datetime.datetime.strptime(record['response_date'], "%Y-%m-%dT%H:%M:%S.%f%z").replace(tzinfo=None)
            formatted_response_date = response_date.strftime('%d %B %Y %H:%M')

            client_name = record['client_name']
            if record['team_name']:
                client_name = f"{client_name} ({record['team_name']})"

            question = "TRR"
            if record.get("is_extra_question", False):
                question = "EQ"

            if record.get("team_market_name", None):
                market_name = record['team_market_name']
            else:
                market_name = record['market_name']

            feedback.append({
                'key': record['Id'],
                'client': client_name,
                'agency': record['agency_brand_name'],
                'market': market_name,
                'survey_date': record['survey_start_date'],
                'team_name': record['team_name'],
                'account_name': record['account_name'],
                'contact_type': record['contact_type'],
                'rating': record['rating'],
                'question': question,
                'feedback': record['feedback'],
                'feedback_translated': record.get('feedback_translated'),
                'response_date': formatted_response_date,
                'raw_response_date': response_date,
                'round_name': record['round_name'],
                'themes': themes,
            })

        if not count:
            return

        # order feedback by response date
        feedback = sorted(feedback, key=lambda x: x['raw_response_date'], reverse=True)

        return feedback
