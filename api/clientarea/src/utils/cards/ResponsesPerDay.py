import datetime
from collections import OrderedDict
from api.clientarea.src.utils.cards.Card import Card


class ResponsesPerDay(Card):

    def __get_dates_between(self, start_date, end_date):        
        date_list = []
        
        # Loop from start date to end date
        current_date = start_date
        while current_date <= end_date:
            date_list.append(current_date.strftime("%Y-%m-%d"))  
            current_date += datetime.timedelta(days=1) 
        
        return date_list

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        days = {}
        cumulative_rating = 0
        cumulative_responses = 0
        cumulative_data = OrderedDict()
        overall_survey_start_date = None
        overall_survey_end_date = None

        for record in data:
            if record['survey_start_date'] is None or record['survey_end_date'] is None:
                continue
    
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            survey_start_date = datetime.datetime.strptime(record['survey_start_date'], "%Y-%m-%d")
            survey_end_date = datetime.datetime.strptime(record['survey_end_date'], "%Y-%m-%d")
            response_date = datetime.datetime.strptime(record['response_date'], '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%Y-%m-%d')

            if overall_survey_start_date is None or survey_start_date < overall_survey_start_date:
                overall_survey_start_date = survey_start_date
            
            if overall_survey_end_date is None or survey_end_date > overall_survey_end_date:
                overall_survey_end_date = survey_end_date

            if response_date not in days:
                days[response_date] = {
                    'responses': 0,
                    'rating': 0,
                }
            
            days[response_date]['responses'] += 1
            days[response_date]['rating'] += rating

        # no data, just return empty
        if len(days.keys()) == 0:
            return {
                'categories': [],
                'response': {},
                'ratings': {},
            }
        
        # we're seeing responses being added (manually) after the survey has ended, so need to re-calculate the overall survey end date
        # get the max date of days (with is the keys of the days dictionary but as a string date)
        mix_response_date = datetime.datetime.strptime(min(days.keys()), '%Y-%m-%d')
        max_response_date = datetime.datetime.strptime(max(days.keys()), '%Y-%m-%d')
        if mix_response_date < overall_survey_start_date:
            overall_survey_start_date = mix_response_date
        if max_response_date > overall_survey_end_date:
            overall_survey_end_date = max_response_date

        categories = self.__get_dates_between(overall_survey_start_date, overall_survey_end_date)

        # add in missing days
        for date in categories:
            if date not in days:
                days[date] = {
                    'responses': None,
                    'rating': None,
                }

        days = OrderedDict(sorted(days.items(), key=lambda x: datetime.datetime.strptime(x[0], '%Y-%m-%d')))

        for date in days:
            rating = days[date]['rating']
            responses = days[date]['responses']

            if rating is None or responses is None:
                cumulative_data[date] = {
                    'rating': None
                }
                continue

            # Cumulative sums
            cumulative_rating += rating
            cumulative_responses += responses
            cumulative_rating_avg = 0
            if cumulative_rating and cumulative_responses:
                cumulative_rating_avg = cumulative_rating / cumulative_responses

            # Add to the new dictionary
            cumulative_data[date] = {
                'rating': round(cumulative_rating_avg, 2)
            }
        
        return {
            'categories': categories,
            'response': days,
            'ratings': cumulative_data,
        }
    
    def format(self, data: dict):
        return {
            'categories': data.get('categories', []),
            'series': [
                {
                    'data': [data['responses'] for data in data['response'].values()],
                },
                {
                    'data': [data['rating'] for data in data['ratings'].values()],
                }
            ]
        }