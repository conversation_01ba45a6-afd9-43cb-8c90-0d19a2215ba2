import datetime
from api.clientarea.src.utils.cards.Card import Card


class ResponsesBarometer(Card):
    """ Return data for each response to a barometer survey.
        Similar to Responses for TRR, but removes any fields that could identify an individual contact.
    """

    def __calculate_response_time(self, response_date: str):
        if not response_date:
            return ''

        start = datetime.datetime.strptime(response_date, "%Y-%m-%dT%H:%M:%S.%f%z").replace(tzinfo=None)
        end = datetime.datetime.now()
        difference = end - start

        total_seconds = difference.total_seconds()

        if total_seconds < 3600:
            # Less than an hour
            output = f"{total_seconds // 60} Mins"
        elif total_seconds < 86400:
            # Less than a day
            value = int(total_seconds // 3600)
            output = f"{value} Hrs"
        elif total_seconds < 604800:
            # Less than a week
            output = f"{difference.days} Days"
        else:
            # One week or more
            weeks = difference.days // 7
            output = f"{weeks} Wks"

        return output

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        result = {'responses': [], 'data_hidden': False}

        responses_by_account_market = {}
        for idx, record in enumerate(data):
            if record.get("team_market_name", None):
                market_name = record['team_market_name']
            else:
                market_name = record['market_name']

            key = f'{record["account_name"]}_{market_name}'
            tmp = responses_by_account_market.setdefault(key, {'total_surveyed': 0, 'responses': []})
            tmp['total_surveyed'] += 1
            if not record['responded']:
                # just track panel size by account and market
                continue

            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            responded_date = self.__calculate_response_time(record.get('response_date'))
            tmp['responses'].append({
                'key': f'response_{idx}',
                'rating': rating,
                'account': record['account_name'],
                'market': market_name,
                'feedback': record['feedback'],
                'when': responded_date,
                'response_date': record['response_date'],
            })

        # now we have responses by account/market, only retain those where the panel size is > 1
        # TODO do we need to display the fact that some responses have been hidden?
        # TODO check we still need this filtering, the requirement has flip-flopped a lot
        responses = []
        for key, data in responses_by_account_market.items():
            if data['total_surveyed'] > 1:
                responses.extend(data['responses'])
            else:
                result['data_hidden'] = True

        # sort responses by response_date
        responses = sorted(responses, key=lambda x: x['response_date'], reverse=True)
        result['responses'] = responses

        return result
