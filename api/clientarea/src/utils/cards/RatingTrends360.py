from api.clientarea.src.utils.cards.Card import Card


class RatingTrends360(Card):

    def _response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 10, 2)


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        ratings_trr = {}
        ratings_barometer = {}
        trr_csr_ids_by_round = {}

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in data:
            round_id = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            collection = ratings_trr if not record.get('is_barometer', False) else ratings_barometer

            if record.get('is_barometer'):
                if round_id not in collection:
                    collection[round_id] = {
                        'round_name': record['round_name'],
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                    }
                    trr_csr_ids_by_round[round_id] = {'trr_csr_id': record['linked_trr_survey_id'], 'round_name': record['round_name']}

                collection[round_id]['responses'] += 1
                collection[round_id]['rating'] += rating
                collection[round_id]['rating_avg'] = collection[round_id]['rating'] / collection[round_id]['responses']
            else:
                for barometer_round_id, value in trr_csr_ids_by_round.items():
                    if record['customer_survey_round_id'] == value['trr_csr_id']:
                        if barometer_round_id not in collection:
                            collection[barometer_round_id] = {
                                'round_name': value['round_name'],
                                'responses': 0,
                                'rating': 0,
                                'rating_avg': 0,
                            }
                        collection[barometer_round_id]['responses'] += 1
                        collection[barometer_round_id]['rating'] += rating
                        collection[barometer_round_id]['rating_avg'] = collection[barometer_round_id]['rating'] / collection[barometer_round_id]['responses']
                        break

        # sort the data by round_id
        ratings_trr = dict(sorted(ratings_trr.items(), reverse=True))
        ratings_barometer = dict(sorted(ratings_barometer.items(), reverse=True))

        # append norm
        if self.trr_norm and self.barometer_norm:
            ratings_trr['norm'] = {
                'round_name': 'Norm',
                'rating_avg': float(self.trr_norm.get('ccr', 0)),
            }
            ratings_barometer['norm'] = {
                'round_name': 'Norm',
                'rating_avg': float(self.barometer_norm.get('ccr', 0)),
            }

        return {
            'ratings_trr': ratings_trr,
            'ratings_barometer': ratings_barometer,
        }
    
    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data['ratings_trr'].values()],
            'series': [
                {
                    'name': 'Barometer Rating',
                    'data': [round(data['rating_avg'], 2) for data in data['ratings_barometer'].values()],
                },
                {
                    'name': 'TRR Rating',
                    'data': [round(data['rating_avg'], 2) for data in data['ratings_trr'].values()],
                }
            ]
        }