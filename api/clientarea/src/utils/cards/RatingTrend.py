from api.clientarea.src.utils.cards.Card import Card


class RatingTrend(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        records = {}

        for record in data:
            round_id = record[round_index]
            if round_id not in records:
                round_name = record['round_name']
                if rounds[round_id] == 'current':
                    round_name = 'Latest Round'
                if rounds[round_id] == 'previous':
                    round_name = f'{round_name} (Previous Round)'
                records[round_id] = {
                    'round_name': round_name,
                    'responses': 0,
                    'rating': 0,
                    'rating_avg': 0,
                }
            records[round_id]['responses'] += 1
            records[round_id]['rating'] += 0 if not record.get('rating', None) else record.get('rating', 0)
            records[round_id]['rating_avg'] = records[round_id]['rating'] / records[round_id]['responses']
        
        # sort the data by round_id
        records = dict(sorted(records.items(), reverse=True))

        # morph data_gouped into a list of lists
        records = list(records.values())
        
        return records
    
    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data],
            'series': [round(data['rating_avg'], 2) for data in data],
        }