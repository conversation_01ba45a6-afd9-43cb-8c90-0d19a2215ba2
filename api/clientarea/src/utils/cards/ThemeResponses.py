import datetime
from api.clientarea.src.utils.cards.Card import Card


class ThemeResponses(Card):

    def __calculate_response_time(self, response_date: str):
        if not response_date:
            return ''

        start = datetime.datetime.strptime(response_date, "%Y-%m-%dT%H:%M:%S.%f%z").replace(tzinfo=None)
        end = datetime.datetime.now()
        difference = end - start

        total_seconds = difference.total_seconds()

        if total_seconds < 3600:
            # Less than an hour
            output = f"{total_seconds // 60} Mins"
        elif total_seconds < 86400:
            # Less than a day
            value = int(total_seconds // 3600)
            output = f"{value} Hrs"
        elif total_seconds < 604800:
            # Less than a week
            output = f"{difference.days} Days"
        else:
            # One week or more
            weeks = difference.days // 7
            output = f"{weeks} Wks"

        return output

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = []
        count = 0

        for record in data:
            count += 1
            themes = set()
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            responded_date = self.__calculate_response_time(record.get('response_date'))

            for theme in record.get('themes', []):
                word = theme['l3']

                if not word:
                    continue

                themes.add(word)

                if len(themes) == 3:
                    break

            responses.append({
                'key': count,
                'rating': rating,
                'name': record['contact_name'],
                'contact_type': record['contact_type'],
                'job_title': record.get('contact_job_title', ''),
                'account': record['account_name'],
                'agency': record['client_name'],
                'team': record['team_name'],
                'market': record['market_name'],
                'feedback': record['feedback'],
                'themes': themes,
                'when': responded_date,
                'response_date': record['response_date'],
                'contact_id': record['contact_id'],
            })
        
        # sort responses by response_date
        responses = sorted(responses, key=lambda x: x['response_date'], reverse=True)
        return responses
