import datetime
from api.clientarea.src.utils.cards.Card import Card


class ThemeResponses360(Card):

    def __get_sentiment(self, record) -> str:
        p_count = 0
        n_count = 0
        for theme in record.get('themes'):
            if not theme.get('pn'):
                # null or empty string
                continue
            if int(theme['pn']) > 0:
                p_count += 1
            elif int(theme['pn']) < 0:
                n_count += 1

        if p_count > 0 and n_count == 0:
            return 'p'
        elif p_count == 0 and n_count > 0:
            return 'n'
        else:
            return 'm'


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses_trr = []
        responses_barometer = []
        count = 0

        for record in data:
            collection = responses_trr if not record.get('is_barometer') else responses_barometer

            count += 1
            themes = set()
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            for theme in record.get('themes', []):
                word = theme['l3']
                if not word:
                    continue
                themes.add(word)
                if len(themes) == 3:
                    break

            collection.append({
                'key': count,
                'rating': rating,
                'feedback': record['feedback'],
                'themes': themes,
                'sentiment': self.__get_sentiment(record),
            })
        
        # sort responses by rating
        responses_trr = sorted(responses_trr, key=lambda x: x['rating'], reverse=True)
        responses_barometer = sorted(responses_barometer, key=lambda x: x['rating'], reverse=True)

        return {'responses_trr': responses_trr, 'responses_barometer': responses_barometer}
