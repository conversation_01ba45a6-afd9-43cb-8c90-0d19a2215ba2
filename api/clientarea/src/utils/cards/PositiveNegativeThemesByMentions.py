from api.clientarea.src.utils.cards.Card import Card


class PositiveNegativeThemesByMentions(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        themes = {}
        categories = []
        series = {
            'positive': [],
            'negative': [],
        }

        for record in data:
            for theme in record.get('themes'):
                if not theme.get('pn'):
                    # null or empty string
                    continue
                node = theme['l3']

                x = themes.setdefault(node, {'p': 0, 'n': 0})
                if int(theme['pn']) > 0:
                    x['p'] += 1
                elif int(theme['pn']) < 0:
                    x['n'] += -1

        for theme in themes:
            categories.append(theme)
            positive = themes[theme]['p']
            negative = themes[theme]['n']
            series['positive'].append(positive)
            series['negative'].append(negative)

        return {
            'categories': categories,
            'series': series,
        }