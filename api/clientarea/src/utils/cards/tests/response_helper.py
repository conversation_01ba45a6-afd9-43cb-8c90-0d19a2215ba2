# functions to generate dummy response data for testing purposes
import copy

BASE_RESPONSE = {
    "Id" : "PANEL_MEMBER_ID",
    "response_id" : "TEST_RESPONSE_ID",
    "account_id" : "ACCOUNT_ID",
    "account_name" : "ACCOUNT NAME",
    "account_start_year" : 2025,
    "account_view_filter_id" : "a0aWT000007h9QdYAI",
    "account_view_filter_name" : "Marsberg",
    "account_view_filter_type" : "City",
    "agencies" : [None, None],
    "agency_brand_id" : None,
    "agency_brand_name" : None,
    "agency_brand_round_index" : None,
    "agency_brand_sub_1_id" : None,
    "agency_brand_sub_1_name" : None,
    "agency_brand_sub_1_round_index" : None,
    "client_id" : "CLIENT_ID",
    "client_name" : "<PERSON><PERSON><PERSON>",
    "contact_division" : None,
    "contact_email" : "<EMAIL>",
    "contact_id" : "CONTACT_ID",
    "contact_job_title" : "R&D Manager",
    "contact_name" : "<PERSON>",
    "contact_opt_out" : False,
    "contact_seniority" : "Junior",
    "contact_type" : "Primary Contact",
    "customer_survey_id" : "CS_ID",
    "customer_survey_round_id" : "CSR_ID",
    "customer_survey_round_name" : "Q2 2025",
    "feedback" : "Test feedback comment",
    "feedback_question" : "What makes you feel this way about Acme?",
    "feedback_question_en" : "What makes you feel this way about  Acme?",
    "feedback_translated" : None,
    "holding_group_id" : "HOLDIN_GROUP_ID",
    "holding_group_name" : "HOLDING GROUP",
    "holding_group_round_index" : 0,
    "insights_date" : None,
    "insights_start_date" : None,
    "is_extra_question" : False,
    "linked_trr_survey_id" : None,
    "market_country" : "Germany",
    "market_country_id" : "GERMANY_ID",
    "market_id" : "MARSBERG_ID",
    "market_name" : "Marsberg",
    "market_type" : "City",
    "market_view_filter_id" : "a0aWT000007h9QdYAI",
    "market_view_filter_name" : "Marsberg",
    "market_view_filter_type" : "City",
    "network_id" : "NETWORK_ID",
    "network_name" : "Acme",
    "network_round_index" : 0,
    "office_id" : "OFFICE_ID",
    "office_name" : "Acme Marsberg",
    "office_round_index" : 0,
    "rating" : 5.0,
    "rating_question" : "On a scale of 1 to 10, where 1 is low and 10 is high, how likely is it that you would recommend Acme to a friend or colleague?",
    "rating_question_en" : "On a scale of 1 to 10, where 1 is low and 10 is high, how likely is it that you would recommend Acme to a friend or colleague?",
    "responded" : True,
    "response_date" : "2025-06-20T05:42:09.000+0000",
    "round_id" : "ROUND_ID",
    "round_name" : "June 2025",
    "sector" : None,
    "sentiment" : "p",
    "sub_network_id" : None,
    "sub_network_name" : None,
    "sub_network_round_index" : None,
    "survey_end_date" : "2025-06-22",
    "survey_id" : "SURVEY_ID",
    "survey_name" : "Acme",
    "survey_panel_member_id" : "PANEL_MEMBER_ID",
    "survey_start_date" : "2025-06-03",
    "team_customer_survey_id" : None,
    "team_id" : None,
    "team_market_group" : None,
    "team_market_id" : None,
    "team_market_name" : None,
    "team_market_type" : None,
    "team_name" : None,
    "team_type" : None,
    "theme_categories" : [
        "Product Quality / Quality Control"
    ],
    "themes" : [
        {
            "id" : 100264,
            "l1" : "Transactional",
            "l2" : "Product",
            "l3" : "Product Quality / Quality Control",
            "pn" : 1,
        }
    ],
    "with_market_id" : "001WT00002CWVdVYAX",
    "with_market_name" : "Acme Germany",
    "with_market_round_index" : 0,
    "market_region" : "Europe",
    "market_region_id" : "a0aWT000003OKiEYAW"
}


def get_responses(response_count=1):
    responses = []
    for idx in range(response_count):
        response = copy.deepcopy(BASE_RESPONSE)
        response['Id'] = response['Id'] + f"_{idx + 1}"
        response['contact_id'] = response['contact_id'] + f"_{idx + 1}"
        response['survey_panel_member_id'] = response['survey_panel_member_id'] + f"_{idx + 1}"
        response['response_id'] = f"TEST_RESPONSE_ID_{idx + 1}"
        response['rating'] = idx % 10 + 1  # Ratings from 1 to 10
        responses.append(response)

    return responses
