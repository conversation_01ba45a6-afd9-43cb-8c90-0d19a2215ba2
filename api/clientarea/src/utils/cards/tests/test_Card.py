import copy
from datetime import datetime, timezone
import pytz
import unittest
from unittest.mock import patch, MagicMock
from api.clientarea.src.utils.cards.tests.response_helper import get_responses
from api.clientarea.src.utils.cards.Card import Card


class DummyCard(Card):
    def get_data(self, data: list, round_index: str, rounds: dict = {}, filters: dict = {}, norm: dict = {}):
        return data


class TestCard(unittest.TestCase):
    def setUp(self):
        pass

    def test_fetch_mfv_dedupe(self):
        trr_responses = get_responses(4)
        # the first and third responses are from the same contact, the others unique
        trr_responses[0]['contact_id'] = 'contact_1'
        trr_responses[2]['contact_id'] = 'contact_1'
        trr_responses[3]['contact_id'] = 'contact_1'
        trr_responses[3]['customer_survey_round_id'] = 'CSR_ID_2'

        card_projection = {
            "Id": 1,
            "rating": 1,
            "responded": 1
        }
        card = DummyCard(
            trr_data=trr_responses,
            barometer_data=[],
            vertical='mfv',
            card_type='dummy',
            card_query={},
            card_projection=card_projection,
            card_limit=None,
            card_filters=[],
            card_non_response=False,
            card_dedupe_responses=True,
            card_previous_rounds=0,
            card_data_required=['trr'],
            filtered_round=0,
            global_filters={},
            local_filters={},
            round_index_key='office_round_index',
            trr_norm=None,
            barometer_norm=None
        )

        result = card.fetch()
        expected_result = [
            dict(Id='PANEL_MEMBER_ID_1', office_round_index=0, rating=1, responded=True),
            dict(Id='PANEL_MEMBER_ID_2', office_round_index=0, rating=2, responded=True),
            dict(Id='PANEL_MEMBER_ID_4', office_round_index=0, rating=4, responded=True),
        ]
        self.assertEqual(result, expected_result)


    def test_fetch_adv_no_dedupe(self):
        trr_responses = get_responses(4)
        # the first and third responses are from the same contact, the others unique
        trr_responses[0]['contact_id'] = 'contact_1'
        trr_responses[2]['contact_id'] = 'contact_1'
        trr_responses[3]['contact_id'] = 'contact_1'
        trr_responses[3]['customer_survey_round_id'] = 'CSR_ID_2'

        card_projection = {
            "Id": 1,
            "rating": 1,
            "responded": 1
        }
        card = DummyCard(
            trr_data=trr_responses,
            barometer_data=[],
            vertical='adv',
            card_type='dummy',
            card_query={},
            card_projection=card_projection,
            card_limit=None,
            card_filters=[],
            card_non_response=False,
            card_dedupe_responses=True,
            card_previous_rounds=0,
            card_data_required=['trr'],
            filtered_round=0,
            global_filters={},
            local_filters={},
            round_index_key='office_round_index',
            trr_norm=None,
            barometer_norm=None
        )

        result = card.fetch()
        expected_result = [
            dict(Id='PANEL_MEMBER_ID_1', office_round_index=0, rating=1, responded=True),
            dict(Id='PANEL_MEMBER_ID_2', office_round_index=0, rating=2, responded=True),
            dict(Id='PANEL_MEMBER_ID_3', office_round_index=0, rating=3, responded=True),
            dict(Id='PANEL_MEMBER_ID_4', office_round_index=0, rating=4, responded=True),
        ]
        self.assertEqual(result, expected_result)
