from api.clientarea.src.utils.cards.Card import Card


class BarometerTrends(Card):

    def _response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 10, 2)

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        ratings = {}
        responses = {}

        for record in data:
            round_id = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            # responses
            if round_id not in responses:
                responses[round_id] = {
                    'round_name': record['round_name'],
                    'outreach': 0,
                    'responses': 0,
                    'response_rate': 0,
                }
            responses[round_id]['outreach'] += 1

            # ratings
            if record['responded']:
                responses[round_id]['responses'] += 1

                if round_id not in ratings:
                    round_name = record['round_name']
                    if self.rounds_barometer[round_id] == 'current':
                        round_name = 'Latest Round'
                    if self.rounds_barometer[round_id] == 'previous':
                        round_name = f'{round_name} (Previous Round)'
                        
                    ratings[round_id] = {
                        'round_name': round_name,
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                    }
                ratings[round_id]['responses'] += 1
                ratings[round_id]['rating'] += rating
                ratings[round_id]['rating_avg'] = ratings[round_id]['rating'] / ratings[round_id]['responses']
            
            # calculate response rate
            responses[round_id]['response_rate'] = self._response_rate(responses[round_id]['responses'], responses[round_id]['outreach'])

        # sort the data by round_id
        ratings = dict(sorted(ratings.items(), reverse=True))
        responses = dict(sorted(responses.items(), reverse=True))

        # append norm
        if self.barometer_norm:
            ratings['norm'] = {
                'round_name': 'Norm',
                'rating_avg': float(self.barometer_norm.get('ccr', 0)),
            }
            responses['norm'] = {    
                'round_name': 'Norm',
                'response_rate': float(self.barometer_norm.get('response_rate', 0)) / 10,
            }

        return {
            'ratings': ratings,
            'responses': responses
        }


    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data['ratings'].values()],
            'series': [
                {
                    'name': 'Rating',
                    'data': [round(data['rating_avg'], 2) for data in data['ratings'].values()],
                },
                {
                    'name': 'Response Rate',
                    'data': [data['response_rate'] for data in data['responses'].values()],
                }
            ]
        }