from math import gcd
from api.clientarea.src.utils.cards.Card import Card


class FeedbackWordCloud360(Card):

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        words_trr = {}
        words_barometer = {}
        themes_trr = {}
        themes_barometer = {}
        feedback_trr = {'comments': 0, 'lr_comments': 0}
        feedback_barometer = {'comments': 0, 'lr_comments': 0}
        themes_ratio_trr = {'positive': 0, 'negative': 0}
        themes_ratio_barometer = {'positive': 0, 'negative': 0}
        trr_csr_ids_by_round = {}

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in data:
            is_barometer = record.get('is_barometer', False)
            words_collection = words_trr if not is_barometer else words_barometer
            themes_collection = themes_trr if not is_barometer else themes_barometer
            feedback_collection = feedback_trr if not is_barometer else feedback_barometer
            themes_ratio_collection = themes_ratio_trr if not is_barometer else themes_ratio_barometer

            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            if is_barometer:
                # this is a barometer response, so we need to add the trr csr id
                trr_csr_ids_by_round[index] = record['linked_trr_survey_id']
            else:
                # now we're processing trr data, we need to check if the CSR id matches the barometer linked CSR id
                match_found = False
                for barometer_round_id, value in trr_csr_ids_by_round.items():
                    if record['customer_survey_round_id'] == value:
                        index = barometer_round_id
                        match_found = True
                        break
                if not match_found:
                    continue

            if record.get('feedback'):
                if self.rounds_barometer[index] == 'current':
                    feedback_collection['comments'] += 1
                elif self.rounds_barometer[index] == 'previous':
                    feedback_collection['lr_comments'] += 1

            for theme in record.get('themes', []):
                word = theme['l3']

                if not word:
                    continue
                
                if word not in words_collection:
                    words_collection[word] = {
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'in_latest_round': False
                    }

                if word not in themes_collection:
                    themes_collection[word] = {
                        'id': f'{record['Id']}-{word}',
                        'word': word,
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                        'lr_responses': 0,
                        'lr_rating': 0,
                        'lr_rating_avg': 0,
                        'in_latest_round': False
                    }

                if self.rounds_barometer[index] == 'current':
                    words_collection[word]['in_latest_round'] = True
                    words_collection[word]['count'] += 1
                    themes_collection[word]['in_latest_round'] = True
                    themes_collection[word]['count'] += 1
                    themes_collection[word]['responses'] += 1
                    themes_collection[word]['rating'] += rating
                    themes_collection[word]['rating_avg'] = themes_collection[word]['rating'] / themes_collection[word]['responses']
                                    
                    if theme['pn'] and int(theme['pn']) > 0:
                        themes_collection[word]['positive'] += 1
                        words_collection[word]['positive'] += 1
                        themes_ratio_collection['positive'] += 1
                    elif theme['pn'] and int(theme['pn']) < 0:
                        themes_collection[word]['negative'] += 1
                        words_collection[word]['negative'] += 1
                        themes_ratio_collection['negative'] += 1
                elif self.rounds_barometer[index] == 'previous':
                    themes_collection[word]['lr_responses'] += 1
                    themes_collection[word]['lr_rating'] += rating
                    themes_collection[word]['lr_rating_avg'] = themes_collection[word]['lr_rating'] / themes_collection[word]['lr_responses']
        
        # strip out any themes that didn't appear in the latest round
        themes_trr = {k: v for k, v in themes_trr.items() if v['in_latest_round']}
        themes_barometer = {k: v for k, v in themes_barometer.items() if v['in_latest_round']}
        words_trr = {k: v for k, v in words_trr.items() if v['in_latest_round']}
        words_barometer = {k: v for k, v in words_barometer.items() if v['in_latest_round']}
        
        # sort the themes
        positive_themes_trr = sorted(themes_trr.items(), key=lambda x: x[1]['positive'], reverse=True)[:10]
        negative_themes_trr = sorted(themes_trr.items(), key=lambda x: x[1]['negative'], reverse=True)[:10]
        positive_themes_barometer = sorted(themes_barometer.items(), key=lambda x: x[1]['positive'], reverse=True)[:10]
        negative_themes_barometer = sorted(themes_barometer.items(), key=lambda x: x[1]['negative'], reverse=True)[:10]

        # calc ratios
        for collection in [themes_ratio_trr, themes_ratio_barometer]:
            if collection['positive'] == 0 and collection['negative'] == 0:
                collection['ratio_positive'] = 0
                collection['ratio_negative'] = 0
                collection['ratio_number'] = 0
            elif collection['positive'] == 0:
                collection['ratio_positive'] = 0
                collection['ratio_negative'] = 1
                collection['ratio_number'] = 0
            elif collection['negative'] == 0:
                collection['ratio_positive'] = 1
                collection['ratio_negative'] = 0
                collection['ratio_number'] = 1
            else:
                ratio = gcd(collection['positive'], collection['negative'])
                collection['ratio_positive'] = collection['positive'] // ratio
                collection['ratio_negative'] = collection['negative'] // ratio
                if collection['ratio_negative'] != 0:
                    collection['ratio_number'] = collection['ratio_positive'] / collection['ratio_negative']
                else:
                    collection['ratio_number'] = 0

        return {
            'word_cloud_trr': words_trr,
            'word_cloud_barometer': words_barometer,
            'themes_trr': {
                'positive_themes': positive_themes_trr,
                'negative_themes': negative_themes_trr,
            },
            'themes_barometer': {
                'positive_themes': positive_themes_barometer,
                'negative_themes': negative_themes_barometer,
            },
            'feedback_trr': {
                'value': feedback_trr['comments'],
                'comparison': self.__rating_change(feedback_trr['comments'], feedback_trr['lr_comments']),
            },
            'feedback_barometer': {
                'value': feedback_barometer['comments'],
                'comparison': self.__rating_change(feedback_barometer['comments'], feedback_barometer['lr_comments']),
            },
            'themes_ratio_trr': {'positive': themes_ratio_trr['ratio_positive'], 'negative': themes_ratio_trr['ratio_negative'], 'ratio_number': themes_ratio_trr['ratio_number']},
            'themes_ratio_barometer': {'positive': themes_ratio_barometer['ratio_positive'], 'negative': themes_ratio_barometer['ratio_negative'], 'ratio_number': themes_ratio_barometer['ratio_number']},
        }


    def format(self, data: dict):
        words_trr = []
        words_barometer = []
        themes_trr = {}
        themes_barometer = {}

        for word in data.get('word_cloud_trr', {}):
            res = {
                'name': word,
                'weight': data['word_cloud_trr'][word]['count'],
                'p': data['word_cloud_trr'][word]['positive'],
                'n': data['word_cloud_trr'][word]['negative']
            }
            if res['p'] > res['n']:
                res['color'] = '#25a65b'
            else:
                res['color'] = '#f84040'
            words_trr.append(res)

        for word in data.get('word_cloud_barometer', {}):
            res = {
                'name': word,
                'weight': data['word_cloud_barometer'][word]['count'],
                'p': data['word_cloud_barometer'][word]['positive'],
                'n': data['word_cloud_barometer'][word]['negative']
            }
            if res['p'] > res['n']:
                res['color'] = '#25a65b'
            else:
                res['color'] = '#f84040'
            words_barometer.append(res)

        themes_trr = {
            'positive_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in data.get('themes_trr', {}).get('positive_themes', [])],
            'negative_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in data.get('themes_trr', {}).get('negative_themes', [])],
        }

        themes_barometer = {
            'positive_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in data.get('themes_trr', {}).get('positive_themes', [])],
            'negative_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in data.get('themes_barometer', {}).get('negative_themes', [])],
        }

        return {
            'word_cloud_trr': words_trr,
            'word_cloud_barometer': words_barometer,
            'themes_trr': themes_trr,
            'themes_barometer': themes_barometer,
            'feedback_trr':  data['feedback_trr'],
            'feedback_barometer': data['feedback_barometer'],
            'themes_ratio_trr': data['themes_ratio_trr'],
            'themes_ratio_barometer': data['themes_ratio_barometer'],
        }
