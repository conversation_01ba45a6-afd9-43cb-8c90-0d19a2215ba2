from math import gcd
from api.clientarea.src.utils.cards.Card import Card


class PositiveNegativeRatio(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        ratio = 0
        values = {
            'total': 0,
            'positive': 0,
            'negative': 0,
        }

        for record in data:
            for theme in record.get('themes', []):
                values['total'] += 1
                if theme['pn'] and int(theme['pn']) > 0:
                    values['positive'] += 1
                elif theme['pn'] and int(theme['pn']) < 0:
                    values['negative'] += 1
        
        if values['positive'] == 0 and values['negative'] == 0:
            ratio_positive = 0
            ratio_negative = 0
        elif values['positive'] == 0:
            ratio_positive = 0
            ratio_negative = 1
        elif values['negative'] == 0:
            ratio_positive = 1
            ratio_negative = 0
        else:
            ratio = gcd(values['positive'], values['negative'])
            ratio_positive = values['positive'] // ratio
            ratio_negative = values['negative'] // ratio

        return {
            'positive': ratio_positive,
            'negative': ratio_negative,
        }