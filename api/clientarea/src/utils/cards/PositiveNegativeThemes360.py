from api.clientarea.src.utils.cards.Card import Card


class PositiveNegativeThemes360(Card):

    def __frequency(self, count, total):
        if total == 0:
            return 0
        frequency = (count / total) * 100
        return round(frequency, 2)


    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        themes_trr = {}
        themes_barometer = {}
        trr_csr_ids_by_round = {}
        total_mentions_trr = 0
        total_positive_mentions_trr = 0
        total_negative_mentions_trr = 0
        total_mentions_barometer = 0
        total_positive_mentions_barometer = 0
        total_negative_mentions_barometer = 0
        if not data:
            return {}

        # Note: data is ordered so that barometer responses are always before trr responses
        for record in data:
            is_barometer = record.get('is_barometer', False)
            collection = themes_trr if not is_barometer else themes_barometer
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            if is_barometer:
                # this is a barometer response, so we need to add the trr csr id
                trr_csr_ids_by_round[index] = record['linked_trr_survey_id']
            else:
                # now we're processing trr data, we need to check if the CSR id matches the barometer linked CSR id
                match_found = False
                for barometer_round_id, value in trr_csr_ids_by_round.items():
                    if record['customer_survey_round_id'] == value:
                        index = barometer_round_id
                        match_found = True
                        break
                if not match_found:
                    continue


            for theme in record.get('themes', []):
                id = theme['id']
                word = theme['l3']

                if not word:
                    continue
                if word == 'N/A':
                    # skip "N/A" (neutral) codes
                    continue

                if is_barometer:
                    total_mentions_barometer += 1
                else:
                    total_mentions_trr += 1
                if word not in collection:
                    collection[word] = {
                        'id': f'{record['Id']}-{word}-{id}',
                        'word': word,
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'responses': 0,
                        'rating': 0,
                        'rating_positive': 0,
                        'rating_negative': 0,
                        'rating_avg': 0,
                        'rating_positive_avg': 0,
                        'rating_negative_avg': 0,
                        'lr_positive': 0,
                        'lr_negative': 0,
                        'lr_responses': 0,
                        'lr_rating': 0,
                        'lr_rating_positive': 0,
                        'lr_rating_negative': 0,
                        'lr_rating_avg': 0,
                        'lr_rating_positive_avg': 0,
                        'lr_rating_negative_avg': 0,
                        'in_latest_round': False
                    }

                if self.rounds_barometer[index] == 'current':
                    collection[word]['in_latest_round'] = True
                    collection[word]['count'] += 1

                    collection[word]['responses'] += 1
                    collection[word]['rating'] += rating
                    collection[word]['rating_avg'] = collection[word]['rating'] / collection[word]['responses']

                    if int(theme['pn']) > 0:
                        if is_barometer:
                            total_positive_mentions_barometer += 1
                        else:
                            total_positive_mentions_trr += 1
                        collection[word]['positive'] += 1
                        collection[word]['rating_positive'] += rating
                        collection[word]['rating_postive_avg'] = collection[word]['rating_positive'] / collection[word]['positive']
                    elif int(theme['pn']) < 0:
                        if is_barometer:
                            total_negative_mentions_barometer += 1
                        else:
                            total_negative_mentions_trr += 1
                        collection[word]['negative'] += 1
                        collection[word]['rating_negative'] += rating
                        collection[word]['rating_negative_avg'] = collection[word]['rating_negative'] / collection[word]['negative']
                elif self.rounds_barometer[index] == 'previous':
                    collection[word]['lr_responses'] += 1
                    collection[word]['lr_rating'] += rating
                    collection[word]['lr_rating_avg'] = collection[word]['lr_rating'] / collection[word]['lr_responses']
                    if int(theme['pn']) > 0:
                        collection[word]['lr_positive'] += 1
                        collection[word]['lr_rating_positive'] += rating
                        collection[word]['lr_rating_positive_avg'] = collection[word]['lr_rating_positive'] / collection[word]['lr_positive']
                    elif int(theme['pn']) < 0:
                        collection[word]['lr_negative'] += 1
                        collection[word]['lr_rating_negative'] += rating
                        collection[word]['lr_rating_negative_avg'] = collection[word]['lr_rating_negative'] / collection[word]['lr_negative']

        # strip out themes with no responses
        themes_trr = {k: v for k, v in themes_trr.items() if v['in_latest_round']}
        themes_barometer = {k: v for k, v in themes_barometer.items() if v['in_latest_round']}
        
        # sort the themes and get the top 10
        positive_theme_trr = sorted(themes_trr.items(), key=lambda x: x[1]['positive'], reverse=True)[:10]
        negative_themes_trr = sorted(themes_trr.items(), key=lambda x: x[1]['negative'], reverse=True)[:10]
        most_mentioned_themes_trr = sorted(themes_trr.items(), key=lambda x: x[1]['count'], reverse=True)[:10]
        positive_themes_barometer = sorted(themes_barometer.items(), key=lambda x: x[1]['positive'], reverse=True)[:10]
        negative_themes_barometer = sorted(themes_barometer.items(), key=lambda x: x[1]['negative'], reverse=True)[:10]
        most_mentioned_themes_barometer = sorted(themes_barometer.items(), key=lambda x: x[1]['count'], reverse=True)[:10]

        return {
            'positive_theme_trr': positive_theme_trr,
            'negative_themes_trr': negative_themes_trr,
            'most_mentioned_themes_trr': most_mentioned_themes_trr,
            'positive_themes_barometer': positive_themes_barometer,
            'negative_themes_barometer': negative_themes_barometer,
            'most_mentioned_themes_barometer': most_mentioned_themes_barometer,
            'total_mentions_trr': total_mentions_trr,
            'total_positive_mentions_trr': total_positive_mentions_trr,
            'total_negative_mentions_trr': total_negative_mentions_trr,
            'total_mentions_barometer': total_mentions_barometer,
            'total_positive_mentions_barometer': total_positive_mentions_barometer,
            'total_negative_mentions_barometer': total_negative_mentions_barometer,
        }


    def format(self, data: dict):
        positive_themes_trr = data.get('positive_themes_trr', [])
        negative_themes_trr = data.get('negative_themes_trr', [])
        most_mentioned_themes_trr = data.get('most_mentioned_themes_trr', [])
        positive_themes_barometer = data.get('positive_themes_barometer', [])
        negative_themes_barometer = data.get('negative_themes_barometer', [])
        most_mentioned_themes_barometer = data.get('most_mentioned_themes_barometer', [])
        total_mentions_trr = data.get('total_mentions_trr', 0)
        total_positive_mentions_trr = data.get('total_positive_mentions_trr', 0)
        total_negative_mentions_trr = data.get('total_negative_mentions_trr', 0)
        total_mentions_barometer = data.get('total_mentions_barometer', 0)
        total_positive_mentions_barometer = data.get('total_positive_mentions_barometer', 0)
        total_negative_mentions_barometer = data.get('total_negative_mentions_barometer', 0)

        formatted_data = {
            'positive_themes_trr': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'frequency': self.__frequency(theme['positive'], total_positive_mentions_trr),
                'rating_avg': round(theme['rating_postive_avg'], 2),
                'movement': self.__rating_change(theme['rating_postive_avg'], theme['lr_rating_positive_avg'])
            } for word, theme in positive_themes_trr],
            'negative_themes_trr': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'frequency': self.__frequency(theme['negative'], total_negative_mentions_trr),
                'rating_avg': round(theme['rating_negative_avg'], 2),
                'movement': self.__rating_change(theme['rating_negative_avg'], theme['lr_rating_negative_avg'])
            } for word, theme in negative_themes_trr],
            'most_mentioned_themes_trr': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['count'],
                'frequency': self.__frequency(theme['count'], total_mentions_trr),
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in most_mentioned_themes_trr],
            'positive_themes_barometer': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'frequency': self.__frequency(theme['positive'], total_positive_mentions_barometer),
                'rating_avg': round(theme['rating_postive_avg'], 2),
                'movement': self.__rating_change(theme['rating_postive_avg'], theme['lr_rating_positive_avg'])
            } for word, theme in positive_themes_barometer],
            'negative_themes_barometer': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'frequency': self.__frequency(theme['negative'], total_negative_mentions_barometer),
                'rating_avg': round(theme['rating_negative_avg'], 2),
                'movement': self.__rating_change(theme['rating_negative_avg'], theme['lr_rating_negative_avg'])
            } for word, theme in negative_themes_barometer],
            'most_mentioned_themes_barometer': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['count'],
                'frequency': self.__frequency(theme['count'], total_mentions_barometer),
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in most_mentioned_themes_barometer],
        }
    
        return formatted_data
