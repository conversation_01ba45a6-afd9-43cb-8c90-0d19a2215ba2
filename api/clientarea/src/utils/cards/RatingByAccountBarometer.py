import copy
from api.clientarea.src.utils.cards.Card import Card


class RatingByAccountBarometer(Card):

    DEFAULT_DATA = {
        'name': '',
        'id': '',
        'responses': 0,
        'rating': 0,
        'rating_avg': 0,
        'response_rate': 0,
        'lr_responses': 0,
        'lr_rating': 0,
        'lr_rating_avg': 0,
        'total_surveyed': 0,
        'in_latest_round': False,
    }


    def __calculate_metric_movement(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = this_round - last_round
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        response_rate = round((responses / total) * 100, 2)
        response_rate = int(response_rate) if response_rate.is_integer() else response_rate
        return response_rate


    def calc_aggregate_metrics(self, responses: list, dimension_name: str, round_index: str, rounds_lookup: dict) -> list:
        grouped_data = {}
        id_property = f'{dimension_name}_id'
        name_property = f'{dimension_name}_name'

        for record in responses:
            index = record[round_index]

            dimension_id = record.get(id_property)
            name = record.get(name_property)
            if not dimension_id:
                dimension_id = 'unknown'
                name = 'Unknown'

            if name not in grouped_data:
                grouped_data[name] = copy.copy(self.DEFAULT_DATA)
                grouped_data[name]['name'] = name
                grouped_data[name]['id'] = dimension_id

            aggregate_data = grouped_data[name]

            # if it's the latest round, count-up the total surveyed
            if rounds_lookup[index] == 'current':
                aggregate_data['total_surveyed'] += 1

            if rounds_lookup[index] == 'current':
                if record['responded']:
                    aggregate_data['responses'] += 1
                    aggregate_data['rating'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                    aggregate_data['rating_avg'] = aggregate_data['rating'] / aggregate_data['responses']
                aggregate_data['in_latest_round'] = True
            elif rounds_lookup[index] == 'previous':
                if record['responded']:
                    aggregate_data['lr_responses'] += 1
                    aggregate_data['lr_rating'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                    aggregate_data['lr_rating_avg'] = aggregate_data['lr_rating'] / aggregate_data['lr_responses']
            
        # strip out any grouping not in the latest round
        grouped_data = {k: v for k, v in grouped_data.items() if v['in_latest_round']}
        # sort the data by rating_avg
        sorted_data = sorted(grouped_data.values(), key=lambda x: x['rating_avg'], reverse=True)
        return sorted_data


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        groupings = {
            'account': [], 
        }
        if not data:
            return groupings
        
        for key in groupings.keys():
            groupings[key] = self.calc_aggregate_metrics(data, key, round_index, self.rounds_barometer)

        return groupings
    

    def format(self, data: dict):
        formatted_groupings = {}
        for dimension, data in data.items():
            formatted_data = [{
                'id': x['id'],
                'name': x['name'],
                'responses': x['responses'],
                'rating_avg': round(x['rating_avg'], 2),
                'rating_delta': round(x['rating_avg'] - x['lr_rating_avg'], 2),
                'rating_delta_percent': f'{self.__calculate_metric_movement(x['rating_avg'], x['lr_rating_avg'])}%',
                'response_rate': f'{self.__calculate_response_rate(x['responses'], x['total_surveyed'])}%',
                'total_surveyed': x['total_surveyed'],
            } for x in data]
            # ignore any groupings that only have an entry for 'unknown'
            if len(formatted_data) == 1 and formatted_data[0]['id'] == 'unknown':
                continue
            formatted_groupings[dimension] = formatted_data

        return formatted_groupings
