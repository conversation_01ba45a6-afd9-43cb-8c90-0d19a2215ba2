from api.clientarea.src.utils.cards.Card import Card


class RatingDistributionTrendBarometer(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):    
        records = {}  
        all_responses = []  
        barometer_norms = {}
        if self.barometer_norm:
            barometer_norm_labels = [
                'rating_distribution_optout',
                'rating_distribution_1_4',
                'rating_distribution_5_6',
                'rating_distribution_7',
                'rating_distribution_8',
                'rating_distribution_9_10',
            ]
            norm_breakdown_barometer = [float(self.barometer_norm.get(label, 0)) for label in barometer_norm_labels]
            barometer_norms['round_name'] = 'Norm'
            barometer_norms['response'] = norm_breakdown_barometer
        
        for record in data:
            round_id = record[round_index]
            round_name = record['round_name']

            if self.rounds_barometer[round_id] == 'current':
                round_name = 'Latest Round'
            elif self.rounds_barometer[round_id] == 'previous':
                round_name = f'{round_name} (Previous Round)'

            if round_id not in records:
                records[round_id] = {
                    'brackets': {
                        'optout': 0,
                        '1-4': 0,
                        '5-6': 0,
                        '7': 0,
                        '8': 0,
                        '9-10': 0,
                    },
                    'round_name': round_name,
                    'total_ratings': 0
                }
            
            # handle optout case first
            if record['contact_opt_out']:
                records[round_id]['brackets']['optout'] += 1
                records[round_id]['total_ratings'] += 1
                continue
        
            if record['responded']:
                records[round_id]['total_ratings'] += 1
                rating = 0 if not record.get('rating', None) else record.get('rating', 0)
                
                if rating >= 0 and rating < 5:
                    records[round_id]['brackets']['1-4'] += 1
                elif rating >= 5 and rating < 7:
                    records[round_id]['brackets']['5-6'] += 1
                elif rating >= 7 and rating < 8:
                    records[round_id]['brackets']['7'] += 1
                elif rating >= 8 and rating < 9:
                    records[round_id]['brackets']['8'] += 1
                elif rating >= 9 and rating < 10:
                    records[round_id]['brackets']['9-10'] += 1

        # order rounds by round_id
        records = dict(sorted(records.items()))

        for r in records.values():
            response = []
            round_name = r['round_name']
            brackets = r['brackets']
            total_ratings = r['total_ratings']
            total_percentage = 0

            for idx, key in enumerate(brackets):
                value = brackets[key]
                percentage = round((value / total_ratings) * 100, 0) if total_ratings > 0 else 0
                if idx == len(brackets) - 1 and percentage != 0:
                    percentage = 100 - total_percentage
                total_percentage += percentage
                response.append(percentage)
            
            all_responses.append({
                'round_name': round_name,
                'response': response
            })

        if len(all_responses) == 0:
            return {
                'latest': {},
                'previous': {}
            }

        return {
            'latest': all_responses[0],
            'norms': barometer_norms,
            'previous': all_responses[1] if len(all_responses) > 1 else {},
        }