from api.clientarea.src.utils.cards.Card import Card


class TotalComments(Card):

    def __calculate_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        comments = 0
        lr_comments = 0
    
        for record in data:
            index = record[round_index]
            if rounds[index] == 'current':
                count += 1
                if record['feedback']:
                    comments += 1
            elif rounds[index] == 'previous':
                if record['feedback']:
                    lr_comments += 1
        
        if not count:
            return {
                'value': 0,
                'comparison': 0,
            }

        return {
            'value': comments,
            'comparison': self.__calculate_change(comments, lr_comments),
        }