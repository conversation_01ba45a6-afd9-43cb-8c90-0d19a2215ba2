from api.clientarea.src.utils.cards.Card import Card


class ThemeWordCloud(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        words = {}

        if not data:
            return words

        for record in data:
            for theme in record.get('themes', []):
                word = theme['l3']

                if not word:
                    continue
                
                words.setdefault(word, {
                    'count': 0,
                    'positive': 0,
                    'negative': 0,
                })

                words[word]['count'] += 1
                if int(theme['pn']) > 0:
                    words[word]['positive'] += 1
                elif int(theme['pn']) < 0:
                    words[word]['negative'] += 1

        return words
    
    def format(self, data: dict):
        words = []
        for word in data:
            res = {
                'name': word,
                'weight': data[word]['count'],
                'p': data[word]['positive'],
                'n': data[word]['negative']
            }
            if res['p'] > res['n']:
                res['color'] = '#24a65a'
            else:
                res['color'] = '#be3932'
            words.append(res)

        return words