from api.clientarea.src.utils.cards.Card import Card


class RelationshipQuadrants360(Card):

    def calc_aggregate_metrics(self, responses: list, dimension_name: str) -> list:
        grouped_data = {}
        id_property = f'{dimension_name}_id'
        name_property = f'{dimension_name}_name'

        if dimension_name == 'market_country':
            # country name is stored as market_country, not market_country_name
            # TODO should probably fix this in the sync response agent, but will mean updating dashboard config in db to match
            name_property = 'market_country'

        for record in responses:
            suffix = '_trr' if not record.get('is_barometer') else '_barometer'

            dimension_id = record.get(id_property)
            name = record.get(name_property)
            if not dimension_id:
                dimension_id = 'unknown'
                name = 'Unknown'

            if name not in grouped_data:
                grouped_data[name] = {
                    'name': name,
                    'id': dimension_id,
                    'responses_trr': 0,
                    'responses_barometer': 0,
                    'rating_trr': 0,
                    'rating_barometer': 0,
                    'rating_avg_trr': 0,
                    'rating_avg_barometer': 0,
                    'in_latest_round': False,
                }

            aggregate_data = grouped_data[name]

            aggregate_data['responses'+suffix] += 1
            aggregate_data['rating'+suffix] += 0 if not record.get('rating', None) else record.get('rating', 0)
            aggregate_data['rating_avg'+suffix] = aggregate_data['rating'+suffix] / aggregate_data['responses'+suffix]
            aggregate_data['in_latest_round'] = True

        # sort the data by rating_avg
        sorted_data = sorted(grouped_data.values(), key=lambda x: x['rating_avg_trr'], reverse=True)
        return sorted_data


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        groupings = {
            'account': [], 
            'market_country': [],
        }
        if not data:
            return groupings
        
        for key in groupings.keys():
            groupings[key] = self.calc_aggregate_metrics(data, key)

        return {'groupings': groupings}
    

    def format(self, data: dict):
        result = {
            'groupings': {},
            'norm_trr': self.trr_norm.get('ccr', 5.0),
            'norm_barometer': self.barometer_norm.get('ccr', 5.0),
        }
        for dimension, data in data['groupings'].items():
            formatted_data = [{
                'id': x['id'],
                'name': x['name'],
                'x': round(x['rating_avg_trr'], 2),
                'y': round(x['rating_avg_barometer'], 2),
            } for x in data]
            # ignore any groupings that only have an entry for 'unknown'
            if len(formatted_data) == 1 and formatted_data[0]['id'] == 'unknown':
                continue
            # filter out any grouping values that don't have both trr and barometer values
            formatted_data = [x for x in formatted_data if x['x'] > 0 and x['y'] > 0]
            result['groupings'][dimension] = formatted_data
        return result
