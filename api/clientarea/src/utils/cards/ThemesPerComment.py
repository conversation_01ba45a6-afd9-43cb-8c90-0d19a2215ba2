from api.clientarea.src.utils.cards.Card import Card


class ThemesPerComment(Card):

    def __calculate_average_themes(self, themes, responses):
        if not themes or not responses:
            return 0
        return round(themes / responses, 2)

    def __calculate_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = 0
        themes = 0
        lr_themes = 0

        for record in data:
            index = record[round_index]
            response_themes = record.get('themes', [])

            if not response_themes:
                continue

            if rounds[index] == 'current': 
                responses += 1

            for theme in response_themes:
                word = theme['l3']

                if not word:
                    continue

                if rounds[index] == 'current':
                    themes += 1
                elif rounds[index] == 'previous':
                    lr_themes += 1

        return {
            'value': self.__calculate_average_themes(themes, responses),
            'comparison': self.__calculate_change(themes, lr_themes),
        }