from api.clientarea.src.utils.cards.Card import Card


class ContactRating(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        participation_count = 0

        total_ratings = 0
        current_rating = 0
        previous_rating = 0
        average_rating = 0
        top_positive_theme = ''
        top_negative_theme = ''
        top_mentioned_theme = ''
        ratings = {}

        positive_themes = {}
        negative_themes = {}
        mentioned_themes = {}
        
        for record in data:
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            participation_count += 1

            if not record.get('responded', False):
                continue
            
            round_id = record[round_index]
            count += 1
            total_ratings += rating

            if round_id not in ratings:
                ratings[round_id] = 0

            ratings[round_id] = rating

            for theme in record.get('themes', []):
                word = theme['l3']

                if not word:
                    continue

                if int(theme['pn']) > 0:
                    if word not in positive_themes:
                        positive_themes[word] = 0
                    positive_themes[word] += 1
                elif int(theme['pn']) < 0:
                    if word not in negative_themes:
                        negative_themes[word] = 0
                    negative_themes[word] += 1
                
                if word not in mentioned_themes:
                    mentioned_themes[word] = 0
                mentioned_themes[word] += 1
        
        if not count:
            return {}
        
        # sort ratings by date
        ratings = dict(sorted(ratings.items()))
        
        if len(ratings.keys()) > 0:
            key = next(iter(ratings))
            current_rating = ratings[key]
        if len(ratings.keys()) > 1:
            iterator = iter(ratings)
            next(iterator)
            key = next(iterator)
            previous_rating = ratings[key]

        if total_ratings:
            average_rating = round(total_ratings / count, 2)

        sorted_positive_themes = dict(sorted(positive_themes.items(), key=lambda item: item[1], reverse=True))
        sorted_negative_themes = dict(sorted(negative_themes.items(), key=lambda item: item[1], reverse=True))
        sorted_mentioned_themes = dict(sorted(mentioned_themes.items(), key=lambda item: item[1], reverse=True))

        top_positive_theme_list = list(sorted_positive_themes.keys())[:1]
        top_negative_theme_list = list(sorted_negative_themes.keys())[:1]
        top_mentioned_theme_list = list(sorted_mentioned_themes.keys())[:1]

        if top_positive_theme_list:
            top_positive_theme = top_positive_theme_list[0]
        if top_negative_theme_list:
            top_negative_theme = top_negative_theme_list[0]
        if top_mentioned_theme_list:
            top_mentioned_theme = top_mentioned_theme_list[0]
        
        return {
            'current_rating': current_rating,
            'previous_rating': previous_rating,
            'average_rating': average_rating,
            'participation': participation_count,
            'feedback_rate': count,
            'feedback_rate_percentage': round(count / participation_count * 100),
            'most_mentioned_theme': top_mentioned_theme,
            'most_positive_theme':  top_positive_theme,
            'most_negative_theme': top_negative_theme,
        }
