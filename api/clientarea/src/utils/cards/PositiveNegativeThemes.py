from api.clientarea.src.utils.cards.Card import Card


class PositiveNegativeThemes(Card):

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        themes = {}

        if not data:
            return themes
        
        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            for theme in record.get('themes', []):
                id = theme['id']
                word = theme['l3']

                if not word:
                    continue
                
                if word not in themes:
                    themes[word] = {
                        'id': f'{record['Id']}-{word}-{id}',
                        'word': word,
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                        'lr_responses': 0,
                        'lr_rating': 0,
                        'lr_rating_avg': 0,
                        'in_latest_round': False
                    }

                if rounds[index] == 'current':
                    themes[word]['in_latest_round'] = True
                    themes[word]['count'] += 1

                    themes[word]['responses'] += 1
                    themes[word]['rating'] += rating
                    themes[word]['rating_avg'] = themes[word]['rating'] / themes[word]['responses']

                    if int(theme['pn']) > 0:
                        themes[word]['positive'] += 1
                    elif int(theme['pn']) < 0:
                        themes[word]['negative'] += 1
                elif rounds[index] == 'previous':
                    themes[word]['lr_responses'] += 1
                    themes[word]['lr_rating'] += rating
                    themes[word]['lr_rating_avg'] = themes[word]['lr_rating'] / themes[word]['lr_responses']

        # strip out themes with no responses
        themes = {k: v for k, v in themes.items() if v['in_latest_round']}
        
        # sort the themes
        positive_themes = sorted(themes.items(), key=lambda x: x[1]['positive'], reverse=True)[:5]
        negative_themes = sorted(themes.items(), key=lambda x: x[1]['negative'], reverse=True)[:5]
        most_mentioned_themes = sorted(themes.items(), key=lambda x: x[1]['count'], reverse=True)[:5]
                
        return {
            'positive_themes': positive_themes,
            'negative_themes': negative_themes,
            'most_mentioned_themes': most_mentioned_themes,
        }

    def format(self, data: dict):
        positive_themes = data.get('positive_themes', [])
        negative_themes = data.get('negative_themes', [])
        most_mentioned_themes = data.get('most_mentioned_themes', [])

        return {
            'positive_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in positive_themes],
            'negative_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in negative_themes],
            'most_mentioned_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['count'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in most_mentioned_themes]
        }
    
