from api.clientarea.src.utils.cards.Card import Card


class SentimentTrend(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        response = {}
        filtered_theme = filters.get('theme_categories', [])
        
        if filtered_theme:
            filtered_theme = filtered_theme[0]

        for record in data:
            round_id = record[round_index]

            if round_id not in response:
                round_name = record['round_name']
                if round_id == 0:
                    round_name = 'Latest Round'
                if round_id == 1:
                    round_name = f'{round_name} (Previous Round)'
                response[round_id] = {
                    'round_name': round_name,
                    'positive': 0,
                    'negative': 0,
                    'mixed': 0,
                }

            for theme in record.get('themes'):
                node = theme['l3']

                if node.lower() == filtered_theme.lower():
                    if int(theme['pn']) > 0:
                        response[round_id]['positive'] += 1
                    elif int(theme['pn']) < 0:
                       response[round_id]['negative'] += 1
                    else:
                        response[round_id]['mixed'] += 1
        
        # sort the data by round_id
        response = dict(sorted(response.items(), reverse=True))

        # morph data_gouped into a list of lists
        response = list(response.values())
        
        return response

    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data],
            'series': {
                'positive': [round['positive'] for round in data],
                'negative': [round['negative'] for round in data],
                'mixed': [round['mixed'] for round in data],
            }
        }