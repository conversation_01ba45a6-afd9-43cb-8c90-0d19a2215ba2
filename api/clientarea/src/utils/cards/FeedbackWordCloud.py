from api.clientarea.src.utils.cards.Card import Card


class FeedbackWordCloud(Card):

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        words = {}
        themes = {}

        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            count += 1

            for theme in record.get('themes', []):
                word = theme['l3']

                if not word:
                    continue
                
                if word not in words:
                    words[word] = {
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'in_latest_round': False
                    }

                if word not in themes:
                    themes[word] = {
                        'id': f'{record['Id']}-{word}',
                        'word': word,
                        'count': 0,
                        'positive': 0,
                        'negative': 0,
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                        'lr_responses': 0,
                        'lr_rating': 0,
                        'lr_rating_avg': 0,
                        'in_latest_round': False
                    }

                if rounds[index] == 'current':
                    words[word]['in_latest_round'] = True
                    words[word]['count'] += 1
                    themes[word]['in_latest_round'] = True
                    themes[word]['count'] += 1
                    themes[word]['responses'] += 1
                    themes[word]['rating'] += rating
                    themes[word]['rating_avg'] = themes[word]['rating'] / themes[word]['responses']
                                    
                    if int(theme['pn']) > 0:
                        themes[word]['positive'] += 1
                        words[word]['positive'] += 1
                    elif int(theme['pn']) < 0:
                        themes[word]['negative'] += 1
                        words[word]['negative'] += 1
                elif rounds[index] == 'previous':
                    themes[word]['lr_responses'] += 1
                    themes[word]['lr_rating'] += rating
                    themes[word]['lr_rating_avg'] = themes[word]['lr_rating'] / themes[word]['lr_responses']
        
        if not count:
            return {}
        
        # strip out any themes that didn't appear in the latest round
        themes = {k: v for k, v in themes.items() if v['in_latest_round']}
        words = {k: v for k, v in words.items() if v['in_latest_round']}
        
        # sort the themes
        positive_themes = sorted(themes.items(), key=lambda x: x[1]['positive'], reverse=True)[:10]
        negative_themes = sorted(themes.items(), key=lambda x: x[1]['negative'], reverse=True)[:10]

        return {
            'word_cloud': words,
            'themes': {
                'positive_themes': positive_themes,
                'negative_themes': negative_themes,
            }
        }
    
    def format(self, data: dict):
        words = []
        themes = {}
        positive_themes = data.get('themes', {}).get('positive_themes', [])
        negative_themes = data.get('themes', {}).get('negative_themes', [])

        for word in data.get('word_cloud', {}):
            res = {
                'name': word,
                'weight': data['word_cloud'][word]['count'],
                'p': data['word_cloud'][word]['positive'],
                'n': data['word_cloud'][word]['negative']
            }
            if res['p'] > res['n']:
                res['color'] = '#25a65b'
            else:
                res['color'] = '#f84040'
            words.append(res)

        themes = {
            'positive_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['positive'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in positive_themes],
            'negative_themes': [{
                'id': theme['id'],
                'name': word,
                'mentions': theme['negative'],
                'rating_avg': round(theme['rating_avg'], 2),
                'movement': self.__rating_change(theme['rating_avg'], theme['lr_rating_avg'])
            } for word, theme in negative_themes],
        }

        return {
            'word_cloud': words,
            'themes': themes
        }
