from api.clientarea.src.utils.cards.Card import Card


class TopBottomAccounts(Card):

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    
    
    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        accounts = {}

        for record in data:
            index = record[round_index]
            account_id = record.get('account_id')
            account_name = record.get('account_name')
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            if account_id not in accounts:
                accounts[account_id] = {
                    'id': account_id,
                    'name': account_name,
                    'responses': 0,
                    'rating': 0,
                    'rating_avg': 0,
                    'lr_responses': 0,
                    'lr_rating': 0,
                    'lr_rating_avg': 0,
                    'in_latest_round': False,
                }
            if rounds[index] == 'current':
                accounts[account_id]['responses'] += 1
                accounts[account_id]['rating'] += rating
                accounts[account_id]['rating_avg'] = accounts[account_id]['rating'] / accounts[account_id]['responses']
                accounts[account_id]['in_latest_round'] = True
            elif rounds[index] == 'previous':
                accounts[account_id]['lr_responses'] += 1
                accounts[account_id]['lr_rating'] += rating
                accounts[account_id]['lr_rating_avg'] = accounts[account_id]['lr_rating'] / accounts[account_id]['lr_responses']

        # remove accounts with not in the latest round
        accounts = {k: v for k, v in accounts.items() if v['in_latest_round']}
        
        # sort the accounts by the average rating
        top_accounts = sorted(accounts.items(), key=lambda x: x[1]['rating_avg'], reverse=True)[:5]
        bottom_accounts = sorted(accounts.items(), key=lambda x: x[1]['rating_avg'])[:5]

        return {
            'top_accounts': top_accounts,
            'bottom_accounts': bottom_accounts,
        }

    def format(self, data: dict):
        top_accounts = data.get('top_accounts', [])
        bottom_accounts = data.get('bottom_accounts', [])

        return {
            'top_accounts': [{
                'id': account_id,
                'name': account['name'],
                'responses': account['responses'],
                'rating_avg': round(account['rating_avg'], 2),
                'movement': self.__rating_change(account['rating_avg'], account['lr_rating_avg']),
            } for account_id, account in top_accounts],
            'bottom_accounts': [{
                'id': account_id,
                'name': account['name'],
                'responses': account['responses'],
                'rating_avg': round(account['rating_avg'], 2),
                'movement': self.__rating_change(account['rating_avg'], account['lr_rating_avg']),
            } for account_id, account in bottom_accounts],
        }