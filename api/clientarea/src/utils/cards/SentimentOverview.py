from api.clientarea.src.utils.cards.Card import Card


class SentimentOverview(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = {}

        for record in data:
            round_id = record[round_index]
            sentiment = {
                'p': 0,
                'n': 0,
                'm': 0,
            }

            if round_id not in responses:
                round_name = record['round_name']
                if round_id == 0:
                    round_name = 'Latest Round'
                if round_id == 1:
                    round_name = f'{round_name} (Previous Round)'
                responses[round_id] = {
                    'round_name': round_name,
                    'positive': 0,
                    'negative': 0,
                    'mixed': 0,
                }

            for theme in record.get('themes'):
                if theme['pn'] and int(theme['pn']) > 0:
                    sentiment['p'] += 1
                elif theme['pn'] and int(theme['pn']) < 0:
                    sentiment['n'] += 1
                elif theme['pn'] and int(theme['pn']) == 0:
                    sentiment['m'] += 1
            
            if sentiment['p'] > sentiment['n'] and sentiment['p'] > sentiment['m']:
                responses[round_id]['positive'] += 1
            elif sentiment['n'] > sentiment['p'] and sentiment['n'] > sentiment['m']:
                responses[round_id]['negative'] += 1
            elif sentiment['m'] > sentiment['p'] and sentiment['m'] > sentiment['n']:
                responses[round_id]['mixed'] += 1

        # sort the data by round_id
        responses = dict(sorted(responses.items(), reverse=True))

        # morph data_gouped into a list of lists
        responses = list(responses.values())

        return responses

    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data],
            'series': {
                'positive': [round['positive'] for round in data],
                'negative': [round['negative'] for round in data],
                'mixed': [round['mixed'] for round in data]
            }
        }