from api.clientarea.src.utils.cards.Card import Card


class SentimentDistribution(Card):

    def __round_to_100(self, values):
        rounded_values = [round(value, 2) for value in values]
        total = sum(rounded_values)
    
        difference = round(100 - total, 2)
    
        if difference != 0:
            max_index = max(range(len(rounded_values)), key=lambda i: rounded_values[i])
            rounded_values[max_index] += difference
            rounded_values[max_index] = round(rounded_values[max_index], 2)
        
        return rounded_values
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        total_sentiment = {
            'responses': 0,
            'response_with_themes': 0,
            'positive': 0,
            'positive_count': 0,
            'positive_percentage': 0,
            'negative': 0,
            'negative_count': 0,
            'negative_percentage': 0,
            'mixed': 0,
            'mixed_count': 0,
            'mixed_percentage': 0,
        }

        for record in data:
            total_sentiment['responses'] += 1

            if record['sentiment']:
                total_sentiment['response_with_themes'] += 1

                if record['sentiment'] == 'p':
                    total_sentiment['positive'] += 1
                elif record['sentiment'] == 'n':
                    total_sentiment['negative'] += 1
                elif record['sentiment'] == 'm':
                    total_sentiment['mixed'] += 1

            for theme in record.get('themes'):
                if not theme.get('pn'):
                    # null or empty string
                    continue
                if int(theme['pn']) > 0:
                    total_sentiment['positive_count'] += 1
                elif int(theme['pn']) < 0:
                    total_sentiment['negative_count'] += 1
                elif int(theme['pn']) == 0:
                    total_sentiment['mixed_count'] += 1

        if total_sentiment['response_with_themes']:
            rounded_values = self.__round_to_100([
                (total_sentiment['positive'] / total_sentiment['response_with_themes'] * 100),
                (total_sentiment['negative'] / total_sentiment['response_with_themes'] * 100),
                (total_sentiment['mixed'] / total_sentiment['response_with_themes'] * 100),
            ])

            total_sentiment['positive_percentage'] = rounded_values[0]
            total_sentiment['negative_percentage'] = rounded_values[1]
            total_sentiment['mixed_percentage'] = rounded_values[2]

        return total_sentiment
    
    def format(self, data: dict):
        return {
            'positive_count': data['positive_count'],
            'positive_percentage': data['positive_percentage'],
            'negative_count': data['negative_count'],
            'negative_percentage': data['negative_percentage'],
            'mixed_count': data['mixed_count'],
            'mixed_percentage': data['mixed_percentage']
        }