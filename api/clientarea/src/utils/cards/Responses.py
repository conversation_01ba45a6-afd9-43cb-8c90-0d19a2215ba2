import datetime
from api.clientarea.src.utils.cards.Card import Card


class Responses(Card):

    def __calculate_response_time(self, response_date: str):
        if not response_date:
            return ''

        start = datetime.datetime.strptime(response_date, "%Y-%m-%dT%H:%M:%S.%f%z").replace(tzinfo=None)
        end = datetime.datetime.now()
        difference = end - start

        total_seconds = difference.total_seconds()

        if total_seconds < 3600:
            # Less than an hour
            output = f"{total_seconds // 60} Mins"
        elif total_seconds < 86400:
            # Less than a day
            value = int(total_seconds // 3600)
            output = f"{value} Hrs"
        elif total_seconds < 604800:
            # Less than a week
            output = f"{difference.days} Days"
        else:
            # One week or more
            weeks = difference.days // 7
            output = f"{weeks} Wks"

        return output

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = []
        count = 0

        for record in data:
            count += 1
            key = f'response_{count}'
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            responded_date = self.__calculate_response_time(record.get('response_date'))

            #if record.get("team_market_name", None):
            #    market_name = record['team_market_name']
            #else:
            #    market_name = record['market_name']
            market_name = record.get('market_country', '')

            responses.append({
                'key': key,
                'rating': rating,
                'name': record.get('contact_name', ''),
                'contact_type': record.get('contact_type', ''),
                'account': record.get('account_name', ''),
                'market': market_name,
                'agency': record.get('client_name', ''),
                'team': record.get('team_name', ''),
                'feedback': record.get('feedback', ''),
                'when': responded_date,
                'response_date': record.get('response_date', ''),
                'contact_id': record.get('contact_id', ''),
                'responded': record.get('responded', False),
            })

        # sort responses by response_date
        responses = sorted(responses, key=lambda x: x['response_date'] or '', reverse=True)

        return responses
