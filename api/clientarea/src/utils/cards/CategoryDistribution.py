from api.clientarea.src.utils.cards.Card import Card


class CategoryDistribution(Card):

    def __round_to_100(self, values):
        rounded_values = [round(value, 2) for value in values]
        total = sum(rounded_values)
    
        difference = round(100 - total, 2)
    
        if difference != 0:
            max_index = max(range(len(rounded_values)), key=lambda i: rounded_values[i])
            rounded_values[max_index] += difference
            rounded_values[max_index] = round(rounded_values[max_index], 2)
        
        return rounded_values
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        categories = {
            'total_feedback': 0,
            'feedback_relational': 0,
            'feedback_relational_percentage': 0,
            'feedback_transactional': 0,
            'feedback_transactional_percentage': 0,
            'feedback_general': 0,
            'feedback_general_percentage': 0,
        }

        for record in data:
            count += 1
            for theme in record.get('themes', []):
                category = theme['l1']

                if not category:
                    continue

                categories['total_feedback'] += 1

                if category == 'Relational':
                    categories['feedback_relational'] += 1
                elif category == 'Transactional':
                    categories['feedback_transactional'] += 1
                else:
                    categories['feedback_general'] += 1
        
        if not count:
            return categories
        
        if categories['total_feedback']:
            rounded_values = self.__round_to_100([
                (categories['feedback_relational'] / categories['total_feedback'] * 100),
                (categories['feedback_transactional'] / categories['total_feedback'] * 100),
                (categories['feedback_general'] / categories['total_feedback'] * 100),
            ])

            categories['feedback_relational_percentage'] = rounded_values[0]
            categories['feedback_transactional_percentage'] = rounded_values[1]
            categories['feedback_general_percentage'] = rounded_values[2]

        return categories