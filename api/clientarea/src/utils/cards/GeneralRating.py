from api.clientarea.src.utils.cards.Card import Card


class GeneralRating(Card):

    def __calculate_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        avg_rating = 0
        relational_ratings = 0
        total_ratings = 0
        lr_avg_rating = 0
        lr_relational_ratings = 0
        lr_total_ratings = 0

        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            for theme in record.get('themes', []):
                category = theme['l1']

                if not category:
                    continue

                if category != 'Relational' and category != 'Transactional':
                    if rounds[index] == 'current':
                        total_ratings += 1
                        relational_ratings += rating
                    elif rounds[index] == 'previous':
                        lr_total_ratings += 1
                        lr_relational_ratings += rating
                    break
                    
        if total_ratings and relational_ratings:
            avg_rating = round((relational_ratings / total_ratings), 2)
        
        if lr_total_ratings and lr_relational_ratings:
            lr_avg_rating = round((lr_relational_ratings / lr_total_ratings), 2)

        return {
            'value': avg_rating,
            'comparison': self.__calculate_change(avg_rating, lr_avg_rating),
        }