from api.clientarea.src.utils.cards.Card import Card


class ResponseRateTrend(Card):

    def _response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 100, 2)


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = {}

        for record in data:
            round_id = record[round_index]

            if round_id not in responses:
                round_name = record['round_name']
                if self.rounds_barometer[round_id] == 'current':
                    round_name = 'Latest Round'
                if self.rounds_barometer[round_id] == 'previous':
                    round_name = f'{round_name}'
                responses[round_id] = {
                    'round_name': round_name,
                    'outreach': 0,
                    'responses': 0,
                    'response_rate': 0,
                }
            responses[round_id]['outreach'] += 1

            if record['responded']:
                responses[round_id]['responses'] += 1

            # calculate response rate
            responses[round_id]['response_rate'] = self._response_rate(responses[round_id]['responses'], responses[round_id]['outreach'])

        # sort the data by round_id
        responses = dict(sorted(responses.items(), reverse=True))

        # append norm
        if self.barometer_norm:
            responses['norm'] = {    
                'round_name': 'Norm',
                'response_rate': float(norm.get('response_rate', 0)),
            }

        return {
            'responses': responses
        }


    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data['responses'].values()],
            'series': [
                {
                    'name': 'Responders',
                    'data': [data['response_rate'] for data in data['responses'].values()],
                },
                {
                    'name': 'Non-Responders',
                    'data': [(100.0 - data['response_rate']) for data in data['responses'].values()],
                }
            ]
        }
