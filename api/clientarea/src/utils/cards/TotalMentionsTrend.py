from api.clientarea.src.utils.cards.Card import Card


class TotalMentionsTrend(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = {}
        filtered_theme = filters.get('theme_categories', [])
        
        if filtered_theme:
            filtered_theme = filtered_theme[0]

        for record in data:
            round_id = record[round_index]

            if round_id not in responses:
                round_name = record['round_name']
                if round_id == 0:
                    round_name = 'Latest Round'
                if round_id == 1:
                    round_name = f'{round_name} (Previous Round)'
                responses[round_id] = {
                    'round_name': round_name,
                    'mentions': 0,
                }

            for theme in record.get('themes'):
                node = theme['l3']

                if node.lower() == filtered_theme.lower():
                    responses[round_id]['mentions'] += 1
        
        # sort the data by round_id
        responses = dict(sorted(responses.items(), reverse=True))

        # morph data_gouped into a list of lists
        responses = list(responses.values())

        return responses

    def format(self, data: dict):
        return {
            'categories': [round['round_name'] for round in data],
            'series': [data['mentions'] for data in data],
        }