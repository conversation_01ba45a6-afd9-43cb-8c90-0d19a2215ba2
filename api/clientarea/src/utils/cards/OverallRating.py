from api.clientarea.src.utils.cards.Card import Card


class OverallRating(Card):

    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 100, 2)
    

    def __calculate_metric_movement(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = this_round - last_round
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def __calculate_point_difference(self, this_round, last_round):
        if this_round == 0:
            return 0
        return round(this_round - last_round, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        latest_round_total_surveyed = 0
        latest_round_responses = 0
        latest_round_average_rating = 0
        latest_round_total_rating = 0
        last_round_total_surveyed = 0
        last_round_responses = 0
        last_round_response_rate = 0
        last_round_average_rating = 0
        last_round_total_rating = 0
        rating_movement = 0
        response_movement = 0
        rating_norm = 0
        response_norm = 0

        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            # latest round metrics
            if rounds[index] == 'current':
                latest_round_total_surveyed += 1

                if record['responded']:
                    latest_round_responses += 1
                    latest_round_total_rating += rating
            
            # last round metrics
            if rounds[index] == 'previous':
                last_round_total_surveyed += 1

                if record['responded']:
                    last_round_responses += 1
                    last_round_total_rating += rating
        
        if latest_round_responses == 0:
            return {'rating': 0, 'response_rate': 0, 'responses': 0, 'total_surveyed': 0, 'rating_movement': 0, 'response_movement': 0, 'last_round_rating': 0, 'last_round_responses': 0, 'last_round_total_surveyed': 0, 'rating_norm': 0, 'response_norm': 0}

        latest_round_average_rating = round(latest_round_total_rating / latest_round_responses, 2)
        latest_round_response_rate = self.__calculate_response_rate(latest_round_responses, latest_round_total_surveyed)
        
        if last_round_responses > 0:
            last_round_average_rating = round(last_round_total_rating / last_round_responses, 2)
            last_round_response_rate = self.__calculate_response_rate(last_round_responses, last_round_total_surveyed)
            rating_movement = self.__calculate_metric_movement(latest_round_average_rating, last_round_average_rating)
            response_movement = self.__calculate_point_difference(latest_round_response_rate, last_round_response_rate)

        if norm:
            rating_norm = self.__calculate_metric_movement(latest_round_average_rating, float(norm.get('ccr', 0)))
            response_norm = self.__calculate_point_difference(latest_round_response_rate, float(norm.get('response_rate', 0)))

        return {
            'rating': latest_round_average_rating,
            'response_rate': latest_round_response_rate,
            'responses': latest_round_responses,
            'total_surveyed': latest_round_total_surveyed,
            'rating_movement': rating_movement,
            'response_movement': response_movement,
            'last_round_response_rate': last_round_response_rate,
            'last_round_responses': last_round_responses,
            'last_round_total_surveyed': last_round_total_surveyed,
            'last_round_rating': last_round_average_rating,
            'rating_norm': rating_norm,
            'response_norm': response_norm,
            # FIXME: this is a temporary check for Omnicom demo and validation
            'norm_enabled': bool(norm)
        }