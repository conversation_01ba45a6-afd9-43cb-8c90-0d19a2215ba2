from api.clientarea.src.utils.cards.Card import Card


class RatingBarometer(Card):
    """ Overall rating widget for barometer responses
    """
    def __calculate_avg_rating(self, rating, responses):
        if responses == 0:
            return 0
        return round(rating / responses, 2)


    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        return round((responses / total) * 100, 2)
    

    def __calculate_metric_movement(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = this_round - last_round
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    

    def __calculate_point_difference(self, this_round, last_round):
        if this_round == 0:
            return 0
        return round(this_round - last_round, 2)
    

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        latest_round_total_surveyed = 0
        latest_round_responses = 0
        latest_round_total_rating = 0
        last_round_total_surveyed = 0
        last_round_responses = 0
        last_round_total_rating = 0

        result = {
            'rating': 0,
            'response_rate': 0,
            'responses': 0,
            'total_surveyed': 0,
            'rating_movement': 0,
            'response_movement': 0,
            'last_round_response_rate': 0,
            'last_round_responses': 0,
            'last_round_total_surveyed': 0,
            'last_round_rating': 0,
            'rating_norm': 0,
            'rating_norm_movement': 0,
            'response_norm': 0,
            'response_norm_movement': 0,
            # FIXME: this is a temporary check for Omnicom demo and validation
            'norm_enabled': bool(self.barometer_norm),
            'data_hidden': False
        }

        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            # latest round metrics
            if self.rounds_barometer[index] == 'current':
                latest_round_total_surveyed += 1

                if record['responded']:
                    latest_round_responses += 1
                    latest_round_total_rating += rating
            
            # last round metrics
            if self.rounds_barometer[index] == 'previous':
                last_round_total_surveyed += 1

                if record['responded']:
                    last_round_responses += 1
                    last_round_total_rating += rating
        
        # anonymity turned off for now until requirements are clear
        # if latest_round_responses < 2:
        #     # to keep data anonymous, we can't show results from a single response
        #     result['data_hidden'] = True
        #     return result

        result['rating'] = self.__calculate_avg_rating(latest_round_total_rating, latest_round_responses)
        result['response_rate'] = self.__calculate_response_rate(latest_round_responses, latest_round_total_surveyed)
        result['responses'] = latest_round_responses
        result['total_surveyed'] = latest_round_total_surveyed

        if last_round_responses > 0:
            result['last_round_rating'] = self.__calculate_avg_rating(last_round_total_rating, last_round_responses)
            result['last_round_response_rate'] = self.__calculate_response_rate(last_round_responses, last_round_total_surveyed)
            result['last_round_responses'] = last_round_responses
            result['last_round_total_surveyed'] = last_round_total_surveyed
            result['rating_movement'] = self.__calculate_metric_movement(result['rating'], result['last_round_rating'])
            result['response_movement'] = self.__calculate_point_difference(result['response_rate'], result['last_round_response_rate'])

        if self.barometer_norm:
            result['rating_norm'] = float(self.barometer_norm.get('ccr', 0))
            result['rating_norm_movement'] = self.__calculate_metric_movement(result['rating'], float(self.barometer_norm.get('ccr', 0)))
            result['response_norm'] = float(self.barometer_norm.get('response_rate', 0))
            result['response_norm_movement'] = self.__calculate_point_difference(result['response_rate'], float(self.barometer_norm.get('response_rate', 0)))

        return result
