import json
import requests
from datetime import datetime, timedelta
from jose import JW<PERSON>rror, ExpiredSignatureError, jwt
from pydantic import BaseModel, computed_field
from litestar.exceptions import NotAuthorizedException
from lib.settings import settings


API_KEY_HEADER = "Authorization"
ASSUMED_ROLE_HEADER = "X-CRC-Assumed-Role"
ASSUMED_ROLE_ACCEPTED_DOMAINS = ["clientrelationship.com", "customer-relationship.com", "vaullabs.com", "createfuture.com"]
ASSUMED_ROLE_REJECTED_DOMAINS = ["clientrelationship.com", "customer-relationship.com"]
DEFAULT_TIME_DELTA = timedelta(days=1)
ALGORITHM = "RS256"


class User(BaseModel):
    salesforce_id: str = None
    user_id: str
    user_internal: bool = False
    user_name: str
    user_email: str
    user_account_id: str = None
    user_account_name: str = None
    user_account_vertical: str | None = None
    user_team_id: str | None = None
    user_team_name: str | None = None
    user_account_organisation_level: str | None = None
    user_sector: str | None = None
    user_features: list[str] = []
    user_roles: dict[str, bool] = {}
    user_permissions: list[str] = []
    user_account_hierarchy: list[str] = []
    user_key_accounts: list[str] = []
    user_key_markets: list[str] = []
    user_reporting_role: str | None = None
    user_reporting_roles: list[str] | None = []
    user_reporting_views: dict[str, dict] = {}
    user_round_index_limit: list[int] = []
    assumed_role_id: str | None = None
    assumed_role_name: str | None = None
    assumed_role_email: str | None = None
    assumed_role_account_id: str | None = None
    assumed_role_account_name: str | None = None
    assumed_role_account_vertical: str | None = None
    assumed_role_team_id: str | None = None
    assumed_role_team_name: str | None = None
    assumed_role_account_organisation_level: str | None = None
    assumed_role_sector: str | None = None
    assumed_role_features: list[str] | None = []
    assumed_role_roles: dict[str, bool] | None = {}
    assumed_role_permissions: list[str] | None = []
    assumed_role_account_hierarchy: list[str] | None = []
    assumed_role_key_accounts: list[str] | None = []
    assumed_role_key_markets: list[str] | None = []
    assumed_role_reporting_role: str | None = None
    assumed_role_reporting_roles: list[str] | None = []
    assumed_role_reporting_views: dict[str, dict] | None = {}
    assumed_role_round_index_limit: list[int] = []

    @computed_field
    def id(self) -> str:
        if self.assumed_role_id:
            return self.assumed_role_id
        else:
            return self.salesforce_id
    
    @computed_field
    def name(self) -> str:
        if self.assumed_role_name:
            return self.assumed_role_name
        else:
            return self.user_name

    @computed_field
    def email(self) -> str:
        if self.assumed_role_email:
            return self.assumed_role_email
        else:
            return self.user_email
    
    @computed_field
    def account_id(self) -> str | None:
        if self.assumed_role_account_id:
            return self.assumed_role_account_id
        else:
            return self.user_account_id
    
    @computed_field
    def account_name(self) -> str | None:
        if self.assumed_role_account_name:
            return self.assumed_role_account_name
        else:
            return self.user_account_name
        
    @computed_field
    def account_vertical(self) -> str | None:
        if self.assumed_role_account_vertical:
            return self.assumed_role_account_vertical
        else:
            return self.user_account_vertical
        
    @computed_field
    def team_id(self) -> str | None:
        if self.assumed_role_team_id:
            return self.assumed_role_team_id
        else:
            return self.user_team_id
    
    @computed_field
    def team_name(self) -> str | None:
        if self.assumed_role_team_name:
            return self.assumed_role_team_name
        else:
            return self.user_team_name
        
    @computed_field
    def account_organisation_level(self) -> str | None:
        if self.assumed_role_account_organisation_level:
            return self.assumed_role_account_organisation_level
        else:
            return self.user_account_organisation_level
        
    @computed_field
    def sector(self) -> str | None:
        if self.assumed_role_sector:
            return self.assumed_role_sector
        else:
            return self.user_sector
        
    @computed_field
    def features(self) -> list[str]:
        if self.assumed_role_features:
            return self.assumed_role_features
        else:
            return self.user_features
    
    @computed_field
    def reporting_role(self) -> str | None:
        if self.assumed_role_reporting_role:
            return self.assumed_role_reporting_role
        else:
            return self.user_reporting_role
        
    @computed_field
    def reporting_roles(self) -> list[str]:
        if self.assumed_role_reporting_roles:
            return self.assumed_role_reporting_roles
        else:
            return self.user_reporting_roles
        
    @computed_field
    def reporting_views(self) -> dict[str, dict]:
        if self.assumed_role_reporting_views:
            return self.assumed_role_reporting_views
        else:
            return self.user_reporting_views
    
    @computed_field
    def roles(self) -> dict[str, bool]:
        if self.assumed_role_roles:
            return self.assumed_role_roles
        else:
            return self.user_roles

    @computed_field
    def permissions(self) -> list[str]:
        if self.assumed_role_permissions:
            return self.assumed_role_permissions
        else:
            return self.user_permissions
        
    @computed_field
    def account_hierarchy(self) -> list[str]:
        if self.assumed_role_account_hierarchy:
            return self.assumed_role_account_hierarchy
        else:
            return self.user_account_hierarchy
    
    @computed_field
    def key_accounts(self) -> list[str]:
        if self.assumed_role_key_accounts:
            return self.assumed_role_key_accounts
        else:
            return self.user_key_accounts
    
    @computed_field
    def key_markets(self) -> list[str]:
        if self.assumed_role_key_markets:
            return self.assumed_role_key_markets
        else:
            return self.user_key_markets
        
    @computed_field
    def round_index_limit(self) -> list[int]:
        if self.assumed_role_round_index_limit:
            return self.assumed_role_round_index_limit
        else:
            return self.user_round_index_limit


class Token(BaseModel):
    exp: datetime
    iat: datetime
    sub: str
    email: str


def decode_jwt_token(encoded_token: str) -> Token:
    try:
        jwks = requests.get(settings.cognito_keys_url).json()
        payload = jwt.decode(token=encoded_token, key=jwks, algorithms=[ALGORITHM], audience=settings.cognito_audience, issuer=settings.cognito_issuer, options={"verify_at_hash": False})
        return Token(**payload)
    except ExpiredSignatureError as e:
        raise NotAuthorizedException("Token expired") from e
    except JWTError as e:
        raise NotAuthorizedException("Invalid token") from e