RESPONSE_ERROR_CODES = {
    'sign_up': {
        'NotAuthorizedException': 'Invalid username or password',
        'UsernameExistsException': 'Username already exists',
        'InvalidPasswordException': 'Password does not meet requirements',
    },
    'confirm_sign_up': {
        'NotAuthorizedException': 'Invalid username or password',
        'ExpiredCodeException': 'Confirmation code has expired. Please request a new one',
        'CodeMismatchException': 'Invalid confirmation code. Please try again',
        'TooManyFailedAttemptsException': 'Too many failed attempts. Please try again later',
    },
    'forgotten_password': {
        'NotAuthorizedException': 'Invalid username or password',
    },
    'confirm_forgot_password': {
        'NotAuthorizedException': 'Invalid username or password',
        'ExpiredCodeException': 'Confirmation code has expired. Please request a new one',
        'CodeMismatchException': 'Invalid confirmation code. Please try again',
        'TooManyFailedAttemptsException': 'Too many failed attempts. Please try again later',
    },
    'resend_confirmation_code': {
        'NotAuthorizedException': 'Invalid username or password',
    },
    'initiate_auth': {
        'NotAuthorizedException': 'Invalid username or password',
        'PasswordResetRequiredException': 'Password reset required',
    },
}

class CognitoError:
    def __init__(self, api: str, error: str):
        self.api = api
        self.error = error

        # extract error code
        self.error_code = error['Code']

        # extract original/provided error message
        self.provided_message = error['Message']

        # get mapped error message
        self.error_message = self.get_error_message()

    def get_error_message(self):
        return RESPONSE_ERROR_CODES.get(self.api, {}).get(self.error_code, 'An issue occurred. Please try again later')