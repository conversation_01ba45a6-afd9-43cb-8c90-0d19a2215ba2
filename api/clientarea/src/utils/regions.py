SF_REGIONS_TO_COUNTRY_LOOKUP = {'a0aWT000003OKbl': ['a0aWT000003OKhk',
  'a0aWT000003OKkL',
  'a0aWT000003OKh5',
  'a0aWT000003OKhz',
  'a0aWT000003OKhO',
  'a0aWT000003OKiA',
  'a0aWT000003OKkH',
  'a0aWT000003OKkD',
  'a0aWT000003OKhE',
  'a0aWT000003OKhi',
  'a0aWT000003OKns',
  'a0aWT000003OKoH',
  'a0aWT000003OKhW',
  'a0aWT000003OKhT',
  'a0aWT000003OKhX',
  'a0aWT000003OKkP',
  'a0aWT000003OKh0',
  'a0aWT000003OKoI',
  'a0aWT000003OKgl',
  'a0aWT000003OKgm',
  'a0aWT000003unkV',
  'a0aWT000003OKh1',
  'a0aWT000003OKhh',
  'a0aWT000003OKnt',
  'a0aWT000003OKlS',
  'a0aWT000003OKgo',
  'a0aWT000003OKhd',
  'a0aWT000003OKhU',
  'a0aWT000003OKkG',
  'a0aWT000003OKgi',
  'a0aWT000003OKhg',
  'a0aWT000003OKk3',
  'a0aWT000003OKh3',
  'a0aWT000003OKnu',
  'a0aWT000003OKkB',
  'a0aWT000003OKhS',
  'a0aWT000003OKgj',
  'a0aWT000003OKi5',
  'a0aWT000003OKhj',
  'a0aWT000003OKht',
  'a0aWT000003OKgu',
  'a0aWT000003OKhF',
  'a0aWT000003OKhv',
  'a0aWT000003OKha',
  'a0aWT000003OKhQ',
  'a0aWT000003OKk6',
  'a0aWT000003OKhG',
  'a0aWT000003OKk5',
  'a0aWT000003OKoE',
  'a0aWT000003OKi8',
  'a0aWT000003OKkN',
  'a0aWT000003OKgz',
  'a0aWT000003ZPTF',
  'a0aWT000003OKh7',
  'a0aWT000003OKk9',
  'a0aWT000003OKhR',
  'a0aWT000003OKk4',
  'a0aWT000003OKhw',
  'a0aWT000003OKkQ',
  'a0aWT000003OKgr',
  'a0aWT000003OKgn',
  'a0aWT000003OKhV',
  'a0aWT000003OKi9',
  'a0aWT000003OKkK',
  'a0aWT000003OKhD',
  'a0aWT000003OKhc',
  'a0aWT000003OKho',
  'a0aWT000003OKhL',
  'a0aWT000003OKoG',
  'a0aWT000003OKh2',
  'a0aWT000003OKk8',
  'a0aWT000003OKkC',
  'a0aWT000003OKhm',
  'a0aWT000003OKhB',
  'a0aWT000003OKhq',
  'a0aWT000003OKh6',
  'a0aWT000003OKgs',
  'a0aWT000003OKhb',
  'a0aWT000003OKh9',
  'a0aWT000003OKkO',
  'a0aWT000003OKhy',
  'a0aWT000003OKhl',
  'a0aWT000003OKiB',
  'a0aWT000003OKk7',
  'a0aWT000003OKgx',
  'a0aWT000003OKgt',
  'a0aWT000003OKoD',
  'a0aWT000003OKi6',
  'a0aWT000003OKhr',
  'a0aWT000003OKkI',
  'a0aWT000003OKgy',
  'a0aWT000003OKkE',
  'a0aWT000003OKlU',
  'a0aWT000003OKkA',
  'a0aWT000003OKgw',
  'a0aWT000003OKhJ',
  'a0aWT000003OKhM',
  'a0aWT000003OKi2',
  'a0aWT000003OKh4',
  'a0aWT000003OKhK',
  'a0aWT000003OKnv',
  'a0aWT000003OKhA',
  'a0aWT000003OKi7',
  'a0aWT000003OKhp',
  'a0aWT000003OKhu',
  'a0aWT000003OKkF',
  'a0aWT000003OKhf',
  'a0aWT000003OKhN',
  'a0aWT000003OKoF',
  'a0aWT000003OKgp',
  'a0aWT000003OKhH',
  'a0aWT000003OKhC',
  'a0aWT000003OKgv',
  'a0aWT000003OKhe',
  'a0aWT000003OKi4',
  'a0aWT000003OKkJ',
  'a0aWT000003OKhZ',
  'a0aWT000003OKgk',
  'a0aWT000003OKnw',
  'a0aWT000003OKgq',
  'a0aWT000003OKhI',
  'a0aWT000003OKi1',
  'a0aWT000003OKhP',
  'a0aWT000003OKhn',
  'a0aWT000003OKi0',
  'a0aWT000003OKi3',
  'a0aWT000003OKlT',
  'a0aWT000003OKkR',
  'a0aWT000003OKhx',
  'a0aWT000003OKh8',
  'a0aWT000003OKgh',
  'a0aWT000003OKkM',
  'a0aWT000003OKhY'],
 'a0aWT000003OKdO': ['a0aWT000003OKgn',
  'a0aWT000003OKgp',
  'a0aWT000003OKh3',
  'a0aWT000003OKgv',
  'a0aWT000003OKh2',
  'a0aWT000003OKgj',
  'a0aWT000003OKh6',
  'a0aWT000003OKgs',
  'a0aWT000003OKgu',
  'a0aWT000003OKgk',
  'a0aWT000003OKgq',
  'a0aWT000003OKh0',
  'a0aWT000003OKgx',
  'a0aWT000003OKgt',
  'a0aWT000003OKgl',
  'a0aWT000003OKgm',
  'a0aWT000003OKgy',
  'a0aWT000003OKh1',
  'a0aWT000003OKgz',
  'a0aWT000003OKgo',
  'a0aWT000003OKgw',
  'a0aWT000003OKh7',
  'a0aWT000003OKh4',
  'a0aWT000003OKgh',
  'a0aWT000003OKgi',
  'a0aWT000003OKgr'],
 'a0aWT000003OKiD': ['a0aWT000003OKgn',
  'a0aWT000003OKgp',
  'a0aWT000003OKh3',
  'a0aWT000003OKgv',
  'a0aWT000003OKh2',
  'a0aWT000003OKgj',
  'a0aWT000003OKgs',
  'a0aWT000003OKgu',
  'a0aWT000003OKgk',
  'a0aWT000003OKgq',
  'a0aWT000003OKh0',
  'a0aWT000003OKgx',
  'a0aWT000003OKgt',
  'a0aWT000003OKgl',
  'a0aWT000003OKgm',
  'a0aWT000003OKgy',
  'a0aWT000003OKh1',
  'a0aWT000003OKgz',
  'a0aWT000003OKgo',
  'a0aWT000003OKgw',
  'a0aWT000003OKh4',
  'a0aWT000003OKgh',
  'a0aWT000003OKgi',
  'a0aWT000003OKgr'],
 'a0aWT000003OKjp': ['a0aWT000003OKgh'],
 'a0aWT000003OKjq': ['a0aWT000003OKgn',
  'a0aWT000003OKgl',
  'a0aWT000003OKgk',
  'a0aWT000003OKgm',
  'a0aWT000003OKgj',
  'a0aWT000003OKgi'],
 'a0aWT000003OKjr': ['a0aWT000003OKgq',
  'a0aWT000003OKgp',
  'a0aWT000003OKgo',
  'a0aWT000003OKgt',
  'a0aWT000003OKgs',
  'a0aWT000003OKgu',
  'a0aWT000003OKgr'],
 'a0aWT000003OKjs': ['a0aWT000003OKh0',
  'a0aWT000003OKgz',
  'a0aWT000003OKh3',
  'a0aWT000003OKgx',
  'a0aWT000003OKgw',
  'a0aWT000003OKh4',
  'a0aWT000003OKh2',
  'a0aWT000003OKgv',
  'a0aWT000003OKgy',
  'a0aWT000003OKh1'],
 'a0aWT000003OKdP': ['a0aWT000003OKhk',
  'a0aWT000003OKh5',
  'a0aWT000003OKhz',
  'a0aWT000003OKhO',
  'a0aWT000003OKiA',
  'a0aWT000003OKhE',
  'a0aWT000003OKhi',
  'a0aWT000003OKns',
  'a0aWT000003OKoH',
  'a0aWT000003OKhW',
  'a0aWT000003OKhT',
  'a0aWT000003OKhX',
  'a0aWT000003OKoI',
  'a0aWT000003OKhh',
  'a0aWT000003OKnt',
  'a0aWT000003OKlS',
  'a0aWT000003OKhd',
  'a0aWT000003OKhU',
  'a0aWT000003OKhg',
  'a0aWT000003OKnu',
  'a0aWT000003OKhS',
  'a0aWT000003OKi5',
  'a0aWT000003OKhj',
  'a0aWT000003OKht',
  'a0aWT000003OKhF',
  'a0aWT000003OKhv',
  'a0aWT000003OKha',
  'a0aWT000003OKhQ',
  'a0aWT000003OKhG',
  'a0aWT000003OKoE',
  'a0aWT000003OKi8',
  'a0aWT000003ZPTF',
  'a0aWT000003OKhR',
  'a0aWT000003OKhw',
  'a0aWT000003OKhV',
  'a0aWT000003OKi9',
  'a0aWT000003OKhD',
  'a0aWT000003OKhc',
  'a0aWT000003OKho',
  'a0aWT000003OKhL',
  'a0aWT000003OKoG',
  'a0aWT000003OKhm',
  'a0aWT000003OKhB',
  'a0aWT000003OKhq',
  'a0aWT000003OKhb',
  'a0aWT000003OKh9',
  'a0aWT000003OKhy',
  'a0aWT000003OKhl',
  'a0aWT000003OKiB',
  'a0aWT000003OKoD',
  'a0aWT000003OKi6',
  'a0aWT000003OKhr',
  'a0aWT000003OKlU',
  'a0aWT000003OKhJ',
  'a0aWT000003OKhM',
  'a0aWT000003OKi2',
  'a0aWT000003OKhK',
  'a0aWT000003OKnv',
  'a0aWT000003OKhA',
  'a0aWT000003OKi7',
  'a0aWT000003OKhu',
  'a0aWT000003OKhp',
  'a0aWT000003OKhf',
  'a0aWT000003OKhN',
  'a0aWT000003OKoF',
  'a0aWT000003OKhH',
  'a0aWT000003OKhC',
  'a0aWT000003OKhe',
  'a0aWT000003OKi4',
  'a0aWT000003OKhZ',
  'a0aWT000003OKnw',
  'a0aWT000003OKhI',
  'a0aWT000003OKi1',
  'a0aWT000003OKhP',
  'a0aWT000003OKhn',
  'a0aWT000003OKi0',
  'a0aWT000003OKi3',
  'a0aWT000003OKlT',
  'a0aWT000003OKhx',
  'a0aWT000003OKh8',
  'a0aWT000003OKhY'],
 'a0aWT000003OKiE': ['a0aWT000003OKhk',
  'a0aWT000003OKhg',
  'a0aWT000003OKh5',
  'a0aWT000003OKoF',
  'a0aWT000003OKhz',
  'a0aWT000003OKi9',
  'a0aWT000003OKiA',
  'a0aWT000003OKho',
  'a0aWT000003OKnu',
  'a0aWT000003OKoG',
  'a0aWT000003OKi4',
  'a0aWT000003OKhi',
  'a0aWT000003OKi5',
  'a0aWT000003OKhj',
  'a0aWT000003OKht',
  'a0aWT000003OKhm',
  'a0aWT000003OKns',
  'a0aWT000003OKhq',
  'a0aWT000003OKoH',
  'a0aWT000003OKnw',
  'a0aWT000003OKhv',
  'a0aWT000003OKhy',
  'a0aWT000003OKhl',
  'a0aWT000003OKhu',
  'a0aWT000003OKiB',
  'a0aWT000003OKoI',
  'a0aWT000003OKoD',
  'a0aWT000003OKi6',
  'a0aWT000003OKhr',
  'a0aWT000003OKi1',
  'a0aWT000003OKoE',
  'a0aWT000003OKhh',
  'a0aWT000003OKnt',
  'a0aWT000003OKi8',
  'a0aWT000003OKlU',
  'a0aWT000003OKlS',
  'a0aWT000003OKhn',
  'a0aWT000003OKi0',
  'a0aWT000003OKi2',
  'a0aWT000003OKi3',
  'a0aWT000003OKlT',
  'a0aWT000003OKnv',
  'a0aWT000003OKhx',
  'a0aWT000003OKhw',
  'a0aWT000003OKi7',
  'a0aWT000003OKhp'],
 'a0aWT000003OKjt': ['a0aWT000003OKi8',
  'a0aWT000003OKhy',
  'a0aWT000003OKh5',
  'a0aWT000003OKiB',
  'a0aWT000003OKhz',
  'a0aWT000003OKi9',
  'a0aWT000003OKi0',
  'a0aWT000003OKi2',
  'a0aWT000003OKiA',
  'a0aWT000003OKi3',
  'a0aWT000003OKi6',
  'a0aWT000003OKi4',
  'a0aWT000003OKi7',
  'a0aWT000003OKi5',
  'a0aWT000003OKi1'],
 'a0aWT000003OKiF': ['a0aWT000003OKh6', 'a0aWT000003OKh7'],
 'a0aWT000003OKju': ['a0aWT000003OKh6', 'a0aWT000003OKh7'],
 'a0aWT000003OKiG': ['a0aWT000003OKhN',
  'a0aWT000003OKhH',
  'a0aWT000003OKhO',
  'a0aWT000003OKh8',
  'a0aWT000003OKhC',
  'a0aWT000003OKhV',
  'a0aWT000003OKhD',
  'a0aWT000003OKhc',
  'a0aWT000003OKhL',
  'a0aWT000003OKhe',
  'a0aWT000003OKhS',
  'a0aWT000003OKhE',
  'a0aWT000003OKhZ',
  'a0aWT000003OKhB',
  'a0aWT000003OKhY',
  'a0aWT000003OKhW',
  'a0aWT000003OKhb',
  'a0aWT000003OKhT',
  'a0aWT000003OKh9',
  'a0aWT000003OKhF',
  'a0aWT000003OKha',
  'a0aWT000003OKhI',
  'a0aWT000003OKhX',
  'a0aWT000003OKhQ',
  'a0aWT000003OKhG',
  'a0aWT000003OKhP',
  'a0aWT000003ZPTF',
  'a0aWT000003OKhJ',
  'a0aWT000003OKhM',
  'a0aWT000003OKhK',
  'a0aWT000003OKhd',
  'a0aWT000003OKhR',
  'a0aWT000003OKhU',
  'a0aWT000003OKhA',
  'a0aWT000003OKhf'],
 'a0aWT000003OKjx': ['a0aWT000003OKhN',
  'a0aWT000003OKhI',
  'a0aWT000003ZPTF',
  'a0aWT000003OKhG',
  'a0aWT000003OKhH',
  'a0aWT000003OKhJ',
  'a0aWT000003OKhM',
  'a0aWT000003OKhD',
  'a0aWT000003OKhE',
  'a0aWT000003OKhK',
  'a0aWT000003OKhd',
  'a0aWT000003OKhL',
  'a0aWT000003OKhe',
  'a0aWT000003OKhF',
  'a0aWT000003OKhf'],
 'a0aWT000003OKjw': ['a0aWT000003OKhV',
  'a0aWT000003OKhO',
  'a0aWT000003OKh8',
  'a0aWT000003OKhC',
  'a0aWT000003OKhc',
  'a0aWT000003OKhS',
  'a0aWT000003OKhZ',
  'a0aWT000003OKhB',
  'a0aWT000003OKhW',
  'a0aWT000003OKhb',
  'a0aWT000003OKhT',
  'a0aWT000003OKh9',
  'a0aWT000003OKha',
  'a0aWT000003OKhX',
  'a0aWT000003OKhQ',
  'a0aWT000003OKhP',
  'a0aWT000003OKhR',
  'a0aWT000003OKhU',
  'a0aWT000003OKhA',
  'a0aWT000003OKhY'],
 'a0aWT000003OKk0': ['a0aWT000003OKhh',
  'a0aWT000003OKhg',
  'a0aWT000003OKhk',
  'a0aWT000003OKhl',
  'a0aWT000003OKhm',
  'a0aWT000003OKhq',
  'a0aWT000003OKhn',
  'a0aWT000003OKho',
  'a0aWT000003OKhr',
  'a0aWT000003OKhi',
  'a0aWT000003OKhp',
  'a0aWT000003OKhj'],
 'a0aWT000003OKk1': ['a0aWT000003OKnt',
  'a0aWT000003OKhv',
  'a0aWT000003OKns',
  'a0aWT000003OKhx',
  'a0aWT000003OKnv',
  'a0aWT000003OKnw',
  'a0aWT000003OKnu',
  'a0aWT000003OKhw',
  'a0aWT000003OKhu',
  'a0aWT000003OKht'],
 'a0aWT000003OKhs': ['a0aWT000003OKnt',
  'a0aWT000003OKns',
  'a0aWT000003OKnv',
  'a0aWT000003OKnw',
  'a0aWT000003OKnu'],
 'a0aWT000003OKk2': ['a0aWT000003OKoF',
  'a0aWT000003OKlU',
  'a0aWT000003OKoH',
  'a0aWT000003OKlS',
  'a0aWT000003OKoI',
  'a0aWT000003OKoD',
  'a0aWT000003OKlT',
  'a0aWT000003OKoG',
  'a0aWT000003OKoE'],
 'a0aWT000003OKiC': ['a0aWT000003OKoE', 'a0aWT000003OKoF', 'a0aWT000003OKoD'],
 'a0aWT000003OKlR': ['a0aWT000003OKoH', 'a0aWT000003OKoI', 'a0aWT000003OKoG'],
 'a0aWT000003OKdQ': ['a0aWT000003OKkL',
  'a0aWT000003OKk3',
  'a0aWT000003OKkH',
  'a0aWT000003OKkK',
  'a0aWT000003OKkB',
  'a0aWT000003OKkC',
  'a0aWT000003OKkD',
  'a0aWT000003OKk8',
  'a0aWT000003OKkJ',
  'a0aWT000003OKkO',
  'a0aWT000003OKkM',
  'a0aWT000003OKkP',
  'a0aWT000003OKk6',
  'a0aWT000003OKk7',
  'a0aWT000003unkV',
  'a0aWT000003OKk5',
  'a0aWT000003OKkI',
  'a0aWT000003OKkE',
  'a0aWT000003OKkA',
  'a0aWT000003OKkN',
  'a0aWT000003OKk9',
  'a0aWT000003OKk4',
  'a0aWT000003OKkR',
  'a0aWT000003OKkQ',
  'a0aWT000003OKkG',
  'a0aWT000003OKkF'],
 'a0aWT000003OKiH': ['a0aWT000003OKkL',
  'a0aWT000003OKk3',
  'a0aWT000003OKkH',
  'a0aWT000003OKkK',
  'a0aWT000003OKkB',
  'a0aWT000003OKkC',
  'a0aWT000003OKkD',
  'a0aWT000003OKk8',
  'a0aWT000003OKkJ',
  'a0aWT000003OKkO',
  'a0aWT000003OKkM',
  'a0aWT000003OKkP',
  'a0aWT000003OKk6',
  'a0aWT000003OKk7',
  'a0aWT000003unkV',
  'a0aWT000003OKk5',
  'a0aWT000003OKkI',
  'a0aWT000003OKkE',
  'a0aWT000003OKkA',
  'a0aWT000003OKkN',
  'a0aWT000003OKk9',
  'a0aWT000003OKk4',
  'a0aWT000003OKkG',
  'a0aWT000003OKkF'],
 'a0aWT000003OKiI': ['a0aWT000003OKkQ', 'a0aWT000003OKkR']}


def get_countries_by_regions(regions):
    countries = set()
    for region in regions:
        countries.update(SF_REGIONS_TO_COUNTRY_LOOKUP.get(region, []))
    return list(countries)