import datetime
from typing import List
from pymongo.collection import ReturnDocument
from api.clientarea.src.models.collections import ConfirmAccounts, ConfirmAccountsAccountManager


class ConfirmAccountsService():
    def __init__(self, db):
        self.db = db

    def get_by_account_manager(self, survey_id: str, account_manager_id: str) -> List[ConfirmAccounts]:
        # TODO: I've removed the `accounts_confirmed_start_date` check so <PERSON><PERSON> can see the accounts prior to the date
        return [
            ConfirmAccounts(**account)
            for account in self.db.confirmaccounts.find({'customer_survey_round_id': survey_id,
                                                         'account_managers': {'$elemMatch': {'account_manager_id': account_manager_id}},
                                                         'deleted': {'$ne': True}})
        ]

    def get_by_confirm_account(self, confirm_account_id: str) -> ConfirmAccounts:
        return ConfirmAccounts(**self.db.confirmaccounts.find_one({'Id': confirm_account_id, 'deleted': {'$ne': True}}))

    def update_account_manager(self, confirm_account_id: str, account_manager_id: str, delegate_id: str, delegate_name:str, delegate_email: str) -> None:
        #delegation_key = f'delegation.{account_manager_id}'

        confirm_account = self.get_by_confirm_account(confirm_account_id)
        account_managers = confirm_account.account_managers
        delegation = confirm_account.delegation
        new_account_managers = []
        new_delegation = []

        # update the account manager with the new delegate
        account_managers.append(ConfirmAccountsAccountManager(
            account_manager_id=delegate_id,
            account_manager_email=delegate_email,
            account_manager_name=delegate_name,
        ))

        # strip out the old account manager
        for am in account_managers:
            if am.account_manager_id != account_manager_id:
                new_account_managers.append(am.dict())

        # strip out any old delegation
        new_account_managers_ids = [am['account_manager_id'] for am in new_account_managers]
        for d in delegation:
            if d['delegate_id'] == new_account_managers_ids:
                new_delegation.append(d)

        # update delegation
        new_delegation.append({
            'delegate_id': delegate_id,
            'delegate_email': delegate_email,
            'email_account_delegation_sent': False,
        })

        self.db.confirmaccounts.update_one(
            {'Id': confirm_account_id, 'deleted': {'$ne': True}},
            {
                '$set': {
                    'account_managers': new_account_managers,
                    'delegation': new_delegation,
                }
            }
        )

    def update_accounts(self, 
                        confirm_account_id: str, 
                        account_manager_id: str, 
                        account_manager_email: str, 
                        account_manager_name: str, 
                        new_confirmed_date: datetime.datetime,
                        update_stats: dict[str, int], 
                        new_accounts: list[dict],
                        all_accounts: list[dict]) -> ConfirmAccounts:
        record = self.db.confirmaccounts.find_one_and_update(
            {
                'Id': confirm_account_id,
                'account_managers': {'$elemMatch': {'account_manager_id': account_manager_id}},
                'deleted': {'$ne': True}
            },
            {
                '$set': {
                    'confirmed_accounts_last_save_all_accounts': all_accounts,
                    'confirmed_accounts_last_save_date': new_confirmed_date,
                    'confirmed_accounts_last_save_user_id': account_manager_id,
                    'confirmed_accounts_last_save_user_email': account_manager_email,
                    'confirmed_accounts_last_save_user_name': account_manager_name
                },
                '$push': {
                    'confirmed_accounts': {
                        '$each': [{
                            'accounts': new_accounts,
                            'confirm_date': new_confirmed_date,
                            'confirm_user_id': account_manager_id,
                            'confirm_user_email': account_manager_email,
                            'confirm_user_name': account_manager_name,
                            'stats': update_stats
                        }],
                        '$position': 0
                    }
                }
            }, 
            return_document=ReturnDocument.AFTER,
        )
        return ConfirmAccounts(**record)