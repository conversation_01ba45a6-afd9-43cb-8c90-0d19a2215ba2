from lib.portaldbapi import DocumentDB
from api.clientarea.src.models.collections import SurveyRound


class SurveyRoundsService():
    def __init__(self, db: DocumentDB):
        self.collection = db.surveyrounds

    def get_by_user_account(self, user_account_id: str):
        query:dict = {
            '_permissions': user_account_id, 
            'deleted': {'$ne': True}
        }
        projection:dict = {
            'Id': 1,
            'name': 1,
            'account_id': 1,
            'round_start_date': 1,
            'round_end_date': 1,
            'stage': 1,
            'state': 1,
            'progress': 1,
            'current_round': 1,
            'survey_type': 1,
        }
        return [
            SurveyRound(**survey_round)
            for survey_round in self.collection.find(query, projection)
        ]

