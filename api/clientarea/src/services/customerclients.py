import pymongo
from typing import List, Dict
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User


class CustomerClientsService():
    def __init__(self, db:DocumentDB, user:User, survey_id:str):
        self.survey_id:str = survey_id
        self.user:User = user
        self.db:DocumentDB = db


    def __normalize_team_id(self, team_id: str | None) -> str:
        return team_id or 'xxx'


    def get_by_query(self, query: str, limit: int = 50) -> List[Dict[str, str]]:
        result:list[dict] = []

        if not query:
            return result

        query:dict[str, dict] = {
            'name': {'$regex': query, '$options': 'i'},
        }
        projection:dict[str, int] = {
            '_id': 0,
            'Id': 1,
            'name': 1,
            'related_customer_clients': 1,
        }
        
        accounts = (
            self.db.accounts
                .find(query, projection)
                .sort('name', pymongo.ASCENDING)
                .limit(limit)
        )

        # TODO: move this to a service
        confirm_accounts_client_team_ids: set[tuple[str, str]] = set()
        for account in self.db.confirmaccounts.find({'customer_survey_round_id': self.survey_id,
                                                     'account_managers': {'$elemMatch': {'account_manager_id': self.user.id}},
                                                     'deleted': {'$ne': True}},
                                                     {'_id': 0, 'client_id': 1, 'team_id': 1}):
            client_id = account['client_id']
            team_id = self.__normalize_team_id(account['team_id'])
            confirm_accounts_client_team_ids.add((client_id, team_id))

        for account in accounts:
            related_agencies:list[dict] = [
                client for client in account.get('related_customer_clients', [])
                if (client['client_id'], self.__normalize_team_id(client['team_id'])) in confirm_accounts_client_team_ids
            ]

            survey_names_by_agency_team_id:dict[str, str] = {
                f"{client['client_id']}-{self.__normalize_team_id(client['team_id'])}": client['survey_name']
                for client in related_agencies
            }

            result.append({
                'id': account['Id'],
                'name': account['name'],
                'survey_names': survey_names_by_agency_team_id,
                'previously_surveyed': bool(related_agencies),
            })

        return result