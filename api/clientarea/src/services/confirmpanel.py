import datetime
from typing import List
from pymongo.collection import ReturnDocument
from api.clientarea.src.models.collections import ConfirmPanel


class ConfirmPanelService:
    def __init__(self, db):
        self.db = db

    def get_by_user_involvement(self, survey_id: str, user_id: str) -> dict:
        confirm_panels_by_account_manager = self.get_by_account_manager(survey_id, user_id)
        confirm_panels_by_panel_manager = self.get_by_panel_manager(survey_id, user_id)

        return {
            'panel_manager': confirm_panels_by_panel_manager,
            'account_manager': confirm_panels_by_account_manager,
        }

    def get_by_panel_manager(self, survey_id: str, panel_manager_id: str) -> List[ConfirmPanel]:
        today = datetime.datetime.now(datetime.UTC).replace(hour=0, minute=0, second=0, microsecond=0)
        return [
            ConfirmPanel(**panel)
            for panel in self.db.confirmpanel.find({'customer_survey_round_id': survey_id,
                                                    'panel_managers': {'$elemMatch': {'panel_manager_id': panel_manager_id}},
                                                    'deleted': {'$ne': True},
                                                    'panel_confirmed_start_date': {
                                                        '$lte': today
                                                    }})
        ]

    def get_by_account_manager(self, survey_id: str, account_manager_id: str) -> List[ConfirmPanel]:
        today = datetime.datetime.now(datetime.UTC).replace(hour=0, minute=0, second=0, microsecond=0)
        return [
            ConfirmPanel(**panel)
            for panel in self.db.confirmpanel.find({'customer_survey_round_id': survey_id,
                                                    'account_managers': account_manager_id,
                                                    'deleted': {'$ne': True},
                                                    'panel_confirmed_start_date': {
                                                        '$lte': today
                                                    }})
        ]

    def get_by_confirm_panel_id(self, confirm_panel_id: str, panel_manager_id: str) -> ConfirmPanel:
        return ConfirmPanel(**self.db.confirmpanel.find_one({'Id': confirm_panel_id,
                                                             'panel_managers': {'$elemMatch': {'panel_manager_id': panel_manager_id}},
                                                             'deleted': {'$ne': True}}))

    def update_panel(self, 
                     confirm_panel_id: str, 
                     panel_manager_id: str, 
                     panel_manager_email: str, 
                     panel_manager_name: str, 
                     new_confirmed_date: datetime.datetime, 
                     update_stats: dict[str, int], 
                     new_panel_selected: list,
                     all_panel: list) -> ConfirmPanel:

        record = self.db.confirmpanel.find_one_and_update(
            {
                'Id': confirm_panel_id,
                'panel_managers': {'$elemMatch': {'panel_manager_id': panel_manager_id}},
                'deleted': {'$ne': True}
            },
            {
                '$set': {
                    'confirmed_panel_last_save_all_panel': all_panel,
                    'confirmed_panel_last_save_date': new_confirmed_date,
                    'confirmed_panel_last_save_user_id': panel_manager_id,
                    'confirmed_panel_last_save_user_email': panel_manager_email,
                    'confirmed_panel_last_save_user_name': panel_manager_name
                },
                '$push': {
                    'confirmed_panel': {
                        '$each': [{
                            'panel': new_panel_selected,
                            'all_panel': all_panel,
                            'confirm_date': new_confirmed_date,
                            'confirm_user_id': panel_manager_id,
                            'confirm_user_email': panel_manager_email,
                            'confirm_user_name': panel_manager_name,
                            'stats': update_stats
                        }],
                        '$position': 0
                    }
                }
            }, 
            return_document=ReturnDocument.AFTER,
        )
        return ConfirmPanel(**record)