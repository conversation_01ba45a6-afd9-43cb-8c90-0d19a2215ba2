from lib.portaldbapi import DocumentDB


class Feature:
    def __init__(self, db: DocumentDB):
        self.config = db.config
        self.features = db.features

    def get_features(self, contact_id: str, internal: bool = False) -> list:
        combined_features = set()
        system_features = self.config.find_one({'_id': 'features'}, {'_id': 0, 'features': 1}) or {}
        user_features = self.features.find_one({'contact_id': contact_id}, {'_id': 0, 'features': 1}) or {}

        # convert system_features to a dictionary for easier lookup by name
        system_features_lookup = {feature['name']: feature for feature in system_features.get('features', [])}

        # add user features
        for feature, status in user_features.get('features', {}).items():
            if system_features_lookup.get(feature, {}).get('enabled', False) and status and (not system_features_lookup[feature].get('internal') or (system_features_lookup[feature].get('internal') and internal)):
                combined_features.add(feature)

        # add global features
        for feature in system_features_lookup:
            if system_features_lookup[feature].get('enabled', False) and system_features_lookup[feature].get('global', False) and (not system_features_lookup[feature].get('internal') or (system_features_lookup[feature].get('internal') and internal)):
                combined_features.add(feature)

        return combined_features