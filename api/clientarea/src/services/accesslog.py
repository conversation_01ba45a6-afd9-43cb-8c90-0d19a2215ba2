import datetime
from typing import List
from pymongo.collection import ReturnDocument
from api.clientarea.src.models.collections import AccessLog


class AccessLogService():
    def __init__(self, user, db):
        self.user = user
        self.db = db
        self.collection = self.db.accesslog
    
    def add_request(self) -> AccessLog:
        new_access_log = self.collection.find_one_and_update(
        {
            'contact_id': self.user.id,
            'type': 'request'
        },
        {
            '$set': { 
                'contact_id': self.user.id,
                'contact_email': self.user.email,
                'type': 'request'
            },
            '$push': {
                'last_access': datetime.datetime.now()
            }

        },
        upsert=True,
        return_document=ReturnDocument.AFTER)

        return AccessLog(**new_access_log)

    def add_assumed_role(self, assumed_role_id: str, assumed_role_email: str) -> AccessLog:
        new_access_log = self.collection.find_one_and_update(
        {
            'contact_id': self.user.salesforce_id,
            'assumed_role_id': assumed_role_id,
            'type': 'assumed_role'
        },
        {
            '$set': { 
                'contact_id': self.user.salesforce_id,
                'contact_email': self.user.user_email,
                'type': 'assumed_role',
                'assumed_role_id': assumed_role_id,
                'assumed_role_email': assumed_role_email
            },
            '$push': {
                'last_access': datetime.datetime.now()
            }

        },
        upsert=True,
        return_document=ReturnDocument.AFTER)

        return AccessLog(**new_access_log)