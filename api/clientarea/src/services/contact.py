from api.clientarea.src.models.collections import CustomerContacts
from api.clientarea.src.models.collections import SfContacts


class CustomerContactService():
    def __init__(self, db):
        self.db = db
        self.collection = db.customercontacts

    def get_by_email(self, email: str, projection: dict = {}) -> CustomerContacts:
        return CustomerContacts(**self.collection.find_one({'email': email}, projection))

    def get_by_id(self, id: str, projection: dict = {}) -> CustomerContacts:
        return CustomerContacts(**self.collection.find_one({'Id': id}, projection))

    def get_panel_managers(self, account_id: list, projection: dict = {}) -> list[SfContacts]:
        contacts_to_fetch = []
        contact_clients = {}

        for contact in self.collection.find({'client_hierarchy': {'$in': account_id}, 'panel_manager': True}, projection):
            contacts_to_fetch.append(contact['Id'])
            contact_clients[contact['Id']] = contact['client_hierarchy']

        contacts = SfContacts.get_by_contact_ids(self.db, contacts_to_fetch)
        for contact in contacts:
            contact.ClientHierarchy = contact_clients[contact.Id]
        return contacts

    def get_signatories(self, account_id: list, projection: dict = {}) -> list[SfContacts]:
        contacts_to_fetch = []
        contact_clients = {}

        for contact in self.collection.find({'client_hierarchy': {'$in': account_id}, 'signatory': True}, projection):
            contacts_to_fetch.append(contact['Id'])
            contact_clients[contact['Id']] = contact['client_hierarchy']

        contacts = SfContacts.get_by_contact_ids(self.db, contacts_to_fetch)
        for contact in contacts:
            contact.ClientHierarchy = contact_clients[contact.Id]
        return contacts
