import json
from lib.settings import settings
from pydantic import BaseModel
from lib.portaldbapi import DocumentDB
from lib.sqsapi import SQS


class EmailServiceConfig(BaseModel):
    enabled: bool = False
    restricted_domains: list[str] = []
    templates: dict[str, str] = {}


class EmailService():
    def __init__(self, db: DocumentDB, sqs: SQS):
        self.db: DocumentDB = db
        self.sqs: SQS = sqs

    def send(self, recipient: str, template: str, data: dict, metadata: dict) -> dict:
        success: bool = False
        es: EmailServiceConfig = EmailServiceConfig(**self.db.config.find_one({"_id": "email"}))
        template_id: str = es.templates.get(template)

        if not template_id:
            raise Exception(f"Template {template} does not exist")

        try:
            self.sqs.send_message(
                queue=settings.send_email_to_sg_queue_url,
                message_body=f'Email template {template_id} to {recipient}',
                message_attributes={
                     'toEmail': {
                        'DataType': 'String',
                        'StringValue': recipient,
                    },
                     'templateId': {
                        'DataType': 'String',
                        'StringValue': template_id,
                    },
                     'templateData': {
                        'DataType': 'String',
                        'StringValue': json.dumps(data),
                    },
                    'customArgs': {
                        'DataType': 'String',
                        'StringValue': json.dumps(metadata),
                    },
                },
            )

            success: bool = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }
