from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.collections import SfReports


class ReportsService:
    def __init__(self, user: User, db: DocumentDB):
        self.db = db
        self.user = user
        self.collection = db.reports

    def get_reports(self) -> list[SfReports]:
        # if user has no permissions (for whatever reason), bail
        permissions = self.user.permissions
        if not permissions:
            return []
        
        query = {
            '$or': [
                {'contacts': {'$elemMatch': {'$in': [self.user.id]}}},
                {'accounts': {'$elemMatch': {'$in': permissions}}}
            ]
        }

        return list(self.collection.find(query, {'_id': 0, 'id': 1, 'name': 1}))

    def get_report_by_id(self, report_id: str) -> SfReports:
        # if user has no permissions (for whatever reason), bail
        permissions = self.user.permissions
        if not permissions:
            return []
        
        query = {
            'id': report_id, 
            '$or': [
                {'contacts': {'$elemMatch': {'$in': [self.user.id]}}},
                {'accounts': {'$elemMatch': {'$in': permissions}}}
            ]
        }
        
        return self.collection.find_one(query, {'_id': 0, 'report': 1})