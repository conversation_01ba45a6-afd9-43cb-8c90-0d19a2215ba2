import datetime
from api.clientarea.src.models.collections import PendingCustomerContact


class PendingCustomerContactService():
    def __init__(self, db):
        self.db = db

    def get_by_id(self, internal_id: str) -> PendingCustomerContact:
        record = self.db.pendingcustomercontacts.find_one({'internal_id': internal_id})
        return PendingCustomerContact(**record)

    def add_pending_contact(self, email: str, first_name: str, last_name: str, internal_id: str) -> PendingCustomerContact:
        record = self.db.pendingcustomercontacts.insert_one(
            {
                'email': email,
                'first_name': first_name,
                'last_name': last_name,
                'internal_id': internal_id,
                'created_date': datetime.datetime.now(),
            }
        )