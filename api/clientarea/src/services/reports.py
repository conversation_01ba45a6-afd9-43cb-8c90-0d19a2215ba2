import os
import boto3
from urllib.parse import quote
from litestar.exceptions import HTTPException
from lib.settings import settings
from lib.portaldbapi import DocumentDB, SrpReport
from api.clientarea.src.dependencies import User


class SrpReportsService:
    def __init__(self, user: User, db: DocumentDB):
        self.db = db
        self.user = user
        self.collection = db.srpreports

    def get_reports(self) -> list:
        reports = []

        if not self.user.reporting_views:
            return reports

        query = {
            'contact_id': self.user.id,
            'published': True,
            'deleted': {'$ne': True}
        }
        projection = {
            '_id': 0
        }

        reports_raw = [
            SrpReport(**report)
            for report in self.collection.find(query, projection)
        ]

        # just need id, title, csr, published_date
        for report in reports_raw:
            title = report.title

            reports.append({
                'id': report.Id,
                'title': title,
                'customer_survey_round': report.customer_survey_round_title,
                'published_date': report.published_date.strftime('%Y-%m-%d')
            })

        # order reports by published date
        reports = sorted(reports, key=lambda x: x['published_date'], reverse=True)
        return reports
    def get_report(self, report_id: str) -> dict:
        s3_session = boto3.Session()
        s3 = s3_session.client('s3')

        query = {
            'Id': report_id,
            'contact_id': self.user.id,
            'published': True,
            'deleted': {'$ne': True}
        }

        report = self.collection.find_one(query, {'_id': 0})

        if not report:
            raise HTTPException(status_code=404, detail='Report not found')

        try:
            title = report.get("title") or report["s3_filename"]
            title = title.strip().replace(" ", "_")

            s3_filename = report["s3_filename"]
            _, ext = os.path.splitext(s3_filename)
            ext = ext.lower()

            if not title.lower().endswith(ext):
                title += ext  # ensure filename includes the correct extension

            quoted_filename = quote(title)

            presigned_url = s3.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": settings.SF_SRP_REPORTS_BUCKET,
                    "Key": s3_filename,
                    "ResponseContentDisposition": f'attachment; filename="{quoted_filename}"'
                },
                ExpiresIn=300,
            )

            return {'url': presigned_url}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
