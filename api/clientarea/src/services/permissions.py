from lib.portaldbapi import DocumentDB


class Permissions:
    def __init__(self, db: DocumentDB, contact_id: str):
        self.db = db
        self.collection = db.customercontacts
        self.contact_id = contact_id

        self.projection = {
            '_id': 0, 
            'client_hierarchy': 1, 
            'account_hierarchy': 1, 
            'key_accounts': 1, 
            'key_markets': 1, 
            'reporting_accounts': 1, 
            'reporting_markets': 1, 
            'reporting_customers': 1, 
            #'reporting_account_hierarchy': 1
        }

    def get_permissions(self):
        permissions = self.collection.find_one({'Id': self.contact_id}, self.projection)
        if not permissions:
            return []
        return {
            'client_hierarchy': permissions.get('client_hierarchy', []),
            'account_hierarchy': permissions.get('account_hierarchy', []),
            'key_accounts': permissions.get('key_accounts', []),
            'key_markets': permissions.get('key_markets', []),
            'reporting_accounts': permissions.get('reporting_accounts', []),
            'reporting_markets': permissions.get('reporting_markets', []),
            'reporting_customers': permissions.get('reporting_customers', []),
            #'reporting_account_hierarchy': permissions.get('reporting_account_hierarchy', [])
        }