from litestar.middleware import AbstractAuthenticationMiddleware, AuthenticationResult
from litestar.connection import ASGIConnection
from litestar.exceptions import NotAuthorizedException, PermissionDeniedException
from lib.portaldbapi import DocumentDB
from api.clientarea.src.utils.authentication import decode_jwt_token, User, API_KEY_HEADER, ASSUMED_ROLE_HEADER, ASSUMED_ROLE_REJECTED_DOMAINS, ASSUMED_ROLE_ACCEPTED_DOMAINS
from api.clientarea.src.services.features import Feature
from api.clientarea.src.services.permissions import Permissions
from api.clientarea.src.services.accesslog import AccessLogService


class JWTAuthenticationMiddleware(AbstractAuthenticationMiddleware):
    async def authenticate_request(self, connection: ASGIConnection) -> AuthenticationResult:
        db = DocumentDB()
        
        auth_header = connection.headers.get(API_KEY_HEADER)
        assumed_role_header = connection.headers.get(ASSUMED_ROLE_HEADER)
        
        if not auth_header:
            raise NotAuthorizedException()

        try:
            schema, token = auth_header.split()
            if schema.lower() != 'bearer':
                raise NotAuthorizedException(f'Invalid auth schema {schema}')
        except (ValueError, UnicodeDecodeError) as e:
            raise NotAuthorizedException(f'Decode error {e}')
        
        token = decode_jwt_token(encoded_token=token)
        
        salesforce_contact = list(db.customercontacts.find({'email': token.email.lower()}, {'Id': 1, 'internal': 1, 'email': 1, 'name': 1, 'account_id': 1, 'account_name': 1, 'account_vertical': 1, 'team_id': 1, 'team_name': 1, 'account_organisation_level': 1, 'sector': 1, 'panel_manager': 1, 'account_manager': 1, 'insights_viewer': 1, 'reporting_role': 1, 'reporting_roles': 1, 'reporting_views': 1, 'reporting_market_filter': 1, 'round_index_limit': 1}))

        if not salesforce_contact:
            raise PermissionDeniedException('User not found in Salesforce')
        
        if len(salesforce_contact) > 1:
            raise PermissionDeniedException('More than one user found in Salesforce')

        salesforce_contact = salesforce_contact[0]
        permissions = Permissions(db, salesforce_contact['Id']).get_permissions()
        features = Feature(db).get_features(contact_id=salesforce_contact['Id'], internal=salesforce_contact['internal'])
        
        user = User(
            salesforce_id=salesforce_contact['Id'],
            user_id=token.sub,
            user_internal=salesforce_contact['internal'],
            user_name=salesforce_contact['name'],
            user_email=token.email,
            user_account_id=salesforce_contact['account_id'],
            user_account_name=salesforce_contact['account_name'],
            user_account_vertical=salesforce_contact['account_vertical'],
            user_team_id=salesforce_contact['team_id'],
            user_team_name=salesforce_contact['team_name'],
            user_account_organisation_level=salesforce_contact.get('account_organisation_level', None),
            user_sector=salesforce_contact.get('sector', None),
            user_features=features,
            user_roles={'panel_manager': salesforce_contact['panel_manager'], 'account_manager': salesforce_contact['account_manager'], 'insights_viewer': salesforce_contact.get('insights_viewer', False), 'barometer_viewer': salesforce_contact.get('barometer_viewer', False)},
            user_permissions=permissions.get('client_hierarchy', []),
            user_account_hierarchy=permissions.get('account_hierarchy', []),
            user_key_accounts=permissions.get('key_accounts', []),
            user_key_markets=permissions.get('key_markets', []),
            user_reporting_role=salesforce_contact.get('reporting_role', None),
            user_reporting_roles=salesforce_contact.get('reporting_roles', []),
            user_reporting_views=salesforce_contact.get('reporting_views', {}),
            user_round_index_limit=salesforce_contact.get('round_index_limit', []),
        )

        # log the access
        AccessLogService(user, db).add_request()
        
        # the request is attempting to be under an assumed role
        # we need to confirm the authenticated user has permission
        if assumed_role_header:
            # instantly reject if authenticated user is not internal CRC
            if not user.user_email.endswith(tuple(ASSUMED_ROLE_ACCEPTED_DOMAINS)):
                raise PermissionDeniedException('Assumed role not permitted')

            # reject if assumed role is for an internal user
            if assumed_role_header.endswith(tuple(ASSUMED_ROLE_REJECTED_DOMAINS)):
                raise PermissionDeniedException('Assumed role domain rejected')

            # reject if user does not have permission
            if 'assumed_role' not in user.user_features:
                raise PermissionDeniedException('User is not permitted to assume roles')

            assumed_role_contact = db.customercontacts.find_one({'Id': assumed_role_header}, {'Id': 1, 'email': 1, 'name': 1, 'account_id': 1, 'account_name': 1, 'account_vertical': 1, 'team_id': 1, 'team_name': 1, 'account_organisation_level': 1, 'sector': 1, 'panel_manager': 1, 'account_manager': 1, 'insights_viewer': 1, 'reporting_views': 1, 'reporting_role': 1, 'reporting_roles': 1, 'reporting_market_filter': 1, 'round_index_limit': 1})
 
            if not assumed_role_contact:
                raise PermissionDeniedException('Assumed role user not found')

            assumed_role_permissions = Permissions(db, assumed_role_contact['Id']).get_permissions()
            assumed_role_features = Feature(db).get_features(contact_id=assumed_role_contact['Id'])

            user.assumed_role_id = assumed_role_contact['Id']
            user.assumed_role_name = assumed_role_contact['name']
            user.assumed_role_email = assumed_role_contact['email']
            user.assumed_role_account_id = assumed_role_contact['account_id']
            user.assumed_role_account_name = assumed_role_contact.get('account_name', None)
            user.assumed_role_account_vertical = assumed_role_contact.get('account_vertical', None)
            user.assumed_role_team_id = assumed_role_contact.get('team_id', None)
            user.assumed_role_team_name = assumed_role_contact.get('team_name', None)
            user.assumed_role_account_organisation_level = assumed_role_contact.get('account_organisation_level', None)
            user.assumed_role_sector = assumed_role_contact.get('sector', None)
            user.assumed_role_features = assumed_role_features
            user.assumed_role_roles = {'panel_manager': assumed_role_contact['panel_manager'], 'account_manager': assumed_role_contact['account_manager'], 'insights_viewer': assumed_role_contact.get('insights_viewer')}
            user.assumed_role_permissions = assumed_role_permissions.get('client_hierarchy', [])
            user.assumed_role_account_hierarchy = assumed_role_permissions.get('account_hierarchy', [])
            user.assumed_role_key_accounts = assumed_role_permissions.get('key_accounts', [])
            user.assumed_role_key_markets = assumed_role_permissions.get('key_markets', [])
            user.assumed_role_reporting_role = assumed_role_contact.get('reporting_role', None)
            user.assumed_role_reporting_roles = assumed_role_contact.get('reporting_roles', None)
            user.assumed_role_reporting_views = assumed_role_contact.get('reporting_views', {})
            user.assumed_role_round_index_limit = assumed_role_contact.get('round_index_limit', [])

            # add a flag to the request to indicate that the user is under an assumed role
            connection.state.assumed_role = assumed_role_contact['Id']

            # log access for assumed role
            AccessLogService(user, db).add_assumed_role(assumed_role_contact['Id'], assumed_role_contact['email'])

        return AuthenticationResult(user=user, auth=token)
