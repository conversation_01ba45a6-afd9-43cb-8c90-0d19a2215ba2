from litestar import Router
from litestar.middleware import DefineMiddleware
from api.clientarea.src.routers.auth import AuthRouter
from api.clientarea.src.routers.config import ConfigRouter
from api.clientarea.src.routers.panel import PanelRouter
from api.clientarea.src.routers.surveys import SurveysRouter
from api.clientarea.src.routers.reports import ReportsRouter
from api.clientarea.src.routers.srpreports import SrpReportsRouter
from api.clientarea.src.routers.dashboard import DashboardRouter
from api.clientarea.src.routers.accounts import AccountsRouter
from api.clientarea.src.routers.export import ExportRouter
from api.clientarea.src.middleware.authentication import JWTAuthenticationMiddleware


routes = [
    Router(
        path='/auth', 
        route_handlers=[AuthRouter],
    ),

    Router(
        path='/config', 
        route_handlers=[ConfigRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),

    Router(
        path='/surveys', 
        route_handlers=[SurveysRouter, AccountsRouter, PanelRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),

    Router(
        path='/reports', 
        route_handlers=[SrpReportsRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),

    Router(
        path='/overview', 
        route_handlers=[ReportsRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),

    Router(
        path='/dashboard', 
        route_handlers=[DashboardRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),

    Router(
        path='/export', 
        route_handlers=[ExportRouter],
        middleware=[DefineMiddleware(JWTAuthenticationMiddleware)],
    ),
]