from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.responses import SurveyProgressResponse
from api.clientarea.src.models.collections import SfCustomerSurveys, SfCustomerSurveyRound
from api.clientarea.src.services.confirmaccounts import ConfirmAccountsService
from api.clientarea.src.services.confirmpanel import ConfirmPanelService


class GetProgressHandler:
    def __init__(self, user: User, documentdb: DocumentDB):
        self.user = user
        self.db = documentdb

    def handler(self):
        # get customer surveys the current user has an action for
        confirm_accounts = ConfirmAccountsService.get_by_account_manager(self.db, self.user.id)

        # get survey clients by user involvement
        confirm_panel = ConfirmPanelService.get_by_panel_manager(self.db, self.user.id)

        # if we have involvement in a survey client, use that.
        # otherwise, use the customer survey
        customer_survey_rounds = confirm_panel if len(confirm_panel) else confirm_accounts

        # for now, we are only syncing the latest survey round. anymore (or less) just ignore
        if len(customer_survey_rounds) != 1:
            round_status = None
        else:
            # get the customer survey round status
            cs = SfCustomerSurveys.get_by_id(self.db, customer_survey_rounds[0].Id)
            csr = SfCustomerSurveyRound.get_by_id(self.db, cs.Customer_Survey_Round__c)
            round_status = csr.Round_Status__c

        return SurveyProgressResponse.get_current_state(stage=round_status)