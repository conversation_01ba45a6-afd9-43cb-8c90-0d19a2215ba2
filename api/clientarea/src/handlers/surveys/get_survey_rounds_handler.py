from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.responses import SurveysResponse
from api.clientarea.src.services.confirmaccounts import ConfirmAccountsService
from api.clientarea.src.services.confirmpanel import ConfirmPanelService
from api.clientarea.src.models.responses import SurveyProgressResponse
from api.clientarea.src.services.surveyrounds import SurveyRoundsService
from pprint import pprint


class GetSurveyRoundsHandler:
    def __init__(self, user: User, documentdb: DocumentDB):
        self.user = user
        self.db = documentdb

    def handler(self):
        current = []
        previous = []

        # get survey rounds that this user has permission to view
        survey_rounds = SurveyRoundsService(self.db).get_by_user_account(self.user.account)

        # sort the rounds desc so we can get the latest round        
        survey_rounds_desc = sorted(survey_rounds, key=lambda x: x.round_start_date, reverse=True)

        for survey_round in survey_rounds_desc:
            if survey_round.current_round:
                start_date = survey_round.round_start_date.strftime("%Y-%m-%d")
                end_date = survey_round.round_end_date.strftime("%Y-%m-%d")
                round_progress = SurveyProgressResponse.get_current_state(stage=survey_round.progress)
                
                # get customer surveys the current user has an action for
                confirm_accounts = ConfirmAccountsService(self.db).get_by_account_manager(survey_round.Id, self.user.id)

                # get survey clients by panel manager
                confirm_panel_by_panel_manager = ConfirmPanelService(self.db).get_by_panel_manager(survey_round.Id, self.user.id)

                # get survey clients by account manager
                confirm_panel_by_account_manager = ConfirmPanelService(self.db).get_by_account_manager(survey_round.Id, self.user.id)

                account_manager = bool(len(confirm_accounts))
                panel_manager = bool(len(confirm_panel_by_panel_manager))
                accounts_confirmed = all(d.confirm_status is not None and d.confirm_status for d in confirm_accounts)
                panel_confirmed = all(d.confirm_status is not None and d.confirm_status for d in confirm_panel_by_panel_manager)

                current.append(SurveysResponse(
                    id=survey_round.Id,
                    name=survey_round.name,
                    start_date=start_date,
                    end_date=end_date,
                    dates=f'{start_date} - {end_date}',
                    stage=survey_round.stage,
                    state=survey_round.progress,
                    involved=(account_manager or panel_manager),
                    round_progress=round_progress,
                    accounts_confirmed=accounts_confirmed,
                    panel_confirmed=panel_confirmed,
                    account_manager=account_manager,
                    panel_manager=panel_manager,
                    panel_count=(len(confirm_panel_by_panel_manager)+len(confirm_panel_by_account_manager)),
                    account_count=len(confirm_accounts),
                    type=survey_round.survey_type,
                ))
            else:
                start_date = survey_round.round_start_date.strftime("%Y-%m-%d")
                end_date = survey_round.round_end_date.strftime("%Y-%m-%d")

                previous.append(
                    SurveysResponse(
                        id=survey_round.Id,
                        name=survey_round.name,
                        start_date=start_date,
                        end_date=end_date,
                        dates=f'{start_date} - {end_date}',
                        status=survey_round.stage,
                        type=survey_round.survey_type,
                    )
                )

        return {
            'latest': current,
            'previous': previous
        }