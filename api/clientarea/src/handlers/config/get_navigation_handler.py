from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.features import Feature


class GetNavigationHandler:
    def __init__(self, user: User, documentdb: DocumentDB):
        self.user = user
        self.db = documentdb

    def handler(self):
        # See if the contact has their own navigation configuration
        navigation_config = self.db.navigation.find_one({'contact_id': self.user.id})

        # If not, use the default configuration
        if not navigation_config:
            navigation_config = self.db.config.find_one({'_id': 'navigation'})

        enabled_features = self.user.features
        visible_links = {
            'header': [],
            'footer': [],
        }

        for link_section, links in navigation_config['links'].items():
            for link in links:
                if 'exclude_features' in link and len(link['exclude_features']) > 0:
                    # Check if any of the excluded features are enabled
                    # for example, standard dashboard links are not shown if the user has barometer dashboard enabled
                    if any(feature in enabled_features for feature in link['exclude_features']):
                        continue
                if 'features' in link and len(link['features']) > 0:
                    if any(feature in enabled_features for feature in link['features']):
                        visible_links[link_section].append(link)
                else:
                    visible_links[link_section].append(link)

        return visible_links