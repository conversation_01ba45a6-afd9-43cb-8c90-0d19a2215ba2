from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.reports import SrpReportsService


class GetReportsHandler:
    def __init__(self, user: User, documentdb: DocumentDB):
        self.user = user
        self.db = documentdb

    def handler(self):
        reports = SrpReportsService(self.user, self.db).get_reports()

        return reports
        