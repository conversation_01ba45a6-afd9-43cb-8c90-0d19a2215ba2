from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.reports import SrpReportsService


class GetReportHandler:
    def __init__(self, user: User, documentdb: DocumentDB, report_id: str):
        self.user = user
        self.db = documentdb
        self.report_id = report_id

    def handler(self):
        report = SrpReportsService(self.user, self.db).get_report(self.report_id)

        return report
        