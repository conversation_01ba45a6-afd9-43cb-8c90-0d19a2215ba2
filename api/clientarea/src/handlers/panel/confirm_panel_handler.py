import datetime
from litestar.exceptions import HTTPException
from litestar.status_codes import HTTP_400_BAD_REQUEST
from lib.portaldbapi import DocumentDB
from api.clientarea.src.models.collections import ConfirmPanel
from api.clientarea.src.models.requests import ConfirmPanelRequest
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.confirmpanel import ConfirmPanelService


class ConfirmPanelHandler:
    def __init__(self, survey_id: str, data: ConfirmPanelRequest, user: User, db: DocumentDB):
        self.survey_id = survey_id
        self.data = data
        self.user = user
        self.db = db

    def handler(self):
        success: bool = False
        updated: list = []
        new_confirmed_date = datetime.datetime.now()

        # grab all survey clients this panel manager is responsible for
        all_confirm_panels = ConfirmPanelService(self.db).get_by_panel_manager(self.survey_id, self.user.id)

        # create a look of survey clients by id
        confirm_panel_by_id = {confirm_panel.Id: confirm_panel for confirm_panel in all_confirm_panels}

        # check for any validation issues
        for confirm_panel_id in self.data.panel:
            confirm_panel: ConfirmPanel = ConfirmPanelService(self.db).get_by_confirm_panel_id(confirm_panel_id, self.user.id)

            # check if the panel has been saved since the user last accessed the panel
            if confirm_panel.confirmed_panel_last_save_date is not None and (self.data.confirmed_date is None or self.data.confirmed_date.replace(microsecond=0) < confirm_panel.confirmed_panel_last_save_date.replace(microsecond=0)):
                error = f'Panel has since been saved by {confirm_panel.confirmed_panel_last_save_user_name}. Please refresh the page to get the latest updates.'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "panel_save_stale"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # check user belongs to the account the panel is assigned to
            if confirm_panel.client_id not in self.user.permissions:
                error = 'Unable to confirm the panel as you do not have the appropriate permissions'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "panel_not_in_hierarchy"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # check the user has the panel manager role
            if not self.user.roles.get('panel_manager'):
                error = 'Unable to confirm the panel as you do not have the appropriate permissions'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "panel_no_panel_manager_role"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # refuse to update if the user is not the assigned panel manager
            panel_manager_ids = [panel_manager.panel_manager_id for panel_manager in confirm_panel.panel_managers]
            if self.user.id not in panel_manager_ids:
                error = 'Cannot update panel confirmations for a panel you are not a panel manager for'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "panel_not_assigned_panel_manager"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # refuse to update if the confirmation date has passed
            if confirm_panel.panel_confirmed_by_date.date() < datetime.date.today():
                error = 'Cannot update panel confirmations after the confirmation date has passed'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "panel_confirmation_date_passed"},
                    status_code=HTTP_400_BAD_REQUEST
                )

        # update the confirmed panels
        for confirm_panel_id, new_panel in self.data.panel.items():
            pdb_last_panel_confirmed_by_id: dict = {}
            update_stats: dict[str, int] = {
                'added': [],
                'removed': [],
            }

            # add the confirm panel id to the updated list
            updated.append(confirm_panel_id)

            # grab this confirmpanel document (that is in Mongo)
            pdb_confirm_panel = confirm_panel_by_id.get(confirm_panel_id)

            # if the panel has been confirmed before, then we need to get the last panel list
            if len(pdb_confirm_panel.confirmed_panel) > 0:
                pdb_last_panel_confirmed_by_id = {
                    panel_member.contact_id: panel_member
                    for panel_member in pdb_confirm_panel.confirmed_panel[0].panel
                }
            # we have no confirmations yet, so use the "last round" as the default
            else:
                # if the panel has never been confirmed, then we need to create a blank list
                pdb_last_panel_confirmed_by_id = {
                    panel_member.contact_id: panel_member
                    for panel_member in pdb_confirm_panel.panel
                    if panel_member.in_round
                }

            confirmed_panel = []
            all_panel = []
            for panel_member in new_panel:
                panel_dict = panel_member.dict()
                panel_dict['in_round'] = False
                panel_dict.pop('selected', None)

                if panel_member.selected:
                    panel_dict['in_round'] = True
                    confirmed_panel.append(panel_dict)

                all_panel.append(panel_dict)

            confirmed_panel_by_contact_id = [panel_member['contact_id'] for panel_member in confirmed_panel]

            # set the update stats count based on the last saved panel
            update_stats['added'] = [
                {'id': panel_member['contact_id'], 'name': panel_member['contact_name'], 'account': pdb_confirm_panel.account_name, 'client': pdb_confirm_panel.client_name}
                for panel_member in confirmed_panel
                if panel_member['contact_id'] not in pdb_last_panel_confirmed_by_id.keys()
            ]
            update_stats['removed'] = [
                {'id': panel_member.contact_id, 'name': panel_member.contact_name, 'account': pdb_confirm_panel.account_name, 'client': pdb_confirm_panel.client_name}
                for contact_id, panel_member in pdb_last_panel_confirmed_by_id.items()
                if contact_id not in confirmed_panel_by_contact_id
            ]

            ConfirmPanelService(self.db).update_panel(confirm_panel_id,
                                                      self.user.id,
                                                      self.user.email,
                                                      self.user.name,
                                                      new_confirmed_date,
                                                      update_stats,
                                                      confirmed_panel,
                                                      all_panel)

        success = True

        return {
            'success': success,
            'updated': updated,
        }
