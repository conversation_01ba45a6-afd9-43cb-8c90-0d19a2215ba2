from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.responses import GetReportDefinitionResponse
from api.clientarea.src.services.overview import ReportsService


class GetReportDefinitionsHandler:
    def __init__(self, user: User, documentdb: DocumentDB):
        self.user = user
        self.db = documentdb

    def handler(self):
        definitions = ReportsService(self.user, self.db).get_reports()

        return [
            GetReportDefinitionResponse(**definition)
            for definition in definitions
        ]