from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.responses import GetReportResponse
from api.clientarea.src.services.overview import ReportsService


class GetReportHandler:
    def __init__(self, user: User, documentdb: DocumentDB, report_id: str):
        self.user = user
        self.db = documentdb
        self.report_id = report_id

    def handler(self):
        report = ReportsService(self.user, self.db).get_report_by_id(self.report_id)
        if not report:
            return GetReportResponse()
        return GetReportResponse(**report.get('report', {}))