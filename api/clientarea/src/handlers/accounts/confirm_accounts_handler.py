import datetime
from litestar.exceptions import HTTPException
from litestar.status_codes import HTTP_400_BAD_REQUEST
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.confirmaccounts import ConfirmAccountsService


class ConfirmAccountsHandler:
    def __init__(self, survey_id: str, data: dict, user: User, documentdb: DocumentDB):
        self.survey_id = survey_id
        self.data = data
        self.user = user
        self.db = documentdb

    def handler(self):
        success = False
        updated = []
        new_confirmed_date = datetime.datetime.now()

        # grab all customer surveys this account manager is responsible for
        all_confirm_accounts = ConfirmAccountsService(self.db).get_by_account_manager(self.survey_id, self.user.id)

        # create a look of customer surveys by id
        confirm_accounts_by_id = {confirm_account.Id: confirm_account for confirm_account in all_confirm_accounts}

        # check for any validation issues
        for confirm_account_id in self.data.accounts:
            confirm_account = ConfirmAccountsService(self.db).get_by_confirm_account(confirm_account_id)

            # check if the accounts have been saved since the user last accessed the accounts
            if confirm_account.confirmed_accounts_last_save_date is not None and (self.data.confirmed_date is None or self.data.confirmed_date.replace(microsecond=0) < confirm_account.confirmed_accounts_last_save_date.replace(microsecond=0)):
                error = f'Accounts have since been saved by {confirm_account.confirmed_accounts_last_save_user_name}. Please refresh the page to get the latest updates.'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "accounts_save_stale"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # check the user has the account manager role
            if not self.user.roles.get('account_manager'):
                error = 'Unable to confirm accounts as you do not have the appropriate permissions'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "accounts_no_account_manager_role"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # refuse to update if the user is not the assigned account manager
            account_manager_ids = [account_manager.account_manager_id for account_manager in confirm_account.account_managers]
            if self.user.id not in account_manager_ids:
                error = 'Cannot update account confirmations for an account you are not an account manager for'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "accounts_not_assigned_account_manager"},
                    status_code=HTTP_400_BAD_REQUEST
                )

            # refuse to update if the confirmation date has passed
            if confirm_account.accounts_confirmed_by_date.date() < datetime.date.today():
                error = 'Cannot update account confirmations after the confirmation date has passed'
                raise HTTPException(
                    detail=error,
                    extra={"error": error, "code": "accounts_confirmation_date_passed"},
                    status_code=HTTP_400_BAD_REQUEST
                )

        # update each confirmed accounts
        for confirm_account_id, new_accounts in self.data.accounts.items():
            pdb_last_accounts_confirmed_by_id: dict = {}
            pdb_last_selected_accounts_confirmed_by_id: dict = {}
            update_stats: dict[str, int] = {
                'added': [],
                'removed': [],
            }

            # add the confirm account id to the updated list
            updated.append(confirm_account_id)

            # grab this confirmaccount document (that is in Mongo)
            pdb_confirm_account = confirm_accounts_by_id.get(confirm_account_id)

            # if the pdb_confirm_account has confirmed accounts previously saved, create a lookup of the confirmed accounts by account id and force the selected flag
            if len(pdb_confirm_account.confirmed_accounts) > 0:
                # NOTE: MIG-01: this is a temporary fix to get around the fact that the confirmed_accounts_last_save_all_accounts is not always populated
                # whilst the data is being migrated
                current_all_accounts = pdb_confirm_account.confirmed_accounts_last_save_all_accounts
                if current_all_accounts is None:
                    current_all_accounts = pdb_confirm_account.confirmed_accounts[0].all_accounts

                selected_accounts_ids = [account.account_id for account in pdb_confirm_account.confirmed_accounts[0].accounts]
                for account in current_all_accounts:
                    # if the account is in the selected accounts list, set it to selected
                    if account.account_id in selected_accounts_ids:
                        account.selected = True
                    pdb_last_accounts_confirmed_by_id[account.account_id] = account

                # grab the list of just selected accoints from the last confirmed accounts
                pdb_last_selected_accounts_confirmed_by_id = {account.account_id: account for account in pdb_confirm_account.confirmed_accounts[0].accounts}
            # we have no confirmations yet, so use the "last round" as the default
            else:
                for account in pdb_confirm_account.accounts:
                    # if the account was in the last round of selected accounts, pick it
                    if account.in_round:
                        account.selected = True
                        pdb_last_accounts_confirmed_by_id[account.account_id] = account

                # grab the list of just selected accounts from the last round
                pdb_last_selected_accounts_confirmed_by_id = {account.account_id: account for account in pdb_last_accounts_confirmed_by_id.values() if account.selected}

            # iterate over the new accounts list from the UI so we can merge it with the last confirmed accounts list from Mongo
            for account in new_accounts:
                # if selected is False (meaning the user has unselected this account), set it to unselected
                if account.account_id in pdb_last_selected_accounts_confirmed_by_id.keys() and account.selected is False:
                    pdb_last_accounts_confirmed_by_id[account.account_id].selected = False
                    update_stats['removed'].append({'id': account.account_id, 'name': account.account_name, 'client': pdb_confirm_account.client_name})
                # if selected is True (meaning the user kept it selected or added it new), we want to replace the entry entirely as it might have new data (i.e. panel managers, signatory)
                else:
                    # check to see if this is a new account so we can adjust the update stats
                    if account.account_id not in pdb_last_selected_accounts_confirmed_by_id.keys() and account.selected:
                        update_stats['added'].append({'id': account.account_id, 'name': account.account_name, 'client': pdb_confirm_account.client_name})

                    pdb_last_accounts_confirmed_by_id[account.account_id] = account

            # re-shape the new accounts list
            pdb_last_accounts_confirmed = list(pdb_last_accounts_confirmed_by_id.values())

            # remove any "unselected" accounts
            selected_confirmed_accounts = []
            all_confirmed_accounts = []
            for account in pdb_last_accounts_confirmed:
                account_dict = account.dict()
                account_dict.pop('selected', None)
                account_dict['in_round'] = True

                if account.selected:
                    selected_confirmed_accounts.append(account_dict)

                all_confirmed_accounts.append(account_dict)

            ConfirmAccountsService(self.db).update_accounts(confirm_account_id,
                                                            self.user.id,
                                                            self.user.email,
                                                            self.user.name,
                                                            new_confirmed_date,
                                                            update_stats,
                                                            selected_confirmed_accounts,
                                                            all_confirmed_accounts)
        success = True

        return {
            'success': success,
            'updated': updated,
            'new_confirmed_date': new_confirmed_date,
        }
