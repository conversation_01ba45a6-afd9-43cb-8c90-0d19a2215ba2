import datetime
from typing import List, Dict, Set, Tuple
from lib.portaldbapi import DocumentDB
from api.clientarea.src.models.collections import ConfirmAccounts, ConfirmAccountsAccount
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.collections import SfContacts
from api.clientarea.src.models.responses import ConfirmAccountsResponse
from api.clientarea.src.services.contact import CustomerContactService
from api.clientarea.src.services.confirmaccounts import ConfirmAccountsService


class GetAccountsHandler:
    def __init__(self, survey_id: str, user: User, documentdb: DocumentDB):
        self.survey_id = survey_id
        self.user = user
        self.db = documentdb

    
    def __generate_client_idenfitifers(self, 
                                       ca_client_id: str, 
                                       ca_client_name: str, 
                                       ca_team_id: str, 
                                       ca_team_name: str, 
                                       ca_team_market: str, 
                                       vertical: str) -> tuple:    
        client_key:str = ca_client_id
        client_name:str = ca_client_name
        client_name_with_team:str = ca_client_name
        team_id:str = 'xxx' # team_id is not always populated, but is part of the account_key ... this combats pythons/js null vs None when stringifyed
        if ca_team_id:
            team_id:str = ca_team_id
            client_key:str = f'{ca_client_id}-{ca_team_id}'
            client_name_with_team:str = f'{client_name} ({ca_team_name})'
            if vertical == 'adv':
                client_name:str = f'{client_name} ({ca_team_name} - {ca_team_market})'

        return client_key, client_name, client_name_with_team, team_id


    def __add_confirmation_state_account(self,
                                         account:ConfirmAccountsAccount, 
                                         new_panel_managers:List, 
                                         confirm_account:ConfirmAccounts, 
                                         client_name:str, 
                                         client_name_with_team:str, 
                                         team_id:str, 
                                         selected:bool) -> Dict:
        survey_panel_manager_email:List[str] = []
        for pm in account.survey_panel_managers:
            survey_panel_manager_email.append(pm.panel_manager_email)
            if pm.panel_manager_id.startswith('NEW'):
                new_panel_managers.append(pm)

        pm_string:str = '|'.join(survey_panel_manager_email)

        return dict(
            internal_id=confirm_account.Id,
            key=f'{confirm_account.client_id}-{team_id}-{account.account_id}',
            account_id=account.account_id,
            account_name=account.account_name,
            team_id=team_id,
            team_name=confirm_account.team_name,
            team_market=confirm_account.team_market,
            client_id=confirm_account.client_id,
            client_name=client_name,
            client_name_with_team=client_name_with_team,
            client_market=confirm_account.client_market,
            client_market_id=confirm_account.client_market_id,
            client_organisation_level_3_name=confirm_account.client_organisation_level.level_3.name,
            client_organisation_level_4_name=confirm_account.client_organisation_level.level_4.name,
            client_organisation_level_5_name=confirm_account.client_organisation_level.level_5.name,
            market_id=confirm_account.client_market_id,
            confirm_account_id=confirm_account.Id,
            survey_round=confirm_account.survey_round,
            survey_name=account.survey_name,
            survey_panel_manager_email=pm_string,
            survey_panel_managers=account.survey_panel_managers,
            signatory_id=account.signatory_id,
            signatory_email=account.signatory_email,
            signatory_name=account.signatory_name,
            in_round=account.in_round,
            added_by_contact=account.added_by_contact,
            selected=selected,
        )
    

    def __add_confirmed_state_account(self,
                                      account:ConfirmAccountsAccount, 
                                      confirm_account:ConfirmAccounts, 
                                      client_name:str, 
                                      client_name_with_team:str, 
                                      team_id:str) -> Dict:
        survey_panel_manager_email:List[str] = [pm.panel_manager_email for pm in account.survey_panel_managers]
        pm_string:str = '|'.join(survey_panel_manager_email)

        return dict(
            internal_id=confirm_account.Id,
            key=f'{confirm_account.client_id}-{team_id}-{account.account_id}',
            account_id=account.account_id,
            account_name=account.account_name,
            team_id=team_id,
            team_name=confirm_account.team_name,
            team_market=confirm_account.team_market,
            client_id=confirm_account.client_id,
            client_name=client_name,
            client_name_with_team=client_name_with_team,
            client_market=confirm_account.client_market,
            client_market_id=confirm_account.client_market_id,
            client_organisation_level_3_name=confirm_account.client_organisation_level.level_3.name,
            client_organisation_level_4_name=confirm_account.client_organisation_level.level_4.name,
            client_organisation_level_5_name=confirm_account.client_organisation_level.level_5.name,
            market_id=confirm_account.client_market_id,
            confirm_account_id=confirm_account.Id,
            survey_round=confirm_account.survey_round,
            survey_name=account.survey_name,
            survey_panel_manager_email=pm_string,
            survey_panel_managers=account.survey_panel_managers,
            signatory_id=account.signatory_id,
            signatory_email=account.signatory_email,
            signatory_name=account.signatory_name,
            in_round=account.in_round,
            added_by_contact=account.added_by_contact,
        )
    

    def __sort_and_dedupe_audit(self, confirmed_audit:List) -> List:
        confirmed_date:str|None = None
        confirmed_user:str|None = None
        confirmed_email:str|None = None
        confirmed_audit_dedupe:List = []
        merged:Dict = {}

        if confirmed_audit:
            confirmed_audit.sort(key=lambda d: d['date'], reverse=True)

            # since users can save multiple customer surveys at once, we want to dedupe the list
            for item in confirmed_audit:
                saved_user:str = item['email']
                saved_date:str = item['date'].strftime('%Y-%m-%d %H:%M:%S')
                key:Tuple = (saved_user, saved_date)

                item:Dict = {**item, 'date': saved_date}
                item_added:List = item.get('added', [])
                item_removed:List = item.get('removed', [])

                if key not in merged:
                    merged[key] = item
                    merged[key]['added'] = item_added.copy()
                    merged[key]['removed'] = item_removed.copy()
                else:
                    merged[key]['added'].extend(item_added)
                    merged[key]['removed'].extend(item_removed)

            confirmed_audit_dedupe:List = list(merged.values())

            confirmed_date:str = confirmed_audit_dedupe[0].get('date')
            confirmed_user:str = confirmed_audit_dedupe[0].get('user')
            confirmed_email:str = confirmed_audit_dedupe[0].get('email')  

        return confirmed_audit_dedupe, confirmed_date, confirmed_user, confirmed_email  


    def handler(self):
        in_round_accounts:List = []
        f_clients:List = []
        f_panel_managers:List = []
        f_account_managers:List = []
        f_signatories:List = []
        complete_by_date:datetime.datetime|None = None
        complete_by_days:int|None = None
        new_panel_managers:List = []
        account_vertical:str = self.user.account_vertical
        confirmed_audit:List = []
        customer_survey_round_name:str|None = None
        active_confirmed_accounts:List = []
        closed_confirmed_accounts:List = []
        today:datetime.datetime = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

        # get all confirm accounts for this survey
        confirm_accounts:List[ConfirmAccounts] = ConfirmAccountsService(self.db).get_by_account_manager(self.survey_id, self.user.id)

        # split confirm accounts into those that have been confirmed and synced and those that haven't
        for confirm_account in confirm_accounts:
            # set customer survey round name
            customer_survey_round_name:str = confirm_account.customer_survey_round_name

            if confirm_account.accounts_confirmed_by_date and confirm_account.accounts_confirmed_by_date < today:
                closed_confirmed_accounts.append(confirm_account)
            else:
                active_confirmed_accounts.append(confirm_account)

        # if we have active confirmed accounts, we should use them
        if active_confirmed_accounts:
            for confirm_account in active_confirmed_accounts:
                ui_prepop_accounts:List = []
                reporting_view_in_round_accounts:List = []
                reporting_view_new_accounts:List = []
                reporting_view_action:bool = False

                if confirm_account.accounts_confirmed_by_date and (not complete_by_date or confirm_account.accounts_confirmed_by_date < complete_by_date):
                    complete_by_date = confirm_account.accounts_confirmed_by_date.replace(tzinfo=None)
                    complete_by_days = (complete_by_date - datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)).days + 1

                # generate dynamic identifiers around the client
                client_key, client_name, client_name_with_team, team_id = self.__generate_client_idenfitifers(confirm_account.client_id, confirm_account.client_name, confirm_account.team_id, confirm_account.team_name, confirm_account.team_market, account_vertical)

                # if there has been a confirmation, use that data to pre-populate the accounts
                if len(confirm_account.confirmed_accounts) > 0:
                    # get a list of all the edits
                    for confirm in confirm_account.confirmed_accounts:
                        audit:Dict = {
                            'user': confirm.confirm_user_name, 
                            'email': confirm.confirm_user_email, 
                            'date': confirm.confirm_date.replace(microsecond=0)
                        }
                        # FIXME: once the data has been migrated, we can remove this and add it above
                        if confirm.stats:
                            audit['added'] = confirm.stats['added']
                            audit['removed'] = confirm.stats['removed']
                        confirmed_audit.append(audit)

                    # get a list of all the latest accounts (selected + unselected)
                    # NOTE: MIG-01: this is a temporary fix to get around the fact that the confirmed_accounts_last_save_all_accounts is not always populated
                    # whilst the data is being migrated
                    accounts:List[ConfirmAccountsAccount] = confirm_account.confirmed_accounts_last_save_all_accounts
                    if accounts is None:
                        accounts = confirm_account.confirmed_accounts[0].all_accounts

                    # get a list of all the selected confirmed accounts
                    selected_accounts = [account.account_id for account in confirm_account.confirmed_accounts[0].accounts]

                    for account in accounts:
                        res:Dict = self.__add_confirmation_state_account(
                            account=account,
                            new_panel_managers=new_panel_managers,
                            confirm_account=confirm_account,
                            client_name=client_name,
                            client_name_with_team=client_name_with_team,
                            team_id=team_id,
                            selected=bool(account.account_id in selected_accounts)
                        )
                        ui_prepop_accounts.append(res)
                # if there has been no confirmations, default to last rounds accounts
                # FIXME: move this logic to confirmaccount sync agent
                else:
                    for account in confirm_account.accounts:
                        if not account.in_round:
                            continue

                        res:Dict = self.__add_confirmation_state_account(
                            account=account,
                            new_panel_managers=new_panel_managers,
                            confirm_account=confirm_account,
                            client_name=client_name,
                            client_name_with_team=client_name_with_team,
                            team_id=team_id,
                            selected=True
                        )
                        ui_prepop_accounts.append(res)

                # add in any accounts that have been added in SF
                # FIXME: move this logic to confirmaccount sync agent
                for account in confirm_account.accounts:
                    ui_prepop_accounts_ids:List[Dict] = [ui_account['account_id'] for ui_account in ui_prepop_accounts]
                    if account.account_id not in ui_prepop_accounts_ids and account.in_round:
                        res:Dict = self.__add_confirmation_state_account(
                            account=account,
                            new_panel_managers=new_panel_managers,
                            confirm_account=confirm_account,
                            client_name=client_name,
                            client_name_with_team=client_name_with_team,
                            team_id=team_id,
                            selected=True
                        )
                        ui_prepop_accounts.append(res)

                # if the user has an account(s) and/or market(s) reporting role, lets filter to only see their key accounts (and force "in_round" so it appears)
                if any(role in self.user.reporting_roles for role in ['Account', 'Market']):
                    report_view_accounts:Set = set()
                    report_view_accounts_type_account:Set = set()
                    reporting_view_action:bool = True
                    
                    # loop round all reporting views that have type "Account" or "Market" and get the accounts
                    for view in self.user.reporting_views.values():
                        if view['type'] == 'Account':
                            view_accounts:List[str] = view.get('accounts', [])
                            view_accounts_related:List[str] = [account.account_id for account in confirm_account.accounts]
                            view_accounts_confirmed:List[Dict] = [
                                account 
                                for account in ui_prepop_accounts 
                                if (account['account_id'] in view_accounts and account['account_id'] not in view_accounts_related) or (self.user.email == account.get('added_by_contact'))
                            ]
                            report_view_accounts.update(view_accounts)
                            report_view_accounts_type_account.update(view_accounts)

                            # add any new accounts that have been added to the reporting view
                            reporting_view_new_accounts_ids:List[str] = [account['account_id'] for account in reporting_view_new_accounts]
                            for account in view_accounts_confirmed:
                                if account['account_id'] not in reporting_view_new_accounts_ids:
                                    reporting_view_new_accounts.append(account)
                        if view['type'] == 'Market':
                            markets:List[str] = view.get('markets', [])
                            market_accounts:List[str] = [account.account_id for account in confirm_account.accounts if account.market_id in markets]
                            market_accounts_confirmed:List[Dict] = [
                                account
                                for account in ui_prepop_accounts 
                                if (account['account_id'] not in market_accounts and (account.get('market_id') in markets or account.get('market_id') == None)) or (self.user.email == account.get('added_by_contact'))
                            ]
                            report_view_accounts.update(market_accounts)
                            
                            # add any new accounts that have been added to the reporting view
                            reporting_view_new_accounts_ids:List[str] = [account['account_id'] for account in reporting_view_new_accounts]
                            for market_account in market_accounts_confirmed:
                                if market_account['account_id'] not in reporting_view_new_accounts_ids:
                                    reporting_view_new_accounts.append(market_account)

                    report_view_accounts:List = list(report_view_accounts)
                    report_view_accounts_type_account:List = list(report_view_accounts_type_account)

                    # filter to only accounts in both the report view and the confirmaccount
                    accounts:List = [account for account in confirm_account.accounts if account.account_id in report_view_accounts]

                    # we want to force any account added via "account" type into in_round state so the GUI prepopulates them
                    for account in accounts:
                        if account.account_id in report_view_accounts_type_account:
                            account.in_round = True

                    # get list of account ids
                    account_ids:List = [account.account_id for account in accounts]

                    # iterate through the in_round_accounts list and pick out the accounts that are in the report view list
                    for account in ui_prepop_accounts:
                        if account['in_round'] and account['account_id'] in account_ids:
                            reporting_view_in_round_accounts.append(account)
                
                f_clients.append(dict(
                    key=client_key,
                    id=confirm_account.client_id,
                    client_id=confirm_account.client_id,
                    client_name=confirm_account.client_name,
                    team_id=team_id,
                    team_name=confirm_account.team_name,
                    name=client_name,
                    name_with_team=client_name_with_team,
                    market=confirm_account.client_market,
                    market_id=confirm_account.client_market_id,
                    organisation_level_3_name=confirm_account.client_organisation_level.level_3.name,
                    organisation_level_4_name=confirm_account.client_organisation_level.level_4.name,
                    organisation_level_5_name=confirm_account.client_organisation_level.level_5.name,
                    confirm_account_id=confirm_account.Id,
                    survey_round=confirm_account.survey_round,
                    value=confirm_account.client_id,
                    label=(client_name_with_team if account_vertical == 'mfv' else client_name),
                ))

                # re-map the in_round_accounts if we have reporting_role specific
                if reporting_view_action:
                    new_account_ids:Dict = {d['account_id'] for d in reporting_view_new_accounts}
                    in_round_accounts.extend(reporting_view_new_accounts)
                    for d in reporting_view_in_round_accounts:
                        if d['account_id'] not in new_account_ids:
                            in_round_accounts.append(d)                
                else:
                    in_round_accounts.extend(ui_prepop_accounts)
            
            # get remaining filter values
            f_panel_managers:List = CustomerContactService(self.db).get_panel_managers([client['id'] for client in f_clients])
            f_signatories:List = CustomerContactService(self.db).get_signatories([client['id'] for client in f_clients])
            f_account_managers:List = SfContacts.get_account_managers_by_account_id(self.db, [client['id'] for client in f_clients])

            # append any new panel managers
            new_pm_to_add:Dict = {}
            for pm in new_panel_managers:
                if pm.panel_manager_id not in new_pm_to_add:
                    new_pm_to_add[pm.panel_manager_id] = pm
            
            for pm in new_pm_to_add.values():
                # NOTE: this is rough, but gets past the legacy round issue of only name being stored
                if pm.panel_manager_first_name is None or pm.panel_manager_last_name is None:
                    pm.panel_manager_first_name = pm.panel_manager_name.split(' ')[0]
                    pm.panel_manager_last_name = pm.panel_manager_name.split(' ')[1]

                f_panel_managers.append(SfContacts(
                    Id=pm.panel_manager_id,
                    FirstName=pm.panel_manager_first_name,
                    LastName=pm.panel_manager_last_name,
                    Email=pm.panel_manager_email,
                    AccountId=None,
                    Contact_Type__c=None,
                    Title=None,
                    ClientHierarchy=[]
                ))
            
            # order filters
            f_clients:List = sorted(f_clients, key=lambda x: x['name'])
            f_panel_managers:List = sorted(f_panel_managers, key=lambda x: x.FirstName)
            f_signatories:List = sorted(f_signatories, key=lambda x: x.FirstName)
            f_account_managers:List = sorted(f_account_managers, key=lambda x: x['name'])

        # if we have no active confirmed accounts, but we do have closed confirmed accounts, we should use them
        elif closed_confirmed_accounts:
            in_round_accounts:List = []
            confirmed_audit:List = []
            
            for confirm_account in closed_confirmed_accounts:
                # generate dynamic identifiers around the client
                client_key, client_name, client_name_with_team, team_id = self.__generate_client_idenfitifers(confirm_account.client_id, confirm_account.client_name, confirm_account.team_id, confirm_account.team_name, confirm_account.team_market, account_vertical)

                if confirm_account.accounts_confirmed_by_date and (not complete_by_date or confirm_account.accounts_confirmed_by_date < complete_by_date):
                    complete_by_date = confirm_account.accounts_confirmed_by_date.replace(tzinfo=None)

                # get a list of all the edits
                for confirm in confirm_account.confirmed_accounts:
                    audit:Dict = {
                        'user': confirm.confirm_user_name, 
                        'email': confirm.confirm_user_email, 
                        'date': confirm.confirm_date.replace(microsecond=0)
                    }
                    # FIXME: once the data has been migrated, we can remove this and add it above
                    if confirm.stats:
                        audit['added'] = confirm.stats['added']
                        audit['removed'] = confirm.stats['removed']
                    confirmed_audit.append(audit)
                
                for account in confirm_account.final_confirmed_accounts:
                    res:Dict = self.__add_confirmed_state_account(
                        account=account,
                        confirm_account=confirm_account,
                        client_name=client_name,
                        client_name_with_team=client_name_with_team,
                        team_id=team_id
                    )
                    in_round_accounts.append(res)
        
        # sort audit and get latest save
        confirmed_audit_dedupe, confirmed_date, confirmed_user, confirmed_email = self.__sort_and_dedupe_audit(confirmed_audit)

        # order accounts
        in_round_accounts:List = sorted(in_round_accounts, key=lambda x: (x['client_name'], x['account_name']))

        return ConfirmAccountsResponse(
            customer_survey_round_name=customer_survey_round_name,
            user_account_name=self.user.account_name,
            action_required=bool(len(confirm_accounts)),
            complete_by_date=complete_by_date,
            complete_by_days=complete_by_days,
            accounts=in_round_accounts,
            confirmed_audit=confirmed_audit_dedupe,
            confirmed_date=confirmed_date,
            confirmed_user=confirmed_user,
            confirmed_email=confirmed_email,
            confirmed=bool(len(active_confirmed_accounts) == 0 and len(closed_confirmed_accounts) > 0),
            account_vertical=account_vertical,
            f_clients=f_clients,
            f_panel_managers=f_panel_managers,
            f_signatories=f_signatories,
            f_account_managers=f_account_managers,
        )