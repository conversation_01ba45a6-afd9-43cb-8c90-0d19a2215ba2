from typing import Optional
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.customerclients import CustomerClientsService


class GetAccountsSearchHandler:
    def __init__(self, survey_id: str, q: Optional[str], limit: int, user: User, documentdb: DocumentDB):
        self.survey_id:str = survey_id
        self.query:Optional[str] = q
        self.limit:int = limit
        self.user:User = user
        self.db:DocumentDB = documentdb

    def handler(self):
        accounts = CustomerClientsService(self.db, self.user, self.survey_id).get_by_query(self.query, self.limit)

        return accounts