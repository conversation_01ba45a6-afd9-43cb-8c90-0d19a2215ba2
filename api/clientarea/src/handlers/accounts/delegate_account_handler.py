from litestar.exceptions import HTTPException
from litestar.status_codes import HTTP_400_BAD_REQUEST
from lib.portaldbapi import DocumentDB
from api.clientarea.src.dependencies import User
from api.clientarea.src.services.confirmaccounts import ConfirmAccountsService
from api.clientarea.src.services.contact import CustomerContactService
from api.clientarea.src.models.collections import CustomerContacts, ConfirmAccounts
from api.clientarea.src.handlers.accounts.get_accounts_handler import GetAccountsHandler


class DelegateAccountHandler:
    def __init__(self, data: dict, survey_id: str, user: User, documentdb: DocumentDB):
        self.data = data
        self.survey_id = survey_id
        self.user = user
        self.db = documentdb

    def handler(self):
        cas: ConfirmAccountsService = ConfirmAccountsService(self.db)
        contact: CustomerContactService = CustomerContactService(self.db)

        # get stuff off post
        confirm_account_id: str = self.data.confirm_account_id
        delegate_account_manager_id: str = self.data.delegate_id

        # get the survey client from db
        confirm_account: ConfirmAccounts = cas.get_by_confirm_account(confirm_account_id)
        current_confirm_status: bool = confirm_account.confirm_status

        # get the delegate contact from db
        delegate_contact: CustomerContacts = contact.get_by_id(delegate_account_manager_id)

        # check the current survey manager is the same as the current user
        account_manager_ids = [account_manager.account_manager_id for account_manager in confirm_account.account_managers]
        if not self.user.id in account_manager_ids:
            error = 'You are not the current account manager so cannot delegate this account'
            raise HTTPException(
                detail=error,
                extra={"error": error, "code": 'delegate_not_assigned_account_manager'},  
                status_code=HTTP_400_BAD_REQUEST
            )

        # check the delegate is associated with this account
        if confirm_account.client_id not in delegate_contact.client_hierarchy:
            error = 'Delegate is not associated with this account'
            raise HTTPException(
                detail=error,
                extra={"error": error, "code": 'delegate_not_associated'},  
                status_code=HTTP_400_BAD_REQUEST
            )
        
        # check the delegate has the account manager role
        if not delegate_contact.account_manager:
            error = 'Account manager selected for delegation does not have account manager role'
            raise HTTPException(
                detail=error,
                extra={"error": error, "code": 'delegate_not_account_manager_role'},  
                status_code=HTTP_400_BAD_REQUEST
            )
        
        # check the survey client is not already confirmed
        if current_confirm_status:
            error = 'Accounts have already confirmed so unable to delegate'
            raise HTTPException(
                detail=error,
                extra={"error": error, "code": 'delegate_account_confirmed'},  
                status_code=HTTP_400_BAD_REQUEST
            )

        # update the confirm account with the new account manager in the db
        cas.update_account_manager(confirm_account_id, self.user.id, delegate_account_manager_id, delegate_contact.name, delegate_contact.email)

        # return the updated confirm account
        return GetAccountsHandler(self.survey_id, self.user, self.db).handler()