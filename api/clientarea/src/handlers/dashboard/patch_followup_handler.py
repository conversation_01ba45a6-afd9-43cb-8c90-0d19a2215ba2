import datetime
from litestar.exceptions import NotFoundException
from lib.portaldbapi import DocumentDB


class PatchFollowUpHandler:
    def __init__(self, panel_member_id:str, user, db: DocumentDB):
        self.db = db
        self.user = user
        self.panel_member_id = panel_member_id

    async def handler(self):
        """ Update a response document with details of who clicked the copy or email button on the NoResponses widget
            on the followup dashboard.
        """
        response_collection = self.db.responses

        # get the 'not responded' response for the panel member
        response = await response_collection.find_one({ "Id": self.panel_member_id, "response_id": None }, projection={ "responded": 1 })

        if not response:
            raise NotFoundException()

        if response['responded']:
            return { "status": "already_responded" }

        # update the response with followup details
        details = {
            'followup_action_user_id': self.user.id, 
            'followup_action_user': self.user.name, 
            'followup_action_date': datetime.datetime.now(datetime.UTC).isoformat()
        }
        response_collection.update_one({ "Id": self.panel_member_id, "response_id": None}, {'$set': details})

        return { "status": "ok", 'details': details }
