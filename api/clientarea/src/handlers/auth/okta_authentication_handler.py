import base64
import requests
from lib.cognitoapi import Cognito
from lib.settings import settings
from api.clientarea.src.models.requests import OktaAuthenticationRequest
from api.clientarea.src.models.responses import CognitoResponse


class OktaAuthenticationHandler:
    def __init__(self, data: OktaAuthenticationRequest, cognito: Cognito):
        self.cognito = cognito
        self.code = data.code

    def handler(self) -> CognitoResponse:
        status = 'failure'
        data = None

        try:
            # base64 encode the client id and secret
            text = ":".join([
                settings.cognito_client_id,
                settings.cognito_client_secret
            ])
            auth_encoded_bytes = base64.b64encode(text.encode('utf-8'))
            auth_encoded_string = auth_encoded_bytes.decode('utf-8')

            url = settings.cognito_domain + "/oauth2/token"
            payload = {
                "grant_type": "authorization_code",
                "redirect_uri": settings.cognito_redirect_uri,
                "code": self.code
            }
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Authorization": "Basic " + auth_encoded_string
            }

            response = requests.post(url, data=payload, headers=headers)
            data = response.json()
            status = 'ok'
        except Exception as e:
            print(f"OktaAuthenticationHandler: {repr(e)}")

        return CognitoResponse(
            status=status,
            data=data
        )
