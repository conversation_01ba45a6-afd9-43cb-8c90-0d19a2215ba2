import boto3
from lib.cognitoapi import Cognito
from lib.settings import settings
from api.clientarea.src.models.requests import OktaIssuerRequest
from api.clientarea.src.models.responses import OktaIssuerResponse


class OktaIssuerHandler:
    def __init__(self, data: OktaIssuerRequest, cognito: Cognito):
        self.cognito = cognito
        self.iss = data.iss

    def handler(self) -> OktaIssuerResponse:
        url = None
        try:
            # Look up the identity provider
            identity_provider = None

            client = boto3.client('cognito-idp')
            identity_providers = client.list_identity_providers(
                UserPoolId=settings.cognito_user_pool_id
            )

            for provider in identity_providers['Providers']:
                provider_description = client.describe_identity_provider(
                    UserPoolId=settings.cognito_user_pool_id,
                    ProviderName=provider["ProviderName"]
                )

                issuer = provider_description["IdentityProvider"]["ProviderDetails"]["oidc_issuer"]
                if issuer[:len(self.iss)] == self.iss:
                    identity_provider = provider['ProviderName']
                    break

            if not identity_provider:
                raise ValueError(
                    "Identity Provider is empty for issuer", self.iss)

            # Construct the url
            url = "".join([
              settings.cognito_domain,
              "/oauth2/authorize",
              "?response_type=CODE",
              "&scope=email openid profile",
              "&identity_provider={}".format(identity_provider),
              "&redirect_uri={}".format(settings.cognito_redirect_uri),
              "&client_id={}".format(settings.cognito_client_id),
            ])
        except Exception as e:
            print(f"OktaIssuerHandler: {repr(e)}")

        return OktaIssuerResponse(
            url=url
        )
