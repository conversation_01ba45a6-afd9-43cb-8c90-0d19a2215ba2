import botocore
from lib.cognitoapi import Cognito
from api.clientarea.src.models.requests import ResetPasswordRequest
from api.clientarea.src.utils.cognito_errors import CognitoError
from api.clientarea.src.models.responses import CognitoResponse


class ResetPasswordHandler:
    def __init__(self, data: ResetPasswordRequest, cognito: Cognito):
        self.cognito = cognito
        self.username = data.username

    def handler(self) -> CognitoResponse:
        status = 'failure'
        data = None
        error = None
        
        try:
            session = self.cognito.forgotten_password(self.username)
            status = 'ok'
            data = session
        except botocore.exceptions.ClientError as e:
            cognito_error = CognitoError('forgotten_password', e.response['Error'])
            error = {
                'code': cognito_error.error_code,
                'message': cognito_error.error_message,
            }
            print(repr(e))
        except Exception as e:
            print(repr(e))
         
        return CognitoResponse(
            status=status,
            data=data,
            error=error,
        )