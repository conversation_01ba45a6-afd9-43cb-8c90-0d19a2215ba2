import urllib.parse
import botocore
from cryptography.fernet import <PERSON><PERSON><PERSON>
from lib.settings import settings
from lib.cognitoapi import Cognito
from api.clientarea.src.models.requests import ResetPasswordVerificationRequest
from api.clientarea.src.utils.cognito_errors import CognitoError
from api.clientarea.src.models.responses import CognitoResponse


class ResetPasswordVerificationHandler:
    def __init__(self, data: ResetPasswordVerificationRequest, cognito: Cognito):
        self.cognito = cognito
        self.username = data.username
        self.password = data.password
        self.encrypted_params = data.c

    def handler(self) -> CognitoResponse:
        status = 'failure'
        data = None
        error = None
        
        try:
            fernet = Fernet(settings.confirmation_code_encryption_key)
            decrypted_params = fernet.decrypt(self.encrypted_params.encode()).decode()
            params = urllib.parse.parse_qs(decrypted_params)
            session = self.cognito.confirm_forgotten_password(self.username, self.password, params['code'][0])
            status = 'ok'
            data = session
        except botocore.exceptions.ClientError as e:
            cognito_error = CognitoError('confirm_forgotten_password', e.response['Error'])
            error = {
                'code': cognito_error.error_code,
                'message': cognito_error.error_message,
            }
            print(repr(e))
        except Exception as e:
            print(repr(e))
        
        return CognitoResponse(
            status=status,
            data=data,
            error=error,
        )