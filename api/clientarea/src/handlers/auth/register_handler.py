import botocore
from lib.portaldbapi import DocumentDB
from lib.cognitoapi import Cognito
from api.clientarea.src.utils.cognito_errors import CognitoError
from api.clientarea.src.models.requests import RegisterRequest
from api.clientarea.src.models.responses import CognitoResponse
from api.clientarea.src.services.pendingcustomercontacts import PendingCustomerContactService


class RegisterHandler:
    def __init__(self, data: RegisterRequest, documentdb: DocumentDB, cognito: Cognito):
        self.db = documentdb
        self.cognito = cognito
        self.username = data.username
        self.password = data.password
        self.first_name = data.first_name
        self.last_name = data.last_name

    def handler(self) -> CognitoResponse:
        status = 'failure'
        data = None
        error = None

        try:
            pc = PendingCustomerContactService(self.db)
            session = self.cognito.sign_up(self.username, self.password)
            status = 'ok'
            data = session

            # add user as a pending contacts
            pc.add_pending_contact(self.username, self.first_name, self.last_name, session['UserSub'])

        except botocore.exceptions.ClientError as e:
            cognito_error = CognitoError('sign_up', e.response['Error'])
            error = {
                'code': cognito_error.error_code,
                'message': cognito_error.error_message,
            }
            print(repr(e))
        except Exception as e:
            print(repr(e))

        return CognitoResponse(
            status=status,
            data=data,
            error=error,
        )
