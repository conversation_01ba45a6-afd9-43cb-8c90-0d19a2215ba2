import botocore
from lib.cognitoapi import Cognito
from api.clientarea.src.utils.cognito_errors import CognitoError
from api.clientarea.src.models.requests import LoginRequest
from api.clientarea.src.models.responses import CognitoResponse


class LoginHandler:
    def __init__(self, data: LoginRequest, cognito: Cognito):
        self.cognito = cognito
        self.username = data.username
        self.password = data.password

    def handler(self) -> CognitoResponse:
        status = 'failure'
        data = None
        error = None

        try:
            session = self.cognito.initiate_auth(self.username, self.password)
            status = 'ok'
            data = {
                'access_token': session.get('AuthenticationResult', {}).get('AccessToken'),
                'refresh_token': session.get('AuthenticationResult', {}).get('RefreshToken'),
                'id_token': session.get('AuthenticationResult', {}).get('IdToken'),
                'expires_in': session.get('AuthenticationResult', {}).get('ExpiresIn'),
            }
        except botocore.exceptions.ClientError as e:
            cognito_error = CognitoError('initiate_auth', e.response['Error'])
            error = {
                'code': cognito_error.error_code,
                'message': cognito_error.error_message,
            }
            print(repr(e))
        except Exception as e:
            print(repr(e))

        return CognitoResponse(
            status=status,
            data=data,
            error=error,
        )
