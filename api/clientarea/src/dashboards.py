from typing import TypedDict, List, Optional, Dict, Union, cast


class LayoutDict(TypedDict):
    sm: str
    lg: str


class CardDict(TypedDict, total=False):
    layout: LayoutDict
    type: str
    title: Optional[str]
    description: Optional[str]
    info: Optional[str]
    data_required: List[str]
    include_non_response: bool
    dedupe_responses: Optional[bool]
    previous_rounds: int
    round_index: List[int]
    filters: List[str]
    headerSeperator: bool
    query: Dict[str, Union[str, int]]
    projection: Dict[str, Union[int, bool]]


class DashboardDict(TypedDict):
    _id: str
    title: str
    type: str
    cards: List[CardDict]
    showUserInfo: bool
    userFilterable: bool


DashboardConfig = List[DashboardDict]


def get_dashboard_by_type(dashboard_type):
    return next((d for d in dashboards if d['type'] == dashboard_type), None)


dashboards = cast(DashboardConfig, [
    {
        "title": "Overview (360)",
        "type": "overview360",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 5"
                },
                "type": "RatingBarometer",
                "title": "Barometer",
                "description": "Results from contacts surveyed",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 8 / 1",
                    "lg": "1 / 5 / 4 / 9"
                },
                "type": "Rating360",
                "title": "360",
                "description": "The gap between Barometer and TRR ratings",
                "info": "Barometer rating comparison with TRR, with the difference shown as a percentage. This can indicate balance or imbalance in the agency-client experience.",
                "data_required": ["barometer", "trr"],
                "include_non_response": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "1 / 9 / 4 / 13"
                },
                "type": "OverallRatingTRR360",
                "title": "TRR",
                "description": "Results from contacts surveyed",
                "data_required": ["barometer", "trr"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "4 / 1 / 16 / 13"
                },
                "type": "RelationshipQuadrants360",
                "title": "Relationship Quadrants",
                "description": "360 view on account/market risk and opportunity",
                "info": "Comparison of Barometer and TRR ratings withing four zones. The zones indicate relationship balance, potential imbalance or risk, with intersections determined by industry benchmarks.",
                "data_required": ["barometer", "trr"],
                "include_non_response": False,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "account_id": 1,
                    "account_name": 1,
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "market_country": 1,
                    "market_country_id": 1,
                    "rating": 1
                }
            },
            {
                "layout": {
                    "sm": "34 / 1 / 37 / 1",
                    "lg": "16 / 1 / 20 / 7"
                },
                "type": "RatingDistributionTrend360",
                "title": "Rating Distribution",
                "description": "See your rating distribution over the survey round and how they compare to the norm",
                "data_required": ["barometer", "trr"],
                "headerSeperator": True,
                "include_non_response": False,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "round_name": 1,
                    "contact_opt_out": 1
                }
            },
            {
                "layout": {
                    "sm": "24 / 1 / 25 / 1",
                    "lg": "16 / 7 / 20 / 13"
                },
                "type": "RatingTrends360",
                "title": "360 Rating Trends",
                "description": "The average rating of respondents on Barometer and TRR",
                "data_required": ["barometer", "trr"],
                "include_non_response": False,
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "round_name": 1,
                    "responded": 1
                }
            }
        ]
    },
    {
        "title": "Leaderboard (360)",
        "type": "leaderboard360",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 5"
                },
                "type": "RatingBarometer",
                "title": "Barometer",
                "description": "Results from contacts surveyed",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 8 / 1",
                    "lg": "1 / 5 / 4 / 9"
                },
                "type": "Rating360",
                "title": "360",
                "description": "The gap between Barometer and TRR ratings",
                "info": "Barometer rating comparison with TRR, with the difference shown as a percentage. This can indicate balance or imbalance in the agency-client experience.",
                "data_required": ["barometer", "trr"],
                "include_non_response": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "1 / 9 / 4 / 13"
                },
                "type": "OverallRatingTRR360",
                "title": "TRR",
                "description": "Results from contacts surveyed",
                "data_required": ["barometer", "trr"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "12 / 1 / 17 / 1",
                    "lg": "6 / 1 / 11 / 13"
                },
                "type": "RatingByAccount360",
                "data_required": ["barometer", "trr"],
                "include_non_response": False,
                "headerSeperator": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "account_id": 1,
                    "account_name": 1,
                    "rating": 1,
                    "sentiment": 1,
                    "contact_type": 1,
                    "contact_division": 1,
                    "themes": 1,
                    "responded": 1,
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "customer_survey_round_index": 1,
                    "market_type": 1,
                    "market_name": 1,
                    "market_id": 1,
                    "market_country": 1,
                    "market_country_id": 1,
                    "agency_brand_id": 1,
                    "agency_brand_name": 1,
                    "agency_brand_sub_1_name": 1,
                    "agency_brand_sub_1_id": 1,
                    "client_id": 1,
                    "client_name": 1,
                    "holding_group_id": 1,
                    "holding_group_name": 1,
                    "network_id": 1,
                    "network_name": 1,
                    "office_id": 1,
                    "office_name": 1,
                    "sub_network_id": 1,
                    "sub_network_name": 1,
                    "team_id": 1,
                    "team_name": 1
                }
            },
            {
                "layout": {
                    "sm": "24 / 1 / 25 / 1",
                    "lg": "12 / 1 / 18 / 13"
                },
                "type": "RatingTrends360",
                "title": "Rating Trends",
                "description": "Barometer and TRR ratings over time for linked Surveys. Use filters to segment your view",
                "data_required": ["barometer", "trr"],
                "include_non_response": False,
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "round_name": 1,
                    "responded": 1
                }
            }
        ],

    },
    {
        "title": "Contact",
        "type": "contact",
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 13"
                },
                "type": "ContactRating",
                "include_non_response": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["contact_id"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "themes": 1,
                    "responded": 1,
                    "response_date": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 10 / 1",
                    "lg": "4 / 1 / 10 / 13"
                },
                "type": "ContactFeedback",
                "title": "Contact's Feedback",
                "headerSeperator": False,
                "userFilterable": False,
                "showUserInfo": False,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["contact_id"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "themes": 1,
                    "client_name": 1,
                    "team_name": 1,
                    "round_name": 1,
                    "feedback": 1,
                    "feedback_translated": 1,
                    "rating": 1,
                    "response_date": 1,
                    "is_extra_question": 1,
                    "agency_brand_name": 1,
                    "market_name": 1,
                    "account_name": 1,
                    "contact_type": 1,
                    "survey_start_date": 1
                }
            }
        ],
    },
    {
        "title": "Theme Analysis",
        "type": "themeanalysis",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 7"
                },
                "type": "TotalMentionsTrend",
                "title": "Total Mentions",
                "description": "Mentions of selected theme over time",
                "include_non_response": True,
                "headerSeperator": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["theme_categories", "round_id", "account_id", "market_id", "market_country_id", "agencies",
                            "team_type", "team_name", "team_market_id", "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "round_name": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 7 / 1",
                    "lg": "1 / 7 / 4 / 13"
                },
                "type": "SentimentTrend",
                "title": "Sentiment",
                "description": "Overall sentiment trends of selected theme over time",
                "include_non_response": True,
                "headerSeperator": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["theme_categories", "round_id", "account_id", "market_id", "market_country_id", "agencies",
                            "team_type", "team_name", "team_market_id", "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "round_name": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 16 / 1",
                    "lg": "4 / 1 / 13 / 13"
                },
                "type": "ThemeResponses",
                "title": "Individual Responses",
                "headerSeperator": False,
                "filters": ["theme_categories", "round_id", "account_id", "contact_type", "contact_division",
                            "market_id", "market_country_id", "market_view_filter_id", "account_view_filter_id",
                            "agencies", "team_type", "team_name", "team_market_id", "survey_name", "team_market_group",
                            "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "feedback": 1,
                    "contact_name": 1,
                    "contact_type": 1,
                    "contact_job_title": 1,
                    "client_name": 1,
                    "team_name": 1,
                    "account_name": 1,
                    "market_name": 1,
                    "account_view_filter_name": 1,
                    "market_view_filter_name": 1,
                    "response_date": 1,
                    "contact_id": 1,
                    "themes": 1
                }
            }
        ],
    },
    {
        "title": "Non-responders",
        "type": "followup",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 25 / 1",
                    "lg": "1 / 1 / 40 / 13"
                },
                "type": "NoResponses",
                "title": "Panel Members who have not responded",
                "description": "All panel members who have not yet responded to the survey",
                "include_non_response": True,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "contact_name": 1,
                    "contact_type": 1,
                    "client_name": 1,
                    "team_name": 1,
                    "account_name": 1,
                    "market_name": 1,
                    "account_view_filter_name": 1,
                    "market_view_filter_name": 1,
                    "responded": 1,
                    "contact_id": 1,
                    "contact_email": 1,
                    "contact_opt_out": 1,
                    "survey_id": 1,
                    "survey_name": 1,
                    "followup_action_user": 1,
                    "followup_action_date": 1
                }
            }
        ],
    },
    {
        "title": "Feedback Overview (360)",
        "type": "feedbackoverview360",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 10 / 1",
                    "lg": "1 / 1 / 10 / 13"
                },
                "type": "FeedbackWordCloud360",
                "title": "Word Clouds",
                "description": "An overview of commentary, showing frequency of theme mentions",
                "data_required": ["barometer", "trr"],
                "headerSeperator": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "Id": 1,
                    "rating": 1,
                    "themes": 1,
                    "feedback": 1
                }
            },
            {
                "layout": {
                    "sm": "17 / 1 / 21 / 1",
                    "lg": "10 / 1 / 14 / 7"
                },
                "type": "SentimentDistribution360",
                "title": "Barometer",
                "description": "An overview of Barometer respondent feedback",
                "data_required": ["barometer"],
                "headerSeperator": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "sentiment": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "17 / 1 / 21 / 1",
                    "lg": "10 / 7 / 14 / 13"
                },
                "type": "SentimentDistribution360",
                "title": "TRR",
                "description": "An overview of TRR client feedback",
                "data_required": ["barometer", "trr"],
                "headerSeperator": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "sentiment": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "31 / 1 / 39 / 1",
                    "lg": "14 / 1 / 24 / 13"
                },
                "type": "ThemeResponses360",
                "title": "Individual Responses",
                "description": "View non-attributed ratings and feedback from your teams via Barometer and from your clients via TRR",
                "data_required": ["barometer", "trr"],
                "headerSeperator": True,
                "filters": ["theme_categories", "round_id", "account_id", "contact_type", "contact_division",
                            "market_id", "market_country_id", "market_view_filter_id", "account_view_filter_id",
                            "agencies", "team_type", "team_name", "team_market_id", "survey_name", "team_market_group",
                            "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "rating": 1,
                    "feedback": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "21 / 1 / 25 / 1",
                    "lg": "24 / 1 / 40 / 13"
                },
                "type": "PositiveNegativeThemes360",
                "title": "Top 10 Themes",
                "description": "Top mentioned themes on Barometer and TRR. Use the drop down to filter to the most frequently mentioned positive and negative themes",
                "data_required": ["barometer", "trr"],
                "headerSeperator": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_view_filter_id", "account_view_filter_id", "agencies", "team_type", "team_name",
                            "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "customer_survey_round_id": 1,
                    "linked_trr_survey_id": 1,
                    "Id": 1,
                    "rating": 1,
                    "themes": 1
                }
            }
        ],
    },
    {
        "title": "Overview",
        "type": "overview",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 5"
                },
                "type": "OverallRating",
                "title": "Overall Rating",
                "description": "Results from all contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 8 / 1",
                    "lg": "1 / 5 / 4 / 9"
                },
                "type": "RatingByCriticalContacts",
                "title": "Critical Contacts",
                "description": "Results from all critical contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_type": "Critical Contact"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "1 / 9 / 4 / 13"
                },
                "type": "RatingByPrimaryContacts",
                "title": "Primary Contacts",
                "description": "Results from all primary contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_type": "Primary Contact"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "12 / 1 / 20 / 1",
                    "lg": "4 / 1 / 12 / 13"
                },
                "type": "ResponsesPerDay",
                "title": "Responses Per Day",
                "description": "Your survey responses and cumulative ratings for each day of your live survey",
                "headerSeperator": True,
                "dedupe_responses": True,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "response_date": 1,
                    "survey_start_date": 1,
                    "survey_end_date": 1
                }
            },
            {
                "layout": {
                    "sm": "20 / 1 / 25 / 1",
                    "lg": "12 / 1 / 20 / 13"
                },
                "type": "Responses",
                "title": "Responses",
                "description": "Your survey responses as they're answered and submitted",
                "include_non_response": True,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "feedback": 1,
                    "contact_name": 1,
                    "contact_type": 1,
                    "client_name": 1,
                    "team_name": 1,
                    "account_name": 1,
                    "market_country": 1,
                    "market_name": 1,
                    "account_view_filter_name": 1,
                    "market_view_filter_name": 1,
                    "response_date": 1,
                    "contact_id": 1,
                    "team_market_name": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "25 / 1 / 31 / 1",
                    "lg": "20 / 1 / 23 / 13"
                },
                "type": "LeadershipView",
                "title": "Leaderboard",
                "description": "See your ratings based on multiple categories",
                "headerSeperator": True,
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_market_name", "team_name",
                            "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "market_name": 1,
                    "market_id": 1,
                    "market_type": 1,
                    "market_country": 1,
                    "market_country_id": 1,
                    "account_view_filter_name": 1,
                    "market_view_filter_name": 1,
                    "account_view_filter_type": 1,
                    "market_view_filter_type": 1,
                    "responded": 1,
                    "account_name": 1,
                    "account_id": 1,
                    "agency_brand_id": 1,
                    "agency_brand_name": 1,
                    "agency_brand_sub_1_name": 1,
                    "agency_brand_sub_1_id": 1,
                    "client_id": 1,
                    "client_name": 1,
                    "contact_division": 1,
                    "holding_group_id": 1,
                    "holding_group_name": 1,
                    "network_id": 1,
                    "network_name": 1,
                    "office_id": 1,
                    "office_name": 1,
                    "sub_network_id": 1,
                    "sub_network_name": 1,
                    "team_id": 1,
                    "team_name": 1,
                    "team_market_name": 1
                }
            },
            {
                "layout": {
                    "sm": "31 / 1 / 34 / 1",
                    "lg": "23 / 1 / 26 / 13"
                },
                "type": "TopBottomAccounts",
                "title": "",
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "account_id": 1,
                    "account_name": 1,
                    "rating": 1
                }
            },
            {
                "layout": {
                    "sm": "34 / 1 / 37 / 1",
                    "lg": "26 / 1 / 29 / 7"
                },
                "type": "RatingDistributionTrend",
                "title": "Rating Distribution",
                "description": "See your rating distribution over the survey round",
                "headerSeperator": True,
                "include_non_response": False,
                "dedupe_responses": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "round_name": 1,
                    "contact_opt_out": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "37 / 1 / 40 / 1",
                    "lg": "26 / 7 / 29 / 13"
                },
                "type": "TrrTrends",
                "title": "TRR Trends",
                "description": "Client contact ratings over time",
                "headerSeperator": True,
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "round_name": 1,
                    "responded": 1
                }
            }
        ],
    },
    {
        "title": "Feedback Overview",
        "type": "feedbackoverview",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 3 / 1",
                    "lg": "1 / 1 / 3 / 3"
                },
                "type": "TotalComments",
                "title": "Total Comments",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "feedback": 1
                }
            },
            {
                "layout": {
                    "sm": "3 / 1 / 5 / 1",
                    "lg": "1 / 3 / 3 / 5"
                },
                "type": "RelationalRating",
                "title": "Relational Rating",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "5 / 1 / 7 / 1",
                    "lg": "1 / 5 / 3 / 7"
                },
                "type": "TransactionalRating",
                "title": "Transactional Rating",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "7 / 1 / 9 / 1",
                    "lg": "1 / 7 / 3 / 9"
                },
                "type": "GeneralRating",
                "title": "General Rating",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "9 / 1 / 11 / 1",
                    "lg": "1 / 9 / 3 / 11"
                },
                "type": "PositiveNegativeRatio",
                "title": "Positive to Negative",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "11 / 1 / 13 / 1",
                    "lg": "1 / 11 / 3 / 13"
                },
                "type": "ThemesPerComment",
                "title": "Themes Per Comment",
                "titleSize": "s",
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "13 / 1 / 17 / 1",
                    "lg": "3 / 1 / 7 / 7"
                },
                "type": "CategoryDistribution",
                "title": "Category Distribution",
                "description": "Feedback grouped by theme categories",
                "headerSeperator": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "17 / 1 / 21 / 1",
                    "lg": "3 / 7 / 7 / 13"
                },
                "type": "SentimentDistribution",
                "title": "Sentiment Distribution",
                "description": "Feedback grouped by sentiment",
                "headerSeperator": True,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "sentiment": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "21 / 1 / 26 / 1",
                    "lg": "7 / 1 / 12 / 7"
                },
                "type": "RelationalThemes",
                "title": "Relational Themes",
                "description": "See your rating distribution over time",
                "headerSeperator": False,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "26 / 1 / 31 / 1",
                    "lg": "7 / 7 / 12 / 13"
                },
                "type": "TransactionalThemes",
                "title": "Transactional Themes",
                "description": "See your rating distribution over time",
                "headerSeperator": False,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "31 / 1 / 39 / 1",
                    "lg": "12 / 1 / 20 / 13"
                },
                "type": "ThemeResponses",
                "title": "Individual Responses",
                "headerSeperator": False,
                "filters": ["theme_categories", "round_id", "account_id", "contact_type", "contact_division",
                            "market_id", "market_country_id", "market_view_filter_id", "account_view_filter_id",
                            "agencies", "team_type", "team_name", "team_market_id", "survey_name", "team_market_group",
                            "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "feedback": 1,
                    "contact_name": 1,
                    "contact_type": 1,
                    "contact_job_title": 1,
                    "client_name": 1,
                    "team_name": 1,
                    "account_name": 1,
                    "market_name": 1,
                    "account_view_filter_name": 1,
                    "market_view_filter_name": 1,
                    "response_date": 1,
                    "contact_id": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "39 / 1 / 48 / 1",
                    "lg": "20 / 1 / 28 / 13"
                },
                "type": "PositiveNegativeThemesByMentions",
                "title": "Commentary Overview",
                "description": "Distribution of feedback across all themes",
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "48 / 1 / 58 / 1",
                    "lg": "28 / 1 / 38 / 13"
                },
                "type": "FeedbackWordCloud",
                "title": "Word Cloud",
                "description": "See what clients are praising and complaining about most",
                "headerSeperator": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "rating": 1,
                    "themes": 1
                }
            }
        ],
    },
    {
        "title": "Leaderboard",
        "type": "leaderboard",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 1 / 4 / 5"
                },
                "type": "OverallRating",
                "title": "Overall Rating",
                "description": "Results from all contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "team_market_group", "survey_name", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 8 / 1",
                    "lg": "1 / 5 / 4 / 9"
                },
                "type": "RatingByCriticalContacts",
                "title": "Critical Contacts",
                "description": "Results from all critical contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_type": "Critical Contact"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "1 / 9 / 4 / 13"
                },
                "type": "RatingByPrimaryContacts",
                "title": "Primary Contacts",
                "description": "Results from all primary contacts",
                "include_non_response": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "market_view_filter_id",
                            "account_view_filter_id", "agencies", "team_type", "team_name", "team_market_id",
                            "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_type": "Primary Contact"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "12 / 1 / 17 / 1",
                    "lg": "4 / 1 / 9 / 13"
                },
                "type": "RatingByAccount",
                "include_non_response": True,
                "dedupe_responses": True,
                "headerSeperator": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "account_id": 1,
                    "account_name": 1,
                    "rating": 1,
                    "sentiment": 1,
                    "contact_type": 1,
                    "contact_division": 1,
                    "themes": 1,
                    "responded": 1,
                    "customer_survey_round_index": 1,
                    "market_type": 1,
                    "market_name": 1,
                    "market_id": 1,
                    "market_country": 1,
                    "market_country_id": 1,
                    "agency_brand_id": 1,
                    "agency_brand_name": 1,
                    "agency_brand_sub_1_name": 1,
                    "agency_brand_sub_1_id": 1,
                    "client_id": 1,
                    "client_name": 1,
                    "holding_group_id": 1,
                    "holding_group_name": 1,
                    "network_id": 1,
                    "network_name": 1,
                    "office_id": 1,
                    "office_name": 1,
                    "sub_network_id": 1,
                    "sub_network_name": 1,
                    "team_id": 1,
                    "team_name": 1
                }
            },
            {
                "layout": {
                    "sm": "17 / 1 / 21 / 1",
                    "lg": "9 / 1 / 13 / 13"
                },
                "type": "RatingTrend",
                "title": "Rating Trend",
                "description": "See your rating trends over survey rounds",
                "dedupe_responses": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "round_name": 1,
                    "rating": 1
                }
            },
            {
                "layout": {
                    "sm": "21 / 1 / 25 / 1",
                    "lg": "13 / 1 / 17 / 13"
                },
                "type": "PositiveNegativeThemes",
                "title": "Top 5 Themes",
                "description": "Find out which aspects of your business that customers mention overall, positively and negatively",
                "headerSeperator": True,
                "dedupe_responses": True,
                "round_index": [0, 1],
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "Id": 1,
                    "rating": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "25 / 1 / 29 / 1",
                    "lg": "17 / 1 / 21 / 7"
                },
                "type": "SentimentOverview",
                "title": "Sentiment Overview",
                "description": "",
                "dedupe_responses": True,
                "round_index": [0, 1, 2, 3, 4, 5],
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "round_name": 1,
                    "themes": 1
                }
            },
            {
                "layout": {
                    "sm": "29 / 1 / 33 / 1",
                    "lg": "17 / 7 / 21 / 13"
                },
                "type": "ThemeWordCloud",
                "title": "Word Cloud",
                "description": "",
                "dedupe_responses": True,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "market_view_filter_id", "account_view_filter_id", "agencies",
                            "team_type", "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "themes": 1
                }
            }
        ],
    },
    {
        "title": "Overview (Barometer)",
        "type": "overviewbarometer",
        "showUserInfo": True,
        "userFilterable": True,
        "cards": [
            {
                "layout": {
                    "sm": "1 / 1 / 4 / 1",
                    "lg": "1 / 2 / 4 / 6"
                },
                "type": "RatingBarometer",
                "title": "Overall Rating",
                "description": "Results from contacts surveyed",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 8 / 1",
                    "lg": "1 / 8 / 4 / 12"
                },
                "type": "RatingBarometer",
                "title": "Senior Ranked",
                "description": "The overall rating for your Barometer survey.",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_seniority": "Senior"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "8 / 1 / 12 / 1",
                    "lg": "4 / 2 / 8 / 6"
                },
                "type": "RatingBarometer",
                "title": "Mid-Ranked",
                "description": "The overall rating for your Barometer survey.",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_seniority": "Middle"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "4 / 1 / 4 / 1",
                    "lg": "4 / 8 / 8 / 12"
                },
                "type": "RatingBarometer",
                "title": "Junior Ranked",
                "description": "The overall rating for your Barometer survey.",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "market_id", "market_country_id", "agencies", "team_type",
                            "team_name", "team_market_id", "survey_name", "team_market_group", "sector"],
                "query": {
                    "contact_seniority": "Junior"
                },
                "projection": {
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "20 / 1 / 25 / 1",
                    "lg": "8 / 1 / 16 / 13"
                },
                "type": "ResponsesBarometer",
                "title": "Responses",
                "description": "View non-attributed ratings and feedback from your account team",
                "data_required": ["barometer"],
                "include_non_response": True,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "feedback": 1,
                    "account_name": 1,
                    "market_name": 1,
                    "responded": 1,
                    "response_date": 1,
                    "team_market_name": 1
                }
            },
            {
                "layout": {
                    "sm": "20 / 1 / 25 / 1",
                    "lg": "16 / 1 / 24 / 13"
                },
                "type": "RatingByAccountBarometer",
                "title": "Account Ratings",
                "description": "Your latest account ratings, the difference between your precious survey and response rate",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "account_id": 1,
                    "account_name": 1,
                    "rating": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "24 / 1 / 25 / 1",
                    "lg": "24 / 1 / 28 / 7"
                },
                "type": "BarometerTrends",
                "title": "Barometer Rating Trend",
                "description": "Internal narrative over time with benchmark comparison",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "round_name": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "24 / 1 / 25 / 1",
                    "lg": "24 / 7 / 28 / 13"
                },
                "type": "ResponseRateTrend",
                "title": "Response Rate Trend",
                "description": "Response rate over time with benchmark comparison",
                "data_required": ["barometer"],
                "include_non_response": True,
                "previous_rounds": 5,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "round_name": 1,
                    "responded": 1
                }
            },
            {
                "layout": {
                    "sm": "34 / 1 / 37 / 1",
                    "lg": "28 / 1 / 32 / 13"
                },
                "type": "RatingDistributionTrendBarometer",
                "title": "Rating Distribution Trend",
                "description": "See your rating distribution over time and how they comapre to the norm.",
                "data_required": ["barometer"],
                "headerSeperator": True,
                "include_non_response": False,
                "previous_rounds": 1,
                "filters": ["round_id", "account_id", "contact_type", "contact_division", "market_id",
                            "market_country_id", "agencies", "team_type", "team_name", "team_market_id", "survey_name",
                            "team_market_group", "sector"],
                "query": {
                },
                "projection": {
                    "rating": 1,
                    "round_name": 1,
                    "contact_opt_out": 1,
                    "responded": 1
                }
            }
        ],
    }
])
