import datetime
from enum import Enum
from typing import List, Union, Literal
from pydantic import BaseModel, Field


class SurveyProgressStages(Enum):
    pending = 'pending'
    active = 'active'
    complete = 'complete'


class SurveysResponse(BaseModel):
    id: str = ''
    name: str = ''
    start_date: str = ''
    end_date: str = ''
    dates: str = ''
    status: str|None = ''
    stage: str|None = ''
    state: str|None = ''
    type: str = 'TRR'
    involved: bool = False
    accounts_confirmed: bool = False,
    panel_confirmed: bool = False,
    account_manager: bool = False,
    panel_manager: bool = False,
    round_progress: dict = {}
    panel_count: int = 0
    account_count: int = 0


class SurveyProgressResponse(BaseModel):
    round_start: SurveyProgressStages = Field(default=SurveyProgressStages.pending)
    account_updates: SurveyProgressStages = Field(default=SurveyProgressStages.pending)
    panel_updates: SurveyProgressStages = Field(default=SurveyProgressStages.pending)
    live_survey: SurveyProgressStages = Field(default=SurveyProgressStages.pending)
    insights: SurveyProgressStages = Field(default=SurveyProgressStages.pending)

    @classmethod
    def get_current_state(cls, stage: str) -> dict:
        default_state = cls().dict()

        if not stage:
            return default_state

        santised_stage = stage.replace(' ', '_').lower()

        keys = list(cls.__fields__.keys())

        if santised_stage in keys:
            index = keys.index(santised_stage)

            default_state[santised_stage] = SurveyProgressStages.active

            for k in keys[0:index]:
                default_state[k] = SurveyProgressStages.complete

            for k in keys[index + 1:]:
                default_state[k] = SurveyProgressStages.pending

        return cls(**default_state).dict()


class PanelResponse(BaseModel):
    customer_survey_round_name: str|None = ''
    user_account_name: str|None = ''
    complete_by_date: datetime.datetime|None = None
    complete_by_days: int|None = 0
    action_required: bool
    panel: list = []
    confirmed_audit: list = []
    confirmed_user: str|None = None
    confirmed_email: str|None = None
    confirmed_date: datetime.datetime|None = None
    confirmed: bool = False
    sources: list = []
    account_vertical: str|None = None
    am_view_only: bool = False
    f_clients: list = []
    f_accounts: list = []
    f_contacts: list = []
    f_contact_types: list = []
    f_contact_seniority: list = []
    f_contact_job_function: list = []
    f_market_country: list = []
    f_market_city: list = []
    f_languages: list = []

class ConfirmAccountsResponse(BaseModel):
    customer_survey_round_name: str|None = ''
    user_account_name: str|None = ''
    complete_by_date: datetime.datetime|None = None
    complete_by_days: int|None = 0
    accounts: list = []
    confirmed_audit: list = []
    confirmed_user: str|None = None
    confirmed_email: str|None = None
    confirmed_date: datetime.datetime|None = None
    confirmed: bool
    action_required: bool
    account_vertical: str|None = None
    f_clients: list = []
    f_panel_managers: list = []
    f_signatories: list = []
    f_account_managers: list = []


class GetReportDefinitionResponse(BaseModel):
    id: str = ''
    name: str = ''


class GetReportResponse(BaseModel):
    id: str = ''
    name: str = ''
    description: Union[None, str] = ''
    columns: List = []
    rows: List = []


class LoginResponse(BaseModel):
    success: bool = False
    access_token: str = ''
    refresh_token: str = ''
    id_token: str = ''
    expires_in: int = 0


class PasswordResetResponse(BaseModel):
    success: bool = False
    session: dict = {}


class ConfirmPasswordReset(BaseModel):
    success: bool = False
    session: dict = {}


class ConfirmRegistrationResponse(BaseModel):
    success: bool = False
    session: dict = {}


class RegisterResponse(BaseModel):
    success: bool = False
    session: dict = {}


class RefreshRegisterResponse(BaseModel):
    success: bool = False
    session: dict = {}


class CognitoResponse(BaseModel):
    status: Literal['ok', 'failure'] = 'failure'
    data: dict|None = None
    error: dict[str, str]|None = None


class OktaIssuerResponse(BaseModel):
    url: str | None
