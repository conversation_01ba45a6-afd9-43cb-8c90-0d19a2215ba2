from __future__ import annotations
from enum import Enum
from pydantic import BaseModel, computed_field
from typing import Union, Optional


class CustomerSurveyRoundStatus(Enum):
    survey_round_start = "Survey Round Start"
    account_uodates = "Account Updates"
    panel_updates = "Panel Updates"
    survey_design = "Survey Design"
    live_survey = "Live Survey"
    customer_surveys_created = "Customer Surveys Created"


class SurveyClientMember(BaseModel):
    Account: CustomerAccount
    WasInPriorRound: bool


class PanelMember(BaseModel):
    Contact: Contact
    WasInPriorRound: bool


class Contact(BaseModel):
    Id: str
    FirstName: str
    LastName: str
    Email: Union[None, str]
    AccountId: str
    Contact_Type__c: Union[None, str]
    Title: Union[None, str]
    Panel_Manager__c: bool 
    Accounts_Manager__c: bool
    Signatory__c: bool
    Customer_Account__c: Optional[Union[None, str]] = None
    Customers_Client_Account__c: Optional[Union[None, str]] = None
    InternalMappingId: Optional[Union[None, str]] = None
    Survey_Client__c: Optional[Union[None, str]] = None

    @computed_field
    @property
    def Name(self) -> int:
        return f'{self.FirstName}  {self.LastName}'


class SurveyRound(BaseModel):
    Id: str
    Name: str
    Round_Date__c: str
    Survey_Round_Status__c: str


class CustomerSurveyRound(BaseModel):
    Id: str
    Name: str
    Account__c: str
    Round_Date__c: str
    Survey_Round__c: str
    Round_Status__c: str#CustomerSurveyRoundStatus


class CustomerSurvey(BaseModel):
    Id: str
    Name: str
    Customer__c: str
    Customer_Survey_Round__c: str
    Survey_Date__c: Union[None, str]
    Survey_Status__c: Union[None, str]
    Survey_Client_Manager__c: Union[None, str]


class SurveyClient(BaseModel):
    Id: str
    Name: str
    Customer_Survey__c: str
    Customers_Client__c: str
    Status__c: str
    Survey_Panel_Manager__c: str


class CustomerAccount(BaseModel):
    Id: str
    Name: str
    ParentId: Union[None, str]
    RecordTypeId: str
    Round_Frequency__c: str
    Customer_Status__c: Union[None, str]
    Current_Round__c: bool
    Consultant__c: Union[None, str]
    Valid_Email_Domains__c: Union[None, str]
    Customer_Survey__c: Optional[Union[None, str]] = None
    GroupId: Optional[Union[None, str]] = None
    Group: Optional[Union[None, str]] = None
    Panel_Manager: Optional[Union[None, str]] = None
    Panel_Manager_Id: Optional[Union[None, str]] = None
    Signatory: Optional[Union[None, str]] = None
    Signatory_Id: Optional[Union[None, str]] = None


class CustomersClientAccount(BaseModel):
    Id: str
    Name: str
    ParentId: Union[None, str]
    RecordTypeId: str
    Round_Frequency__c: str
    Customer_Status__c: Union[None, str]
    Current_Round__c: bool
    Consultant__c: Union[None, str]
    Survey_Client__c: Optional[Union[None, str]] = None
    Customer_Survey__c: Optional[Union[None, str]] = None