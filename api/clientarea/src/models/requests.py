import datetime
from typing import Optional
from pydantic import BaseModel
from api.clientarea.src.models.collections import ConfirmAccountsAccount, ConfirmPanelMember


class ConfirmCustomerSurveyRequest(BaseModel):
    confirmed_date: datetime.datetime|None = None
    accounts: dict[str, list[ConfirmAccountsAccount]]


class ConfirmPanelRequest(BaseModel):
    confirmed_date: datetime.datetime|None = None
    panel: dict[str, list[ConfirmPanelMember]]


class AddContactRequest(BaseModel):
    email: str
    prefix: str
    first_name: str
    last_name: str
    job_title: str
    contact_type: str
    seniority: str
    account_id: str


class DelegateSurveyClientRequest(BaseModel):
    confirm_account_id: str
    delegate_id: str


class LoginRequest(BaseModel):
    username: str
    password: str


class OktaIssuerRequest(BaseModel):
    iss: str


class OktaAuthenticationRequest(BaseModel):
    code: str


class RegisterRequest(BaseModel):
    username: str
    password: str
    first_name: str
    last_name: str


class RefreshTokenRequest(BaseModel):
    user_id: str
    refresh_token: str


class LogoutRequest(BaseModel):
    refresh_token: str  


class ResetPasswordRequest(BaseModel):
    username: str


class ResetPasswordVerificationRequest(BaseModel):
    username: str
    password: str
    password_verified: str
    c: str


class RegistrationVerificationRequest(BaseModel):
    c: str


class RegistrationVerificationResendRequest(BaseModel):
    c: str