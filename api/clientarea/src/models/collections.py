from __future__ import annotations
import datetime
from pydantic import BaseModel, computed_field, model_validator, Field
from typing import List, Dict, Union, Optional
from lib.portaldbapi import DocumentDB
from api.clientarea.src.models.salesforce import SurveyClient, SurveyRound, CustomerSurveyRound, CustomerSurvey, CustomersClientAccount, CustomerAccount, Contact


class SfCustomerClientAccounts(BaseModel):
    _id: str
    Id: str
    Name: str
    ParentId: Union[None, str]
    RecordTypeId: str
    Round_Frequency__c: str
    Customer_Status__c: Union[None, str]
    Current_Round__c: bool
    Consultant__c: Union[None, str]

    @classmethod
    def get_by_customer_client_account_id(cls, db: DocumentDB) -> List[SfCustomerClientAccounts]:
        # FIXME: right now, we have no link between the customer client account and the customer survey ... so we don't know how to filter this.
        #        just need to return all customer client accounts for now
        return [
            cls(**customer_client_account)
            #for customer_client_account in documentdb.client.portaldb.sfcustomerclientaccounts.find({'Id': customer_client_account_id})
            for customer_client_account in db.customerclientaccounts.find()
        ]


class SfContacts(BaseModel):
    _id: str
    Id: str
    FirstName: str|None
    LastName: str|None
    Email: Union[None, str]
    AccountId: str|None
    Contact_Type__c: Union[None, str]
    Title: Union[None, str]
    ClientHierarchy: List[str] = []
    label: str|None = None
    value: str|None = None

    @computed_field
    @property
    def Name(self) -> str:
        return f'{self.FirstName}  {self.LastName}'

    @classmethod
    def get_by_contact_id(cls, db: DocumentDB, contact_id: List[str]) -> SfContacts:
        return cls(**db.contacts.find_one({'Id': contact_id}))

    @classmethod
    def get_by_contact_ids(cls, db: DocumentDB, contact_ids: List[str]) -> List[SfContacts]:
        return [
            cls(**contact)
            for contact in db.contacts.find({'Id': {'$in': contact_ids}})
        ]

    @classmethod
    def get_by_account_id(cls, db: DocumentDB, account_id: List[str]) -> List[SfContacts]:
        return [
            Contact(**contact)
            for contact in db.contacts.find({'AccountId': {'$in': account_id}})
        ]

    @classmethod
    def get_panel_managers_by_account_id(cls, db: DocumentDB, account_id: List[str]) -> List[SfContacts]:
        return [
            cls(**contact)
            for contact in db.contacts.find({'AccountId': {'$in': account_id}, 'Panel_Manager__c': True})
        ]

    @classmethod
    def get_signatories_by_account_id(cls, db: DocumentDB, account_id: List[str]) -> List[SfContacts]:
        return [
            cls(**contact)
            for contact in db.contacts.find({'AccountId': {'$in': account_id}, 'Signatory__c': True})
        ]

    @classmethod
    def get_account_managers_by_account_id(cls, db: DocumentDB, group_id: List[str]) -> List[Dict]:
        accounts_managers = []
        for contact in db.customercontacts.find({'client_hierarchy': {'$in': group_id}, 'account_manager': True}, {'_id': 0, 'Id': 1, 'name': 1, 'email': 1, 'client_hierarchy': 1}):
            contact['label'] = contact.get('name', '')
            contact['value'] = contact.get('Id')
            accounts_managers.append(contact)

        return accounts_managers


class SfCustomerSurveyRound(BaseModel):
    _id: str
    Id: str
    LastModifiedDate: datetime.datetime
    Name: str
    Account__c: str
    Round_Date__c: str
    Survey_Round_Active_Duration_Days__c: int
    Survey_Round__c: str
    Round_Status__c: str
    Last_Status_Change_Date__c: datetime.datetime|None

    @classmethod
    def get_by_id(cls, db: DocumentDB, customer_survey_round_id: str) -> SfCustomerSurveyRound:
        return SfCustomerSurveyRound(**db.customersurveyround.find_one({'Id': customer_survey_round_id}))

class SfCustomerSurveys(BaseModel):
    _id: str
    Id: str
    LastModifiedDate: datetime.datetime|None
    Name: str
    Customer__c: str
    Customer_Survey_Round__c: str
    Survey_Date__c: str
    Survey_Active_Duration_Days__c: int
    Survey_Status__c: str
    Survey_Client_Manager__c: str
    Last_Status_Change_Date__c: datetime.datetime|None

    @classmethod
    def get_by_user_involvement(cls, db: DocumentDB, user_email: str) -> List:
        # get all the customer surveys where the user is the account manager
        customer_surveys_as_account_manager = [
            {**customer_survey['CustomerSurveyRound']}
            for customer_survey in db.customersurveys.find({'SurveyPanelManagerEmail': user_email})
        ]
        # get all the customer surveys where the user is the panel manager
        customer_surveys_as_panel_manager = [
            {**survey_client['CustomerSurveyRound']}
            for survey_client in db.surveyclients.find({'PanelManagerEmail': user_email})
        ]

        seen_keys = set()
        merged_list = []

        for d in customer_surveys_as_account_manager + customer_surveys_as_panel_manager:
            if d['Id'] not in seen_keys:
                seen_keys.add(d['Id'])
                merged_list.append(d)

        return merged_list

    @classmethod
    def get_by_id(cls, db: DocumentDB, customer_survey_id: str) -> SfCustomerSurveys:
        return SfCustomerSurveys(**db.customersurveys.find_one({'Id': customer_survey_id}))


class SfSurveyClients(BaseModel):
    _id: str
    Id: str
    ConfirmedStatus: bool
    PanelManagerEmail: str
    SurveyRound: SurveyRound
    CustomerSurveyRound: CustomerSurveyRound
    CustomerSurvey: CustomerSurvey
    SurveyClient: SurveyClient
    CustomerAccount: CustomerAccount
    CustomersClientAccount: CustomersClientAccount
    Panel: List[Contact]

    @classmethod
    def get_by_user_involvement(cls, db: DocumentDB, user_email: str) -> List:
        survey_clients = [
            {**survey_client['CustomerSurveyRound']}
            for survey_client in db.surveyclients.find({'PanelManagerEmail': user_email})
        ]

        seen_keys = set()
        merged_list = []

        for d in survey_clients:
            if d['Id'] not in seen_keys:
                seen_keys.add(d['Id'])
                merged_list.append(d)

        return merged_list

class SfReports(BaseModel):
    _id: str
    id: str
    name: str
    report: dict
    last_updated: datetime.datetime

    @classmethod
    def get_all(cls, db: DocumentDB) -> List[SfReports]:
        return list(db.reports.find({}, {'_id': 0, 'id': 1, 'name': 1}))

    @classmethod
    def get_by_id(cls, db: DocumentDB, report_id: str) -> SfReports:
        return db.reports.find_one({'id': report_id}, {'_id': 0, 'report': 1})


class CustomerContacts(BaseModel):
    Id: str
    internal_id: str|None
    email: str
    name: str
    contact_type: str|None
    seniority: str|None
    title: str|None
    internal: bool = False
    panel_manager: bool = False
    account_manager: bool = False
    signatory: bool = False
    consulatant: bool = False
    account_id: str
    account_name: str
    reporting_role: str|None
    reporting_hierarchy: list[str] = []
    client_hierarchy: list[str] = []
    account_hierarchy: list[str] = []
    key_accounts: list[str] = []
    key_markets: list[str] = []
    excluded_accounts: list[str] = []
    excluded_markets: list[str] = []
    _lastsynced: datetime.datetime


class ConfirmAccounts(BaseModel):
    _id: str
    Id: str
    confirm_date: datetime.datetime|None = None
    account_managers: list[ConfirmAccountsAccountManager]
    client_id: str
    client_name: str
    client_market: str|None = None
    client_market_id: str|None = None
    client_organisation_level: ClientOrganisationLevels
    team_id: str|None = None
    team_name: str|None = None
    team_market: str|None = None
    survey_round: str|None = None 
    customer_survey_round_id: str|None = None 
    customer_survey_round_name: str|None = None 
    customer_survey_name: str
    accounts_confirmed_start_date: datetime.datetime
    accounts_confirmed_by_date: datetime.datetime
    panel_confirmed_start_date: datetime.datetime
    panel_confirmed_by_date: datetime.datetime
    live_survey_start_date: datetime.datetime
    live_survey_first_request_date: datetime.datetime
    live_survey_second_request_date: datetime.datetime
    live_survey_third_request_date: datetime.datetime
    live_survey_fourth_request_date: datetime.datetime
    live_survey_end_date: datetime.datetime
    auto_confirmed: bool = False
    confirm_status: bool = False
    email_sender: str|None = None
    sfsync_date: datetime.datetime|None = None
    accounts: List[ConfirmAccountsAccount]
    confirmed_accounts: list[ConfirmedAccount] = []
    confirmed_accounts_last_save_all_accounts: list[ConfirmAccountsAccount]|None = None #TODO: default to this a list after MIG-01 is complete
    confirmed_accounts_last_save_date: datetime.datetime|None = None
    confirmed_accounts_last_save_user_id: str|None = None
    confirmed_accounts_last_save_user_name: str|None = None
    delegation: list[dict] = []
    final_confirmed_accounts: list[ConfirmAccountsAccount]|None = []


class ConfirmAccountsAccount(BaseModel):
    account_id: str
    account_name: str
    in_round: bool
    selected: Optional[bool] = False
    survey_panel_managers: list[ConfirmAccountsPanelManager] = Field(default_factory=list)
    signatory_id: str|None = None
    signatory_email: str|None = None
    signatory_name: str|None = None
    survey_name: str|None = None
    duplicate_account: bool = False
    market_id: str|None = None
    not_confirmed_resolving_required: Optional[bool] = False
    added_by_contact: str|None = None

    @model_validator(mode="before")
    @classmethod
    def move_client_market_id_to_market_id(cls, data):
        if isinstance(data, dict) and "client_market_id" in data and "market_id" not in data:
            data["market_id"] = data["client_market_id"]
        return data


class ConfirmAccountsAccountManager(BaseModel):
    account_manager_id: str
    account_manager_email: str
    account_manager_name: str|None = None


class ConfirmedAccount(BaseModel):
    accounts: list[ConfirmAccountsAccount]
    all_accounts: list[ConfirmAccountsAccount] = []
    confirm_date: datetime.datetime|None = None
    confirm_user_id: str|None = None
    confirm_user_email: str|None = None
    confirm_user_name: str|None = None
    stats: dict[str, list] = {}


class ConfirmAccountsPanelManager(BaseModel):
    panel_manager_id: str
    panel_manager_email: str
    panel_manager_name: str
    panel_manager_first_name: str|None = None
    panel_manager_last_name: str|None = None


class ConfirmPanel(BaseModel):
    Id: str
    client_id: str
    client_name: str
    client_market: str|None = None
    client_market_id: str|None = None
    client_market_language: str|None = None
    client_organisation_level: ClientOrganisationLevels
    account_id: str
    account_name: str
    panel_managers: list[ConfirmAccountsPanelManager]
    account_managers: list
    customer_survey_name: str
    customer_survey_round_name: str|None = None
    team_id: str|None = None
    team_name: str|None = None
    team_market: str|None = None
    survey_name: str|None = None
    accounts_confirmed_start_date: datetime.datetime
    accounts_confirmed_by_date: datetime.datetime
    panel_confirmed_start_date: datetime.datetime
    panel_confirmed_by_date: datetime.datetime
    live_survey_start_date: datetime.datetime
    live_survey_first_request_date: datetime.datetime
    live_survey_second_request_date: datetime.datetime
    live_survey_third_request_date: datetime.datetime
    live_survey_fourth_request_date: datetime.datetime
    live_survey_end_date: datetime.datetime
    sfsync_date: datetime.datetime|None = None
    panel: list[ConfirmPanelMember]
    final_confirmed_panel: list[ConfirmPanelMember]|None = []
    final_account_managers: list[str] = []
    email_sender: str|None = None
    auto_confirmed: bool = False
    confirm_status: bool = False
    confirmed_panel: list[ConfirmedPanel] = []
    confirmed_panel_last_save_all_panel: list[ConfirmPanelMember]|None = None #TODO: change to list after MIG-02 complete
    confirmed_panel_last_save_date: datetime.datetime|None = None
    confirmed_panel_last_save_user_id: str|None = None
    confirmed_panel_last_save_user_name: str|None = None


class ConfirmedPanel(BaseModel):
    panel: list[ConfirmPanelMember]
    all_panel: list[ConfirmPanelMember] = [] #TODO: remove this after MIG-02 complete
    confirm_date: datetime.datetime|None = None
    confirm_user_id: str|None = None
    confirm_user_email: str|None = None
    confirm_user_name: str|None = None
    stats: dict[str, list] = {}


class ConfirmPanelMember(BaseModel):
    contact_id: str
    contact_name: str
    contact_email: str
    contact_type: str|None
    contact_seniority: str|None = None
    contact_language: str | None = None
    contact_job_function: str|None = None
    contact_job_function_2: str | None = None
    contact_division: str | None = None
    contact_job_title: str | None = None
    contact_location: str | None = None
    contact_location_id: str | None = None
    contact_account_site: str | None = None
    contact_account_site_id: str | None = None
    serial_non_responder: bool = False
    in_round: bool
    account_id: str | None = None
    survey_panel_manager_id: str|None = None
    response_status: bool|None = False
    bounced_status: bool|None = False
    survey_name: str|None = None
    selected: Optional[bool] = False
    not_confirmed_resolving_required: Optional[bool] = False


class ClientOrganisationLevel(BaseModel):
    Id: str|None = None
    name: str|None = None


class ClientOrganisationLevels(BaseModel):
    level_1: ClientOrganisationLevel
    level_2: ClientOrganisationLevel
    level_3: ClientOrganisationLevel
    level_4: ClientOrganisationLevel
    level_5: ClientOrganisationLevel
    level_6: ClientOrganisationLevel
    level_7: ClientOrganisationLevel


class SurveyRound(BaseModel):
    Id: str
    name: str
    round_start_date: datetime.datetime
    round_end_date: datetime.datetime
    stage: str|None = None
    state: str|None = None
    survey_type: str|None = None
    progress: str|None = None
    current_round: bool = False
    account_id: str
    _permissions: list[str]


class PendingCustomerContact(BaseModel):
    internal_id: str
    email: str
    first_name: str
    last_name: str
    created_date: datetime.datetime
    sfsync_date: datetime.datetime | None = None


class AccessLog(BaseModel):
    contact_id: str
    contact_email: str
    last_access: list[datetime.datetime]
    type: str
    assumed_role_id: str|None = None
    assumed_role_email: str|None = None


class SrpReport(BaseModel):
    Id: str
    contact_id: str
    version_id: str
    customer_survey_round_id: str
    customer_survey_round_title: str
    s3_filename: str
    title: str
    report_type: str
    published: bool
    published_date: datetime.datetime
    last_modified_date: datetime.datetime
    _lastsynced: datetime.datetime


class Market(BaseModel):
    Id: str
    name: str
    type: str
    parent_market_id: str|None = None