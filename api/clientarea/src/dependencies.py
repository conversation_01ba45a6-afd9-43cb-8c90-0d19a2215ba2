from litestar import Request


class User:
    def __init__(self, request: Request):
        self.id = request.user.id
        self.internal = request.user.user_internal
        self.name = request.user.name
        self.email = request.user.email
        self.account = request.user.account_id
        self.account_name = request.user.account_name
        self.account_vertical = request.user.account_vertical
        self.team_id = request.user.team_id
        self.team_name = request.user.team_name
        self.account_organisation_level = request.user.account_organisation_level
        self.sector = request.user.sector
        self.features = request.user.features
        self.roles = request.user.roles
        self.permissions = request.user.permissions
        self.account_hierarchy = request.user.account_hierarchy
        self.key_accounts = request.user.key_accounts
        self.key_markets = request.user.key_markets
        self.reporting_role = request.user.reporting_role
        self.reporting_roles = request.user.reporting_roles
        self.reporting_views = request.user.reporting_views
        self.round_index_limit = request.user.round_index_limit