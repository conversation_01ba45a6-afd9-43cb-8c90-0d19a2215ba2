import sentry_sdk
from mangum import Mangum
from litestar import Litestar
from litestar.config.response_cache import ResponseCacheConfig
from litestar.config.compression import CompressionConfig
from litestar.logging.config import LoggingConfig
from litestar.config.cors import CORSConfig
from litestar.openapi import OpenAPIConfig
from litestar.openapi.plugins import ScalarRenderPlugin
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.litestar import LitestarIntegration
from lib.settings import settings as settingsapi
from api.clientarea.src.settings import settings
from api.clientarea.src.routes import routes


if settingsapi.sentry_dsn:
    sentry_sdk.init(
        dsn=settingsapi.sentry_dsn,
        traces_sample_rate=1.0,
        profile_session_sample_rate=1.0,
        profile_lifecycle="trace",
        integrations=[
            AwsLambdaIntegration(),
            LitestarIntegration()
        ],
        environment=settingsapi.sentry_environment,
    )

app = Litestar(
    route_handlers=routes,
    cors_config=CORSConfig(**settings.cors.dict()),
    response_cache_config=ResponseCacheConfig(**settings.cache.dict()),
    compression_config=CompressionConfig(**settings.compression.dict()),
    logging_config=LoggingConfig(log_exceptions='always'),
    openapi_config=OpenAPIConfig(
        title='Client Area API',
        version='0.0.1',
        render_plugins=[ScalarRenderPlugin()],
    ),
)

lambda_handler = Mangum(app)