from litestar import Controller, get
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.sfcsv.src.handlers.get_sfcsv_handler import GetSFCSVHandler


class SFCSVRouter(Controller):
    @get(
        '/{filename:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_survey(self, filename: str, filekey: str, apikey: str) -> None:
        return GetSFCSVHandler(filename, filekey, apikey).handler()
