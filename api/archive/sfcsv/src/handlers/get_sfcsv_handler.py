from litestar.exceptions import NotFoundException
from litestar.response import Stream
import boto3
from api.sfcsv.src.settings import settings


class GetSFCSVHandler:
    def __init__(self, filename: str, filekey: str, apikey: str):
        self.filename = filename
        self.filekey = filekey
        self.apikey = apikey

    def handler(self):
        if settings.csv_download_auth_key != self.apikey:
            raise NotFoundException()

        s3 = boto3.client('s3')
        s3_object_head = s3.head_object(Bucket=settings.SF_CSV_BUCKET, Key=self.filename)
        s3_object_filekey = s3_object_head.Metadata.get('filekey')

        if not s3_object_filekey or s3_object_filekey != self.filekey:
            raise NotFoundException()

        s3_object = s3.get_object(Bucket=settings.SF_CSV_BUCKET, Key=self.filename)
        bare_filename = self.filename.split('/')[-1].replace('"', '')
        headers = {'Content-Disposition': f'inline; filename="{bare_filename}"'}
        return Stream(iterator=s3_object['Body'].iter_chunks(), media_type='text/csv', headers=headers)
