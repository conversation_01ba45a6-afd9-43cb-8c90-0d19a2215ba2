from litestar import Litestar
from litestar.config.response_cache import ResponseCacheConfig
from litestar.config.compression import CompressionConfig
from litestar.config.cors import CORSConfig
from litestar.logging.config import LoggingConfig
from litestar.openapi import OpenAPIConfig
from litestar.openapi.plugins import ScalarRenderPlugin
from mangum import Mangum
from api.sfcsv.src.settings import settings
from api.sfcsv.src.routes import routes


app = Litestar(
    route_handlers=routes,
    cors_config=CORSConfig(**settings.cors.dict()),
    response_cache_config=ResponseCacheConfig(**settings.cache.dict()),
    compression_config=CompressionConfig(**settings.compression.dict()),
    logging_config=LoggingConfig(log_exceptions='always'),
    openapi_config=OpenAPIConfig(
        title='CRC API CSV',
        version='0.0.1',
        render_plugins=[ScalarRenderPlugin()],
    ),
)

lambda_handler = Mangum(app)