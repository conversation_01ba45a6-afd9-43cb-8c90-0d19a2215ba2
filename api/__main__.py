import pulumi
import pulumi_aws as aws
from libdeploy import deploy_apigateway_function, stack, config, add_sns_lambda_alert, aws_account_id, aws_region, \
                        create_amplify_app, add_sns_amplify_alert

def deploy():
    stack_ref = pulumi.StackReference(f"organization/crc-backend/{stack}")
    secrets_arn = stack_ref.get_output('secrets_arn')
    secrets_name = stack_ref.get_output('secrets_name')
    apis_ecr_repo_name = stack_ref.require_output('apis_ecr_repo_name')
    apis_ecr_repo_url = stack_ref.require_output('apis_ecr_repo_url')
    default_sg_id = stack_ref.require_output('default_sg_id')
    lambda_security_group_ids = [default_sg_id]
    lambda_subnet_ids = [stack_ref.require_output('private_subnet_a_id')]
    monitoring_sns_topic_arn = stack_ref.require_output('monitoring_topic_arn')
    api_survey_auth_key = stack_ref.require_output('api_survey_auth_key')

    # get the hash of the tagged image in ECR
    ecr_image = aws.ecr.get_image(repository_name=apis_ecr_repo_name, image_tag=stack)


    lambda_policy_extra = [
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
            ],
            "Resource": [
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-send_email_to_sg-{stack}-queue",
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-survey_round_scheduler-{stack}-queue",
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_confirmaccounts_to_portaldb-{stack}-queue",
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_confirmpanel_to_portaldb-{stack}-queue",
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_optout_to_sf-{stack}-queue",
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
            ],
            "Resource": [
                f"arn:aws:s3:::crc-srpreports-data-{stack}",
                f"arn:aws:s3:::crc-srpreports-data-{stack}/*",
            ]
        },
    ]

    survey_rest_api_gateway, survey_rest_api_gateway_stage = deploy_apigateway_function(
        f'crc-api-survey-{stack}',
        f'api.survey.app.lambda_handler',
        lambda_security_group_ids,
        lambda_subnet_ids,
        apis_ecr_repo_url,
        ecr_image.image_digest,
        secrets_arn,
        secrets_name,
        lambda_policy_extra=lambda_policy_extra,
        lambda_function_memory_size_mb=2048
    )
    add_sns_lambda_alert(f'crc-api-survey-{stack}', monitoring_sns_topic_arn)

    agents_rest_api_gateway, agents_rest_api_gateway_stage = deploy_apigateway_function(
        f'crc-api-agents-{stack}',
        f'api.agents.app.lambda_handler',
        lambda_security_group_ids,
        lambda_subnet_ids,
        apis_ecr_repo_url,
        ecr_image.image_digest,
        secrets_arn,
        secrets_name,
        lambda_policy_extra=lambda_policy_extra,
        lambda_function_memory_size_mb=2048
    )
    add_sns_lambda_alert(f'crc-api-agents-{stack}', monitoring_sns_topic_arn)

    clientarea_rest_api_gateway, clientarea_rest_api_gateway_stage = deploy_apigateway_function(
        f'crc-api-clientarea-{stack}',
        f'api.clientarea.app.lambda_handler',
        lambda_security_group_ids,
        lambda_subnet_ids,
        apis_ecr_repo_url,
        ecr_image.image_digest,
        secrets_arn,
        secrets_name,
        lambda_policy_extra=lambda_policy_extra,
        lambda_function_memory_size_mb=4096,
        gateway_timeout_milliseconds=119000
    )
    add_sns_lambda_alert(f'crc-api-clientarea-{stack}', monitoring_sns_topic_arn)

    amplify_survey_build_spec = """
version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - 'npm ci --cache .npm --prefer-offline'

        build:
          commands:
            - 'echo "API_BASE_URL =$API_BASE_URL" >> .env'
            - 'echo "API_KEY=$API_KEY" >> .env'
            - 'npm run build'
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - '.next/cache/**/*'
          - '.npm/**/*'
    appRoot: app-survey
"""
    amplify_survey_env = {
        "AMPLIFY_DIFF_DEPLOY": "false",
        "AMPLIFY_MONOREPO_APP_ROOT": "app-survey",
        "API_BASE_URL": survey_rest_api_gateway_stage.invoke_url,
        "API_KEY": api_survey_auth_key,
    }
    survey_app, survey_branch = create_amplify_app('crc-survey',
                                                    amplify_survey_env,
                                                    amplify_survey_build_spec,
                                                    'https://github.com/vaullabs/crc-frontend',
                                                    config.require_secret('amplify_github_token'),
                                                    config.require('amplify_branch'),
                                                    config.require_bool('amplify_auto_deploy'))
    add_sns_amplify_alert('crc-survey', survey_app.id, monitoring_sns_topic_arn)


    amplify_clientarea_build_spec = """
version: 1
applications:
  - frontend:
      phases:
        preBuild:
          commands:
            - 'npm ci --cache .npm --prefer-offline'

        build:
          commands:
            - 'echo "API_BASE_URL=$API_BASE_URL" >> .env'
            - 'echo "AUTH_TRUST_HOST=$AUTH_TRUST_HOST" >> .env'
            - 'echo "AUTH_SECRET=$AUTH_SECRET" >> .env'
            - 'echo "AUTH_URL=$AUTH_URL" >> .env'
            - 'echo "SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN" >> .env'
            - 'npm run build'
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - '.next/cache/**/*'
          - '.npm/**/*'
    appRoot: app-clientarea
"""
    amplify_clientarea_env = {
        "AMPLIFY_DIFF_DEPLOY": "false",
        "AMPLIFY_MONOREPO_APP_ROOT": "app-clientarea",
        "API_BASE_URL": clientarea_rest_api_gateway_stage.invoke_url,
        "AUTH_SECRET": config.require_secret('clientarea_auth_secret'),
        "AUTH_TRUST_HOST": "true",
        "AUTH_URL": config.require('clientarea_auth_url'),
        "SENTRY_AUTH_TOKEN": config.require_secret('sentry_auth_token'),
    }
    clientarea_app, clientarea_branch = create_amplify_app('crc-clientarea',
                                                            amplify_clientarea_env,
                                                            amplify_clientarea_build_spec,
                                                            'https://github.com/vaullabs/crc-frontend',
                                                            config.require_secret('amplify_github_token'),
                                                            config.require('amplify_branch'),
                                                            config.require_bool('amplify_auto_deploy'))
    add_sns_amplify_alert('crc-clientarea', clientarea_app.id, monitoring_sns_topic_arn)


if __name__ == '__main__':
    deploy()
