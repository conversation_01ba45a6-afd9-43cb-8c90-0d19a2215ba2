from litestar.middleware import AbstractAuthenticationMiddleware, AuthenticationResult
from litestar.connection import ASGIConnection
from litestar.exceptions import NotAuthorizedException
from lib.settings import settings


AUTH_KEY_HEADER = "X-AUTH-KEY"


class SalesforceAuthenticationMiddleware(AbstractAuthenticationMiddleware):
    async def authenticate_request(self, connection: ASGIConnection) -> AuthenticationResult:
        auth_header = connection.headers.get(AUTH_KEY_HEADER)

        if not auth_header:
            raise NotAuthorizedException()

        if auth_header != settings.salesforce_auth_key:
            raise NotAuthorizedException()

        user = {
            "id": "salesforce"
        }

        return AuthenticationResult(user=user, auth=auth_header)