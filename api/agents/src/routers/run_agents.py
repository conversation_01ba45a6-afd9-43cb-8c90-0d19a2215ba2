from typing import Dict
from litestar import Controller, get, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.sqsapi import SQS
from api.agents.src.handlers.run_agents.run_email_handler import RunEmailAgentHandler
from api.agents.src.handlers.run_agents.run_survey_round_scheduler_handler import RunSurveyRoundSchedulerAgentHandler


class RunAgentsRouter(Controller):
    dependencies = {
        'sqs': Provide(SQS, sync_to_thread=False),
    }

    @get(
        '/surveyroundscheduler',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def run_survey_round_scheduler_agent(self, survey_round_id: str, sqs: SQS) -> Dict:
        return RunSurveyRoundSchedulerAgentHandler(survey_round_id, sqs).handler()

    @post(
        '/email',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def run_email_sender_agent(self, data: dict, sqs: SQS) -> Dict:
        return <PERSON><PERSON>mail<PERSON><PERSON><PERSON>and<PERSON>(data, sqs).handler()