from typing import Dict
from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.sqsapi import SQS
from api.agents.src.handlers.sync_agents.run_customer_survey_sync_handler import RunCustomerSurveySyncAgentHandler
from api.agents.src.handlers.sync_agents.run_survey_client_sync_handler import RunSurveyClientSyncAgentHandler


class SyncAgentsRouter(Controller):
    path = '/sync'
    dependencies = {
        'sqs': Provide(SQS, sync_to_thread=False),
    }

    @get(
        '/customersurvey',
         cache=False,
         status_code=HTTP_200_OK,
         sync_to_thread=False,
    )
    def run_customer_survey_sync_agents(self, customer_survey_id: str, sqs: SQS) -> Dict:
        return RunCustomerSurveySyncAgentHandler(customer_survey_id, sqs).handler()


    @get(
        '/surveyclient',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def run_survey_client_sync_agent(self, survey_client_id: str, sqs: SQS) -> Dict:
        return RunSurveyClientSyncAgentHandler(survey_client_id, sqs).handler()