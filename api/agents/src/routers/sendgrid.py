from typing import Dict
from litestar import Controller, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.agents.src.models.sendgrid import SendGridEvent
from api.agents.src.handlers.sendgrid.event_handler import Send<PERSON><PERSON>Handler
from typing import List

class SendGridRouter(Controller):
    dependencies = {
        'documentdb': Provide(DocumentDB, sync_to_thread=False),
    }

    @post(
        '/events',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def sendgridevents(self, data: List[SendGridEvent], documentdb: DocumentDB) -> Dict:
        return SendGridHandler(data, documentdb).handler()