from litestar import Router
from litestar.middleware.base import DefineMiddleware
from api.agents.src.middleware.salesforce import SalesforceAuthenticationMiddleware
from api.agents.src.routers.sendgrid import SendGridRouter
from api.agents.src.routers.sync_agents import SyncAgentsRouter
from api.agents.src.routers.run_agents import RunAgentsRouter

routes = [
    Router(
        path='',
        middleware=[DefineMiddleware(SalesforceAuthenticationMiddleware)],
        route_handlers=[SyncAgentsRouter, RunAgentsRouter],
    ),
    Router(
        path='/sendgrid',
        route_handlers=[SendGridRouter],
        middleware=[],
    )
]