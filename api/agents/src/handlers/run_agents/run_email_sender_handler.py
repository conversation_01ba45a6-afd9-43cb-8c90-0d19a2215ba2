import json
from typing import Dict
from lib.settings import settings
from lib.sqsapi import SQS


class RunEmailSenderAgentHandler:
    def __init__(self, data: dict, sqs: SQS) -> None:
        self.data = data
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        template_id = self.data.get('templateId')
        destination_address = self.data.get('destinationAddress')
        source_address = self.data.get('sourceAddress')
        template_data = json.dumps(self.data)

        try:
            self.sqs.send_message(
                queue=settings.email_sender_queue_url,
                message_body=f'Run email sender',
                message_attributes={
                     'templateId': {
                        'DataType': 'String',
                        'StringValue': template_id,
                    },
                     'destinationAddress': {
                        'DataType': 'String',
                        'StringValue': destination_address,
                    },
                     'sourceAddress': {
                        'DataType': 'String',
                        'StringValue': source_address,
                    },
                    'templateData': {
                        'DataType': 'String',
                        'StringValue': template_data,
                    },
                },
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }