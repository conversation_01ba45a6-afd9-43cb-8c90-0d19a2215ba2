import json
from typing import Dict
from lib.sqsapi import SQS
from lib.settings import settings


class RunEmailAgentHandler:
    def __init__(self, data: dict, sqs: SQS) -> None:
        self.data = data
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        to_email = self.data.get('toEmail')
        template_id = self.data.get('templateId')
        template_data = json.dumps(self.data.get('templateData'))
        custom_args = json.dumps(self.data.get('customArgs'))
        
        try:
            self.sqs.send_message(
                queue=settings.send_email_to_sg_queue_url,
                message_body=f'Email template {template_id} to {to_email}',
                message_attributes={
                     'toEmail': {
                        'DataType': 'String',
                        'StringValue': to_email,
                    },
                     'templateId': {
                        'DataType': 'String',
                        'StringValue': template_id,
                    },
                     'templateData': {
                        'DataType': 'String',
                        'StringValue': template_data,
                    },
                    'customArgs': {
                        'DataType': 'String',
                        'StringValue': custom_args,
                    },                
                },
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }