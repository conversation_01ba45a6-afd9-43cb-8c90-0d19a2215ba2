from typing import Dict
from lib.settings import settings
from lib.sqsapi import SQS


class RunSurveyRoundSchedulerAgentHandler:
    def __init__(self, survey_round_id: str, sqs: SQS) -> None:
        self.survey_round_id = survey_round_id
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        try:
            self.sqs.send_message(
                queue=settings.survey_round_scheduler_queue_url,
                message_body=f'Run survey round scheduler for survey round {self.survey_round_id}',
                message_attributes={
                    'SurveyRoundId': {
                        'DataType': 'String',
                        'StringValue': self.survey_round_id,
                    },
                },
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'survey_round_id': self.survey_round_id,
            'success': success,
        }