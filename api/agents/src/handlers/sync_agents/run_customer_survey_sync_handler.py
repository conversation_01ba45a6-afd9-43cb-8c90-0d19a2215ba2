from typing import Dict
from lib.settings import settings
from lib.sqsapi import SQS


class RunCustomerSurveySyncAgentHandler:
    def __init__(self, customer_survey_id: str, sqs: SQS) -> None:
        self.customer_survey_id = customer_survey_id
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        try:
            # send message to sqs to run the sf_sync_customer_surveys
            self.sqs.send_message(
                queue=settings.sync_customer_surveys_queue_url,
                message_body=f'Sync a customer survey to portal DB from SF flow through customer survey {self.customer_survey_id}',
                message_attributes={
                    'CustomerSurveyId': {
                        'DataType': 'String',
                        'StringValue': self.customer_survey_id,
                    },
                },
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'customer_survey_id': self.customer_survey_id,
            'success': success,
        }