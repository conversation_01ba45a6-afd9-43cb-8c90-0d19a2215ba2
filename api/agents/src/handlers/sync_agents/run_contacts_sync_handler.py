from typing import Dict
from lib.settings import settings
from lib.sqsapi import SQS


class RunContactsSyncAgentHandler:
    def __init__(self, sqs: SQS) -> None:
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        try:
            # send message to sqs to run the sf_sync_contacts
            self.sqs.send_message(
                queue=settings.sync_contacts_queue_url,
                message_body=f'Sync contacts to portal DB from SF flow',
                message_attributes={},
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }