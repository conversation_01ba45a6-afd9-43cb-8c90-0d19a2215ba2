from typing import Dict
from lib.settings import settings
from lib.sqsapi import SQS


class RunSurveyClientSyncAgentHandler:
    def __init__(self, survey_client_id: str, sqs: SQS) -> None:
        self.survey_client_id = survey_client_id
        self.sqs = sqs

    def handler(self) -> Dict:
        success = False

        try:
            self.sqs.send_message(
                queue=settings.sync_survey_clients_queue_url,
                message_body=f'Sync survey client {self.survey_client_id} to portal DB from SF flow',
                message_attributes={
                    'SurveyClientId': {
                        'DataType': 'String',
                        'StringValue': self.survey_client_id,
                    },
                },
            )

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'survey_client_id': self.survey_client_id,
            'success': success,
        }