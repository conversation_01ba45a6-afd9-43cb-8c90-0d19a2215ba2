from datetime import datetime
from pymongo import InsertOne
from typing import Dict, List

from api.agents.src.models.sendgrid import SendGrid<PERSON>vent
from lib.settings import settings
from lib.portaldbapi import DocumentDB, MONGO_BATCH_SIZE


TRACKED_EVENTS = {'delivered', 'open', 'click', 'bounce', 'drop'}


class SendGridHandler:
    def __init__(self, data: List[SendGridEvent], db: DocumentDB) -> None:
        self.data = data
        self.db = db

    def handler(self) -> Dict:
        success = False

        # get the tracked templates from settings
        tracked_templates = settings.TRACKED_TEMPLATES.split(',')

        batch: list = []
        stats = {'template_null': 0, 'untracked_template': 0, 'untracked_event': 0, 'no_panel_member_id': 0, 'processed': 0}
        try:
            for event in self.data:
                if event.tracked_template_name is None:
                    stats['template_null'] += 1
                    continue
                if event.tracked_template_name not in tracked_templates:
                    stats['untracked_template'] += 1
                    continue

                # if the event name is not one we currently track, skip it
                if event.event not in TRACKED_EVENTS:
                    stats['untracked_event'] += 1
                    continue

                # if no survey panel member ID is found on the custom args, skip this email
                if not event.survey_panel_member_id:
                    stats['no_panel_member_id'] += 1
                    continue
                
                survey_panel_member_ids = []
                if event.survey_panel_member_ids:
                    # track all panel members associated with the email
                    survey_panel_member_ids = event.survey_panel_member_ids.split(',')

                email_dict = dict(
                    email=event.email,
                    event=event.event,
                    timestamp=datetime.fromtimestamp(event.timestamp),
                    sg_event_id=event.sg_event_id,
                    survey_panel_member_id=event.survey_panel_member_id,
                    survey_panel_member_ids=survey_panel_member_ids,
                    template=event.tracked_template_name,
                    sg_machine_open=event.sg_machine_open,
                )
                batch.append(InsertOne(email_dict))
                stats['processed'] += 1

                if len(batch) > MONGO_BATCH_SIZE:
                    self.db.sendgridevents.bulk_write(batch)
                    batch.clear()

            if batch:
                self.db.sendgridevents.bulk_write(batch)
                batch.clear()

            success = True
        except Exception as e:
            raise ValueError(repr(e))

        print(F"event stats: {stats}")

        return {
            'success': success,
        }
