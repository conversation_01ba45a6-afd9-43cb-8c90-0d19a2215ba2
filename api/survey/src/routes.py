from litestar import Router
from api.survey.src.routers.survey import SurveyRouter
from api.survey.src.routers.surveys import SurveysRouter
from api.survey.src.routers.privacy import PrivacyRouter
from api.survey.src.routers.optout import OptOutRouter


routes = [
    Router(
        path='/survey',
        route_handlers=[SurveyRouter],
    ),

    Router(
        path='/surveys',
        route_handlers=[SurveysRouter],
    ),

    Router(
        path='/privacy',
        route_handlers=[PrivacyRouter],
    ),

    Router(
        path='/optout',
        route_handlers=[OptOutRouter],
    )
]