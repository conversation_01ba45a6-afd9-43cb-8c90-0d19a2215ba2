import datetime
from litestar.exceptions import NotFoundException
from lib.portaldbapi import DocumentDB
from api.survey.src.services.survey import SurveyService


class GetSurveyHandler:
    def __init__(self, survey_id: str, db: DocumentDB):
        self.survey_id = survey_id
        self.db = db

    def handler(self):
        # get the survey document and check it has not been completed
        survey = SurveyService(self.db).get_survey(self.survey_id)

        if not survey:
            raise NotFoundException()
        
        if survey.end_date.replace(tzinfo=None) < datetime.datetime.now(datetime.UTC).replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None):
            return { "status": "closed", "config": survey.config }

        if survey.response:
            return { "status": "already_completed", "config": survey.config }

        return {
            "status": "ok",
            "config": survey.config,
            "client_name": survey.client_name,
            "account_id": survey.account_id,
        }