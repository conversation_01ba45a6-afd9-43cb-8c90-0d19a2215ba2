from sendgrid import SendGridAPIClient
from lib.portaldbapi import DocumentDB
from lib.sqsapi import SQS
from lib.settings import settings


class PatchOptOutHandler:
    def __init__(self, contact_id: str, data: dict, db: DocumentDB, sqs: SQS):
        self.contact_id = contact_id
        self.data = data
        self.db = db
        self.sqs = sqs

    def handler(self):
        sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)

        if self.data:
            contact_opted_out_all = self.data.get('contactOptOutAll', False)
            if contact_opted_out_all:
                # add SQS message to opt out agent
                self.sqs.send_message(
                    queue=settings.sync_optout_to_sf_queue_url,
                    message_body=f'Opt-Out for contact {self.contact_id}',
                    message_attributes={
                        'contactId': {
                            'DataType': 'String',
                            'StringValue': self.contact_id,
                        },
                    },
                )

                # get contact from portaldb
                contact = self.db.contacts.find_one({'Id': self.contact_id}, {'_id': 0, 'Id': 1, 'Email': 1})
                if contact:
                    # add email to global suppression list in SendGrid
                    response = sg.client.asm.suppressions._("global").post(
                        request_body={
                            "recipient_emails": [contact['Email']],
                        }
                    )
            else:
                # check the survey panel members
                spm_optouts = self.data.get('panelMembersOptedOut', [])
                for spm_id in spm_optouts:
                    # add SQS message to opt out agent
                    self.sqs.send_message(
                        queue=settings.sync_optout_to_sf_queue_url,
                        message_body=f'Opt-Out for Survey Panel Member {spm_id}',
                        message_attributes={
                            'surveyPanelMemberId': {
                                'DataType': 'String',
                                'StringValue': spm_id,
                            },
                        },
                    )

        return { "status": "ok" }