import datetime
from lib.portaldbapi import DocumentDB
from api.survey.src.services.survey import SurveyService


class GetSurveysHandler:
    def __init__(self, referer: str, email: str, db: DocumentDB):
        self.referer = referer
        self.email = email
        self.db = db

    def handler(self):
        surveys = SurveyService(self.db).get_surveys_by_email(self.email)

        # log request
        self.db.surveysrequestlog.insert_one({
            "email": self.email,
            "referer": self.referer,
            "surveys": surveys,
            "timestamp": datetime.datetime.now(datetime.UTC)
        })

        if not surveys:
            return { "status": "no_surveys", "surveys": surveys }

        return {
            "status": "ok",
            "surveys": surveys
        }