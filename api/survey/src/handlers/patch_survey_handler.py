import json
from litestar.exceptions import NotFoundException, InternalServerException
from lib.portaldbapi import DocumentDB
from api.survey.src.services.survey import SurveyService


class PatchSurveyHandler:
    def __init__(self, survey_id: str, data: dict, db: DocumentDB):
        self.survey_id = survey_id
        self.db = db
        self.data = data
        self.max_response_size = 20 * 1024

    def handler(self):
        ss = SurveyService(self.db)

        # make sure the response is not too big
        if json.dumps(self.data).encode("utf-8").__sizeof__() > self.max_response_size:
            raise InternalServerException()

        # get the survey and check its not already completed
        survey = ss.get_survey(self.survey_id)

        if not survey:
            raise NotFoundException()

        if survey.response:
            return { "status": "already_completed" }

        # update the survey with the response and remove the sfsync_date field
        status = ss.update_response(self.survey_id, self.data)

        if status.modified_count != 1:
            return InternalServerException()

        return { "status": "ok" }