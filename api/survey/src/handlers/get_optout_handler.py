from bson import ObjectId
from simple_salesforce import format_soql

from lib import sfapi
from lib.settings import settings


class GetOptOutHandler:
    def __init__(self, contact_id: str):
        self.contact_id = contact_id

    def handler(self):
        # get the survery panel members associated with a contact
        # for current surveys
        sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
        soql = """
        SELECT Id,
               Name,
               Survey_Type__c,
               Contact__r.Id,
               Contact__r.Email,
               Survey_Client__r.Survey_Name__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
        FROM  Survey_Panel_Member__c
        WHERE Contact__r.Id = {contact_id}
        AND   Opt_Out__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = {survey_stage}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """

        email = ""
        panel_members = []
        survey_stage = 'Live Survey'
        # survey_stage = 'Insights'  # for testing
        query = format_soql(soql, contact_id=self.contact_id, survey_stage=survey_stage)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            survey_type = spm.get('Survey_Type__c', 'TRR')
            contact = spm.get('Contact__r', {}) or {}
            email = contact.get('Email', email)
            survey_client = spm.get('Survey_Client__r', {}) or {}
            customer_survey = survey_client.get('Customer_Survey__r', {}) or {}
            media_agency = customer_survey.get('Customer__r', {}) or {}
            brand_agency = media_agency.get('Parent', {}) or {}

            agency_brand_name = brand_agency.get('Name')
            if survey_type and survey_type == 'Barometer' and survey_client.get('Survey_Name__c'):
                agency_brand_name = survey_client.get('Survey_Name__c')
            spm_data = {"id": spm.get('Id'), "agency_brand_name": agency_brand_name}
            panel_members.append(spm_data)

        # print(panel_members)
        return {
            "email": email,
            "survey_panel_members": panel_members,
        }
