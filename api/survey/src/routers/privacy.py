from litestar import Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.survey.src.handlers.get_privacy_handler import GetPrivacyHandler


class PrivacyRouter(Controller):
    dependencies = {
        'db': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '/{c:str}',
        cache=True,
        status_code=HTTP_200_OK,
        sync_to_thread=False
    )
    def get_privacy(self, c: str, db: DocumentDB) -> dict:
        return GetPrivacyHandler(c, db).handler()