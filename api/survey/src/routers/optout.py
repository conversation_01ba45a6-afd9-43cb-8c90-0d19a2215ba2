from litestar import Controller, get, post
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from lib.sqsapi import SQS
from api.survey.src.handlers.get_optout_handler import GetOptOutHandler
from api.survey.src.handlers.patch_optout_handler import PatchOptOutHandler


class OptOutRouter(Controller):
    dependencies = {
        'db': Provide(DocumentDB, sync_to_thread=False),
        'sqs': Provide(SQS, sync_to_thread=False)
    }

    @get(
        '/{contact_id:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_survey(self, contact_id: str) -> None:
        return GetOptOutHandler(contact_id).handler()


    @post(
        '/{contact_id:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False
    )
    def update_optout(self, contact_id: str, data: dict, db: DocumentDB, sqs: SQS) -> dict:
        return PatchOptOutHandler(contact_id, data, db, sqs).handler()