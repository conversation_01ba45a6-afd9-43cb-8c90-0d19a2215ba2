from litestar import Controller, get, patch
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.survey.src.handlers.get_survey_handler import GetSurveyHandler
from api.survey.src.handlers.patch_survey_handler import PatchSurveyHandler


class SurveyRouter(Controller):
    dependencies = {
        'db': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '/{survey_id:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_survey(self, survey_id: str, db: DocumentDB) -> None:
        return GetSurveyHandler(survey_id, db).handler()


    @patch(
        '/{survey_id:str}',
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def patch_survey(self, survey_id: str, data: dict, db: DocumentDB) -> None:
        return PatchSurveyHandler(survey_id, data, db).handler()
