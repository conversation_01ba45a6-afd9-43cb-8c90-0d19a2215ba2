from litestar import Request, Controller, get
from litestar.di import Provide
from litestar.status_codes import HTTP_200_OK
from lib.portaldbapi import DocumentDB
from api.survey.src.handlers.get_surveys_handler import GetSurveysHandler


class SurveysRouter(Controller):
    dependencies = {
        'db': Provide(DocumentDB, sync_to_thread=False),
    }

    @get(
        '/{email:str}',
        cache=False,
        status_code=HTTP_200_OK,
        sync_to_thread=False,
    )
    def get_surveys(self, request: Request, email: str, db: DocumentDB) -> None:
        referer = request.headers.get('X-SURVEYS-REFERER')
        return GetSurveysHandler(referer, email, db).handler()