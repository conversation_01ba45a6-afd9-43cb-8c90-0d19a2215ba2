import datetime
from bson import ObjectId
from lib.portaldbapi import Survey, DocumentDB


class SurveyService():
    def __init__(self, db: DocumentDB):
        self.db = db

    def get_survey(self, survey_id: str) -> Survey | None:
        now = datetime.datetime.now(datetime.UTC)
        query = dict(
            _id=ObjectId(survey_id),
            #start_date={ "$lte": now },
            #end_date={ "$gt": now + datetime.timedelta(days=1)}
        )
        res = self.db.surveys.find_one(query)
        return Survey(**res) if res else None

    def get_surveys_by_email(self, email: str) -> list[str]:
        survey_links = []
        now = datetime.datetime.now(datetime.UTC)
        query = {
            "response": None,
            "sfsync_date": None,
            "end_date": {"$gte": now},
            "expose_to_find_surveys": True,
            "questions": {
                "$not": {
                    "$elemMatch": {
                        "panel_members": {
                            "$not": {
                                "$elemMatch": {
                                    "survey_panel_member_name": email
                                }
                            }
                        }
                    }
                }
            }
        }
        res = self.db.surveys.find(query)
        for survey in res:
            survey_links.append(
                f'https://survey.thereferralrating.com/survey/{str(survey["_id"])}'
            )
        return survey_links


    def update_response(self, survey_id: str, data: dict) -> dict:
        update = {
            "$set": {
                "response": data,
                "response_date": datetime.datetime.now(datetime.UTC),
            },
            "$unset": {
                "sfsync_date": True
            }
        }
        query = { "_id": ObjectId(survey_id), "response": { "$eq": None }, "response_date": { "$eq": None }}
        return self.db.surveys.update_one(query, update)
