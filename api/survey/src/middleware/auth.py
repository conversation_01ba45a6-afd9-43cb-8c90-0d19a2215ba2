from litestar.middleware import AbstractAuthenticationMiddleware, AuthenticationResult
from litestar.connection import ASGIConnection
from litestar.exceptions import NotAuthorizedException
from api.survey.src.settings import settings


AUTH_KEY_HEADER = "X-API-KEY"


class AuthenticationMiddleware(AbstractAuthenticationMiddleware):
    async def authenticate_request(self, connection: ASGIConnection) -> AuthenticationResult:
        auth_header = connection.headers.get(AUTH_KEY_HEADER)

        if not auth_header:
            raise NotAuthorizedException()

        if auth_header != settings.api_survey_auth_key:
            raise NotAuthorizedException()

        user = {
            "id": "internal"
        }

        return AuthenticationResult(user=user, auth=auth_header)