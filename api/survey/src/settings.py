from pydantic import BaseModel
from lib import settingsapi


class CacheSettings(BaseModel):
    default_expiration: int = 7200


class CorsSettings(BaseModel):
    allow_origins: list = [
        #local development
        'http://localhost:3000',
        'http://127.0.0.1:3000',
        #amplify
        'https://main.da8wekfw476r2.amplifyapp.com',
    ]
    allow_methods: list = [
        'get',
        'post',
        'PATCH'
    ]


class CompressionSettings(BaseModel):
    backend: str = 'brotli'
    brotli_gzip_fallback: bool = True


class Settings(settingsapi.Settings):
    cache: CacheSettings = CacheSettings()
    cors: CorsSettings = CorsSettings()
    compression: CompressionSettings = CompressionSettings()

settings = Settings()
