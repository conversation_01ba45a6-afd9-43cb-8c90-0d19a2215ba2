import sentry_sdk
from pathlib import Path
from litestar import Litestar
from litestar.config.response_cache import ResponseCacheConfig
from litestar.config.compression import CompressionConfig
from litestar.logging.config import LoggingConfig
from litestar.config.cors import CORSConfig
from litestar.middleware import DefineMiddleware
from litestar.template.config import TemplateConfig
from litestar.openapi import OpenAPIConfig
from litestar.openapi.plugins import ScalarRenderPlugin
from litestar.contrib.jinja import JinjaTemplateEngine
from sentry_sdk.integrations.aws_lambda import AwsLambdaIntegration
from sentry_sdk.integrations.litestar import LitestarIntegration
from mangum import Mangum
from api.survey.src.middleware.auth import AuthenticationMiddleware
from lib.settings import settings as settingsapi
from api.survey.src.settings import settings
from api.survey.src.routes import routes


if settingsapi.sentry_dsn_survey:
    sentry_sdk.init(
        dsn=settingsapi.sentry_dsn_survey,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        integrations=[
            AwsLambdaIntegration(),
            LitestarIntegration()
        ],
        environment=settingsapi.sentry_environment,
    )


app = Litestar(
    route_handlers=routes,
    #middleware=[DefineMiddleware(AuthenticationMiddleware)],
    cors_config=CORSConfig(**settings.cors.dict()),
    response_cache_config=ResponseCacheConfig(**settings.cache.dict()),
    compression_config=CompressionConfig(**settings.compression.dict()),
    logging_config=LoggingConfig(log_exceptions='always'),
    template_config=TemplateConfig(
        directory=Path('api/survey/src/templates'),
        engine=JinjaTemplateEngine,
    ),
    openapi_config=OpenAPIConfig(
        title='CRC API Survey',
        version='0.0.1',
        render_plugins=[ScalarRenderPlugin()],
    ),
)

lambda_handler = Mangum(app)