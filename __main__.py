import pulumi
from lib.deploy import  deploy_secrets, deploy_ecr_repo, deploy_documentdb, deploy_s3_bucket, \
                        config, create_standard_vpc, create_security_group, create_ec2_keypair, \
                        create_rds_subnet_group, create_cognito_user_pool_with_lambda_email_handler, \
                        aws_region, aws_account_id, stack, create_vpn_instance, create_user_pool_app_client, \
                        create_sns_topic, create_iam_user, create_rds_postgres, create_cognito_user_pool_domain



def deploy():
    # create the VPC
    vpc, public_subnets, private_subnets, admin_sg, publicssh_sg, default_sg = create_standard_vpc("crc", config.require('vpc_cidr'))
    mongodb_sg = create_security_group("crc-mongodb",
                                       vpc.id,
                                       [dict(from_port=27017, to_port=27017, ip_protocol="tcp", cidr_ipv4="0.0.0.0/0")])
    postgres_sg = create_security_group("crc-postgres",
                                        vpc.id,
                                        [dict(cidr_ipv4="0.0.0.0/0", from_port=5432, to_port=5432, ip_protocol="tcp")])

    # create the ecr repos
    agents_ecr_repo = deploy_ecr_repo("crc-backend-agents")
    apis_ecr_repo = deploy_ecr_repo("crc-backend-apis")

    # create the documentdb cluster
    rds_private_subnet_group = create_rds_subnet_group("crc-private", [x.id for x in private_subnets.values()])
    docdb_cluster = deploy_documentdb("crc-portaldb",
                                      config.require('portaldb_username'),
                                      config.require_secret('portaldb_password'),
                                      rds_private_subnet_group.name,
                                      [mongodb_sg.id, admin_sg.id],
                                      config.require_object('portaldb_instance_azs'),
                                      config.require('portaldb_instance_type'))

    # create the monitor instance
    monitor_rds_instance = None
    if stack in {'prod'}:
        monitor_rds_instance = create_rds_postgres("crc-monitordb", 200, 'a', 'db.t4g.micro', rds_private_subnet_group.name, [postgres_sg.id, admin_sg.id])

    logging_bucket_policy = {
        "Version": "2012-10-17",
        "Id": "S3-Console-Auto-Gen-Policy-*************",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "logging.s3.amazonaws.com"
                },
                "Action": "s3:PutObject",
                "Resource": "arn:aws:s3:::crc-srpreports-logs-prod/*",
                "Condition": {
                    "StringEquals": {
                        "aws:SourceAccount": "************"
                    }
                }
            }
        ]
    }

    # s3 buckets
    sf_data_bucket = deploy_s3_bucket(f"crc-sf-data-{stack}")
    sf_csv_bucket = deploy_s3_bucket(f"crc-csv-data-{stack}")
    sf_banners_bucket = deploy_s3_bucket(f"crc-banners-data-{stack}")
    sf_signatures_bucket = deploy_s3_bucket(f"crc-signatures-data-{stack}")
    sf_srpreports_logs_bucket = deploy_s3_bucket(f"crc-srpreports-logs-{stack}", bucket_policy=logging_bucket_policy)
    sf_srpreports_bucket = deploy_s3_bucket(f"crc-srpreports-data-{stack}", logging_bucket=sf_srpreports_logs_bucket)
    monitor_assets_bucket = deploy_s3_bucket(f"crc-monitor-assets-{stack}")
    sg_template_cache_bucket = deploy_s3_bucket(f"crc-sg-template-cache-{stack}")

    # create the vpn instance
    vaulroot_keypair = create_ec2_keypair("vaulroot", config.require("vaulroot_public_key"))
    vpn_instance, wireguard_sg = create_vpn_instance(vpc, config.require("vpc_cidr"), vaulroot_keypair, public_subnets['a'], admin_sg)

    # setup user pool with customer email sender lambda
    email_sender_arn = f"arn:aws:lambda:{aws_region}:{aws_account_id}:function:crc-agent-cognito_email_trigger-{stack}"
    user_pool, user_pool_kms_key = create_cognito_user_pool_with_lambda_email_handler('crc', email_sender_arn)
    user_pool_app_client = create_user_pool_app_client('crc-clientarea', user_pool.id)
    user_pool_domain = create_cognito_user_pool_domain('crc', config.require('cognito_domain_prefix'), user_pool.id)

    if monitor_rds_instance:
        monitordb_emails_password = config.require_secret('monitordb_emails_password')
        monitordb_timeseries_password = config.require_secret('monitordb_timeseries_password')
        monitordb_salesforce_password = config.require_secret('monitordb_salesforce_password')
        monitordb_emails_url = pulumi.Output.format('postgres://crc_emails:{}@{}/crc_emails', monitordb_emails_password, monitor_rds_instance.endpoint)
        monitordb_timeseries_url = pulumi.Output.format('postgres://crc_timeseries:{}@{}/crc_timeseries', monitordb_timeseries_password, monitor_rds_instance.endpoint)
        monitordb_salesforce_url = pulumi.Output.format('postgres://crc_salesforce:{}@{}/crc_salesforce', monitordb_salesforce_password, monitor_rds_instance.endpoint)
    else:
        monitordb_emails_url = monitordb_timeseries_url = monitordb_salesforce_url = ""

    # secrets for use by the lambdas
    params = dict(username=config.require('portaldb_username'),
                  password=config.require_secret('portaldb_password'),
                  hostname=docdb_cluster.endpoint)
    portaldb_url = pulumi.Output.format('mongodb://{username}:{password}@{hostname}:27017/?tls=true&tlsInsecure=true&replicaSet=rs0&readPreference=secondaryPreferred&retryWrites=false', **params)
    secrets_dict = {
        # sendgrid
        'SENDGRID_API_KEY': config.require_secret('sendgrid_api_key'),
        'SENDGRID_FROM_EMAIL': config.require('sendgrid_from_email'),
        # sendgrid email event tracking
        'TRACKED_TEMPLATES': config.require('tracked_templates'),
        # this is set in non-prod environments to prevent prod tracking emails sent in dev
        'TRACKED_TEMPLATE_SUFFIX': config.require('tracked_template_suffix'),
        # portaldb
        'PORTALDB_URL': portaldb_url,
        # salesforce
        'SF_INSTANCE': config.require('sf_instance'),
        'SF_CLIENT_ID': config.require_secret('sf_client_id'),
        'SF_CLIENT_SECRET': config.require_secret('sf_client_secret'),
        # s3
        'SF_DATA_BUCKET': sf_data_bucket.bucket,
        'SF_CSV_BUCKET': sf_csv_bucket.bucket,
        'SF_BANNERS_BUCKET': sf_banners_bucket.bucket,
        'SF_SIGNATURES_BUCKET': sf_signatures_bucket.bucket,
        'SF_SRP_REPORTS_BUCKET': sf_srpreports_bucket.bucket,
        'SF_ADH_BUCKET': config.require('sf_adh_bucket'),
        'SG_TEMPLATE_CACHE_BUCKET': sg_template_cache_bucket.bucket,
        # apis
        'api_survey_auth_key': config.require_secret('api_survey_auth_key'),
        'salesforce_auth_key': config.require_secret('salesforce_auth_key'),
        'csv_download_auth_key': config.require_secret('csv_download_auth_key'),
        # cognito
        'confirmation_code_encryption_key': config.require_secret('confirmation_code_encryption_key'),
        'verify_login_link': config.require('verify_login_link'),
        'forgotten_password_link': config.require('forgotten_password_link'),
        'cognito_key_ids_arn': user_pool_kms_key.arn,
        'cognito_client_id': user_pool_app_client.id,
        'cognito_client_secret': user_pool_app_client.client_secret,
        'cognito_issuer': pulumi.Output.format("https://{}", user_pool.endpoint),
        'cognito_audience': user_pool_app_client.id,
        'cognito_keys_url': pulumi.Output.format("https://{}/.well-known/jwks.json", user_pool.endpoint),
        'cognito_domain': pulumi.Output.format("https://{}.auth.{}.amazoncognito.com", user_pool_domain.domain, aws_region),
        'cognito_user_pool_id': user_pool.id,
        'cognito_redirect_uri': config.require('cognito_redirect_uri'),
        # sqs queues
        'survey_round_scheduler_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-survey_round_scheduler-{stack}-queue',
        'sync_customer_surveys_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-sync_confirmaccounts_to_portaldb-{stack}-queue',
        'sync_survey_clients_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-sync_confirmpanel_to_portaldb-{stack}-queue',
        'sync_optout_to_sf_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-sync_optout_to_sf-{stack}-queue',
        'send_email_to_sg_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-send_email_to_sg-{stack}-queue',
        'send_email_to_ses_queue_url': f'https://sqs.{aws_region}.amazonaws.com/{aws_account_id}/crc-agent-send_email_to_ses-{stack}-queue',
        # ses
        'ses_access_key': config.require('ses_access_key'),
        'ses_secret_access_key': config.require_secret('ses_secret_access_key'),
        'ses_configuration_set_name': config.require('ses_configuration_set_name'),
        # monitoring
        'sns_slack_webhook_url': config.require_secret('sns_slack_webhook_url'),
        'sns_slack_resolution_error_webhook_url': config.require_secret('sns_slack_resolution_error_webhook_url'),
        # ui
        'client_area_ui_link': config.require('client_area_ui_link'),
        'survey_ui_link': config.require('survey_ui_link'),
        'survey_amplify_domain': config.require('survey_amplify_domain'),
        'survey_amplify_app_id': config.require('survey_amplify_app_id'),
        # monitoring settings
        'monitordb_timeseries_url': monitordb_timeseries_url,
        'monitordb_email_url': monitordb_emails_url,
        'monitordb_salesforce_url': monitordb_salesforce_url,
        'monitor_email_tenant_id': config.require('monitor_email_tenant_id'),
        'monitor_email_client_id': config.require('monitor_email_client_id'),
        'monitor_email_client_secret': config.require_secret('monitor_email_client_secret'),
        'monitor_assets_bucket': monitor_assets_bucket.bucket,
    }
    secrets, secrets_version = deploy_secrets("crc-backend", secrets_dict)

    monitoring_topic = create_sns_topic('crc-monitoring')

    sf_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Action": "sqs:SendMessage",
                "Resource": [
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-send_email_to_sg-{stack}-queue",
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-send_email_to_ses-{stack}-queue",
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-survey_round_scheduler-{stack}-queue",
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_confirmaccounts_to_portaldb-{stack}-queue",
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_confirmpanel_to_portaldb-{stack}-queue",
                    f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-sync_customersurvey_dates_to_portaldb-{stack}-queue"
                ]
            }
        ]
    }
    crc_sf_iam_user, crc_sf_iam_user_access_key = create_iam_user('crc-sf', sf_policy)
    pulumi.export('crc_sf_access_key_id', crc_sf_iam_user_access_key.id)
    pulumi.export('crc_sf_secret_access_key', crc_sf_iam_user_access_key.secret)

    pulumi.export("aws_region", aws_region)
    pulumi.export("aws_account_id", aws_account_id)
    pulumi.export("secrets_arn", secrets.arn)
    pulumi.export("secrets_name", secrets.name)
    pulumi.export("agents_ecr_repo_arn", agents_ecr_repo.arn)
    pulumi.export("agents_ecr_repo_url", agents_ecr_repo.repository_url)
    pulumi.export("agents_ecr_repo_name", agents_ecr_repo.name)
    pulumi.export("apis_ecr_repo_arn", apis_ecr_repo.arn)
    pulumi.export("apis_ecr_repo_url", apis_ecr_repo.repository_url)
    pulumi.export("apis_ecr_repo_name", apis_ecr_repo.name)
    pulumi.export("doccb_cluster_arn", docdb_cluster.arn)
    pulumi.export("doccb_cluster_endpoint", docdb_cluster.endpoint)
    pulumi.export("doccb_cluster_endpoint_ro", docdb_cluster.reader_endpoint)
    pulumi.export("sf_data_bucket_arn", sf_data_bucket.arn)
    pulumi.export("sf_data_bucket_name", sf_data_bucket.bucket)
    pulumi.export("sf_csv_bucket_arn", sf_csv_bucket.arn)
    pulumi.export("sf_csv_bucket_name", sf_csv_bucket.bucket)
    pulumi.export("default_sg_id", default_sg.id)
    pulumi.export("admin_sg_id", admin_sg.id)
    pulumi.export("mongodb_sg_id", mongodb_sg.id)
    pulumi.export("user_pool_arn", user_pool.arn)
    pulumi.export("user_pool_id", user_pool.id)
    pulumi.export("user_pool_kms_key_arn", user_pool_kms_key.arn)
    pulumi.export("user_pool_kms_key_id", user_pool_kms_key.key_id)
    pulumi.export("user_pool_app_client_id", user_pool_app_client.id)
    pulumi.export("monitoring_topic_arn", monitoring_topic.arn)
    pulumi.export("monitoring_topic_name", monitoring_topic.name)
    pulumi.export("api_survey_auth_key", config.require_secret('api_survey_auth_key'))
    for az, subnet in public_subnets.items():
        pulumi.export(f"public_subnet_{az}_id", subnet.id)
    for az, subnet in private_subnets.items():
        pulumi.export(f"private_subnet_{az}_id", subnet.id)


if __name__ == '__main__':
    deploy()
