#!/bin/sh

set -e

XLSX=$1
TYPE=$2
WRITEIT=$3
XLSXBASE=`basename "$XLSX"`

if [ "$STACK" = "prod" ]; then
    read -p "Are you sure you mean to run against prod? (type YES if you are) " PROD_IS_OK
    if [ "$PROD_IS_OK" != "YES" ]; then
        echo "Aborting"
        exit 1
    fi
    export PROD_IS_OK
fi

python3 -c "
import pandas as pd
df = pd.read_excel('$XLSX')
df.to_csv('$XLSX.csv', index=False)
"

python3 -m sftools.mapthemes "$XLSX.csv" $TYPE
python3 -m sftools.processthemes "${XLSX}_processed.csv" $WRITEIT