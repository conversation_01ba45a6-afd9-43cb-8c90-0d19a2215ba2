import copy
from datetime import datetime
import unittest
from unittest.mock import patch, MagicMock
import lib.emailapi as emailapi

class TestEmailApi(unittest.TestCase):
    def setUp(self):
        self.email_config = {
            "_id": "email",
            "enabled": True,
            "restricted_sender_domains": [
                "therelationshiprating.com",
                "thereferralrating.com",
                "customer-relationship.com"
            ],
            "restricted_domains": [],
            "templates": {
                "live_survey_participant" : "123",
                "adv:live_survey_participant" : "456",
                "mfv:live_survey_participant" : "789",
                "live_survey_participant:fr" : "123fr",
                "adv:live_survey_participant:fr" : "456fr",
                "mfv:live_survey_participant:fr" : "789fr",
            }
        }
        self.expected_message_attributes = {
            'fromEmail': {
                'DataType': 'String',
                'StringValue': '<EMAIL>',
            },
            'toEmail': {
                'DataType': 'String',
                'StringValue': '<EMAIL>'
            },
            'templateId': {
                'DataType': 'String',
                'StringValue': None,
            },
            'templateData': {
                'DataType': 'String',
                'StringValue': '{"key": "value"}',
            },
            'customArgs': {
                'DataType': 'String',
                'StringValue': '{}',
            },
            'fromName': {
                'DataType': 'String',
                'StringValue': 'Joe Bloggs',
            },
        }
        self.maxDiff = None


    @patch('lib.emailapi.settings')
    def test_send_email_sendgrid(self, mock_settings):
        db_mock = MagicMock()
        sqs_mock = MagicMock()
        # Mock the config
        db_mock.config.find_one.return_value = self.email_config
        mock_settings.send_email_to_sg_queue_url = 'sg-queue-url'

        email_service = emailapi.EmailService(db=db_mock, sqs=sqs_mock)
        # Call the send method
        result = email_service.send(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            template="live_survey_participant",
            data={"key": "value"},
            metadata={},
            send_at=None,
            from_name='Joe Bloggs',
            language=None,
            vertical=None,
            banner_attachment=None,
            signature_attachment=None,
            portaldb_record_map=None,
            service_provider=None,
            ses_delay_offset=0
        )
        # Check that the result is as expected
        self.assertTrue(result['success'])
        sqs_mock.send_message.assert_called_once()
        expected_result = copy.deepcopy(self.expected_message_attributes)
        expected_result['templateId']['StringValue'] = '123'
        call_kwargs = sqs_mock.send_message.call_args.kwargs
        self.assertEqual(call_kwargs['queue'], 'sg-queue-url')
        self.assertEqual(call_kwargs['message_body'], 'Email template 123 to <EMAIL>')
        self.assertDictEqual(call_kwargs['message_attributes'], expected_result)
        self.assertEqual(call_kwargs['delay_seconds'], 0)


    @patch('lib.emailapi.settings')
    def test_send_email_sendgrid_french_mfv(self, mock_settings):
        db_mock = MagicMock()
        sqs_mock = MagicMock()
        # Mock the config
        db_mock.config.find_one.return_value = self.email_config
        mock_settings.send_email_to_sg_queue_url = 'sg-queue-url'

        email_service = emailapi.EmailService(db=db_mock, sqs=sqs_mock)
        # Call the send method
        result = email_service.send(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            template="live_survey_participant",
            data={"key": "value"},
            metadata={},
            send_at=datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None),
            from_name='Joe Bloggs',
            language='fr',
            vertical='mfv',
            banner_attachment=None,
            signature_attachment=None,
            portaldb_record_map='id-abcd1234',
            service_provider=None,
            ses_delay_offset=0
        )
        # Check that the result is as expected
        self.assertTrue(result['success'])
        sqs_mock.send_message.assert_called_once()
        expected_result = copy.deepcopy(self.expected_message_attributes)
        expected_result['templateId']['StringValue'] = '789fr'
        expected_result['sendAt'] = {'DataType': 'String', 'StringValue': '2025-03-18 12:14:00'}
        expected_result['portaldbRecordMap'] = {'DataType': 'String', 'StringValue': 'id-abcd1234'}

        call_kwargs = sqs_mock.send_message.call_args.kwargs
        self.assertEqual(call_kwargs['queue'], 'sg-queue-url')
        self.assertEqual(call_kwargs['message_body'], 'Email template 789<NAME_EMAIL>')
        self.assertDictEqual(call_kwargs['message_attributes'], expected_result)
        self.assertEqual(call_kwargs['delay_seconds'], 0)


    @patch('lib.emailapi.settings')
    def test_send_email_ses(self, mock_settings):
        db_mock = MagicMock()
        sqs_mock = MagicMock()
        # Mock the config
        db_mock.config.find_one.return_value = self.email_config
        mock_settings.send_email_to_sg_queue_url = 'sg-queue-url'
        mock_settings.send_email_to_ses_queue_url = 'ses-queue-url'

        email_service = emailapi.EmailService(db=db_mock, sqs=sqs_mock)
        # Call the send method
        result = email_service.send(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            template="live_survey_participant",
            data={"key": "value"},
            metadata={},
            send_at=None,
            from_name='Joe Bloggs',
            language=None,
            vertical=None,
            banner_attachment=None,
            signature_attachment=None,
            portaldb_record_map=None,
            service_provider='ses',
            ses_delay_offset=0
        )
        # Check that the result is as expected
        self.assertTrue(result['success'])
        sqs_mock.send_message.assert_called_once()
        expected_result = copy.deepcopy(self.expected_message_attributes)
        expected_result['templateId']['StringValue'] = '123'
        call_kwargs = sqs_mock.send_message.call_args.kwargs
        self.assertEqual(call_kwargs['queue'], 'ses-queue-url')
        self.assertEqual(call_kwargs['message_body'], 'Email template 123 to <EMAIL>')
        self.assertDictEqual(call_kwargs['message_attributes'], expected_result)
        self.assertEqual(call_kwargs['delay_seconds'], 0)


    @patch('lib.emailapi.settings')
    def test_ses_send_delays(self, mock_settings):
        # Test various combinations of send at and ses_delay_offset
        db_mock = MagicMock()
        sqs_mock = MagicMock()
        # Mock the config
        db_mock.config.find_one.return_value = self.email_config
        mock_settings.send_email_to_sg_queue_url = 'sg-queue-url'
        mock_settings.send_email_to_ses_queue_url = 'ses-queue-url'

        email_service = emailapi.EmailService(db=db_mock, sqs=sqs_mock)
        mock_now = MagicMock()
        emailapi.EmailService.get_now = mock_now
        scenarios = {
            "send_now": {
                "send_at": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 0,
                "expected_delay_seconds": 0,
            },
            "send_at_in_future": {
                "send_at": datetime(2025, 3, 18, 12, 1, 1).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 0,
                "expected_delay_seconds": 61,
            },
            "send_at_in_past": {
                "send_at": datetime(2025, 3, 18, 11, 1, 1).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 0,
                "expected_delay_seconds": 0,
            },
            "send_in_14_mins": {
                "send_at": datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 0,
                "expected_delay_seconds": 840,
            },
            "send_in_16_mins": {
                "send_at": datetime(2025, 3, 18, 12, 16, 0).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 0,
                "expected_delay_seconds": 900,
            },
            "send_now_with_offset": {
                "send_at": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 13,
                "expected_delay_seconds": 13,
            },
            "send_at_in_future_with_offset": {
                "send_at": datetime(2025, 3, 18, 12, 1, 0).replace(tzinfo=None),
                "now": datetime(2025, 3, 18, 12, 0, 0).replace(tzinfo=None),
                "ses_delay_offset": 70,
                "expected_delay_seconds": 10,
            },
        }
        for scenario, test_config in scenarios.items():
            sqs_mock.reset_mock()
            mock_now.return_value = test_config['now']
            # Call the send method
            result = email_service.send(
                sender="<EMAIL>",
                recipient="<EMAIL>",
                template="live_survey_participant",
                data={"key": "value"},
                metadata={},
                send_at=test_config['send_at'],
                from_name='Joe Bloggs',
                language=None,
                vertical=None,
                banner_attachment=None,
                signature_attachment=None,
                portaldb_record_map=None,
                service_provider='ses',
                ses_delay_offset=test_config['ses_delay_offset']
            )
            # Check that the result is as expected
            self.assertTrue(result['success'])
            sqs_mock.send_message.assert_called_once()
            expected_result = copy.deepcopy(self.expected_message_attributes)
            expected_result['templateId']['StringValue'] = '123'
            expected_result['sendAt'] = {'DataType': 'String', 'StringValue': test_config['send_at'].strftime('%Y-%m-%d %H:%M:%S')}

            call_kwargs = sqs_mock.send_message.call_args.kwargs
            self.assertEqual(call_kwargs['queue'], 'ses-queue-url', msg=f'Scenario: {scenario}')
            self.assertEqual(call_kwargs['message_body'], 'Email template 123 to <EMAIL>', msg=f'Scenario: {scenario}')
            self.assertDictEqual(call_kwargs['message_attributes'], expected_result, msg=f'Scenario: {scenario}')
            self.assertEqual(call_kwargs['delay_seconds'], test_config['expected_delay_seconds'], msg=f'Scenario: {scenario}')
