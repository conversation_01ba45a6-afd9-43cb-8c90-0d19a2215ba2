import pulumi
import pulumi_aws as aws
import ipaddress
import copy


config = pulumi.Config()
aws_account_id = config.require('aws_account_id')
aws_region = config.require('aws_region')
stack = pulumi.get_stack()


def deploy_sqs_queue(sqs_queue_name: str,
                     sqs_dead_queue: aws.sqs.Queue | None = None,
                     sqs_max_receive_count: int = 3):
    sqs_queue_arn = f"arn:aws:sqs:{aws_region}:{aws_account_id}:{sqs_queue_name}"
    sqs_queue_policy = {
        "Statement": [
        {
            "Action": "SQS:*",
            "Effect": "Allow",
            "Principal": {
                "AWS": f"arn:aws:iam::{aws_account_id}:root"
            },
            "Resource": sqs_queue_arn,
        }
        ],
        "Version": "2012-10-17"
    }

    if sqs_dead_queue is not None:
        sqs_redrive_policy = {
            "deadLetterTargetArn": sqs_dead_queue.arn,
            "maxReceiveCount": sqs_max_receive_count
        }
    else:
        sqs_redrive_policy = None

    sqs_queue = aws.sqs.Queue(sqs_queue_name,
        visibility_timeout_seconds=2*10*60,
        kms_data_key_reuse_period_seconds=300,
        name=sqs_queue_name,
        policy=pulumi.Output.json_dumps(sqs_queue_policy),
        redrive_policy=pulumi.Output.json_dumps(sqs_redrive_policy) if sqs_redrive_policy is not None else None,
        sqs_managed_sse_enabled=True)

    return sqs_queue


def deploy_sqs_sns_queue(sqs_queue_name: str,
                        sqs_dead_queue: aws.sqs.Queue | None = None,
                        sqs_max_receive_count: int = 3):
    # setup the queues allowing send messages from SNS
    sqs_queue_arn = f"arn:aws:sqs:{aws_region}:{aws_account_id}:{sqs_queue_name}"
    sqs_queue_policy = {
        "Statement": [
            {
                "Action": "SQS:*",
                "Effect": "Allow",
                "Principal": {
                    "AWS": f"arn:aws:iam::{aws_account_id}:root"
                },
                "Resource": sqs_queue_arn,
            },
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "sns.amazonaws.com"
                },
                "Action": "SQS:SendMessage",
                "Resource": sqs_queue_arn,
                "Condition": {
                    "ArnEquals": {
                        "aws:SourceArn": config.require('ses_sns_arn')
                    }
                }
            }
        ],
        "Version": "2012-10-17"
    }

    if sqs_dead_queue is not None:
        sqs_redrive_policy = {
            "deadLetterTargetArn": sqs_dead_queue.arn,
            "maxReceiveCount": sqs_max_receive_count
        }
    else:
        sqs_redrive_policy = None

    sqs_queue = aws.sqs.Queue(sqs_queue_name,
        visibility_timeout_seconds=2*10*60,
        kms_data_key_reuse_period_seconds=300,
        name=sqs_queue_name,
        policy=pulumi.Output.json_dumps(sqs_queue_policy),
        redrive_policy=pulumi.Output.json_dumps(sqs_redrive_policy) if sqs_redrive_policy is not None else None,
        sqs_managed_sse_enabled=True)

    return sqs_queue


def deploy_function(lambda_function_name: str,
                    lambda_function_entry_point: str,
                    lambda_security_group_ids,
                    lambda_subnet_ids,
                    ecr_repo_url: pulumi.Output[str],
                    ecr_image_hash: pulumi.Output[str],
                    secrets_arn: pulumi.Output[str],
                    secrets_name: pulumi.Output[str],
                    lambda_function_timeout_secs: int = 15*60,
                    lambda_function_storage_gb: int = 512,
                    lambda_function_memory_size_mb: int = 128,
                    policy_extra: list = None,
                    env_extra: dict = None,
                    lambda_function_log_retention_days: int = 7,
                    async_max_retry_attempts: int = 2,
                    async_max_event_age_seconds: int = 6 * 60 * 60):

    # setup the lambda execution policy
    lambda_assume_role_policy = {
        "Statement": [
            {
                "Action": "sts:AssumeRole",
                "Effect": "Allow",
                "Principal": {
                    "Service": "lambda.amazonaws.com"
                }
            }
        ],
        "Version": "2012-10-17"
    }

    lambda_policy = {
        "Statement": [
            {
                "Action": [
                    "logs:CreateLogStream",
                    "logs:PutLogEvents"
                ],
                "Effect": "Allow",
                "Resource": f"arn:aws:logs:{aws_region}:{aws_account_id}:log-group:/aws/lambda/{lambda_function_name}:*",
            },
            {
                "Action": [
                    "logs:CreateLogGroup"
                ],
                "Effect": "Allow",
                "Resource": [
                    f"arn:aws:logs:{aws_region}:{aws_account_id}:*"
                ],
            },
            {
                "Action": [
                    "secretsmanager:GetSecretValue",
                ],
                "Effect": "Allow",
                "Resource": [
                    secrets_arn
                ],
            },
            {
                "Effect": "Allow",
                "Action": [
                    "ec2:DescribeNetworkInterfaces",
                    "ec2:CreateNetworkInterface",
                    "ec2:DeleteNetworkInterface",
                    "ec2:DescribeInstances",
                    "ec2:AttachNetworkInterface"
                ],
                "Resource": "*"
            },
            {
                "Effect": "Allow",
                "Action": [
                    "cognito-idp:ListIdentityProviders",
                    "cognito-idp:DescribeIdentityProvider"
                ],
                "Resource": f"arn:aws:cognito-idp:{aws_region}:{aws_account_id}:userpool/*"
            },
        ],
        "Version": "2012-10-17"
    }
    if policy_extra:
        lambda_policy["Statement"].extend(policy_extra)

    lambda_environment_variables = {
        "STACK": pulumi.get_stack(),
        "AWS_SECRETS_NAME": secrets_name,
    }
    if env_extra:
        lambda_environment_variables.update(env_extra)

    name = f"{lambda_function_name}-role"
    iam_lambda_role = aws.iam.Role(name,
        assume_role_policy=pulumi.Output.json_dumps(lambda_assume_role_policy),
        inline_policies=[aws.iam.RoleInlinePolicyArgs(
            name=f"{lambda_function_name}-policy",
            policy=pulumi.Output.json_dumps(lambda_policy)),
        ],
        name=name,
        path="/service-role/")

    log_group = aws.cloudwatch.LogGroup(f"{lambda_function_name}-log-group",
                                        name=f"/aws/lambda/{lambda_function_name}",
                                        retention_in_days=lambda_function_log_retention_days)

    lambda_function = aws.lambda_.Function(lambda_function_name,
        architectures=["arm64"],
        ephemeral_storage=aws.lambda_.FunctionEphemeralStorageArgs(
            size=lambda_function_storage_gb,
        ),
        timeout=lambda_function_timeout_secs,
        memory_size=lambda_function_memory_size_mb,
        image_config=aws.lambda_.FunctionImageConfigArgs(
            commands=[lambda_function_entry_point],
        ),
        image_uri=pulumi.Output.format("{ecr_repo_url}@{ecr_image_hash}",
                                       ecr_repo_url=ecr_repo_url,
                                       ecr_image_hash=ecr_image_hash),
        logging_config=aws.lambda_.FunctionLoggingConfigArgs(
            log_format="JSON",
            log_group=f"/aws/lambda/{lambda_function_name}",
        ),
        environment=aws.lambda_.FunctionEnvironmentArgs(
            variables=lambda_environment_variables,
        ),
        vpc_config=aws.lambda_.FunctionVpcConfigArgs(
            security_group_ids=lambda_security_group_ids,
            subnet_ids=lambda_subnet_ids,
        ),
        name=lambda_function_name,
        package_type="Image",
        role=iam_lambda_role.arn,
        tracing_config=aws.lambda_.FunctionTracingConfigArgs(
            mode="PassThrough",
        ))

    aws.lambda_.FunctionEventInvokeConfig(f"{lambda_function_name}-invoke-config",
        function_name=lambda_function.name,
        maximum_retry_attempts=async_max_retry_attempts,
        maximum_event_age_in_seconds=async_max_event_age_seconds)

    return iam_lambda_role, lambda_function


def deploy_sqs_function(lambda_function_name: str,
                        lambda_function_entry_point: str,
                        lambda_security_group_ids,
                        lambda_subnet_ids,
                        ecr_repo_url: pulumi.Output[str],
                        ecr_image_hash: pulumi.Output[str],
                        secrets_arn: pulumi.Output[str],
                        secrets_name: pulumi.Output[str],
                        lambda_function_timeout_secs: int = 10*60,
                        lambda_function_storage_gb: int = 512,
                        lambda_function_memory_size_mb: int = 128,
                        lambda_policy_extra: list = None):

    # setup the queues
    sqs_queue_name = f'{lambda_function_name}-queue'
    sqs_dead_queue = deploy_sqs_queue(f"{sqs_queue_name}-dead")
    sqs_queue = deploy_sqs_queue(sqs_queue_name, sqs_dead_queue)

    policy_extra = [
        {
            "Action": [
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:GetQueueAttributes",
            ],
            "Effect": "Allow",
            "Resource": [
                sqs_queue.arn
            ],
        },
        {
            "Action": [
                "sqs:SendMessage",
                "sqs:GetQueueAttributes",
            ],
            "Effect": "Allow",
            "Resource": [
                sqs_dead_queue.arn
            ],
        },
    ]
    if lambda_policy_extra:
        policy_extra.extend(lambda_policy_extra)
    env_extra = {
        "DEAD_QUEUE_NAME": sqs_dead_queue.name,
    }

    iam_lambda_role, lambda_function = deploy_function(lambda_function_name,
                                                       lambda_function_entry_point,
                                                       lambda_security_group_ids,
                                                       lambda_subnet_ids,
                                                       ecr_repo_url,
                                                       ecr_image_hash,
                                                       secrets_arn,
                                                       secrets_name,
                                                       lambda_function_timeout_secs=lambda_function_timeout_secs,
                                                       lambda_function_storage_gb=lambda_function_storage_gb,
                                                       lambda_function_memory_size_mb=lambda_function_memory_size_mb,
                                                       policy_extra=policy_extra,
                                                       env_extra=env_extra)

    sqs_event_source = aws.lambda_.EventSourceMapping(f"{lambda_function_name}-sqs-event-source",
        batch_size=10,
        enabled=True,
        event_source_arn=sqs_queue.arn,
        function_name=lambda_function.arn)

    return iam_lambda_role, lambda_function, sqs_event_source, sqs_queue, sqs_dead_queue


def deploy_sqs_sns_function(lambda_function_name: str,
                            lambda_function_entry_point: str,
                            lambda_security_group_ids,
                            lambda_subnet_ids,
                            ecr_repo_url: pulumi.Output[str],
                            ecr_image_hash: pulumi.Output[str],
                            secrets_arn: pulumi.Output[str],
                            secrets_name: pulumi.Output[str],
                            lambda_function_timeout_secs: int = 10*60,
                            lambda_function_storage_gb: int = 512,
                            lambda_function_memory_size_mb: int = 128,
                            lambda_policy_extra: list = None):

    # setup the queues allowing send messages from SNS
    sqs_queue_name = f'{lambda_function_name}-queue'
    sqs_dead_queue = deploy_sqs_queue(f"{sqs_queue_name}-dead")
    sqs_queue = deploy_sqs_sns_queue(sqs_queue_name, sqs_dead_queue)

    policy_extra = [
        {
            "Action": [
                "sqs:ReceiveMessage",
                "sqs:DeleteMessage",
                "sqs:GetQueueAttributes",
            ],
            "Effect": "Allow",
            "Resource": [
                sqs_queue.arn
            ],
        },
        {
            "Action": [
                "sqs:SendMessage",
                "sqs:GetQueueAttributes",
            ],
            "Effect": "Allow",
            "Resource": [
                sqs_dead_queue.arn
            ],
        },
    ]
    if lambda_policy_extra:
        policy_extra.extend(lambda_policy_extra)
    env_extra = {
        "DEAD_QUEUE_NAME": sqs_dead_queue.name,
    }

    iam_lambda_role, lambda_function = deploy_function(lambda_function_name,
                                                       lambda_function_entry_point,
                                                       lambda_security_group_ids,
                                                       lambda_subnet_ids,
                                                       ecr_repo_url,
                                                       ecr_image_hash,
                                                       secrets_arn,
                                                       secrets_name,
                                                       lambda_function_timeout_secs=lambda_function_timeout_secs,
                                                       lambda_function_storage_gb=lambda_function_storage_gb,
                                                       lambda_function_memory_size_mb=lambda_function_memory_size_mb,
                                                       policy_extra=policy_extra,
                                                       env_extra=env_extra)

    sqs_event_source = aws.lambda_.EventSourceMapping(f"{lambda_function_name}-sqs-event-source",
        batch_size=10,
        enabled=True,
        event_source_arn=sqs_queue.arn,
        function_name=lambda_function.arn)

    return iam_lambda_role, lambda_function, sqs_event_source, sqs_queue, sqs_dead_queue


def apigateway_resource(base_name: str,
                        parent_id: str,
                        path_part: str,
                        rest_api: aws.apigateway.RestApi):
    return aws.apigateway.Resource(f"{base_name}-api-resource-{path_part}",
        parent_id=parent_id,
        path_part=path_part,
        rest_api=rest_api.id)


def apigateway_lambda_method(lambda_function_name: str,
                             path: str,
                             http_method_name: str,
                             resource: aws.apigateway.Resource,
                             rest_api: aws.apigateway.RestApi,
                             gateway_timeout_milliseconds: int=29000):
    base_name = f"{lambda_function_name}-{path}"
    method = aws.apigateway.Method(f"{base_name}-api-method-{http_method_name}",
        authorization="NONE",
        http_method=http_method_name,
        request_parameters={
            "method.request.path.proxy": True,
        },
        resource_id=resource.id,
        rest_api=rest_api.id)

    integration = aws.apigateway.Integration(f"{base_name}-api-integration-{http_method_name}",
        cache_namespace=resource.id,
        connection_type="INTERNET",
        content_handling="CONVERT_TO_TEXT",
        http_method=http_method_name,
        integration_http_method="POST",
        passthrough_behavior="WHEN_NO_MATCH",
        request_parameters={
            "integration.request.path.proxy": "method.request.path.proxy",
        },
        resource_id=resource.id,
        rest_api=rest_api.id,
        timeout_milliseconds=gateway_timeout_milliseconds,
        type="AWS_PROXY",
        uri=f"arn:aws:apigateway:{aws_region}:lambda:path/2015-03-31/functions/arn:aws:lambda:{aws_region}:{aws_account_id}:function:{lambda_function_name}/invocations")

    return method, integration


def deploy_apigateway_function(lambda_function_name: str,
                               lambda_function_entry_point: str,
                               lambda_security_group_ids,
                               lambda_subnet_ids,
                               ecr_repo_url: pulumi.Output[str],
                               ecr_image_hash: pulumi.Output[str],
                               secrets_arn: pulumi.Output[str],
                               secrets_name: pulumi.Output[str],
                               lambda_function_timeout_secs: int = 10*60,
                               lambda_function_storage_gb: int = 512,
                               lambda_function_memory_size_mb: int = 128,
                               lambda_policy_extra: list | None = None,
                               gateway_timeout_milliseconds: int = 29000) -> tuple[aws.apigateway.RestApi, aws.apigateway.Stage]:


    iam_lambda_role, lambda_function = deploy_function(lambda_function_name,
                                                       lambda_function_entry_point,
                                                       lambda_security_group_ids,
                                                       lambda_subnet_ids,
                                                       ecr_repo_url,
                                                       ecr_image_hash,
                                                       secrets_arn,
                                                       secrets_name,
                                                       lambda_function_timeout_secs,
                                                       lambda_function_storage_gb,
                                                       lambda_function_memory_size_mb,
                                                       policy_extra=lambda_policy_extra)

    rest_api_gateway = aws.apigateway.RestApi(f"{lambda_function_name}-api",
        api_key_source="HEADER",
        endpoint_configuration=aws.apigateway.RestApiEndpointConfigurationArgs(
            types="REGIONAL",
        ),
        name=lambda_function_name,
        put_rest_api_mode="overwrite")

    proxy_resource = apigateway_resource(lambda_function_name, rest_api_gateway.root_resource_id, "{proxy+}", rest_api_gateway)
    method, integration = apigateway_lambda_method(lambda_function_name, "proxy", "ANY", proxy_resource, rest_api_gateway,
                                                   gateway_timeout_milliseconds=gateway_timeout_milliseconds)

    aws.lambda_.Permission(f"{lambda_function_name}-apigateway-permission",
        action="lambda:InvokeFunction",
        function=lambda_function.arn,
        principal="apigateway.amazonaws.com",
        source_arn=pulumi.Output.concat(rest_api_gateway.execution_arn, '/*/*'))

    rest_api_gateway_deployment = aws.apigateway.Deployment(f"{lambda_function_name}-api-deployment",
                                                            rest_api=rest_api_gateway.id,
                                                            description=stack,
                                                            opts=pulumi.ResourceOptions(depends_on=[proxy_resource, method, integration]))
    rest_api_gateway_stage = aws.apigateway.Stage(f"{lambda_function_name}-api-stage-{stack}",
        deployment=rest_api_gateway_deployment.id,
        rest_api=rest_api_gateway.id,
        stage_name=stack)

    return rest_api_gateway, rest_api_gateway_stage


def apigateway_cloudwatch_setup():
    apigateway_assume_role_policy = {
        "Statement": [
            {
                "Action": "sts:AssumeRole",
                "Effect": "Allow",
                "Principal": {
                    "Service": "apigateway.amazonaws.com"
                }
            }
        ],
        "Version": "2012-10-17"
    }

    name = f"apigateway-cloudwatch-role-{aws_region}"
    cloudwatch_role = aws.iam.Role(name,
        assume_role_policy=pulumi.Output.json_dumps(apigateway_assume_role_policy),
        managed_policy_arns=['arn:aws:iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs'],
        name=name)
    apigateway_account = aws.apigateway.Account("apigateway-account-{aws_region}", cloudwatch_role_arn=cloudwatch_role.arn)


def deploy_s3_bucket(bucket_name, bucket_policy=None, logging_bucket=None, logging_bucket_prefix=""):
    bucket = aws.s3.Bucket(f"s3-bucket-{bucket_name}",
        bucket=bucket_name,
        server_side_encryption_configuration={
            "rule": {
                "apply_server_side_encryption_by_default": {
                    "sse_algorithm": "AES256",
                },
                "bucket_key_enabled": True,
            },
        })

    if bucket_policy:
        aws.s3.BucketPolicy(f"s3-bucket-{bucket_name}-s3-bucket-policy",
            bucket=bucket.bucket,
            policy=pulumi.Output.json_dumps(bucket_policy))

    if logging_bucket:
        aws.s3.BucketLoggingV2(f"s3-bucket-logging-{bucket_name}",
            bucket=bucket_name,
            target_bucket=logging_bucket.id,
            target_prefix=logging_bucket_prefix,
            target_object_key_format={
                "simple_prefix": {},
            })

    return bucket


def deploy_secrets(name, secrets_dict: dict[str, str]):
    aws_secret = aws.secretsmanager.Secret(f"{name}-secrets-{aws_region}-{stack}",
                                           name=f"{name}-secrets-{aws_region}-{stack}")
    aws_secret_version = aws.secretsmanager.SecretVersion(f"{name}-secrets-version-{aws_region}-{stack}",
                                                          secret_id=aws_secret.id,
                                                          secret_string=pulumi.Output.json_dumps(secrets_dict))

    return aws_secret, aws_secret_version


def deploy_ecr_repo(name):
    ecr_repo = aws.ecr.Repository(f"{name}-{aws_region}-{stack}",
        encryption_configurations=[aws.ecr.RepositoryEncryptionConfigurationArgs(
            encryption_type="AES256",
            kms_key="",
        )],
        image_scanning_configuration=aws.ecr.RepositoryImageScanningConfigurationArgs(
            scan_on_push=False,
        ),
        image_tag_mutability="MUTABLE",
        name=f"{name}-{aws_region}-{stack}")

    policy_rules = {
        "rules": [
            {
                "action": {
                    "type": "expire"
                },
                "rulePriority": 1,
                "selection": {
                    "countNumber": 10,
                    "countType": "imageCountMoreThan",
                    "tagStatus": "untagged"
                }
            }
        ]
    }

    policy = aws.ecr.LifecyclePolicy(f"{name}-{aws_region}-{stack}-lifecycle",
        policy=policy_rules,
        repository=ecr_repo.name)

    return ecr_repo


def deploy_documentdb(basename,
                      docdb_username,
                      docdb_password,
                      subnet_group_name,
                      docdb_security_group_ids,
                      docdb_instance_availability_zones,
                      docdb_instance_type):
    docdb_cluster = aws.docdb.Cluster(f"{basename}-docdb-cluster-{aws_region}-{stack}",
        cluster_identifier=f"{basename}-docdb-cluster-{aws_region}-{stack}",
        db_cluster_parameter_group_name="default.docdb5.0",
        db_subnet_group_name=subnet_group_name,
        deletion_protection=False,
        engine_version="5.0.0",
        master_username=docdb_username,
        master_password=docdb_password,
        preferred_backup_window="00:00-00:30",
        preferred_maintenance_window="sat:02:34-sat:03:04",
        skip_final_snapshot=True,
        storage_encrypted=True,
        vpc_security_group_ids=docdb_security_group_ids)

    for az in [f"{aws_region}{az}" for az in docdb_instance_availability_zones]:
        docdb_instance = aws.docdb.ClusterInstance(f"{basename}-docdb-{az}-{stack}",
            availability_zone=f"{az}",
            ca_cert_identifier="rds-ca-rsa2048-g1",
            cluster_identifier=docdb_cluster.cluster_identifier,
            identifier=f"{basename}-docdb-{az}-{stack}",
            instance_class=docdb_instance_type,
            preferred_maintenance_window="wed:01:14-wed:01:44",
            promotion_tier=1)

    return docdb_cluster


def eventbridge_schedule(lambda_function_name, schedule_expression, schedule_state):
    eventbridge_assume_role_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "scheduler.amazonaws.com"
                },
                "Action": "sts:AssumeRole",
                "Condition": {
                    "StringEquals": {
                        "aws:SourceAccount": aws_account_id
                    }
                }
            }
        ]
    }

    eventbridge_policy = {
        "Statement": [
            {
                "Action": "lambda:InvokeFunction",
                "Effect": "Allow",
                "Resource": [
                    f"arn:aws:lambda:{aws_region}:{aws_account_id}:function:{lambda_function_name}:*",
                    f"arn:aws:lambda:{aws_region}:{aws_account_id}:function:{lambda_function_name}"
                ]
            }
        ],
        "Version": "2012-10-17"
    }
    iam_eventbridge_role = aws.iam.Role(f"{lambda_function_name}-ebr",
        assume_role_policy=pulumi.Output.json_dumps(eventbridge_assume_role_policy),
        inline_policies=[aws.iam.RoleInlinePolicyArgs(
            name=f"{lambda_function_name}-eventbridge-policy",
            policy=pulumi.Output.json_dumps(eventbridge_policy)),
        ],
        name=f"{lambda_function_name}-ebr",
        path="/service-role/")

    eventbridge_lambda_schedule = aws.scheduler.Schedule(f"{lambda_function_name}-schedule",
                                            flexible_time_window={
                                                "mode": "OFF",
                                            },
                                            group_name="default",
                                            name=lambda_function_name,
                                            schedule_expression=schedule_expression,
                                            schedule_expression_timezone="Europe/London",
                                            state=schedule_state,
                                            target={
                                                "arn": f"arn:aws:lambda:{aws_region}:{aws_account_id}:function:{lambda_function_name}",
                                                "role_arn": iam_eventbridge_role.arn
                                            })

    return iam_eventbridge_role, eventbridge_lambda_schedule


def create_standard_vpc(basename: str,
                        vpc_cidr: str,
                        vpc_azs: list[str] | None = None,
                        subnet_prefixlen: int = 20):
    if vpc_azs is None:
        vpc_azs = ['a', 'b', 'c']

    vpc_cidr = ipaddress.ip_network(vpc_cidr)
    all_possible_subnets = list(vpc_cidr.subnets(subnet_prefixlen - vpc_cidr.prefixlen))

    # create the VPC
    vpc = aws.ec2.Vpc(f"{basename}-vpc-{aws_region}-{stack}",
        cidr_block=str(vpc_cidr),
        enable_dns_hostnames=True,
        instance_tenancy="default",
        tags={
            "Name": f"{basename}-vpc-{aws_region}-{stack}",
        })

    # create the public subnets
    public_subnets = {}
    for az in vpc_azs:
        public_subnets[az] = aws.ec2.Subnet(f"{basename}-public-{aws_region}{az}-{stack}",
            availability_zone=f"{aws_region}{az}",
            cidr_block=str(all_possible_subnets.pop(0)),
            map_public_ip_on_launch=True,
            private_dns_hostname_type_on_launch="ip-name",
            tags={
                "Name": f"{basename}-public-{aws_region}{az}-{stack}"
            },
            vpc_id=vpc.id)

    # create the private subnets
    private_subnets = {}
    for az in vpc_azs:
        private_subnets[az] = aws.ec2.Subnet(f"{basename}-private-{aws_region}{az}-{stack}",
            availability_zone=f"{aws_region}{az}",
            cidr_block=str(all_possible_subnets.pop(0)),
            private_dns_hostname_type_on_launch="ip-name",
            tags={
                "Name": f"{basename}-private-{aws_region}{az}-{stack}"
            },
            vpc_id=vpc.id)

    # create an elastic ip for the natgw
    natgw_eip = aws.ec2.Eip(f"{basename}-natgw-eip-{aws_region}-{stack}",
        domain="vpc",
        network_border_group=aws_region,
        public_ipv4_pool="amazon")

    # create the gateways
    igw = aws.ec2.InternetGateway(f"{basename}-igw-{aws_region}-{stack}",
        tags={
            "Name": f"{basename}-igw-{aws_region}-{stack}",
        },
        vpc_id=vpc.id)
    natgw = aws.ec2.NatGateway(f"{basename}-natgw-{aws_region}-{stack}",
        subnet_id=public_subnets['a'].id,
        allocation_id=natgw_eip.allocation_id,
        tags={
            "Name": f"{basename}-natgw-{aws_region}-{stack}",
        })

    # we reuse the default route table for the public subnets
    public_rtl = aws.ec2.DefaultRouteTable(f"{basename}-public-rtl-{aws_region}-{stack}",
                                            default_route_table_id=vpc.default_route_table_id,
                                            routes=[{
                                                "cidr_block": "0.0.0.0/0",
                                                "gateway_id": igw.id,
                                            }],
                                            tags={
                                                "Name": f"{basename}-public-rtl-{aws_region}-{stack}",
                                            })

    # create a new route table for the private subnets
    private_rtl = aws.ec2.RouteTable(f"{basename}-private-rtl-{aws_region}-{stack}",
        routes=[{
            "cidr_block": "0.0.0.0/0",
            "nat_gateway_id": natgw.id,
        }],
        tags={
            "Name": f"{basename}-private-rtl-{aws_region}-{stack}",
        },
        vpc_id=vpc.id)

    # associate the private subnets with the private route table
    # (the public ones will use the default route table)
    for az, subnet in private_subnets.items():
        assoc = aws.ec2.RouteTableAssociation(f"{basename}-private-rtl-assoc-{aws_region}{az}-{stack}",
                                              route_table_id=private_rtl.id,
                                              subnet_id=subnet.id)

    # setup default sg
    default_sg =aws.ec2.DefaultSecurityGroup(f"{basename}-default-sg-{aws_region}-{stack}",
                                             tags={"Name": f"{basename}-default-sg-{aws_region}-{stack}"},
                                             vpc_id=vpc.id)
    aws.vpc.SecurityGroupEgressRule(f"default-sg-egress-rule-0-{aws_region}-{stack}", security_group_id=default_sg.id, ip_protocol="-1", cidr_ipv4="0.0.0.0/0")

    admin_sg = create_security_group(f"{basename}-admin", vpc.id,
                                     [dict(ip_protocol="-1", referenced_security_group_id="SELF")])
    publicssh_sg = create_security_group(f"{basename}-publicssh", vpc.id,
                                         [dict(from_port=22, to_port=22, ip_protocol="tcp", cidr_ipv4="0.0.0.0/0")])

    return vpc, public_subnets, private_subnets, admin_sg, publicssh_sg, default_sg


def create_security_group(basename: str, vpc_id: str, ingress_rules: list[dict], egress_rules:list[dict]|None=None):
    if egress_rules is None:
        egress_rules=[
            dict(ip_protocol="-1",
                 cidr_ipv4="0.0.0.0/0")
        ]

    fullname = f"{basename}-sg-{aws_region}-{stack}"
    sg = aws.ec2.SecurityGroup(fullname,
        description=fullname,
        name=fullname,
        tags={
            "Name": fullname,
        },
        vpc_id=vpc_id)

    for idx, rule in enumerate(ingress_rules):
        if rule.get('referenced_security_group_id') == 'SELF':
            rule = copy.copy(rule)
            rule['referenced_security_group_id'] = sg.id
        aws.vpc.SecurityGroupIngressRule(f"{basename}-ingress-rule-{idx}-{aws_region}-{stack}", security_group_id=sg.id, **rule)
    for idx, rule in enumerate(egress_rules):
        if rule.get('referenced_security_group_id') == 'SELF':
            rule = copy.copy(rule)
            rule['referenced_security_group_id'] = sg.id
        aws.vpc.SecurityGroupEgressRule(f"{basename}-egress-rule-{idx}-{aws_region}-{stack}", security_group_id=sg.id, **rule)

    return sg


def create_rds_subnet_group(basename: str, subnet_ids: list):
    rds_subnet_group = aws.rds.SubnetGroup(f"{basename}-rds-subnetgroup-{aws_region}-{stack}",
        description=f"{basename}-rds-subnetgroup-{aws_region}-{stack}",
        name=f"{basename}-rds-subnetgroup-{aws_region}-{stack}",
        subnet_ids=subnet_ids)

    return rds_subnet_group


def create_rds_postgres(basename, storage_gb: int, az: str, instance_type: str, subnet_group_name, security_group_ids: list, public: bool = False):
    rdspg = aws.rds.Instance(f"{basename}-rds-pg-{aws_region}{az}-{stack}",
        allocated_storage=storage_gb,
        availability_zone=f"{aws_region}{az}",
        backup_retention_period=7,
        backup_target="region",
        backup_window="00:36-01:06",
        ca_cert_identifier="rds-ca-rsa2048-g1",
        copy_tags_to_snapshot=True,
        db_subnet_group_name=subnet_group_name,
        engine="postgres",
        engine_lifecycle_support="open-source-rds-extended-support-disabled",
        engine_version="16.3",
        identifier=f"{basename}-rds-pg-{aws_region}{az}-{stack}",
        instance_class=instance_type,
        license_model="postgresql-license",
        maintenance_window="fri:22:22-fri:22:52",
        max_allocated_storage=1000,
        network_type="IPV4",
        option_group_name="default:postgres-16",
        parameter_group_name="default.postgres16",
        port=5432,
        publicly_accessible=public,
        skip_final_snapshot=True,
        storage_encrypted=True,
        storage_type=aws.rds.StorageType.GP3,
        username="postgres",
        manage_master_user_password=True,
        vpc_security_group_ids=security_group_ids,
        performance_insights_enabled=True,
        performance_insights_retention_period=7)

    return rdspg


def create_ec2_keypair(name, public_key):
    name = f"{name}-keypair-{aws_region}-{stack}"
    keypair = aws.ec2.KeyPair(name,
                              key_name=name,
                              public_key=public_key)
    return keypair


def create_ec2_ubuntu_instance(basename: str,
                               instance_type: str,
                               keypair_name: str,
                               subnet_id: str,
                               security_group_ids: list[str],
                               associate_public_ip: bool = False,
                               source_dest_check: bool = True,
                               user_data: str = None):

    ubuntu_ami = aws.ec2.get_ami(owners=["amazon"],
                                 most_recent=True,
                                 filters=[{"name":"architecture","values":["arm64"]},
                                          {"name": "name", "values": [f"ubuntu/images/hvm-ssd-gp3/ubuntu-*-24.04-arm64-server-*"]}])

    name = f"{basename}-ec2-{aws_region}-{stack}"
    return aws.ec2.Instance(name,
        ami=ubuntu_ami.id,
        associate_public_ip_address=associate_public_ip,
        disable_api_termination=True,
        ebs_optimized=True,
        instance_initiated_shutdown_behavior="stop",
        instance_type=instance_type,
        key_name=keypair_name,
        user_data=user_data,
        user_data_replace_on_change=True,
        private_dns_name_options={
            "enable_resource_name_dns_a_record": True,
            "hostname_type": "ip-name",
        },
        root_block_device={
            "volume_size": 8,
            "volume_type": "gp2",
        },
        source_dest_check=source_dest_check,
        subnet_id=subnet_id,
        tags={
            "Name": name,
        },
        vpc_security_group_ids=security_group_ids,
        opts=pulumi.ResourceOptions(ignore_changes=["ami"]))


def make_vpn_userdata(vpc_cidr):
    vpc_cidr = ipaddress.ip_network(vpc_cidr)
    assert vpc_cidr.prefixlen == 16
    assert isinstance(vpc_cidr, ipaddress.IPv4Network)
    vpn_subnet = f"10.8.{vpc_cidr.network_address.packed[1]}.x"

    return """
#cloud-config
packages:
- docker.io
- docker-compose-v2
package_update: true
package_upgrade: true
write_files:
- path: /root/docker-compose.yml
  content: |
    version: "3.8"
    services:
        wg-easy:
            env_file:
            - path: /root/wg-easy.env
              required: true
            environment:
            # Change Language:
            # (Supports: en, ua, ru, tr, no, pl, fr, de, ca, es, ko, vi, nl, is, pt, chs, cht, it, th, hi)
            - LANG=en
            # ⚠ Required:
            # Change this to your host's public address
            # - WG_HOST=  (set by environment file above)
            # Optional:
            - PASSWORD=L*ttz0%N^E3lMokqkfnWJ*%PhG7J^2
            # - WG_PORT=51820
            - WG_DEFAULT_ADDRESS={vpn_subnet}
            # - WG_DEFAULT_DNS=*******
            # - WG_MTU=1420
            - WG_ALLOWED_IPS={vpc_cidr}
            - WG_PERSISTENT_KEEPALIVE=25
            # - WG_PRE_UP=echo "Pre Up" > /etc/wireguard/pre-up.txt
            # - WG_POST_UP=echo "Post Up" > /etc/wireguard/post-up.txt
            # - WG_PRE_DOWN=echo "Pre Down" > /etc/wireguard/pre-down.txt
            # - WG_POST_DOWN=echo "Post Down" > /etc/wireguard/post-down.txt
            # - UI_TRAFFIC_STATS=true
            # - UI_CHART_TYPE=0 # (0 Charts disabled, 1 # Line chart, 2 # Area chart, 3 # Bar chart)
            image: ghcr.io/wg-easy/wg-easy
            container_name: wg-easy
            volumes:
            - /etc/wireguard:/etc/wireguard
            ports:
            - "51820:51820/udp"
            - "51821:51821/tcp"
            restart: unless-stopped
            cap_add:
            - NET_ADMIN
            - SYS_MODULE
            sysctls:
            - net.ipv4.ip_forward=1
            - net.ipv4.conf.all.src_valid_mark=1
runcmd:
- echo WG_HOST=`curl -s http://checkip.amazonaws.com` > /root/wg-easy.env
- docker compose -f /root/docker-compose.yml up -d
""".format(vpc_cidr=vpc_cidr, vpn_subnet=vpn_subnet)


def create_vpn_instance(vpc, vpc_cidr, keypair, subnet, admin_sg):
    vpn_userdata = make_vpn_userdata(vpc_cidr)

    wireguard_sg = create_security_group("wireguard",
                                         vpc.id,
                                         [dict(from_port=51820, to_port=51820, ip_protocol="udp", cidr_ipv4="0.0.0.0/0")])

    vpn_instance = create_ec2_ubuntu_instance("vpn", 't4g.nano', keypair.key_name,
                                              subnet.id,
                                              [wireguard_sg.id, admin_sg.id],
                                              associate_public_ip=True,
                                              source_dest_check=False,
                                              user_data=vpn_userdata)

    return vpn_instance, wireguard_sg


def create_cognito_user_pool(basename):
    # create the user pool
    poolname = f"{basename}-cognito-userpool-{aws_region}-{stack}"
    user_pool = aws.cognito.UserPool(poolname,
        account_recovery_setting={
            "recovery_mechanisms": [{
                "name": "verified_email",
                "priority": 1,
            }],
        },
        auto_verified_attributes=["email"],
        deletion_protection="ACTIVE",
        email_configuration={
            "email_sending_account": "COGNITO_DEFAULT",
        },
        mfa_configuration="OFF",
        name=poolname,
        password_policy={
            "minimum_length": 8,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": True,
            "require_uppercase": True,
            "temporary_password_validity_days": 7,
        },
        schemas=[{
            "attribute_data_type": "String",
            "developer_only_attribute": False,
            "mutable": True,
            "name": "email",
            "required": True,
            "string_attribute_constraints": {
                "max_length": "2048",
                "min_length": "0",
            },
        }],
        user_attribute_update_settings={
            "attributes_require_verification_before_updates": ["email"],
        },
        username_attributes=["email"],
        username_configuration={
            "case_sensitive": False,
        },
        verification_message_template={
            "default_email_option": "CONFIRM_WITH_CODE",
            "email_message_by_link": "Please click the link below to verify your email address. {##Verify Email##}",
            "email_subject_by_link": "Your verification link",
        })

    return user_pool


def create_cognito_user_pool_with_lambda_email_handler(basename, email_lambda_arn):
    # create a KMS key for comms with lambda
    kms_key = aws.kms.Key(f"{basename}-cognito-userpool-{aws_region}-{stack}",
                          customer_master_key_spec="SYMMETRIC_DEFAULT",
                          key_usage="ENCRYPT_DECRYPT")

    # create the user pool
    poolname = f"{basename}-cognito-userpool-{aws_region}-{stack}"
    user_pool = aws.cognito.UserPool(poolname,
        account_recovery_setting={
            "recovery_mechanisms": [{
                "name": "verified_email",
                "priority": 1,
            }],
        },
        auto_verified_attributes=["email"],
        deletion_protection="ACTIVE",
        email_configuration={
            "email_sending_account": "COGNITO_DEFAULT",
        },
        lambda_config={
            "custom_email_sender": {
                "lambda_arn": email_lambda_arn,
                "lambda_version": "V1_0",
            },
            "kms_key_id": kms_key.arn,
        },
        mfa_configuration="OFF",
        name=poolname,
        password_policy={
            "minimum_length": 8,
            "require_lowercase": True,
            "require_numbers": True,
            "require_symbols": True,
            "require_uppercase": True,
            "temporary_password_validity_days": 7,
        },
        schemas=[{
            "attribute_data_type": "String",
            "developer_only_attribute": False,
            "mutable": True,
            "name": "email",
            "required": True,
            "string_attribute_constraints": {
                "max_length": "2048",
                "min_length": "0",
            },
        }],
        user_attribute_update_settings={
            "attributes_require_verification_before_updates": ["email"],
        },
        username_attributes=["email"],
        username_configuration={
            "case_sensitive": False,
        },
        verification_message_template={
            "default_email_option": "CONFIRM_WITH_CODE",
            "email_message_by_link": "Please click the link below to verify your email address. {##Verify Email##}",
            "email_subject_by_link": "Your verification link",
        })

    # add policy to the kms key to allow cognito to use it
    kms_policy_json = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Action": "kms:*",
                "Effect": "Allow",
                "Principal": {
                    "AWS": f"arn:aws:iam::{aws_account_id}:root"
                },
                "Resource": "*",
            },
            {
                "Action": "kms:CreateGrant",
                "Effect": "Allow",
                "Principal": {
                    "Service": "cognito-idp.amazonaws.com"
                },
                "Resource": f"arn:aws:kms:{aws_region}:{aws_account_id}:key/*",
                "Condition": {
                    "StringEquals": {
                        "aws:SourceAccount": aws_account_id
                    },
                    "ArnLike": {
                        "aws:SourceArn": user_pool.arn
                    }
                }
            }
        ]
    }
    kms_policy = aws.kms.KeyPolicy(f"{basename}-cognito-lambda-email-kms-policy-{aws_region}-{stack}",
                                    key_id=kms_key.id,
                                    policy=pulumi.Output.json_dumps(kms_policy_json))

    return user_pool, kms_key


def create_cognito_user_pool_domain(basename, domain_prefix, user_pool_id):
    pooldomainname = f"{basename}-cognito-userpool-domain-{aws_region}-{stack}"
    return aws.cognito.UserPoolDomain(pooldomainname,
        domain=domain_prefix,
        user_pool_id=user_pool_id)


def create_user_pool_app_client(basename, user_pool_id) -> aws.cognito.UserPoolClient:
    name = f"{basename}-{aws_region}-{stack}"
    client = aws.cognito.UserPoolClient(name,
        access_token_validity=1,
        auth_session_validity=3,
        enable_token_revocation=True,
        generate_secret=True,
        explicit_auth_flows=[
            "ALLOW_REFRESH_TOKEN_AUTH",
            "ALLOW_USER_PASSWORD_AUTH",
        ],
        id_token_validity=1,
        name=name,
        prevent_user_existence_errors="ENABLED",
        read_attributes=[
            "email",
            "email_verified",
        ],
        refresh_token_validity=30,
        token_validity_units={
            "access_token": "days",
            "id_token": "days",
            "refresh_token": "days",
        },
        user_pool_id=user_pool_id,
        write_attributes=["email"])

    return client


def create_sns_topic(basename) -> aws.sns.Topic:
    name = f"{basename}-sns-{aws_region}-{stack}"
    policy = {"Version": "2008-10-17",
              "Statement": [{
                    "Action": [
                        "SNS:GetTopicAttributes",
                        "SNS:SetTopicAttributes",
                        "SNS:AddPermission",
                        "SNS:RemovePermission",
                        "SNS:DeleteTopic",
                        "SNS:Subscribe",
                        "SNS:ListSubscriptionsByTopic",
                        "SNS:Publish"
                    ],
                    "Condition": {
                        "StringEquals": {
                            "AWS:SourceOwner": aws_account_id
                        }
                    },
                    "Effect": "Allow",
                    "Principal": {
                        "AWS": "*"
                    },
                    "Resource": f"arn:aws:sns:{aws_region}:{aws_account_id}:{name}",
                }
            ],
        }

    topic = aws.sns.Topic(name,
        name=name,
        policy=pulumi.Output.json_dumps(policy),
        tracing_config="PassThrough")

    return topic


def add_sns_lambda_target(lambda_function_name, lambda_function_arn, sns_topic_arn):
    sns_target = aws.sns.TopicSubscription(f"{lambda_function_name}-sns-target-{aws_region}-{stack}",
                                           endpoint=lambda_function_arn,
                                           protocol="lambda",
                                           topic=sns_topic_arn)

    sns_target_permission = aws.lambda_.Permission(f"{lambda_function_name}-sns-lambda-permission-{aws_region}-{stack}",
                                                   action="lambda:InvokeFunction",
                                                   function=lambda_function_arn,
                                                   principal="sns.amazonaws.com",
                                                   source_arn=sns_topic_arn)

    return sns_target, sns_target_permission


def add_sns_lambda_alert(lambda_function_name, sns_topic_arn):
    error_alert = aws.cloudwatch.MetricAlarm(f"{lambda_function_name}-errors-alert",
        alarm_actions=[sns_topic_arn],
        comparison_operator="GreaterThanThreshold",
        datapoints_to_alarm=1,
        dimensions={
            "FunctionName": lambda_function_name,
        },
        evaluation_periods=1,
        metric_name="Errors",
        name=f"{lambda_function_name}-errors-alert",
        namespace="AWS/Lambda",
        period=600,
        statistic="Sum")

    return error_alert


def create_amplify_app(basename: str,
                       env: dict[str, str],
                       amplify_build_spec: str,
                       repo: str,
                       repo_access_token: str,
                       branch: str,
                       enable_auto_build: bool,
                       custom_rules: list = None,
                       platform: str = "WEB_COMPUTE",
                       framework: str = "Next.js - SSR") -> tuple[aws.amplify.App, aws.amplify.Branch]:

    if custom_rules is None:
        custom_rules = [{
            "source": "/<*>",
            "status": "404-200",
            "target": "/index.html",
        }]

    amplify_logging_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Action": [
                    "logs:CreateLogStream",
                    "logs:PutLogEvents"
                ],
                "Effect": "Allow",
                "Resource": f"arn:aws:logs:{aws_region}:{aws_account_id}:log-group:/aws/amplify/*:log-stream:*",
            },
            {
                "Action": "logs:CreateLogGroup",
                "Effect": "Allow",
                "Resource": f"arn:aws:logs:{aws_region}:{aws_account_id}:log-group:/aws/amplify/*",
            },
            {
                "Action": "logs:DescribeLogGroups",
                "Effect": "Allow",
                "Resource": f"arn:aws:logs:{aws_region}:{aws_account_id}:log-group:*",
            }
        ],
    }

    amplify_assume_role_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Action": "sts:AssumeRole",
                "Effect": "Allow",
                "Principal": {
                    "Service": [
                        f"amplify.{aws_region}.amazonaws.com",
                        "amplify.amazonaws.com"
                    ]
                }
            }
        ],
    }

    amplify_role = aws.iam.Role(f"{basename}-{aws_region}-amplify-role-{stack}",
        assume_role_policy=amplify_assume_role_policy,
        managed_policy_arns=[
            "arn:aws:iam::aws:policy/service-role/AmplifyBackendDeployFullAccess",
        ],
        inline_policies=[aws.iam.RoleInlinePolicyArgs(
            name=f"{basename}-amplify-policy",
            policy=pulumi.Output.json_dumps(amplify_logging_policy)),
        ],
        name=f"{basename}-{aws_region}-amplify-role-{stack}",
        path="/service-role/")

    app = aws.amplify.App(f"{basename}-{aws_region}-amplify-app-{stack}",
        access_token=repo_access_token,
        build_spec=amplify_build_spec,
        custom_rules=custom_rules,
        environment_variables=env,
        iam_service_role_arn=amplify_role.arn,
        name=f"{basename}-{aws_region}-amplify-app-{stack}",
        platform=platform,
        repository=repo)

    log_group = aws.cloudwatch.LogGroup(f"{basename}-{aws_region}-amplify-app-{stack}-log-group",
                                        name=pulumi.Output.format("/aws/amplify/{}", app.id),
                                        retention_in_days=7)

    branch = aws.amplify.Branch(f"{basename}-{aws_region}-amplify-branch-main-{stack}",
        app_id=app.id,
        branch_name=branch,
        display_name=branch,
        framework=framework,
        stage="PRODUCTION",
        ttl="5",
        enable_auto_build=enable_auto_build)

    return app, branch


def add_sns_amplify_alert(app_name, app_id, sns_topic_arn):
    error_alert = aws.cloudwatch.MetricAlarm(f"{app_name}-{aws_region}-errors-alert-{stack}",
        alarm_actions=[sns_topic_arn],
        comparison_operator="GreaterThanThreshold",
        datapoints_to_alarm=1,
        dimensions={
            "App": app_id,
        },
        evaluation_periods=1,
        metric_name="5xxErrors",
        name=f"{app_name}-{aws_region}-errors-alert-{stack}",
        namespace="AWS/AmplifyHosting",
        period=600,
        statistic="Sum")

    return error_alert




def create_ses_domain(domain, email):
    route53_zone = aws.route53.get_zone(name=domain, private_zone=False)

    # create a domain identity
    ses_domain_identity = aws.ses.DomainIdentity(f"{domain}-ses-domain-identity", domain=domain)
    ses_domain_identity_verification_record = aws.route53.Record(f"{domain}-ses-domain-identity-verification-record",
        zone_id=route53_zone.zone_id,
        name=f"_amazonses.{domain}",
        type=aws.route53.RecordType.TXT,
        ttl=600,
        records=[ses_domain_identity.verification_token])
    ses_domain_identity_verification = aws.ses.DomainIdentityVerification(f"{domain}-ses-domain-identity-verification",
                                                                          domain=ses_domain_identity.id,
                                                                          opts=pulumi.ResourceOptions(depends_on=[ses_domain_identity_verification_record]))

    ses_domain_dkim = aws.ses.DomainDkim(f"{domain}-ses-domain-dkim", domain=domain)

    ses_domain_dkim.dkim_tokens.apply(lambda dkim_tokens: [
        aws.route53.Record(f"{domain}-ses-dkim-verification-record-{token}",
            zone_id=route53_zone.zone_id,
            name=f"{token}._domainkey.{domain}",
            type=aws.route53.RecordType.CNAME,
            ttl=600,
            records=[f"{token}.dkim.amazonses.com"]) for token in dkim_tokens
    ])

    ses_mx_record = aws.route53.Record(f"{domain}-ses-mx-record",
        zone_id=route53_zone.zone_id,
        name=domain,
        type=aws.route53.RecordType.MX,
        ttl=600,
        records=[f"10 inbound-smtp.{aws_region}.amazonaws.com"])

    # create s3 bucket to receive emails
    ses_emails_bucket = deploy_s3_bucket(f"{domain}-ses-receipt-emails")
    bucket_policy = {
        "Version": "2012-10-17",
        "Statement": [
            {
                "Effect": "Allow",
                "Principal": {
                    "Service": "ses.amazonaws.com"
                },
                "Action": "s3:PutObject",
                "Resource": pulumi.Output.format("arn:aws:s3:::{}/*", ses_emails_bucket.bucket),
                "Condition": {
                    "StringEquals": {
                        "AWS:SourceAccount": aws_account_id
                    }
                }
            }
        ]
    }
    allow_ses_s3_access_policy = aws.s3.BucketPolicy(f"{domain}-s3-bucket-policy-receipt-emails",
        bucket=ses_emails_bucket.bucket,
        policy=pulumi.Output.json_dumps(bucket_policy))

    lifecycle = aws.s3.BucketLifecycleConfigurationV2(f"{domain}-s3-bucket-lifecycle-receipt-emails",
        bucket=ses_emails_bucket.bucket,
        rules=[{
            "abort_incomplete_multipart_upload": {
                "days_after_initiation": 1,
            },
            "expiration": {
                "days": 5,
            },
            "id": "zap",
            "noncurrent_version_expiration": {
                "noncurrent_days": 1,
            },
            "status": "Enabled",
        }])

    # create receipt rule set and activate it
    ses_recipt_rule_set = aws.ses.ReceiptRuleSet(f"{domain}-ses-receipt-ruleset",
                                                 rule_set_name=f"{domain}-ses-receipt-ruleset")
    ses_receipt_rule = aws.ses.ReceiptRule(f"{domain}-ses-receipt-rule-s3",
                                           name=f"{domain}-ses-receipt-rule-s3",
                                           rule_set_name=ses_recipt_rule_set.rule_set_name,
                                           recipients=[f"{email}@{domain}"],
                                           enabled=True,
                                           s3_actions=[{"bucket_name": ses_emails_bucket.bucket, "position": 1}])
    ses_active_rule_set = aws.ses.ActiveReceiptRuleSet(f"{domain}-ses-active-receipt-ruleset",
                                                       rule_set_name=ses_recipt_rule_set.rule_set_name)



    # create email identity
    ses_email_identity = aws.ses.EmailIdentity(f"{email}AT{domain}-ses-email-identity",
                                               email=f"{email}@{domain}")

    return ses_domain_identity, ses_domain_identity_verification_record, ses_domain_identity_verification, \
            ses_emails_bucket, ses_recipt_rule_set, ses_receipt_rule, ses_email_identity


def create_iam_user(name, policy):
    iam_user = aws.iam.User(f"iam-user-{name}-{stack}",
                            name=f"{name}-{stack}")


    iam_user_policy = aws.iam.UserPolicy(f"iam-user-{name}-policy-{stack}",
                                         name="policy",
                                         policy=pulumi.Output.json_dumps(policy),
                                         user=iam_user.name)#

    iam_user_access_key = aws.iam.AccessKey(f"iam-user-{name}-accesskey-{stack}",
                                            status="Active",
                                            user=iam_user.name)

    return iam_user, iam_user_access_key

