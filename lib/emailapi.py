import json
import datetime
from lib.settings import settings
from pydantic import BaseModel
from lib.sqsapi import SQS
from lib.portaldbapi import DocumentDB


class EmailServiceConfig(BaseModel):
    enabled: bool = False
    restricted_sender_domains: list[str] = []
    restricted_domains: list[str] = []
    templates: dict[str, str] = {}


class EmailService():
    def __init__(self, db: DocumentDB, sqs: SQS):
        self.db: DocumentDB = db
        self.sqs: SQS = sqs

    def get_now() -> datetime.datetime:
        # static method to aid unit tests
        return datetime.datetime.now(datetime.UTC).replace(tzinfo=None)


    def send(self, 
             sender: str, 
             recipient: str, 
             template: str, 
             data: dict, 
             metadata: dict, 
             send_at: datetime.datetime|None=None, 
             from_name: str|None=None, 
             language: str|None=None, 
             vertical: str|None=None, 
             banner_attachment: dict|None=None, 
             signature_attachment: dict|None=None,
             portaldb_record_map:str|None=None,
             service_provider:str|None=None,
             ses_delay_offset:int=0) -> dict:
        success: bool = False
        es: EmailServiceConfig = EmailServiceConfig(**self.db.config.find_one({"_id": "email"}))

        # get template
        template_name: str = template
        if vertical:
            template_name = f"{vertical.lower()}:{template_name}"
        if language:
            template_name = f"{template_name}:{language.lower()}"
        template_id = es.templates.get(template_name)

        if not template_id:
            raise Exception(f"Template {template} does not exist")
        
        if service_provider and service_provider not in ['sendgrid', 'ses']:
            raise Exception(f"Email service provider {service_provider} is not supported")

        message_attributes = {
            'fromEmail': {
                'DataType': 'String',
                'StringValue': sender,
            },
            'toEmail': {
                'DataType': 'String',
                'StringValue': recipient,
            },
            'templateId': {
                'DataType': 'String',
                'StringValue': template_id,
            },
            'templateData': {
                'DataType': 'String',
                'StringValue': json.dumps(data),
            },
            'customArgs': {
                'DataType': 'String',
                'StringValue': json.dumps(metadata),
            },
        }

        # add banner attachment if provided
        if banner_attachment:
            message_attributes['banner_attachment'] = {
                'DataType': 'String',
                'StringValue': json.dumps(banner_attachment),
            }

        # add signature attachment if provided
        if signature_attachment:
            message_attributes['signature_attachment'] = {
                'DataType': 'String',
                'StringValue': json.dumps(signature_attachment),
            }
            
        # add send_at attribute if provided
        if send_at:
            message_attributes['sendAt'] = {
                'DataType': 'String',
                'StringValue': send_at.strftime('%Y-%m-%d %H:%M:%S')
            }
        
        # add display name if provided
        if from_name:
            message_attributes['fromName'] = {
                'DataType': 'String',
                'StringValue': from_name,
            }

        # add portaldb record map if provided
        if portaldb_record_map:
            message_attributes['portaldbRecordMap'] = {
                'DataType': 'String',
                'StringValue': portaldb_record_map,
            }

        queue_url = settings.send_email_to_sg_queue_url
        delay_seconds = 0
        if service_provider and service_provider == 'ses':
            queue_url = settings.send_email_to_ses_queue_url
            if send_at:
                # delay the message from consumers for up to 15 mins
                now = EmailService.get_now()
                delay_seconds = max(int((send_at - now).total_seconds()), 0)
                delay_seconds = abs(delay_seconds - ses_delay_offset)
                if delay_seconds > 900:
                    # cap at 900 seconds (15 minutes), the max delay SQS supports
                    delay_seconds = 900

        try:
            self.sqs.send_message(
                queue=queue_url,
                message_body=f'Email template {template_id} to {recipient}',
                message_attributes=message_attributes,
                delay_seconds=delay_seconds,
            )

            success: bool = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }
