import boto3
import os


class SQS:
    def __init__(self):
        self.client = boto3.client('sqs', region_name=os.environ.get('AWS_REGION', 'eu-west-1'))

    def send_message(self, queue: str, message_body: str, message_attributes: dict, delay_seconds: int = 0) -> None:
        self.client.send_message(
            QueueUrl = queue,
            MessageBody = message_body,
            MessageAttributes = message_attributes,
            DelaySeconds= delay_seconds,
        )
