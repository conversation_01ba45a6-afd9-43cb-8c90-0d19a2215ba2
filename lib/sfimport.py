import lib.sfapi as sfapi
import datetime
import re
import boto3
import tempfile
from lib.settings import settings
import zipfile
import io
import csv
import os


RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING = 'CRC\'s Customer - Advertising'
RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING = 'CRC\'s Customer - Manufacturing'
RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT = 'Customer\'s Client Account'


SFAPI_TO_CSV_MAPPING = {
    re.sub(r'(?<!^)(?=[A-Z])', '_', x).lower() + '.csv': getattr(sfapi, x) for x in sfapi.SFAPI_OBJECT_NAMES + sfapi.SFAPI_KEY_OBJECT_NAMES
}

sfcache: dict[str] = {}
sfid_mapping: dict[str, str] = {}
new_accounts: list[sfapi.Account] = []
new_contacts: list[sfapi.Contact] = []
new_divisions: list[sfapi.Division] = []
new_customer_client_relationships: list[sfapi.CustomerClientRelationship] = []
new_survey_rounds: list[sfapi.SurveyRound] = []
new_customer_survey_rounds: list[sfapi.CustomerSurveyRound] = []
new_customer_surveys: list[sfapi.CustomerSurvey] = []
new_survey_clients: list[sfapi.SurveyClient] = []
new_survey_panel_members: list[sfapi.SurveyPanelMember] = []
new_survey_account_managers: list[sfapi.SurveyAccountManager] = []
new_survey_panel_managers: list[sfapi.SurveyPanelManager] = []
new_survey_responses: list[sfapi.SurveyResponse] = []
new_contact_key_accounts: list[sfapi.ContactKeyAccount] = []
new_contact_key_markets: list[sfapi.ContactKeyMarket] = []
new_contact_key_customers: list[sfapi.ContactKeyCustomer] = []
new_account_contact_relations: list[sfapi.AccountContactRelation] = []
new_markets: list[sfapi.Market] = []
new_teams: list[sfapi.Team] = []
new_account_groups: list[sfapi.AccountGroup] = []
new_contact_report_views: list[sfapi.ContactReportView] = []
new_account_tiers: list[sfapi.AccountTier] = []
new_product_segments: list[sfapi.ProductSegment] = []
new_survey_client_product_segments: list[sfapi.SurveyClientProductSegment] = []


sfid_mapping[None] = None
sfid_mapping[''] = ''

LANGUAGE_MAP = {
    'br-pt': 'Brazilian Portuguese',
    'zh': 'Chinese (Cantonese)',
    'zh-ha': 'Chinese (Mandarin)',
    'sr': 'Serbian',
    'ko': 'Korean',
    'id': 'Indonesian',
    'tr': 'Turkish',
    'vi': 'Vietnamese',
    'cs': 'Czech',
    'fi': 'Finnish',
    'bs': 'Bosnian',
    'ru': 'Russian',
    'sk': 'Slovak',
    'en': 'English',
    'en-us': 'English (US)',
    'ja': 'Japanese',
    'sv': 'Swedish',
    'es': 'Spanish',
    'fr': 'French',
    'fr-ca': 'French',
    'it': 'Italian',
    'de': 'German',
    'no': 'Norwegian',
    'da': 'Danish',
    'th': 'Thai',
    'hu': 'Hungarian',
    'pl': 'Polish',
    'pt': 'Portuguese',
    'nl': 'Dutch',
    'uk': 'Ukrainian',
}

JOB_FUNCTION_MAP = {
    'Brand': ['Marketing', 'Brand'],
    'Commercial': ['Sales & Commercial', None],
    'Creative': ['Marketing', 'Creative'],
    'Digital': ['Marketing', 'Digital'],
    'Media': ['Marketing', 'Media'],
    'Product': ['Marketing', 'Product'],
    'Public Relations': ['Communications', 'Public Affairs/Relations'],
    'Senior Management': ['Executive & Senior Management', None],
    'Strategy': ['Marketing', 'Strategy & Planning'],
}

EXCLUDED_LEGACY_DIVISIONS = [
    'No Division',
    'Empty',
    'N/A'
]

MARKET_MAP = {
    'Sao Paulo-City': 'São Paulo',
    'Dusseldorf-City': 'Düsseldorf',
    'New York-City': 'New York City',
    'Singapore-City': 'Singapore',
    'Makati City-City': 'Makati',
}

ORGANISATION_LEVELS_ADVERTISING = ['Holding Group', 'Network', 'Sub-network', 'Agency Brand', 'Agency Brand (Sub 1)', 'With Market', 'Office']
ORGANISATION_LEVEL_FIELDS_ADVERTISING_STAFF = [
    ('holding group', 'Holding Group'),
    ('network', 'Network'),
    ('sub-network', 'Sub-network'),
    ('agency brand', 'Agency Brand'),
    ('agency brand subsidiary', 'Agency Brand (Sub 1)')
]
ORGANISATION_LEVEL_FIELDS_ADVERTISING_SURVEYCSV = [
    ('holding_group', 'Holding Group'),
    ('network', 'Network'),
    ('sub_network', 'Sub-network'),
    ('agency_brand', 'Agency Brand'),
    ('agency_brand_subsidiary', 'Agency Brand (Sub 1)')
]

ORGANISATION_LEVELS_MANUFACTURING = ['Company', 'Division', 'Sub-division', 'Sub Segment 1', 'Sub Segment 2', 'Country Market', 'Site']
ORGANISATION_LEVEL_FIELDS_MANUFACTURING_STAFF = [
    ('company', 'Company'),
    ('division', 'Division'),
    ('sub division', 'Sub-division'),
    ('sub segment 1', 'Sub Segment 1'),
    ('sub segment 2', 'Sub Segment 2')
]
ORGANISATION_LEVEL_FIELDS_MANUFACTURING_SURVEYCSV = [
    ('company', 'Company'),
    ('division', 'Division'),
    ('sub_division', 'Sub-division'),
    ('sub_segment_1', 'Sub Segment 1'),
    ('sub_segment_2', 'Sub Segment 2')
]


def get_or_create_cached_account(name: str,
                                 parent_account: sfapi.Account,
                                 account_record_type: sfapi.AccountRecordType,
                                 round_frequency: str = '',
                                 customer_status: str = '',
                                 current_round: bool = False,
                                 consultant: str = '',
                                 market: sfapi.Market = None,
                                 organisational_level: str = '',
                                 industry: str = '',
                                 supersector: str = '',
                                 sector: str = '',
                                 subsector: str = '',
                                 office_city: str = None,
                                 office_country: str = None,
                                 legacy_sector: str = None,
                                 global_client: bool = False):
    business_key = sfapi.Account.business_key(name, parent_account, account_record_type)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    parent_account_id = parent_account.Id if parent_account else None
    account = sfapi.Account(Id=business_key,
                            Name=name,
                            ParentId=parent_account_id,
                            RecordTypeId=account_record_type.Id,
                            Round_Frequency__c=round_frequency,
                            Customer_Status__c=customer_status,
                            Current_Round__c=current_round,
                            Consultant__c=consultant,
                            Market__c=market.Id if market else None,
                            Organisation_Level__c=organisational_level,
                            Industry=industry,
                            Supersector__c=supersector,
                            Sector__c=sector,
                            Subsector__c=subsector,
                            Office_City__c=office_city,
                            Office_Country__c=office_country,
                            Legacy_Sector__c=legacy_sector,
                            Global_Client__c=global_client)
    new_accounts.append(account)

    sfcache[business_key] = account
    return account


def get_or_create_cached_contact(account: sfapi.Account,
                                 first_name: str,
                                 last_name: str,
                                 email: str,
                                 contact_type: str = None,
                                 seniority: str = None,
                                 job_title: str = None,
                                 language: str = None,
                                 job_function: str = None,
                                 job_function_level2: str = None,
                                 city: str = None,
                                 accountsmanager: bool = False,
                                 consultant: bool = False,
                                 panelmanager: bool = False,
                                 signatory: bool = False,
                                 hub_contact_id: str = None,
                                 legacy_division: str = None,
                                 division: sfapi.Division = None,
                                 location: str = None,
                                 account_site: str = None,
                                 report_customisation: str = None,
                                 reporting_global_accounts: str = None,
                                 reporting_role: str = None,
                                 team: sfapi.Team = None,
                                 created_in_client_area: bool = False):
    business_key = sfapi.Contact.business_key(account, email)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    contact = sfapi.Contact(Id=business_key,
                            AccountId=account.Id,
                            FirstName=first_name,
                            LastName=last_name,
                            Email=email,
                            Contact_Type__c=contact_type,
                            Seniority__c=seniority,
                            Title=job_title,
                            Language__c=language,
                            Job_Function__c=job_function,
                            Job_Function_Level_2__c=job_function_level2,
                            MailingCity=city,
                            Accounts_Manager__c=accountsmanager,
                            Consultant__c=consultant,
                            Panel_Manager__c=panelmanager,
                            Signatory__c=signatory,
                            Hub_Contact_Id__c=hub_contact_id,
                            Legacy_Division__c=legacy_division,
                            Division__c=division.Id if division else None,
                            Location__c=location,
                            Account_Site__c=account_site,
                            Report_Customisation__c=report_customisation,
                            Reporting_Global_Accounts__c=reporting_global_accounts,
                            Reporting_Role__c=reporting_role,
                            Team__c=team.Id if team else None,
                            Created_In_Client_Area__c=created_in_client_area)
    new_contacts.append(contact)

    sfcache[business_key] = contact
    return contact


def get_or_create_cached_division(account: sfapi.Account,
                                  name: str):
    business_key = sfapi.Division.business_key(account, name)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    division = sfapi.Division(Id=business_key,
                              Account__c=account.Id,
                              Name=name)
    new_divisions.append(division)

    sfcache[business_key] = division
    return division


def get_or_create_cached_customer_client_relationship(customer_account: sfapi.Account,
                                                      customers_client_account: sfapi.Account,
                                                      relationship_type: str,
                                                      account_group: sfapi.AccountGroup | None = None,
                                                      account_tier: sfapi.AccountTier | None = None,
                                                      customers_client_segment: str | None = None):
    business_key = sfapi.CustomerClientRelationship.business_key(customer_account, customers_client_account)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    ccr = sfapi.CustomerClientRelationship(Id=business_key,
                                           Name=sfapi.CustomerClientRelationship.make_name(customer_account, customers_client_account),
                                           Customer_Account__c=customer_account.Id,
                                           Customers_Client__c=customers_client_account.Id,
                                           Relationship_Type__c=relationship_type,
                                           Account_Group__c=account_group.Id if account_group else None,
                                           Account_Tier__c=account_tier.Id if account_tier else None,
                                           Customers_Client_Segment__c=customers_client_segment if customers_client_segment else None)
    new_customer_client_relationships.append(ccr)

    sfcache[business_key] = ccr
    return ccr


def get_or_create_cached_contact_key_account(contact_report_view: sfapi.ContactReportView,
                                             account: sfapi.Account):
    business_key = sfapi.ContactKeyAccount.business_key(account, contact_report_view)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    ccr = sfapi.ContactKeyAccount(Id=business_key, Contact_Report_View__c=contact_report_view.Id, Account__c=account.Id)
    new_contact_key_accounts.append(ccr)

    sfcache[business_key] = ccr
    return ccr


def get_or_create_cached_contact_key_market(contact_report_view: sfapi.ContactReportView,
                                            market: sfapi.Market):
    business_key = sfapi.ContactKeyMarket.business_key(market, contact_report_view)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    ccr = sfapi.ContactKeyMarket(Id=business_key, Contact_Report_View__c=contact_report_view.Id, Market__c=market.Id, Excluded__c=False)
    new_contact_key_markets.append(ccr)

    sfcache[business_key] = ccr
    return ccr


def get_or_create_cached_contact_key_customer(contact_report_view: sfapi.ContactReportView,
                                              customer: sfapi.Account):
    business_key = sfapi.ContactKeyCustomer.business_key(customer, contact_report_view)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    ccr = sfapi.ContactKeyCustomer(Id=business_key, Contact_Report_View__c=contact_report_view.Id, Customer__c=customer.Id, Excluded__c=False)
    new_contact_key_customers.append(ccr)

    sfcache[business_key] = ccr
    return ccr


def get_or_create_cached_survey_round(round_date: datetime.date, round_end_date: datetime.date, stage: str, survey_type: str, is_imported: bool = False):
    business_key = sfapi.SurveyRound.business_key(round_date, survey_type, is_imported)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_round = sfapi.SurveyRound(Id=business_key,
                                     Name=sfapi.SurveyRound.make_name(round_date, survey_type),
                                     Round_Date__c=round_date,
                                     Round_End_Date__c=round_end_date,
                                     Stage__c=stage,
                                     Is_Imported__c=is_imported,
                                     Survey_Type__c=survey_type)
    new_survey_rounds.append(survey_round)

    sfcache[business_key] = survey_round
    return survey_round


def get_or_create_cached_customer_survey_round(survey_round: sfapi.SurveyRound,
                                               account: sfapi.Account,
                                               stage: str):
    business_key = sfapi.CustomerSurveyRound.business_key(survey_round, account)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    customer_survey_round = sfapi.CustomerSurveyRound(Id=business_key,
                                                        Name=sfapi.CustomerSurveyRound.make_name(account, survey_round),
                                                        Account__c=account.Id,
                                                        Survey_Round__c=survey_round.Id,
                                                        Stage__c=stage)
    new_customer_survey_rounds.append(customer_survey_round)

    sfcache[business_key] = customer_survey_round
    return customer_survey_round


def get_or_create_cached_customer_survey_round_with_name(survey_round: sfapi.SurveyRound,
                                                    account: sfapi.Account,
                                                    stage: str,
                                                    csr_name: str):
    # Special case for Barometer where we may be importing more than one survey in a quarter
    # and need to use the name to differentiate them, explicitly specifying the name rather than using the generated default
    business_key = sfapi.CustomerSurveyRound.business_key(survey_round, account)
    business_key = f"{business_key}_{csr_name}"
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    customer_survey_round = sfapi.CustomerSurveyRound(Id=business_key,
                                                        Name=csr_name,
                                                        Account__c=account.Id,
                                                        Survey_Round__c=survey_round.Id,
                                                        Stage__c=stage)
    new_customer_survey_rounds.append(customer_survey_round)

    sfcache[business_key] = customer_survey_round
    return customer_survey_round


def get_or_create_cached_customer_survey(customer_survey_round: sfapi.CustomerSurveyRound,
                                         customer: sfapi.Account,
                                         stage: str,
                                         parent_customer_survey: sfapi.CustomerSurvey | None = None,
                                         team: sfapi.Team | None = None):
    business_key = sfapi.CustomerSurvey.business_key(customer_survey_round, customer, team)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    parent_customer_survey_id = parent_customer_survey.Id if parent_customer_survey else None
    customer_survey = sfapi.CustomerSurvey(Id=business_key,
                                            Name=sfapi.CustomerSurvey.make_name(customer_survey_round, customer, team),
                                            Customer__c=customer.Id,
                                            Customer_Survey_Round__c=customer_survey_round.Id,
                                            Stage__c=stage,
                                            Parent_Customer_Survey__c=parent_customer_survey_id,
                                            Team__c=team.Id if team else None)
    new_customer_surveys.append(customer_survey)

    sfcache[business_key] = customer_survey
    return customer_survey


def get_or_create_cached_survey_client(customer_survey: sfapi.CustomerSurvey,
                                       customers_client_account: sfapi.Account,
                                       state: str,
                                       survey_name: str | None = None):
    business_key = sfapi.SurveyClient.business_key(customer_survey, customers_client_account)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_client = sfapi.SurveyClient(Id=business_key,
                                        Name=sfapi.SurveyClient.make_name(customer_survey, customers_client_account),
                                        Customer_Survey__c=customer_survey.Id,
                                        Customers_Client__c=customers_client_account.Id,
                                        State__c=state,
                                        Survey_Name__c=survey_name)
    new_survey_clients.append(survey_client)

    sfcache[business_key] = survey_client
    return survey_client


def get_or_create_cached_product_segment(account: sfapi.Account,
                                         name: str, description: str | None = None):
    business_key = sfapi.ProductSegment.business_key(account, name)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    product_segment = sfapi.ProductSegment(Id=business_key,
                                          Name=name,
                                          Account__c=account.Id,
                                          Description__c=description)
    new_product_segments.append(product_segment)

    sfcache[business_key] = product_segment
    return product_segment


def get_or_create_cached_survey_client_product_segment(survey_client: sfapi.SurveyClient,
                                                      product_segment: sfapi.ProductSegment):

    business_key = sfapi.SurveyClientProductSegment.business_key(survey_client, product_segment)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_client_product_segment = sfapi.SurveyClientProductSegment(
        Id=business_key,
        Name="Auto",  # Placeholder for the auto-number field
        Survey_Client__c=survey_client.Id,
        Product_Segment__c=product_segment.Id
    )
    new_survey_client_product_segments.append(survey_client_product_segment)

    sfcache[business_key] = survey_client_product_segment
    return survey_client_product_segment


def get_or_create_cached_survey_panel_member(survey_client: sfapi.SurveyClient,
                                             contact: sfapi.Contact,
                                             hub_row_key: str):
    business_key = sfapi.SurveyPanelMember.business_key(survey_client, contact)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_panel_member = sfapi.SurveyPanelMember(Id=business_key,
                                                    Name=sfapi.SurveyPanelMember.make_name(contact),
                                                    Survey_Client__c=survey_client.Id,
                                                    Contact__c=contact.Id,
                                                    Hub_Row_Key__c=hub_row_key,
                                                    Contact_Email__c=contact.Email,
                                                    Contact_Seniority__c=contact.Seniority,
                                                    Contact_Title__c=contact.JobTitle,
                                                    Contact_Division__c=contact.LegacyDivision,
                                                    Contact_Job_Function__c=contact.JobFunction,
                                                    Contact_Job_Function2__c=contact.JobFunctionLevel2,
                                                    Contact_Type__c=contact.ContactType)
    new_survey_panel_members.append(survey_panel_member)

    sfcache[business_key] = survey_panel_member
    return survey_panel_member


def get_or_create_cached_survey_account_manager(customer_survey: sfapi.CustomerSurvey,
                                                contact: sfapi.Contact):
    business_key = sfapi.SurveyAccountManager.business_key(contact, customer_survey)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_account_manager = sfapi.SurveyAccountManager(Id=business_key,
                                                        Account_Manager__c=contact.Id,
                                                        Customer_Survey__c=customer_survey.Id)


    new_survey_account_managers.append(survey_account_manager)

    sfcache[business_key] = survey_account_manager
    return survey_account_manager


def get_or_create_cached_survey_panel_manager(survey_client: sfapi.SurveyClient,
                                              contact: sfapi.Contact):
    business_key = sfapi.SurveyPanelManager.business_key(contact, survey_client)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_panel_manager = sfapi.SurveyPanelManager(Id=business_key,
                                                      Contact__c=contact.Id,
                                                      Survey_Client__c=survey_client.Id)

    new_survey_panel_managers.append(survey_panel_manager)

    sfcache[business_key] = survey_panel_manager
    return survey_panel_manager


def get_or_create_cached_survey_response(survey_panel_member: sfapi.SurveyPanelMember,
                                         score_question: str | None = None,
                                         score: int | None = None,
                                         feedback_question: str | None = None,
                                         feedback: str | None = None,
                                         feedback_translated: str | None = None,
                                         themes: str | None = None,
                                         is_extra_question: bool | None = None,
                                         response_date_time: datetime.datetime | None = None):
    business_key = sfapi.SurveyResponse.business_key(survey_panel_member, score_question, feedback_question, is_extra_question)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    survey_response = sfapi.SurveyResponse(Id=business_key,
                                            Name=sfapi.SurveyResponse.make_name(survey_panel_member),
                                            Survey_Panel_Member__c=survey_panel_member.Id,
                                            Feedback_Question__c=feedback_question,
                                            Feedback__c=feedback,
                                            Feedback_Translated__c=feedback_translated,
                                            Score_Question__c=score_question,
                                            Score__c=score,
                                            Themes__c=themes,
                                            Response_Date_Time__c=response_date_time,
                                            Is_Extra_Question__c=is_extra_question)
    new_survey_responses.append(survey_response)

    sfcache[business_key] = survey_response
    return survey_response


def get_or_create_cached_account_contact_relation(account: sfapi.Account,
                                                  contact: sfapi.Contact,
                                                  customer_external_contact_id: str = None):
    business_key = sfapi.AccountContactRelation.business_key(account, contact)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    account_contact_relation = sfapi.AccountContactRelation(Id=business_key,
                                                            AccountId=account.Id,
                                                            ContactId=contact.Id,
                                                            Customer_DB_External_Contact_Id__c=customer_external_contact_id)
    new_account_contact_relations.append(account_contact_relation)

    sfcache[business_key] = account_contact_relation
    return account_contact_relation


def get_or_create_cached_market(name: str,
                                parent_market: sfapi.Market | None,
                                market_type: str):
    business_key = sfapi.Market.business_key(name, parent_market, market_type)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    parent_market_id = parent_market.Id if parent_market else None
    market = sfapi.Market(Id=business_key,
                          Name=name,
                          Parent_Market__c=parent_market_id,
                          Market_Type__c=market_type)
    new_markets.append(market)

    sfcache[business_key] = market
    return market


def get_or_create_cached_team(name: str, account: sfapi.Account, market: sfapi.Market, type: str):
    business_key = sfapi.Team.business_key(name, account, market, type)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    team = sfapi.Team(Id=business_key,
                      Name=name,
                      Account__c=account.Id,
                      Market__c=market.Id,
                      Type__c=type)
    new_teams.append(team)

    sfcache[business_key] = team
    return team


def get_or_create_cached_account_group(account: sfapi.Account, name: str):
    business_key = sfapi.AccountGroup.business_key(account, name)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    team = sfapi.AccountGroup(Id=business_key,
                      Name=name,
                      Account__c=account.Id)
    new_account_groups.append(team)

    sfcache[business_key] = team
    return team


def get_or_create_cached_account_tier(account: sfapi.Account, name: str):
    business_key = sfapi.AccountTier.business_key(account, name)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    account_tier = sfapi.AccountTier(Id=business_key,
                          Name=name,
                          Account__c=account.Id)
    new_account_tiers.append(account_tier)

    sfcache[business_key] = account_tier
    return account_tier


def get_or_create_cached_contact_report_view(contact: sfapi.Contact,
                                             role: str,
                                             ids: list[str]):
    business_key = sfapi.ContactReportView.business_key(contact, role, ids)
    try:
        return sfcache[business_key]
    except KeyError:
        pass

    crv = sfapi.ContactReportView(Id=business_key,
                                  Contact__c=contact.Id,
                                  Role__c=role)
    crv.ExternalId = business_key
    new_contact_report_views.append(crv)

    sfcache[business_key] = crv
    return crv


def bulk_write_accounts_to_sf(sf, new_accounts):
    # we try and resolve the ids in accounts and write out those it succeeded for
    # then we repeat the try-resolve-ids step until we have no accounts left which do not resolve
    while len(new_accounts) > 0:
        account_batch = []
        for account in list(new_accounts):
            # try and resolve the ids - if it fails, we'll try again next batch once the missing parents are created
            try:
                account.try_resolve_ids(sfid_mapping)
                account_batch.append(account)
                new_accounts.remove(account)
            except KeyError:
                pass

        if not account_batch:
            raise Exception('New account batch was unexpectedly empty!')

        sfapi.bulk_import_batch_to_sf(sf, sfid_mapping, sfcache, account_batch, sfapi.Account)


def bulk_write_customer_surveys_to_sf(sf, new_customer_surveys):
    # we try and resolve the ids in accounts and write out those it succeeded for
    # then we repeat the try-resolve-ids step until we have no accounts left which do not resolve
    while len(new_customer_surveys) > 0:
        customer_survey_batch = []
        for customer_survey in list(new_customer_surveys):
            # try and resolve the ids - if it fails, we'll try again next batch once the missing parents are created
            try:
                customer_survey.try_resolve_ids(sfid_mapping)
                customer_survey_batch.append(customer_survey)
                new_customer_surveys.remove(customer_survey)
            except KeyError:
                pass

        if not customer_survey_batch:
            raise Exception('New customer survey batch was unexpectedly empty!')

        sfapi.bulk_import_batch_to_sf(sf, sfid_mapping, sfcache, customer_survey_batch, sfapi.CustomerSurvey)


def bulk_write_markets_to_sf(sf, new_markets):
    # we try and resolve the ids in markets and write out those it succeeded for
    # then we repeat the try-resolve-ids step until we have no markets left which do not resolve
    while len(new_markets) > 0:
        market_batch = []
        for market in list(new_markets):
            # try and resolve the ids - if it fails, we'll try again next batch once the missing parents are created
            try:
                market.try_resolve_ids(sfid_mapping)
                market_batch.append(market)
                new_markets.remove(market)
            except KeyError:
                pass

        if not market_batch:
            raise Exception('New market batch was unexpectedly empty!')

        sfapi.bulk_import_batch_to_sf(sf, sfid_mapping, sfcache, market_batch, sfapi.Market)


def find_account_by_name_list(account_forest_by_name, account_forest_by_id, account_name_list):
    accounts = account_forest_by_name.get(account_name_list[0])
    if not accounts:
        return None
    if len(accounts) > 1:
        raise ValueError("Multiple accounts with the same name", account_name_list[0])
    account = accounts[0]

    for name in account_name_list[1:]:
        found = False
        for child in account['children']:
            if child.Name == name:
                account = account_forest_by_id[child.Id]
                found = True
                break
        if not found:
            return None

    return account


def generate_account_forest(all_accounts=None):
    if all_accounts is None:
        all_accounts = [c for c in sfcache.values() if isinstance(c, sfapi.Account)]

    account_forest_by_id = {}
    account_forest_by_name = {}
    root_accounts = []
    for account in all_accounts:
        details = {'account': account, 'children': []}
        account_forest_by_id[account.Id] = details
        account_forest_by_name.setdefault(account.Name, []).append(details)

    for account in all_accounts:
        if account.ParentId is not None:
            account_forest_by_id[account.ParentId]['children'].append(account)
        else:
            root_accounts.append(account)

    return root_accounts, account_forest_by_id, account_forest_by_name


def make_safe_email(unsafe_email_address: str):
    return re.sub(r'[^\w\s]', '-', unsafe_email_address) + '@clientrelationship.com'


def load_sf_from_latest_dump(include_files=None, exclude_files=None):
    s3 = boto3.client('s3')

    now = datetime.date.today()

    prefix = f"crc-sf/yy={now:%Y}/mm={now:%m}/dd={now:%d}/"
    response = s3.list_objects_v2(Bucket=settings.SF_DATA_BUCKET, Prefix=prefix)
    if 'Contents' not in response:
        raise ValueError(f"No objects found in bucket {settings.SF_DATA_BUCKET} with prefix {prefix}")
    keys = sorted(response['Contents'], key=lambda x: x['LastModified'], reverse=True)
    latest_key = keys[0]['Key']
    print(latest_key)

    all_sfobjects = {}
    with s3_cached_download(settings.SF_DATA_BUCKET, latest_key) as tmp_file:
        with zipfile.ZipFile(tmp_file.name, 'r') as zip_file:
            for csv_filename in zip_file.namelist():
                if include_files and csv_filename not in include_files:
                    continue
                if exclude_files and csv_filename in exclude_files:
                    continue

                sfobject = SFAPI_TO_CSV_MAPPING[csv_filename]
                all_sfobjects.setdefault(sfobject.__name__, {})
                this_sfobjects = all_sfobjects[sfobject.__name__]

                with zip_file.open(csv_filename) as rawstream:
                    with io.TextIOWrapper(rawstream, encoding="utf-8") as textstream:
                        csv_reader = csv.DictReader(textstream)
                        for row in csv_reader:
                            o = sfobject.model_validate(row)
                            this_sfobjects[o.Id] = o

    return all_sfobjects


def write_record_batch(conn, table_name, record_batch: list[dict]):
    """
    Write a batch of records to the database using the COPY command for speeeeed.
    """

    if not record_batch:
        return

    with conn.cursor() as cursor:
        header = set(record_batch[0].keys())

        f = io.StringIO()
        csvout = csv.DictWriter(f, header)
        csvout.writeheader()
        csvout.writerows(record_batch)
        f.seek(0, io.SEEK_SET)
        cursor.copy_expert(
            f'COPY {table_name} ({",".join(header)}) FROM STDIN WITH (FORMAT CSV, HEADER)',
            f,
        )
        conn.commit()


def s3_cached_download(bucket, key):
    s3 = boto3.client('s3')

    os.makedirs('s3cache', exist_ok=True)

    cache_file = os.path.join('s3cache', key.replace('/', '-'))
    if os.path.exists(cache_file):
        return open(cache_file, 'rb')

    s3.download_file(bucket, key, cache_file)
    return open(cache_file, 'rb')
