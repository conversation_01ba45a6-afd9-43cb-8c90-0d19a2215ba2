import boto3
import hmac
import hashlib
import base64
from lib.portaldbapi import DocumentDB
from lib.settings import settings


class Cognito:
    def __init__(self):
        self.client = boto3.client('cognito-idp', region_name='eu-west-1')
        self.db = DocumentDB()

    def _calculate_secret_hash(self, username: str):
        message = username + settings.cognito_client_id
        dig = hmac.new(str(settings.cognito_client_secret).encode('utf-8'), msg = str(message).encode('utf-8'), digestmod = hashlib.sha256).digest()
        return base64.b64encode(dig).decode()

    def sign_up(self, username: str, password: str):
        response = self.client.sign_up(
            ClientId=settings.cognito_client_id,
            SecretHash=self._calculate_secret_hash(username),
            Username=username,
            Password=password,
            UserAttributes=[
                {
                    'Name': 'email',
                    'Value': username
                }
            ]
        )

        return response

    def confirm_sign_up(self, username: str, confirmation_code: str):
        #contact = self.db.customercontacts.find_one({'email': username}, {'Id': 1})
        response = self.client.confirm_sign_up(
            ClientId=settings.cognito_client_id,
            SecretHash=self._calculate_secret_hash(username),
            Username=username,
            ConfirmationCode=confirmation_code
        )

        return response
    
    def initiate_auth(self, username: str, password: str):
        session = self.client.initiate_auth(
            AuthFlow='USER_PASSWORD_AUTH',
            AuthParameters={
                'USERNAME': username,
                'PASSWORD': password,
                'SECRET_HASH': self._calculate_secret_hash(username)
            },
            ClientId=settings.cognito_client_id
        )

        return session
    
    def refresh_auth(self, refresh_token: str, user_id: str):
        session = self.client.initiate_auth(
            AuthFlow='REFRESH_TOKEN_AUTH',
            AuthParameters={
                'REFRESH_TOKEN': refresh_token,
                'SECRET_HASH': self._calculate_secret_hash(user_id)
            },
            ClientId=settings.cognito_client_id
        )

        return session

    def forgotten_password(self, username: str):
        response = self.client.forgot_password(
            ClientId=settings.cognito_client_id,
            SecretHash=self._calculate_secret_hash(username),
            Username=username,
        )

        return response

    def confirm_forgotten_password(self, username: str, password: str, confirmation_code: str):
        response = self.client.confirm_forgot_password(
            ClientId=settings.cognito_client_id,
            SecretHash=self._calculate_secret_hash(username),
            Username=username,
            Password=password,
            ConfirmationCode=confirmation_code,
        )

        return response
    
    def revoke_auth(self, refresh_token: str):
        response = self.client.revoke_token(
            Token=refresh_token,
            ClientId=settings.cognito_client_id,
            ClientSecret=settings.cognito_client_secret,
        )

        return response

    def resend_confirmation_code(self, username: str):
        response = self.client.resend_confirmation_code(
            ClientId=settings.cognito_client_id,
            SecretHash=self._calculate_secret_hash(username),
            Username=username,
        )

        return response