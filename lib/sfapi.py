from simple_salesforce import Salesforce, format_soql, bulk2, util
from pydantic import BaseModel, ConfigDict, Field, field_serializer, field_validator
from typing import Optional
from itertools import islice
import datetime
import requests
import tempfile
import csv
import html
import copy
import string


SFAPI_OBJECT_NAMES = [
    'CustomerClientRelationship',
    'SurveyResponse',
    'SurveyPanelMember',
    'SurveyClient',
    'CustomerSurvey',
    'CustomerSurveyRound',
    'SurveyRound',
    'Account',
    'Division',
    'Contact',
    'ContactReportView',
    'ContactKeyAccount',
    'ContactKeyMarket',
    'ContactKeyCustomer',
    'ContactKeyTeam',
    'ContactReportingCategory',
    'AccountContactRelation',
    'SurveyPanelManager',
    'SurveyAccountManager',
    'Team',
    'PendingCustomerContact',
    'AccountGroup',
    'AccountTier',
    'ProductSegment',
    'SurveyClientProductSegment',
]
SFAPI_KEY_OBJECT_NAMES = [
    'SurveyTemplateDefinition',
    'Market',
    'AccountRecordType',
]

VALID_SURVEY_TYPES = ['TRR', 'Barometer']


class AccountRecordType(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)

    @classmethod
    def sfapi_name(cls):
        return 'RecordType'

    def business_key2(self):
        return f"AccountRecordType-{self.Name}"

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}


class Market(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    MarketType: str | None = Field(alias='Market_Type__c', default=None)
    ParentMarketId: str | None = Field(alias='Parent_Market__c', default=None, fk="Market.Id")
    Timezone: str | None = Field(alias='Timezone__c', default=None)
    Language: str | None = Field(alias='Language__c', default=None)

    @classmethod
    def sfapi_name(cls):
        return 'Market__c'

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @classmethod
    def business_key(cls, name: str, parent_market: 'Market', market_type: str):
        parent_market_id = parent_market.Id if parent_market else None
        return f"Market-{name}-{parent_market_id}-{market_type}"

    def business_key2(self):
        return f"Market-{self.Name}-{self.ParentMarketId}-{self.MarketType}"

    def try_resolve_ids(self, sfid_mapping):
        self.ParentMarketId = sfid_mapping[self.ParentMarketId]


class Account(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    RecordTypeId: str = Field(fk="AccountRecordType.Id")
    CurrentRound: bool = Field(alias='Current_Round__c', default=False)
    PreviousRound: bool = Field(alias='Previous_Round__c', default=False)
    ParentId: str | None = Field(default=None, fk="Account.Id")
    RoundFrequency: str | None = Field(alias='Round_Frequency__c', default=None)
    CustomerStatus: str | None = Field(alias='Customer_Status__c', default=None)
    ConsultantId: str | None = Field(alias='Consultant__c', default=None)   #  circular dep!
    MarketId: str | None = Field(alias='Market__c', default=None, fk="Market.Id")
    ValidEmailDomains: str | None = Field(alias='Valid_Email_Domains__c', default=None)
    OrganisationalLevel: str | None = Field(alias='Organisation_Level__c', default=None)
    CalculatedOrganisationalLevel: str | None = Field(alias='Calculated_Organisation_Level__c', default=None)
    Industry: str | None = Field(alias='Industry', default=None)
    Supersector: str | None = Field(alias='Supersector__c', default=None)
    Sector: str | None = Field(alias='Sector__c', default=None)
    Subsector: str | None = Field(alias='Subsector__c', default=None)
    GlobalClient: bool = Field(alias='Global_Client__c', default=False)
    LegacySector: str | None = Field(alias='Legacy_Sector__c', default=None)
    NoOfChildAccounts: int | None = Field(alias='No_of_Child_Accounts__c', default=None)
    TopOfHierarchy: bool = Field(alias='Top_of_Hierarchy__c', default=False)
    UltimateParent: str | None = Field(alias='Ultimate_Parent__c', default=None)  # not an actual fk in salesforce
    ChildAccountCount: int | None = Field(alias='No_of_Child_Accounts__c', default=None)
    ChurnDate: datetime.date | None = Field(alias='Churn_Date__c', default=None)
    ChurnReason: str | None = Field(alias='Churn_Reason__c', default=None)
    SurveyAccountManagerId: str | None = Field(alias='Survey_Account_Manager__c', default=None)  # circular dep!
    ClientHierarchyLevel: str | None = Field(alias='Client_Hierarchy_Level__c', default=None)
    PreferredStartDate: datetime.date | None = Field(alias='Preferred_Start_Date__c', default=None)
    CreatedInClientArea: bool = Field(alias='Created_In_Client_Area__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy', 'TopOfHierarchy',
                'ClientHierarchyLevel', 'PreferredStartDate'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('ChurnDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Account'

    @classmethod
    def business_key(cls, name: str, parent_account: 'Account', account_record_type: AccountRecordType):
        parent_account_id = parent_account.Id if parent_account else None
        return f"Account-{name}-{parent_account_id}-{account_record_type.Id}"

    @classmethod
    def get_child_accounts(cls, sf, parent_id: str, recurse: bool = True):
        parent_account_ids = set([parent_id])
        seen_account_ids = set()
        while parent_account_ids:
            cur_parent_account_id = parent_account_ids.pop()
            seen_account_ids.add(cur_parent_account_id)

            soql = format_soql(soql_fields(cls, "select FIELDS from Account where ParentId = {parent_id}"), parent_id=cur_parent_account_id)
            for row in sf.query_all_iter(soql):
                yield Account.model_validate(row)

                if recurse and row['Id'] not in seen_account_ids:
                    parent_account_ids.add(row['Id'])

    @classmethod
    def set_consultant(self, sf, account_id, consultant_id):
        sf.Account.update(account_id, {'Consultant__c': consultant_id})

    @classmethod
    def set_account_manager(self, sf, account_id, manager_id):
        sf.Account.update(account_id, {'Survey_Account_Manager__c': manager_id})

    @classmethod
    def set_current_round(self, sf, account_id, current_round):
        sf.Account.update(account_id, {'Current_Round__c': current_round})

    def business_key2(self):
        return f"Account-{self.Name}-{self.ParentId}-{self.RecordTypeId}"

    def is_root_account(self):
        return self.ParentId is None

    def try_resolve_ids(self, sfid_mapping):
        self.ParentId = sfid_mapping[self.ParentId]


class Contact(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    InternalId: str | None = Field(alias='Internal_Id__c', default=None)
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Consultant: bool = Field(alias='Consultant__c', default=False)
    AccountsManager: bool = Field(alias='Accounts_Manager__c', default=False)
    PanelManager: bool = Field(alias='Panel_Manager__c', default=False)
    Signatory: bool = Field(alias='Signatory__c', default=False)
    InsightsViewer: bool = Field(alias='Insights_Viewer__c', default=False)
    FirstName: str | None
    LastName: str | None
    Email: str | None
    ContactType: str | None = Field(alias='Contact_Type__c', default=None)
    Seniority: str | None = Field(alias='Seniority__c', default=None)
    JobTitle: str | None = Field(alias='Title', default=None)
    AccountId: str | None = Field(default=None, fk="Account.Id")
    Language: str | None = Field(alias='Language__c', default=None)
    MailingCity: str | None = Field(default=None)
    JobFunction: str | None = Field(alias='Job_Function__c', default=None)
    JobFunctionLevel2: str | None = Field(alias='Job_Function_Level_2__c', default=None)
    HubContactId: str | None = Field(alias='Hub_Contact_Id__c', default=None)
    LegacyDivision: str | None = Field(alias='Legacy_Division__c', default=None)
    Division: str | None = Field(alias='Division__c', default=None, fk="Division.Id")
    Location: str | None = Field(alias='Location__c', default=None, fk="Market.Id")
    AccountSite: str | None = Field(alias='Account_Site__c', default=None, fk="Market.Id")
    HasOptedOutOfEmail: bool | None = Field(alias='HasOptedOutOfEmail', default=False)
    ClientAreaAssumeRole: str | None = Field(alias='Client_Area_Assume_Role__c', default=None)
    CreatedInClientArea: bool = Field(alias='Created_In_Client_Area__c', default=False)
    LastAccessClientArea: datetime.datetime | None = Field(alias='Last_Access_Client_Area__c', default=None)
    PendingNewUser: bool = Field(alias='Pending_New_User__c', default=False)
    TeamId: str | None = Field(alias='Team__c', default=None, fk="Team.Id")
    Signature: str | None = Field(alias='Signature__c', default=None)
    Banner: str | None = Field(alias='Banner__c', default=None)
    Timezone: str | None = Field(alias='Timezone__c', default=None)
    Location: str | None = Field(alias="Location__c", default=None)   # dupe field with above?!?!
    AccountSite: str | None = Field(alias="Account_Site__c", default=None)  # dupe field with above?!?!
    DisableExternalCommunications: bool = Field(alias="Disable_External_Communication__c", default=False)
    # FIXME: remove these in favour of the new report objects when everything is migrated
    ReportCustomisation: str | None = Field(alias='Report_Customisation__c', default=None)
    ReportingGlobalAccounts: bool | None = Field(alias='Reporting_Global_Accounts__c', default=False)
    ReportingRole: str | None = Field(alias='Reporting_Role__c', default=None)
    BarometerViewer: bool = Field(alias='Barometer_Viewer__c', default=False)
    Department: str | None = Field(alias='Department', default=None)


    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('LastAccessClientArea')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy', 'ClientAreaAssumeRole'}

    @classmethod
    def exclude_fields_to_export(cls):
        return {'Client_Area_Assume_Role__c'}

    @classmethod
    def sfapi_name(cls):
        return 'Contact'

    @classmethod
    def business_key(cls, account: Account, email: str):
        if email:
            email = email.lower()
        return f"Contact-{email}"

    def business_key2(self):
        email = self.Email
        if email:
            email = email.lower()
        return f"Contact-{email}"

    def try_resolve_ids(self, sfid_mapping):
        self.AccountId = sfid_mapping[self.AccountId]
        self.Division = sfid_mapping[self.Division]
        self.TeamId = sfid_mapping[self.TeamId]

    @classmethod
    def set_accounts_manager(self, sf, contact_id, is_accounts_manager):
        sf.Contact.update(contact_id, {'Accounts_Manager__c': is_accounts_manager})

    @classmethod
    def set_panel_manager(self, sf, contact_id, is_panel_manager):
        sf.Contact.update(contact_id, {'Panel_Manager__c': is_panel_manager})

    @classmethod
    def set_signatory(self, sf, contact_id, is_signatory):
        sf.Contact.update(contact_id, {'Signatory__c': is_signatory})


class Division(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)

    AccountId: str | None = Field(default=None, alias="Account__c", fk="Account.Id")

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Division__c'

    @classmethod
    def business_key(cls, account: Account, name: str):
        return f"Division-{account.Name}-{name}"

    def business_key2(self):
        return f"Division-{self.AccountId}-{self.Name}"

    def try_resolve_ids(self, sfid_mapping):
        self.AccountId = sfid_mapping[self.AccountId]


class CustomerClientRelationship(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    CustomerAccountId: str | None = Field(alias='Customer_Account__c', default=None, fk="Account.Id")
    CustomersClientAccountId: str | None = Field(alias='Customers_Client__c', default=None, fk="Account.Id")
    RelationshipType: str | None = Field(alias='Relationship_Type__c', default=None)
    SurveyPanelManagerId: str | None = Field(alias="Survey_Panel_Manager__c", default=None, fk="Contact.Id")
    SignatoryId: str | None = Field(alias="Signatory__c", default=None, fk="Contact.Id")
    ChurnReason: str | None = Field(alias="Churn_Reason__c", default=None)
    EndDate: datetime.date | None = Field(alias="End_Date__c", default=None)
    StartDate: datetime.date | None = Field(alias="Start_Date__c", default=None)
    AccountReview: datetime.date | None = Field(alias="Account_Review__c", default=None)
    Income: float | None = Field(alias="Income__c", default=None)
    ServiceProvided: str | None = Field(alias="Service_Provided__c", default=None)
    ProjectStatus: str | None = Field(alias="Project_Status__c", default=None)
    AccountGroupId: str | None = Field(alias="Account_Group__c", default=None, fk="AccountGroup.Id")
    SurveyName: str | None = Field(alias="Survey_Name__c", default=None)
    AccountLabel: str | None = Field(alias="Account_Label__c", default=None)
    IntegratedTeam: bool | None = Field(alias="Integrated_Team__c", default=None)
    KeyAccount: bool | None = Field(alias="Key_Account__c", default=None)
    AccountTierId: str | None = Field(alias="Account_Tier__c", default=None, fk="AccountTier.Id")
    CustomersClientSegment: str | None = Field(alias="Customers_Client_Segment__c", default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('StartDate', 'EndDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Customer_Client_Relationship__c'

    @classmethod
    def business_key(cls, customer_account: Account, customer_client_account: Account):
        return f"CustomerClientRelationship-{customer_account.Id}-{customer_client_account.Id}"

    @classmethod
    def make_name(cls, customer_account: Account, customers_client_account: Account):
        return f'{customer_account.Name} {customers_client_account.Name}'[:80]

    def business_key2(self):
        return f"CustomerClientRelationship-{self.CustomerAccountId}-{self.CustomersClientAccountId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.CustomerAccountId = sfid_mapping[self.CustomerAccountId]
        self.CustomersClientAccountId = sfid_mapping[self.CustomersClientAccountId]
        self.AccountGroupId = sfid_mapping[self.AccountGroupId]
        self.SignatoryId = sfid_mapping[self.SignatoryId]
        self.SurveyPanelManagerId = sfid_mapping[self.SurveyPanelManagerId]
        self.AccountTierId = sfid_mapping[self.AccountTierId]

    @classmethod
    def set_panel_manager(self, sf, customer_client_relationship_id, manager_id):
        sf.Customer_Client_Relationship__c.update(customer_client_relationship_id, {'Survey_Panel_Manager__c': manager_id})

    @classmethod
    def set_signatory(self, sf, customer_client_relationship_id, signatory_id):
        sf.Customer_Client_Relationship__c.update(customer_client_relationship_id, {'Signatory__c': signatory_id})


class ContactReportView(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactId: str | None = Field(alias='Contact__c', default=None, fk="Contact.Id")
    GlobalAccounts: bool | None = Field(alias='Global_Accounts__c', default=None)
    ReportCustomisation: str | None = Field(alias='Report_Customisation__c', default=None)
    Role: str | None = Field(alias='Role__c', default=None)
    ParentAccountReport: bool | None = Field(alias='Parent_Account_Report__c', default=None)
    UseAccountGroupName: bool | None = Field(alias='Use_Account_Group_Name__c', default=None)
    ExternalId: str | None = Field(alias='External_Id__c', default=None)
    GenerateSRPReport: bool | None = Field(alias='Generate_SRP_Report__c', default=None)
    SurveyType: str | None = Field(alias="Survey_Type__c", default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @classmethod
    def exclude_fields_to_export(cls):
        return {'External_Id__c'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Report_View__c'

    @classmethod
    def business_key(cls, contact: Contact, role: str, ids: list[str]):
        ids = '-'.join(sorted(ids))
        return f'ContactReportView-{contact.Id}-{role}-{ids}'

    def business_key2(self):
        if self.ExternalId:
            return self.ExternalId
        else:
            # YUCCKKKKKK - workaround so stuff works a bit
            return f'ContactReportView-{self.ContactId}-{self.Role}-{datetime.datetime.now()}'

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactId = sfid_mapping[self.ContactId]


class ContactKeyAccount(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactReportViewId: str | None = Field(alias='Contact_Report_View__c', default=None, fk="ContactReportView.Id")
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")
    Excluded: bool = Field(alias='Excluded__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Key_Account__c'

    @classmethod
    def business_key(cls, account: Account, contact_report_view: ContactReportView):
        return f"ContactKeyAccount-{account.Id}-{contact_report_view.Id}"

    def business_key2(self):
        return f"ContactKeyAccount-{self.AccountId}-{self.ContactReportViewId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactReportViewId = sfid_mapping[self.ContactReportViewId]
        self.AccountId = sfid_mapping[self.AccountId]


class ContactReportingCategory(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactReportViewId: str | None = Field(alias='Contact_Report_View__c', default=None, fk="ContactReportView.Id")
    Category: str | None = Field(alias='Category__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Reporting_Category__c'

    @classmethod
    def business_key(cls, contact_report_view: ContactReportView, category: str):
        return f"ContactReportingCategory-{contact_report_view.Id}-{category}"

    def business_key2(self):
        return f"ContactReportingCategory-{self.ContactReportViewId}-{self.Category}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactReportViewId = sfid_mapping[self.ContactReportViewId]


class Team(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str | None = Field(alias='Name', default=None)
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")
    MarketId: str | None = Field(alias='Market__c', default=None, fk="Market.Id")
    Type: str | None = Field(alias='Type__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Team__c'

    @classmethod
    def business_key(cls, name: str, account: Account, market: Market, type: str):
        return f"Team-{account.Id}-{name}-{market.Id}-{type}"

    def business_key2(self):
        return f"Team-{self.AccountId}-{self.Name}-{self.MarketId}-{self.Type}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.AccountId = sfid_mapping[self.AccountId]
        self.MarketId = sfid_mapping[self.MarketId]


class AccountGroup(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str | None = Field(alias='Name', default=None)
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Account_Group__c'

    @classmethod
    def business_key(cls, account: Account, name: str):
        return f"AccountGroup-{account.Id}-{name}"

    def business_key2(self):
        return f"AccountGroup-{self.AccountId}-{self.Name}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.AccountId = sfid_mapping[self.AccountId]


class AccountTier(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Account_Tier__c'

    @classmethod
    def business_key(cls, account: Account, name: str):
        return f"AccountTier-{account.Id}-{name}"

    def business_key2(self):
        return f"AccountTier-{self.AccountId}-{self.Name}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.AccountId = sfid_mapping[self.AccountId]


class ProductSegment(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    Name: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")
    Description: str | None = Field(alias='Description__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Product_Segment__c'

    @classmethod
    def business_key(cls, account: Account, name: str):
        return f"ProductSegment-{account.Id}-{name}"

    def business_key2(self):
        return f"ProductSegment-{self.AccountId}-{self.Name}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.AccountId = sfid_mapping[self.AccountId]


class SurveyClientProductSegment(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str | None = Field(default="Auto")  # struggling with this autoinc in sf
    SurveyClientId: str | None = Field(alias='Survey_Client__c', default=None, fk="SurveyClient.Id")
    ProductSegmentId: str | None = Field(alias='Product_Segment__c', default=None, fk="ProductSegment.Id")

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy', 'Name'}  # excluding 'Name' here for now

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Client_Product_Segment__c'

    @classmethod
    def business_key(cls, survey_client: 'SurveyClient', product_segment: 'ProductSegment'):
        return f"SurveyClientProductSegment-{survey_client.Id}-{product_segment.Id}"

    def business_key2(self):
        return f"SurveyClientProductSegment-{self.SurveyClientId}-{self.ProductSegmentId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.SurveyClientId = sfid_mapping[self.SurveyClientId]
        self.ProductSegmentId = sfid_mapping[self.ProductSegmentId]


class PendingCustomerContact(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Email: str | None = Field(alias='Email__c', default=None)
    InternalId: str | None = Field(alias='Internal_Id__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Pending_Contact__c'

    def business_key2(self):
        return f"PendingCustomerContact-{self.Email}"

    def try_resolve_ids(self, sfid_mapping: dict):
        pass


class ContactKeyMarket(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactReportViewId: str | None = Field(alias='Contact_Report_View__c', default=None, fk="ContactReportView.Id")
    Market: str | None = Field(alias='Market__c', default=None, fk="Market.Id")
    Excluded: bool = Field(alias='Excluded__c')

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Key_Market__c'

    @classmethod
    def business_key(cls, market, contact_report_view: ContactReportView):
        return f"ContactKeyMarket-{market.Id}-{contact_report_view.Id}"

    def business_key2(self):
        return f"ContactKeyMarket-{self.Market}-{self.ContactReportViewId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactReportViewId = sfid_mapping[self.ContactReportViewId]
        self.Market = sfid_mapping[self.Market]


class ContactKeyCustomer(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactReportViewId: str | None = Field(alias='Contact_Report_View__c', default=None, fk="ContactReportView.Id")
    CustomerId: str | None = Field(alias='Customer__c', default=None, fk="Account.Id")
    Excluded: bool = Field(alias='Excluded__c')

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Key_Customer__c'

    @classmethod
    def business_key(cls, customer: Account, contact_report_view: ContactReportView):
        return f"ContactKeyCustomer-{customer.Id}-{contact_report_view.Id}"

    def business_key2(self):
        return f"ContactKeyCustomer-{self.CustomerId}-{self.ContactReportViewId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactReportViewId = sfid_mapping[self.ContactReportViewId]
        self.CustomerId = sfid_mapping[self.CustomerId]


class ContactKeyTeam(BaseModel):
    model_config = ConfigDict(from_attributes=True)
    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactReportViewId: str | None = Field(alias='Contact_Report_View__c', default=None, fk="ContactReportView.Id")
    ContactId: str | None = Field(alias='Contact__c', default=None, fk="Contact.Id")
    TeamId: str | None = Field(alias='Team__c', default=None, fk="Team.Id")
    Excluded: bool | None = Field(alias='Excluded__c')

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Contact_Key_Team__c'

    @classmethod
    def business_key(cls, team: Team, contact: Contact, contact_report_view: ContactReportView):
        return f"ContactKeyTeam-{team.Id}-{contact.Id}-{contact_report_view.Id}"

    def business_key2(self):
        return f"ContactKeyTeam-{self.TeamId}-{self.ContactId}-{self.ContactReportViewId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactReportViewId = sfid_mapping[self.ContactReportViewId]
        self.ContactId = sfid_mapping[self.ContactId]
        self.TeamId = sfid_mapping[self.TeamId]


class AccountContactRelation(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    CustomerAccountId: str | None = Field(alias='AccountId', default=None, fk="Account.Id")
    ContactId: str | None = Field(alias='ContactId', default=None, fk="Contact.Id")
    ContactAccountId: str | None = Field(alias='Contacts_Account_ID__c', default=None)  # this isn't an actual salesforce fk - so we can't trust it
    SerialNonResponder: bool = Field(alias='Serial_Non_Responder__c', default=False)
    NonResponderCount: int | None = Field(alias='Non_Responder_Count__c', default=None)
    CustomerDBExternalContactId: str | None = Field(alias='Customer_DB_External_Contact_Id__c', default=None)
    StartDate: datetime.date | None = Field(alias='StartDate', default=None)

    @classmethod
    def sfapi_name(cls):
        return 'AccountContactRelation'

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('StartDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def business_key(cls, account: Account, contact: Contact):
        return f"AccountContactRelation-{account.Id}-{contact.Id}"

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'ContactAccountId', 'SerialNonResponder', 'LastModifiedDate', 'LastModifiedBy'}

    def business_key2(self):
        return f"AccountContactRelation-{self.CustomerAccountId}-{self.ContactId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.CustomerAccountId = sfid_mapping[self.CustomerAccountId]
        self.ContactId = sfid_mapping[self.ContactId]


class AccountConfirmationAudit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    CustomerSurveyId: str | None = Field(alias='Customer_Survey__c', default=None, fk="CustomerSurvey.Id")
    AccountManagerId: str | None = Field(alias='Account_Manager__c', default=None, fk="Contact.Id")
    ConfirmedDate: str | None = Field(alias='Confirmed_Date__c', default=None)
    ConfirmedAccountsCount: int | None = Field(alias='Confirmed_Accounts_Count__c', default=None)
    ConfirmedAccounts: str | None = Field(alias='Confirmed_Accounts__c', default=None)
    LatestSave: bool = Field(alias='Latest_Save__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('ConfirmedDate')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def business_key(cls, name: str):
        return f"AccountConfirmationAudit-{name}"

    def business_key2(self):
        return f"AccountConfirmationAudit-{self.CustomerSurveyId}"

    @classmethod
    def sfapi_name(cls):
        return 'Account_Confirmation_Audit__c'


class PanelConfirmationAudit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    SurveyClientId: str | None = Field(alias='Survey_Client__c', default=None, fk="SurveyClient.Id")
    PanelManagerId: str | None = Field(alias='Panel_Manager__c', default=None, fk="Contact.Id")
    ConfirmedDate: str | None = Field(alias='Confirmed_Date__c', default=None)
    ConfirmedPanelCount: int | None = Field(alias='Confirmed_Panel_Count__c', default=None)
    ConfirmedPanel: str | None = Field(alias='Confirmed_Panel__c', default=None)
    LatestSave: bool = Field(alias='Latest_Save__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('ConfirmedDate')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def business_key(cls, name: str):
        return f"PanelConfirmationAudit-{name}"

    def business_key2(self):
        return f"PanelConfirmationAudit-{self.SurveyClientId}"

    @classmethod
    def sfapi_name(cls):
        return 'Panel_Confirmation_Audit__c'


class SurveyRound(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    RoundDate: datetime.date | None = Field(alias='Round_Date__c', default=None)
    RoundEndDate: datetime.date | None = Field(alias='Round_End_Date__c', default=None)
    Stage: str | None = Field(alias='Stage__c', default=None)
    DisableExternalCommunication: bool = Field(alias='Disable_External_Communication__c', default=False)
    IsImported: bool = Field(alias='Is_Imported__c', default=False)
    SurveyType: str = Field(alias='Survey_Type__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('RoundDate', 'RoundEndDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Round__c'

    @classmethod
    def business_key(cls, round_date: datetime.date, survey_type: str, is_imported: bool = False):
        if survey_type not in VALID_SURVEY_TYPES:
            raise ValueError(f"Invalid survey type: {survey_type}. Must be one of {VALID_SURVEY_TYPES}")
        return f"SurveyRound-{round_date}-{is_imported}-{survey_type}"

    @classmethod
    def make_name(cls, round_date: datetime.date, survey_type: str):
        if survey_type not in VALID_SURVEY_TYPES:
            raise ValueError(f"Invalid survey type: {survey_type}. Must be one of {VALID_SURVEY_TYPES}")
        suffix = 'Barometer' if survey_type == 'Barometer' else ''
        quarter = (round_date.month - 1) // 3 + 1
        name = f'Q{quarter} {round_date.year} {suffix}'.strip()[:80]
        return name

    def business_key2(self):
        return f"SurveyRound-{self.RoundDate}-{self.IsImported}-{self.SurveyType}"

    def try_resolve_ids(self, sfid_mapping: dict):
        pass


class CustomerSurveyRound(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    AccountId: str | None = Field(alias='Account__c', default=None, fk="Account.Id")
    SurveyRoundId: str = Field(alias='Survey_Round__c', default=None, fk="SurveyRound.Id")
    SurveyTemplateDefinitionId: str | None = Field(alias="Survey_Template_Definition__c", default=None, fk="SurveyTemplateDefinition.Id")
    Stage: str | None = Field(alias='Stage__c', default=None)
    State: str | None = Field(alias='State__c', default=None)
    Progress: str | None = Field(alias='Progress__c', default=None)
    DisableExternalCommunication: bool = Field(alias='Disable_External_Communication__c', default=False)
    AccountUpdatesStartDate: datetime.date | None = Field(alias='Account_Updates_Start_Date__c', default=None)
    AccountUpdatesEndDate: datetime.date | None = Field(alias='Account_Updates_End_Date__c', default=None)
    PanelUpdatesStartDate: datetime.date | None = Field(alias='Panel_Updates_Start_Date__c', default=None)
    PanelUpdatesEndDate: datetime.date | None = Field(alias='Panel_Updates_End_Date__c', default=None)
    LiveSurveyStartDate: datetime.date | None = Field(alias='Live_Survey_Start_Date__c', default=None)
    LiveSurveyFirstRequest: datetime.date | None = Field(alias='Live_Survey_First_Request__c', default=None)
    LiveSurveySecondRequest: datetime.date | None = Field(alias='Live_Survey_Second_Request__c', default=None)
    LiveSurveyThirdRequest: datetime.date | None = Field(alias='Live_Survey_Third_Request__c', default=None)
    LiveSurveyFourthRequest: datetime.date | None = Field(alias='Live_Survey_Fourth_Request__c', default=None)
    LiveSurveyEndDate: datetime.date | None = Field(alias='Live_Survey_End_Date__c', default=None)
    ExcludeFromNorms: bool = Field(alias='Exclude_From_Norms__c', default=False)
    InsightsStartDate: datetime.date | None = Field(alias='Insights_Start_Date__c', default=None)
    SurveyType: str | None = Field(alias='Survey_Type__c', default=None)
    LinkedTRRSurveyId: str | None = Field(alias='Linked_TRR_Survey__c', default=None, fk="CustomerSurveyRound.Id")

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy', 'SurveyType'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('AccountUpdatesStartDate', 'AccountUpdatesEndDate', 'PanelUpdatesStartDate', 'PanelUpdatesEndDate', 'LiveSurveyStartDate', 'LiveSurveyFirstRequest', 'LiveSurveySecondRequest', 'LiveSurveyThirdRequest', 'LiveSurveyFourthRequest', 'LiveSurveyEndDate', 'InsightsStartDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Customer_Survey_Round__c'

    @classmethod
    def business_key(cls, survey_round: SurveyRound, account: Account):
        return f"CustomerSurveyRound-{survey_round.Id}-{account.Id}"

    @classmethod
    def make_name(cls, account: Account, survey_round: SurveyRound):
        return f"{survey_round.Name} {account.Name}"[:80]

    @classmethod
    def get_by_survey_round_id(cls, sf, survey_round_id: str):
        soql = format_soql(soql_fields(cls, "select FIELDS from Customer_Survey_Round__c where Survey_Round__c = {survey_round_id}"), survey_round_id=survey_round_id)
        for row in sf.query_all_iter(soql):
            yield CustomerSurveyRound.model_validate(row)

    def business_key2(self):
        return f"CustomerSurveyRound-{self.SurveyRoundId}-{self.AccountId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.AccountId = sfid_mapping[self.AccountId]
        self.SurveyRoundId = sfid_mapping[self.SurveyRoundId]


class CustomerSurvey(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    Email: str | None = Field(alias='Email__c', default=None)
    CustomerId: str | None = Field(alias='Customer__c', default=None, fk="Account.Id")
    ParentCustomerSurveyId: str | None = Field(alias='Parent_Customer_Survey__c', default=None, fk="CustomerSurvey.Id")
    CustomerSurveyRoundId: str | None = Field(alias='Customer_Survey_Round__c', default=None, fk="CustomerSurveyRound.Id")
    Stage: str | None = Field(alias='Stage__c', default=None)
    State: str | None = Field(alias='State__c', default=None)
    SurveyClientManagerId: str | None = Field(alias="Survey_Client_Manager__c", default=None, fk="Contact.Id")
    SurveyTemplateDefinitionId: str | None = Field(alias="Survey_Template_Definition__c", default=None, fk="SurveyTemplateDefinition.Id")
    CSVFilePath: str | None = Field(alias='CSV_File_Path__c', default=None)
    TeamId: str | None = Field(alias='Team__c', default=None, fk="Team.Id")
    #EmailConfirmYourAccounts: bool = Field(alias='Email_Confirm_Your_Accounts__c', default=False)
    #EmailConfirmYourAccountsReminder: bool = Field(alias='Email_Confirm_Your_Accounts_Reminder__c', default=False)
    SurveyClientCount: int | None = Field(alias='Survey_Client_Count__c', default=None)
    AccountsAutoConfirmed: bool = Field(alias='Accounts_Auto_Confirmed__c', default=False)
    #EmailLiveSurvey: bool = Field(alias='Email_Live_Survey__c', default=False)
    AccountUpdatesStartDate: datetime.date | None = Field(alias='Account_Updates_Start_Date__c', default=None)
    AccountUpdatesEndDate: datetime.date | None = Field(alias='Account_Updates_End_Date__c', default=None)
    PanelUpdatesStartDate: datetime.date | None = Field(alias='Panel_Updates_Start_Date__c', default=None)
    PanelUpdatesEndDate: datetime.date | None = Field(alias='Panel_Updates_End_Date__c', default=None)
    LiveSurveyStartDate: datetime.date | None = Field(alias='Live_Survey_Start_Date__c', default=None)
    LiveSurveyFirstRequest: datetime.date | None = Field(alias='Live_Survey_First_Request__c', default=None)
    LiveSurveySecondRequest: datetime.date | None = Field(alias='Live_Survey_Second_Request__c', default=None)
    LiveSurveyThirdRequest: datetime.date | None = Field(alias='Live_Survey_Third_Request__c', default=None)
    LiveSurveyFourthRequest: datetime.date | None = Field(alias='Live_Survey_Fourth_Request__c', default=None)
    LiveSurveyEndDate: datetime.date | None = Field(alias='Live_Survey_End_Date__c', default=None)
    DisableExternalCommunication: bool = Field(alias='Disable_External_Communication__c', default=False)
    CurrentRound: bool = Field(alias='Current_Round__c', default=False)
    PDTOwner: str | None = Field(alias='PDT_Owner__c', default=None)
    InsightsStartDate: datetime.date | None = Field(alias='Insights_Start_Date__c', default=None)
    SurveyType: str | None = Field(alias='Survey_Type__c', default=None)
    M124NoOfResponses: int | None = Field(alias='M1_24_No_of_Responses__c', default=None)
    M224NoOfResponses: int | None = Field(alias='M2_24_No_of_Responses__c', default=None)
    M324NoOfResponses: int | None = Field(alias='M3_24_No_of_Responses__c', default=None)
    M424NoOfResponses: int | None = Field(alias='M4_24_No_of_Responses__c', default=None)
    M1EndNoOfResponses: int | None = Field(alias='M1_End_No_of_Responses__c', default=None)
    M2EndNoOfResponses: int | None = Field(alias='M2_End_No_of_Responses__c', default=None)
    M3EndNoOfResponses: int | None = Field(alias='M3_End_No_of_Responses__c', default=None)
    NoOfResponses: int | None = Field(alias='No_of_Responses__c', default=None)
    CreatedById: str | None = Field(alias='CreatedById', default=None)
    CreatedBy: str | None = Field(alias='CreatedBy.Name', default=None)
    CreatedDate: datetime.datetime | None = Field(default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'CreatedById', 'CreatedBy', 'CreatedDate', 'LastModifiedDate', 'LastModifiedBy', 'CurrentRound', 'SurveyType'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('AccountUpdatesStartDate', 'AccountUpdatesEndDate', 'PanelUpdatesStartDate', 'PanelUpdatesEndDate', 'LiveSurveyStartDate', 'LiveSurveyFirstRequest', 'LiveSurveySecondRequest', 'LiveSurveyThirdRequest', 'LiveSurveyFourthRequest', 'LiveSurveyEndDate', 'InsightsStartDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Customer_Survey__c'

    @classmethod
    def business_key(cls, customer_survey_round: CustomerSurveyRound, customer: Account, team: Team | None = None):
        team_id = team.Id if team else None
        return f"CustomerSurvey-{customer_survey_round.Id}-{customer.Id}-{team_id}"

    @classmethod
    def make_name(cls, customer_survey_round: CustomerSurveyRound, customer: Account, team: Team | None = None):
        if team:
            return f"{customer_survey_round.Name} {customer.Name} {team.Name}"[:80]
        else:
            return f"{customer_survey_round.Name} {customer.Name}"[:80]

    @classmethod
    def get_by_customer_survey_round_id(cls, sf, customer_survey_round_id: str):
        soql = format_soql(soql_fields(cls, "select FIELDS from Customer_Survey__c where Customer_Survey_Round__c = {customer_survey_round_id}"), customer_survey_round_id=customer_survey_round_id)
        for row in sf.query_all_iter(soql):
            yield CustomerSurvey.model_validate(row)

    def business_key2(self):
        return f"CustomerSurvey-{self.CustomerSurveyRoundId}-{self.CustomerId}-{self.TeamId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.CustomerId = sfid_mapping[self.CustomerId]
        self.CustomerSurveyRoundId = sfid_mapping[self.CustomerSurveyRoundId]
        self.SurveyClientManagerId = sfid_mapping[self.SurveyClientManagerId]
        self.ParentCustomerSurveyId = sfid_mapping[self.ParentCustomerSurveyId]
        self.TeamId = sfid_mapping[self.TeamId]


class SurveyAccountManager(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactId: str | None = Field(alias='Account_Manager__c', default=None, fk="Contact.Id")
    CustomerSurveyId: str | None = Field(alias='Customer_Survey__c', default=None, fk="CustomerSurvey.Id")
    EmailConfirmYourAccounts: bool = Field(alias='Email_Confirm_Your_Accounts__c', default=False)
    EmailConfirmYourAccountsReminder: bool = Field(alias='Email_Confirm_Your_Accounts_Reminder__c', default=False)
    EmailLiveSurvey: bool = Field(alias='Email_Live_Survey__c', default=False)
    EmailMonitorResponseRate: bool = Field(alias='Email_Monitor_Response_Rate__c', default=False)
    EmailSurveyReadyToGo: bool = Field(alias='Email_Survey_Ready_To_Go__c', default=False)
    EmailSurveyClosed: bool = Field(alias='Email_Survey_Closed__c', default=False)
    HasResponded: bool = Field(alias='Has_Responded__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Account_Manager__c'

    @classmethod
    def business_key(cls, contact: Contact, customer_survey: CustomerSurvey):
        return f"SurveyAccountManager-{contact.Id}-{customer_survey.Id}"

    def business_key2(self):
        return f"SurveyAccountManager-{self.ContactId}-{self.CustomerSurveyId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactId = sfid_mapping[self.ContactId]
        self.CustomerSurveyId = sfid_mapping[self.CustomerSurveyId]


class SurveyClient(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    State: str | None = Field(alias='State__c', default=None)
    CustomerSurveyId: str | None = Field(alias='Customer_Survey__c', default=None, fk="CustomerSurvey.Id")
    CustomersClientId: str | None = Field(alias='Customers_Client__c', default=None, fk="Account.Id")
    CustomersClientName: str | None = Field(alias='Customers_Client_Name__c', default=None)
    SurveyName: str | None = Field(alias='Survey_Name__c', default=None)
    SurveyTemplateDefinitionId: str | None = Field(alias="Survey_Template_Definition__c", default=None, fk="SurveyTemplateDefinition.Id")
    CSVFilePath: str | None = Field(alias='CSV_File_Path__c', default=None)
    PanelConfirmedByDate: datetime.date | None = Field(alias='Panel_Confirmed_By_Date__c', default=None)
    PanelAutoConfirmed: bool = Field(alias='Panel_Auto_Confirmed__c', default=False)
    SignatoryId: str | None = Field(alias='Signatory__c', default=None, fk="Contact.Id")
    DuplicateAccountsDetected: bool = Field(alias='Duplicate_Accounts_Detected__c', default=False)
    CurrentRound: bool = Field(alias='Current_Round__c', default=False)
    LastSyncDate: datetime.datetime | None = Field(alias='Last_Sync_Date__c', default=None)
    RequiresPortaldbSync: bool = Field(alias='Requires_Portaldb_Sync__c', default=False)
    CurrencyIsoCode: str | None = Field(alias='CurrencyIsoCode', default=None)
    SurveyClientIncome: float | None = Field(alias='Survey_Client_Income__c', default=None)
    SurveyType: str | None = Field(alias='Survey_Type__c', default=None)
    NoOfSurveyMemberResponses: int | None = Field(alias='No_of_Survey_Member_Responses__c', default=None)
    NoOfSurveyMembers: int | None = Field(alias='No_of_Survey_Members__c', default=None)  
    PanelManagerCount: int | None = Field(alias='Panel_Manager_Count__c', default=None)
    PanelManagerRespondedCount: int | None = Field(alias='Panel_Manager_Responded_Count__c', default=None)
    CreatedById: str | None = Field(alias='CreatedById', default=None)
    CreatedBy: str | None = Field(alias='CreatedBy.Name', default=None)
    CreatedDate: datetime.datetime | None = Field(default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'CreatedById', 'CreatedBy', 'CreatedDate', 'LastModifiedDate', 'LastModifiedBy', 'CurrentRound', 'SurveyType'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('PanelConfirmedByDate')
    def serialize_date(self, dt: datetime.date, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Client__c'

    @classmethod
    def business_key(cls, customer_survey: CustomerSurvey, customers_client: Account):
        return f"SurveyClient-{customer_survey.Id}-{customers_client.Id}"

    @classmethod
    def make_name(cls, customer_survey: CustomerSurvey, customers_client: Account):
        return f"{customer_survey.Name} {customers_client.Name}"[:80][:80]

    @classmethod
    def get_by_customer_survey_id(cls, sf, customer_survey_id: str):
        soql = format_soql(soql_fields(cls, "select FIELDS from Survey_Client__c where Customer_Survey__c = {customer_survey_id}"), customer_survey_id=customer_survey_id)
        for row in sf.query_all_iter(soql):
            yield SurveyClient.model_validate(row)

    def business_key2(self):
        return f"SurveyClient-{self.CustomerSurveyId}-{self.CustomersClientId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.CustomerSurveyId = sfid_mapping[self.CustomerSurveyId]
        self.CustomersClientId = sfid_mapping[self.CustomersClientId]


class SurveyPanelManager(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    ContactId: str | None = Field(alias='Contact__c', default=None, fk="Contact.Id")
    SurveyClientId: str | None = Field(alias='Survey_Client__c', default=None, fk="SurveyClient.Id")
    FirstName: str | None = Field(alias='First_Name__c', default=None)
    LastName: str | None = Field(alias='Last_Name__c', default=None)
    EmailConfirmYourPanel: bool = Field(alias='Email_Confirm_Your_Panel__c', default=False)
    EmailConfirmYourPanelReminder: bool = Field(alias='Email_Confirm_Your_Panel_Reminder__c', default=False)
    EmailSurveyClosed: bool = Field(alias='Email_Survey_Closed__c', default=False)
    HasResponded: bool = Field(alias='Has_Responded__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Panel_Manager__c'

    @classmethod
    def business_key(cls, contact: Contact, survey_client: SurveyClient):
        return f"SurveyPanelManager-{contact.Id}-{survey_client.Id}"

    def business_key2(self):
        return f"SurveyPanelManager-{self.ContactId}-{self.SurveyClientId}"

    def try_resolve_ids(self, sfid_mapping: dict):
        self.ContactId = sfid_mapping[self.ContactId]
        self.SurveyClientId = sfid_mapping[self.SurveyClientId]


class SurveyPanelMember(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    SurveyClientId: str | None = Field(alias='Survey_Client__c', default=None, fk="SurveyClient.Id")
    ContactId: str | None = Field(alias='Contact__c', default=None, fk="Contact.Id")
    SurveyEmailTriggered: bool = Field(alias='Survey_Email_Triggered__c', default=False)
    SurveyEmailDelivered: bool = Field(alias='Survey_Email_Delivered__c', default=False)
    SurveyEmailOpened: bool = Field(alias='Survey_Email_Opened__c', default=False)
    SurveyEmailClicked: bool = Field(alias='Survey_Email_Clicked__c', default=False)
    SurveyEmailBounced: bool = Field(alias='Survey_Email_Bounced__c', default=False)
    SurveyEmailReminder1: bool = Field(alias='Survey_Email_Reminder_1__c', default=False)
    SurveyEmailReminder2: bool = Field(alias='Survey_Email_Reminder_2__c', default=False)
    SurveyEmailReminder3: bool = Field(alias='Survey_Email_Reminder_3__c', default=False)
    NonResponseCount: int | None = Field(alias='Non_Response_Count__c', default=None)
    ContactEmail: str | None = Field(alias='Contact_Email__c', default=None)
    ContactSeniority: str | None = Field(alias='Contact_Seniority__c', default=None)
    ContactTitle: str | None = Field(alias='Contact_Title__c', default=None)
    ContactDivision: str | None = Field(alias='Contact_Division__c', default=None)
    ContactJobFunction: str | None = Field(alias='Contact_Job_Function__c', default=None)
    ContactJobFunction2: str | None = Field(alias='Contact_Job_Function_2__c', default=None)
    ContactType: str | None = Field(alias='Contact_Type__c', default=None)
    HasResponded: bool | None = Field(alias='Has_Responded__c', default=False)
    SurveyTemplateDefinitionId: str | None = Field(alias="Survey_Template_Definition__c", default=None, fk="SurveyTemplateDefinition.Id")
    SurveyLink: str | None = Field(alias='Survey_Link__c', default=None)
    SurveyJsId: str | None = Field(alias='SurveyJsId__c', default=None)
    Responses: int | None = Field(alias='Responses__c', default=None)
    HubRowKey: str | None = Field(alias='Hub_Row_Key__c', default=None)
    SurveyPanelManager: str | None = Field(alias='Survey_Panel_Manager__c', default=None, fk="SurveyPanelManager.Id")
    New: bool | None = Field(alias='New__c', default=None)
    OptOut: bool | None = Field(alias='Opt_Out__c', default=None)
    OptOutDateTime: datetime.datetime | None = Field(alias='Opt_Out_DateTime__c', default=None)
    SerialNonResponder: bool | None = Field(alias='Serial_Non_Responder__c', default=None)
    SurveyType: str | None = Field(alias='Survey_Type__c', default=None)
    ViewedSurveyCount: int | None = Field(alias='Viewed_Survey_Count__c', default=None)
    SurveyEmailM2Delivered: bool = Field(alias='Survey_Email_Reminder_1_Delivered__c', default=False)
    SurveyEmailM2Bounced: bool = Field(alias='Survey_Email_Reminder_1_Bounced__c', default=False)
    SurveyEmailM3Delivered: bool = Field(alias='Survey_Email_Reminder_2_Delivered__c', default=False)
    SurveyEmailM3Bounced: bool = Field(alias='Survey_Email_Reminder_2_Bounced__c', default=False)
    SurveyEmailM4Delivered: bool = Field(alias='Survey_Email_Reminder_3_Delivered__c', default=False)
    SurveyEmailM4Bounced: bool = Field(alias='Survey_Email_Reminder_2_Bounced__c', default=False)
    CreatedById: str | None = Field(alias='CreatedById', default=None)
    CreatedBy: str | None = Field(alias='CreatedBy.Name', default=None)
    CreatedDate: datetime.datetime | None = Field(default=None)
    AccountStartYear: int | None = Field(alias='Account_Start_Year__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'CreatedById', 'CreatedBy', 'CreatedDate', 'LastModifiedDate', 'LastModifiedBy', 'HasResponded', 'SurveyLink', 'Responses', 'SurveyType'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('OptOutDateTime')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Panel_Member__c'

    @classmethod
    def business_key(cls, survey_client: SurveyClient, contact: Contact):
        return f"SurveyPanelMember-{survey_client.Id}-{contact.Id}"

    @classmethod
    def make_name(cls, contact: Contact):
        return f"{contact.Email}"[:80]

    @classmethod
    def get_by_survey_client_id(cls, sf, survey_client_id: str):
        soql = format_soql(soql_fields(cls, "select FIELDS from Survey_Panel_Member__c where Survey_Client__c = {survey_client_id}"), survey_client_id=survey_client_id)
        for row in sf.query_all_iter(soql):
            yield SurveyPanelMember.model_validate(row)

    def business_key2(self):
        return f"SurveyPanelMember-{self.SurveyClientId}-{self.ContactId}"

    def try_resolve_ids(self, sfid_mapping):
        self.SurveyClientId = sfid_mapping[self.SurveyClientId]
        self.ContactId = sfid_mapping[self.ContactId]


class SurveyResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    SurveyPanelMemberId: str | None = Field(alias='Survey_Panel_Member__c', default=None, fk="SurveyPanelMember.Id")
    ScoreQuestion: str | None = Field(alias='Score_Question__c', default=None)
    Score: int | None = Field(alias='Score__c', default=None)
    FeedbackQuestion: str | None = Field(alias='Feedback_Question__c', default=None)
    Feedback: str | None = Field(alias='Feedback__c', default=None)
    FeedbackTranslated: str | None = Field(alias='Feedback_Translated__c', default=None)
    Themes: str | None = Field(alias='Themes__c', default=None)
    ResponseDateTime: datetime.datetime | None = Field(alias='Response_Date_Time__c', default=None)
    IsExtraQuestion: bool = Field(alias='Is_Extra_Question__c', default=False)
    ScoreQuestionEn: str | None = Field(alias='Score_Question_EN__c', default=None)
    FeedbackQuestionEn: str | None = Field(alias='Feedback_Question_EN__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_serializer('ResponseDateTime')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Response__c'

    @classmethod
    def business_key(cls, survey_panel_member: SurveyPanelMember, score_question: str | None, feedback_question: str | None, is_extra_question: bool):
        if score_question is None:
            score_question = ''
        score_question = score_question.replace(' ','').replace('\n', '').lower()
        if feedback_question is None:
            feedback_question = ''
        feedback_question = feedback_question.replace(' ','').replace('\n', '').lower()
        return f"SurveyResponse-{survey_panel_member.Id}-{score_question}-{feedback_question}-{is_extra_question}"

    @classmethod
    def make_name(cls, survey_panel_member: SurveyPanelMember):
        return f"{survey_panel_member.Name}"[:80]

    @classmethod
    def get_by_survey_panel_member_id(cls, sf, survey_panel_member_id: str):
        soql = format_soql(soql_fields(cls, "select FIELDS from Survey_Response__c where Survey_Panel_Member__c = {survey_panel_member_id}"), survey_panel_member_id=survey_panel_member_id)
        for row in sf.query_all_iter(soql):
            yield SurveyResponse.model_validate(row)

    def business_key2(self):
        score_question = self.ScoreQuestion
        if score_question is None:
            score_question = ''
        score_question = score_question.replace(' ','').replace('\n', '').lower()

        feedback_question = self.FeedbackQuestion
        if feedback_question is None:
            feedback_question = ''
        feedback_question = feedback_question.replace(' ','').replace('\n', '').lower()

        return f"SurveyResponse-{self.SurveyPanelMemberId}-{score_question}-{feedback_question}-{self.IsExtraQuestion}"

    def try_resolve_ids(self, sfid_mapping):
        self.SurveyPanelMemberId = sfid_mapping[self.SurveyPanelMemberId]


class SurveyAccessAudit(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    SurveyPanelMemberId: str | None = Field(alias='Survey_Panel_Member__c', default=None, fk="SurveyPanelMember.Id")
    AccessDate: str | None = Field(alias='Access_Date__c', default=None)
    AccessAction: str | None = Field(alias='Access_Action__c', default=None)
    AccessedViaSalesforce: bool = Field(alias='Accessed_Via_Salesforce__c', default=False)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @field_serializer('AccessDate')
    def serialize_datetime(self, dt: datetime.datetime, _info):
        return dt.isoformat() if dt else None

    @classmethod
    def business_key(cls, name: str):
        return f"SurveyAccessAudit-{name}"

    def business_key2(self):
        return f"SurveyAccessAudit-{self.SurveyPanelMemberId}"

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Access_Audit__c'


class SurveyTemplateDefinition(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    Id: str
    LastModifiedDate: datetime.datetime | None = Field(default=None)
    LastModifiedBy: str | None = Field(alias='LastModifiedBy.Name', default=None)
    Name: str
    Questions: str | None = Field(alias='Questions__c', default=None)
    ZH_CN: str | None = Field(alias='ZH_CN__c', default=None)
    CS: str | None = Field(alias='CS__c', default=None)
    DA: str | None = Field(alias='DA__c', default=None)
    NL: str | None = Field(alias='NL__c', default=None)
    EN: str | None = Field(alias='EN__c', default=None)
    FI: str | None = Field(alias='FI__c', default=None)
    FR: str | None = Field(alias='FR__c', default=None)
    DE: str | None = Field(alias='DE__c', default=None)
    HU: str | None = Field(alias='HU__c', default=None)
    IT: str | None = Field(alias='IT__c', default=None)
    JA: str | None = Field(alias='JA__c', default=None)
    KO: str | None = Field(alias='KO__c', default=None)
    NO: str | None = Field(alias='NO__c', default=None)
    PL: str | None = Field(alias='PL__c', default=None)
    PT: str | None = Field(alias='PT__c', default=None)
    PT_BR: str | None = Field(alias='PT_BR__c', default=None)
    SK: str | None = Field(alias='SK__c', default=None)
    ES: str | None = Field(alias='ES__c', default=None)
    SV: str | None = Field(alias='SV__c', default=None)
    TH: str | None = Field(alias='TH__c', default=None)
    TR: str | None = Field(alias='TR__c', default=None)

    @classmethod
    def exclude_fields_to_sf(cls):
        return {'Id', 'LastModifiedDate', 'LastModifiedBy'}

    @field_validator('*', mode='before')
    def empty_str(value):
        return value if value != '' else None

    @classmethod
    def business_key(cls, name: str):
        return f"SurveyTemplateDefinition-{name}"

    def business_key2(self):
        return f"SurveyTemplateDefinition-{self.Name}"

    @classmethod
    def sfapi_name(cls):
        return 'Survey_Template_Definition__c'


def get_sf_connection(sf_instance=None, sf_client_id=None, sf_client_secret=None) -> Salesforce:
    if not sf_instance or not sf_client_id or not sf_client_secret:
        raise Exception('Missing Salesforce connection details')

    data = {
        'client_id': sf_client_id,
        'client_secret': sf_client_secret,
        'grant_type': 'client_credentials'
    }
    r = requests.post(f'https://{sf_instance}/services/oauth2/token', data=data)
    if not r.ok:
        print(r.json())
        r.raise_for_status()
    auth = r.json()
    sf = Salesforce(instance=sf_instance, session_id=auth['access_token'])

    # nasty bodge to make bulk2 work properly!
    bulk2._Bulk2Client.get_ingest_results = patched_bulk2_get_ingest_results
    return sf


def bulk_query(sf, query: str):
    with tempfile.TemporaryDirectory(ignore_cleanup_errors=True) as dir:
        for file in getattr(sf.bulk2, 'a_fake_object').download(query, dir):
            with open(file['file']) as f:
                csvin = csv.DictReader(f)
                for row in csvin:
                    yield row


# fixed version which correctly forces encoding to utf-8
def patched_bulk2_get_ingest_results(bulk2instance,
                        job_id: str,
                        results_type: str
                        ) -> str:
    """Get record results"""
    url = bulk2instance._construct_request_url(
        job_id,
        False
        ) + "/" + results_type
    headers = bulk2instance._get_headers(
        bulk2instance.JSON_CONTENT_TYPE,
        bulk2instance.CSV_CONTENT_TYPE
        )
    result = util.call_salesforce(
        url=url,
        method="GET",
        session=bulk2instance.session,
        headers=headers
        )
    result.encoding = 'UTF-8'  # this added here since sf returns UTF-8 encoded data, but doesn't explicitly say it, so requests assumes ISO-8859-1
    return result.text


def detect_bulk2_errors(record_status):
    for row in record_status['failedRecords']:
        print('Failed to insert', row)
    for row in record_status['unprocessedRecords']:
        print('Failed to insert', row)
    if record_status['unprocessedRecords'] or record_status['failedRecords']:
        raise Exception('Failed to process some records')


def bulk_insert(sf, object_name: str, records: list[dict]):
    statuses = getattr(sf.bulk2, object_name).insert(records=records)
    status_records = []
    for status in statuses:
        status_records.append(getattr(sf.bulk2, object_name).get_all_ingest_records(status['job_id']))
    return status_records


def bulk_update(sf, object_name: str, records: list[dict]):
    statuses = getattr(sf.bulk2, object_name).update(records=records)
    status_records = []
    for status in statuses:
        status_records.append(getattr(sf.bulk2, object_name).get_all_ingest_records(status['job_id']))
    return status_records


def bulk_delete(sf, object_name: str, records: list[str]):
    records = [{'Id': r} for r in records]
    with tempfile.NamedTemporaryFile(encoding='utf-8', mode='w') as f:
        csvout = csv.DictWriter(f, fieldnames=['Id'])
        csvout.writeheader()
        csvout.writerows(records)
        f.flush()

        statuses = getattr(sf.bulk2, object_name).delete(csv_file=f.name)
        status_records = []
        for status in statuses:
            status_records.append(getattr(sf.bulk2, object_name).get_all_ingest_records(status['job_id']))
        return status_records


def add_items_to_cache(sfid_mapping, sf_cache, items):
    for item in items:
        bk = item.business_key2()
        sfid_mapping[item.Id] = item.Id

        # if bk in sf_cache:
        #     print("Duplicate cache business key", bk, item, sf_cache[bk])
        sf_cache[bk] = item


def bulk_import_batch_to_sf(sf, sfid_mapping: dict[str, str] | None, sf_cache: dict[str, object], batch: list, sfapi_object):
    # print("Writing", sfapi_object.sfapi_name())
    # don't bother if there's nothing to do
    if not batch:
        return

    # resolve any temporary IDs in the objects in the batch.
    # if it fails during, something is wrong with the mapping (ie we have a temporary ID that we don't know the real ID for)
    if sfid_mapping is not None:
        for a in batch:
            a.try_resolve_ids(sfid_mapping)

    batch_temp_key_map = {a.business_key2(): a.Id for a in batch}
    exclude_fields_to_sf = sfapi_object.exclude_fields_to_sf()
    for record_status in bulk_insert(sf, sfapi_object.sfapi_name(), [a.model_dump(exclude=exclude_fields_to_sf, by_alias=True) for a in batch]):
        for successful_record in record_status['successfulRecords']:
            a = sfapi_object(Id=successful_record['sf__Id'], **successful_record)
            business_key = a.business_key2()

            # now, we need to update the cache with the new SF IDs mapping from the temporary ID to the actual id
            if sfid_mapping is not None:
                temp_id = batch_temp_key_map[business_key]
                sfid_mapping[temp_id] = a.Id
                sfid_mapping[a.Id] = a.Id
            sf_cache[business_key] = a

        detect_bulk2_errors(record_status)


def get_picklist_values(sf, objectApiName, fieldApiName, recordTypeId=None):
    if recordTypeId is None:
        url = f"ui-api/object-info/{objectApiName}"
        r = sf.restful(url)
        recordTypeId = list(r['recordTypeInfos'].keys())[0]

    url = f"ui-api/object-info/{objectApiName}/picklist-values/{recordTypeId}/{fieldApiName}"
    r = sf.restful(url)
    return set(html.unescape(x['value']) for x in r['values'])


def get_all(sf, sfobject, bulk=False, query_suffix='', is_export=False):
    soql = soql_fields(sfobject, f"select FIELDS from {sfobject.sfapi_name()} " + query_suffix, is_export)
    if bulk:
        rows = bulk_query(sf, soql)
    else:
        rows = sf.query_all_iter(soql)
    for row in rows:
        yield sfobject.model_validate(row)


def soql_fields(sfobject: BaseModel, soql: str, is_export=False):
    fields = set(sfobject.model_json_schema()['properties'].keys())
    if is_export and hasattr(sfobject, 'exclude_fields_to_export'):
        fields -= sfobject.exclude_fields_to_export()
    fields = ",".join(fields)
    return soql.replace('FIELDS', fields)


def get_by_id(sf: Salesforce, sfobject: BaseModel, id: str, query_suffix=""):
    return get_by_field(sf, sfobject, 'Id', id, query_suffix)


def get_by_field(sf: Salesforce, sfobject: BaseModel, fieldname: str, fieldvalue: str, query_suffix=""):
    soql = soql_fields(sfobject, f"select FIELDS from {sfobject.sfapi_name()} where {fieldname} = {{fieldvalue}} " + query_suffix)
    soql = format_soql(soql, fieldvalue=fieldvalue)
    for row in sf.query_all_iter(soql):
        return sfobject.model_validate(row)


def create_in_sf(sf: Salesforce, instance: BaseModel):
    raw_fields = instance.model_dump(exclude={'Id'}, by_alias=True)
    r = getattr(sf, instance.sfobject_name()).create(raw_fields)
    assert r['success'] is True
    i = copy.deepcopy(instance)
    i.Id = r['id']
    return i


def delete_by_id(sf: Salesforce, sfobject: BaseModel, id: str):
    getattr(sf, sfobject.sfapi_name()).delete(id)


def _get_bit(char) -> str:
    return '1' if str(char).isupper() else '0'

def _convert_chunk_to_bit(chunk: str) -> str:
    binary_repr = ''.join(reversed([_get_bit(char) for char in chunk]))
    return str(int(binary_repr, 2))

def convert_15_to_18_char_id(short: str) -> str:
    assert len(short) == 15

    suffix = ''
    # split id into three chunks
    chunks = [short[i:i+5] for i in range(0, len(short), 5)]
    # construct an array of A-Z and 0-5
    array = string.ascii_uppercase + string.digits[:6]

    for chunk in chunks:
        bit_char = _convert_chunk_to_bit(chunk)
        suffix += array[int(bit_char)]

    return short + suffix


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch
