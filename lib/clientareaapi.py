import requests
import cfgsettings
from typing import List
from pydantic import BaseModel


BASE_URL = 'https://np8nxruk6k.execute-api.eu-west-1.amazonaws.com/dev'
CRC_BASE_URL = 'https://api-preprod.crcaws.com'


class CustomersResponse(BaseModel):
    customer_survey: List = []  #  -> list of ones I can see
    previous_surveyed_accounts: List = []
    confirmed_accounts: List = []
    accounts: List = []  # accounts == accounts (coke)
    groups: List = []  # groups == crc clients (dentsu)
    panel_managers: List = []
    signatories: List = []
    survey_managers: List = []
    confirmed: bool = False


class PanelResponse(BaseModel):
    panel: List = []
    accounts: List = [] # -- only accounts for survey clients I can see
    contacts: List = []
    confirmed: bool = False
    confirmed_panel: List = []


class RobbieAPI:

    def __init__(self):
        self.access_token = None

    def login(self, username, password):
        url = f'{CRC_BASE_URL}/auth/login'
        data = {
            'email': username,
            'password': password,
        }
        response = requests.post(url, json=data)
        response.raise_for_status()
        self.access_token = response.json()['data']['accessToken']

    def headers(self, email):
        if not self.access_token:
            raise Exception('No access token')
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'X-User-Email': email,
        }
        return headers

    def get_accounts(self, email):
        url = f'{BASE_URL}/accounts'
        response = requests.get(url, headers=self.headers(email))
        response.raise_for_status()
        return response.json()

    def confirm_accounts(self, email, data):
        url = f'{BASE_URL}/accounts'
        response = requests.post(url, headers=self.headers(email), json=data)
        response.raise_for_status()
        return response.json()

# POST: /accounts
# {
#   'a03J9000001IeHSIA0' (customer survey id): [{
#                 "Id" : "001J9000003vwSTIAY",
#                 "Name" : "LargeClientA-1-1-1-1Account5",
#                 "ParentId" : null,
#                 "RecordTypeId" : "0128e000000S0k5AAC",
#                 "Round_Frequency__c" : "1",
#                 "Customer_Status__c" : null,
#                 "Current_Round__c" : false,
#                 "Consultant__c" : null,
#                 "Valid_Email_Domains__c" : "clientrelationship.com",
#                 "Customer_Survey__c" : "a03J9000001IeHSIA0",
#                 "GroupId" : "001J9000003vwhBIAQ",
#                 "Group" : "LargeClientA-1-1-1-1",
#                 "Panel_Manager" : "panelmanager  panelmanager+LargeClientA-1-1-1-1",
#                 "Panel_Manager_Id" : "003J9000003QJOWIA4",
#                 "Signatory" : "signatory  signatory+LargeClientA-1-1-1-1",
#                 "Signatory_Id" : "003J9000003QJMqIAO",
# }]
# }

    def get_panel(self, email):
        url = f'{BASE_URL}/panel'
        response = requests.get(url, headers=self.headers(email))
        return response.json()

# RESPONSE -->
# class PanelResponse(BaseModel):
#     panel: List = []
#     accounts: List = []  -- only accounts for survey clients I can see
#     contacts: List = []
#     confirmed: bool = False
#     confirmed_panel: List = []

    def confirm_panel(self, email, data):
        url = f'{BASE_URL}/panel'
        response = requests.post(url, headers=self.headers(email), json=data)
        response.raise_for_status()
        return response.json()

# POST: /panel
# {
#   'a06J9000000wLmHIAU'  (survey client id): [{
#                 "Id" : "003J9000003QHG1IAO",
#                 "FirstName" : "responder",
#                 "LastName" : "responder+LargeClientA-1-1-1-1Account5_r1",
#                 "Email" : "<EMAIL>",
#                 "AccountId" : "001J9000003vwSTIAY",
#                 "Contact_Type__c" : "Primary Contact",
#                 "Title" : "Media Planner",
#                 "Panel_Manager__c" : false,
#                 "Accounts_Manager__c" : false,
#                 "Signatory__c" : false,
#                 "Customer_Account__c" : "LargeClientA-1-1-1-1",
#                 "Customers_Client_Account__c" : "LargeClientA-1-1-1-1Account5",
#                 "InternalMappingId" : "a03J9000001IeHSIA0",
#                 "Survey_Client__c" : "a06J9000000wLmHIAU",
#                 "Name" : "responder  responder+LargeClientA-1-1-1-1Account5_r1"
# }]
# }



    # def create_or_update_template(self, subject, text, html, template_id=None):
    #     data = {
    #         'subject': subject,
    #         'text': text,
    #         'html': html,
    #     }
    #     if template_id is None:
    #         url = f'{BASE_URL}/email/template'
    #         response = requests.post(url, json=data, headers=self.headers())
    #     else:
    #         url = f'{BASE_URL}/email/template/{template_id}'
    #         response = requests.patch(url, json=data, headers=self.headers())
    #     response.raise_for_status()
    #     return response.json()['data']


    # def delete_template(self, template_id):
    #     url = f'{BASE_URL}/email/template/{template_id}'
    #     response = requests.delete(url, headers=self.headers())
    #     response.raise_for_status()


    # def send_email(self, template_id, destination_address, source_address, template_data):
    #     data = {
    #         'templateId':  template_id,
    #         # 'emailTriggerName': '',
    #         'destinationAddress': destination_address,
    #         'sourceAddress': source_address,
    #         'templateData': template_data,
    #     }
    #     url = f'{BASE_URL}/email/send'
    #     response = requests.post(url, json=data, headers=self.headers())
    #     response.raise_for_status()
    #     return response.json()['data']


    # def get_email_status(self, email_send_id):
    #     url = f'{BASE_URL}/email/send/{email_send_id}'
    #     response = requests.get(url, headers=self.headers())
    #     response.raise_for_status()
    #     return response.json()


# if __name__ == '__main__':
#     email = Email()
#     email.login(cfgsettings.CRCAPI_USER, cfgsettings.CRCAPI_PASSWORD)


#     # 01906ed0-d5d9-7f7e-886f-d2927a93da57
#     # print(email.get_template('01906ed0-d5d9-7f7e-886f-d2927a93da57'))

#     # print(email.get_email_status('cc051f14-280e-44ef-824a-06ad7a86eab4'))

#     email_send_id = email.send_email('01906ed0-d5d9-7f7e-886f-d2927a93da57', '<EMAIL>', '<EMAIL>', '{}')
#     # # email_send_id = email.send_email('01906ed0-d5d9-7f7e-886f-d2927a93da57', '<EMAIL>', '<EMAIL>', '')
#     print(email_send_id)
#     print(email.get_email_status(email_send_id))

# # cc051f14-280e-44ef-824a-06ad7a86eab4

#     # template_id = email.create_or_update_template("Please choose clients for survey", "Please choose clients for survey", "Please choose clients for survey")
#     # print(template_id)


if __name__ == '__main__':
    robbie = RobbieAPI()
    robbie.login(cfgsettings.CRCAPI_USER, cfgsettings.CRCAPI_PASSWORD)

    print(robbie.get_accounts('<EMAIL>'))
