import csv
import csv
import codecs
import sys

COLS = ['srcrow', 'feedback_id', 'langdetect_lang', 'text', 'crc_translation', 'crc_lang', 'hc_google_translation', 'hc_google_lang', 'amazon_translation', 'amazon_lang', 'deepl_translation', 'deepl_lang']

def main(files):
    seen_feedback_ids = set()

    # merge files together
    with open('translated.csv', 'w') as outf:
        outf.write(codecs.BOM_UTF8.decode('utf-8'))
        csvout = csv.DictWriter(outf, fieldnames=COLS)
        csvout.writeheader()

        for file in files:
            with open(file, 'r', encoding='utf-8-sig') as inf:

                csvin = csv.DictReader(inf)

                for row in csvin:
                    if row['feedback_id'] in seen_feedback_ids:
                        print(f"Skipping duplicate feedback_id {row['feedback_id']}")
                        continue
                    seen_feedback_ids.add(row['feedback_id'])

                    csvout.writerow(row)

    # find any missing rows which got dropped somehow
    with open('missing.csv', 'w') as outf:
        outf.write(codecs.BOM_UTF8.decode('utf-8'))
        csvout = csv.writer(outf)

        with open('/home/<USER>/dev/crc-data/hypercube_feedback_translation_2024-07-02.csv', 'r') as inf:
            csvin = csv.reader(inf)
            for row in csvin:
                if row[0] not in seen_feedback_ids:
                    csvout.writerow(row)


if __name__ == '__main__':
    import sys
    main(sys.argv[1:])
