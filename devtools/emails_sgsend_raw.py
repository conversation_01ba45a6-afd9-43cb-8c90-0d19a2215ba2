from typing import Any
from io import BytesIO
import base64
from sendgrid import Send<PERSON>ridAP<PERSON><PERSON>
from sendgrid.helpers.mail import Mail, Attachment, CustomArg
from lib.settings import settings
import argparse
import json
import datetime
from pydantic import BaseModel, EmailStr
import boto3
from lib import portaldbapi
import jinja2


DEFAULT_FROM_NAME = "VerityRI"


class Email(BaseModel):
    from_email: EmailStr
    from_name: str|None = None
    to_email: EmailStr
    template_id: str
    template_data: dict[str, str|None]|None = {}
    custom_args: dict[str, str|None]|None = {}
    sent_at: int|None = None


def get_attachment_from_s3(attachment: str, s3_bucket: str) -> Attachment:
    attachment_dict: dict[str, str] = json.loads(attachment)

    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=s3_bucket, Key=attachment_dict['key'])
    response_body = response['Body'].read()

    with By<PERSON><PERSON>(response_body) as f:
        content = f.read()

    return Attachment(
        disposition=attachment_dict['disposition'],
        file_name=attachment_dict['key'],
        file_type=attachment_dict['file_type'],
        file_content=base64.b64encode(content).decode(),
        content_id=attachment_dict['content_id']
    )


def _get_template(template_name: str, template_ext: str):
    with open(f'emailtemplates/{template_name}.{template_ext}', 'r') as file:
        return jinja2.Template(file.read())


def send_email(sg,
                from_email: str,
                to_email: str,
                template_name:  str,
                template_data: str,
                custom_args: str,
                banner_attachment: str|None,
                signature_attachment: str|None,
                send_at: str|None,
                from_name: str|None,
                token: str|None,
                allow_nonvaul: bool|None) -> str:
    if not allow_nonvaul:
        assert to_email.endswith('@vaullabs.com')

    # convert template data to dict
    template_data_dict: dict[str, str] = json.loads(template_data)

    for k, v in template_data_dict.items():
        if v.startswith('https://'):
            template_data_dict[k] = v.replace('//', '/')

    # convert custom args to dict
    custom_args_dict: dict[str, str] = json.loads(custom_args)

    # convert send_at to datetime object (if provided)
    if send_at:
        send_at: datetime.datetime = datetime.datetime.strptime(send_at, '%Y-%m-%d %H:%M:%S')
        send_at: int = int(send_at.timestamp())

    # set default from name if not provided
    if not from_name:
        from_name = DEFAULT_FROM_NAME

    # build sender string
    sender = f"{from_name} <{from_email}>"

    # render the body
    body_html = _get_template(template_name, 'html').render(**template_data_dict).strip()
    body_text = _get_template(template_name, 'txt').render(**template_data_dict).strip()
    subject = _get_template(template_name, 'subject').render(**template_data_dict).strip()

    # tracking token for spam testing
    if token:
        body_text += f" {token}"

    # create instance of Email for validation
    email: Email = Email(
        from_email=from_email,
        from_name=from_name,
        to_email=to_email,
        body_html=body_html,
        body_text=body_text,
        template_data=template_data_dict,
        custom_args=custom_args_dict,
        sent_at=send_at,
        template_id='RAW',
    )

    # define initial SG message
    message: Mail = Mail(
        from_email=sender,
        to_emails=[to_email],
        subject=subject,
        html_content=body_html,
    )
    if body_text:
        message.add_content(body_text, 'text/plain')

    # attach attachment to the message (if provided)
    attachments: list[Attachment] = []

    if banner_attachment:
        try:
            banner = get_attachment_from_s3(banner_attachment, settings.SF_BANNERS_BUCKET)
            attachments.append(banner)
        except Exception as e:
            print(f"Error fetching banner attachment from S3: {e}")
            raise Exception('Error fetching banner attachment from S3')

    if signature_attachment:
        try:
            signature = get_attachment_from_s3(signature_attachment, settings.SF_SIGNATURES_BUCKET)
            attachments.append(signature)
        except Exception as e:
            print(f"Error fetching signature attachment from S3: {e}")
            raise Exception('Error fetching signature attachment from S3')

    # if we have any attachments, add them to the mail
    if attachments:
        message.attachment = attachments

    # attach send_at to the message (if provided)
    if send_at:
        message.send_at = send_at

    # if we have custom args, append them to the message
    if email.custom_args:
        metadata: list[CustomArg] = []
        for key, value in email.custom_args.items():
            metadata.append(CustomArg(key=key, value=value))
        message.custom_arg = metadata

    # attempt to send the email
    status_code:int = 0
    try:
        response = sg.send(message)
        status_code, body, headers = response.status_code, response.body, response.headers
        print(f"Response status_code: {status_code}")
        print(f"Recipient: {to_email}")
    except Exception as e:
       print("Error: {0}".format(e))


def main(original_recipient: str, to_emails: str, template_name: str, token: str = None, allow_nonvaul: bool = False) -> None:
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()

    scheduledemails_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION]

    to_emails = to_emails.split(',')

    q = {
        'recipient': original_recipient,
        'deleted': {'$exists': False}
    }
    for doc in scheduledemails_collection.find(q):
        for to_email in to_emails:
            print(f"Sending email to {to_email} for {doc['_id']}")

            # get other gubbins
            template_data = json.dumps(doc.get('template_data', {}))
            banner_attachment = None
            if doc.get('banner_attachment'):
                banner_attachment = json.dumps(doc['banner_attachment'])
            signature_attachment = None
            if doc.get('signature_attachment'):
                signature_attachment = json.dumps(doc['signature_attachment'])

            send_email(sg,
                       doc.get('sender_email'),
                       to_email,
                       template_name,
                       template_data,
                       '{}',   # never supply custom_args to prevent confusing the real system tracking
                       banner_attachment,
                       signature_attachment,
                       None, # always send now
                       doc.get('sender_name'),
                       token=token,
                       allow_nonvaul=allow_nonvaul)

        break


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('template_name')
    ap.add_argument('original_recipient')
    ap.add_argument('to_emails')
    ap.add_argument('--token')
    ap.add_argument('--allow-nonvaul', action='store_true', help='Allow sending to non-vaullabs.com email addresses')
    args = ap.parse_args()

    main(args.original_recipient, args.to_emails, args.template_name, args.token, args.allow_nonvaul)
