import lib.portaldbapi as portaldbapi
import argparse


def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    for confirm_account in confirm_accounts.find({'sfsync_date': None, 'Id': 'a03WT00000FodmwYAB'}):
        top_level_accounts = {a['account_id']: a for a in confirm_account.get('accounts', [])}

        confirmed_accounts = confirm_account.get('confirmed_accounts', [])
        if not confirmed_accounts:
            continue

        accounts_added_by = {}
        for caccount in reversed(confirmed_accounts):
            for added_deets in caccount.get('stats', {}).get('added', []):
                added_account_id = added_deets['id']
                top_level_account = top_level_accounts.get(added_account_id)

                # if the account exists and is already in round, skip it
                if top_level_account is not None and top_level_account.get('in_round'):
                    continue

                # only record the first one
                if added_account_id not in accounts_added_by:
                    accounts_added_by[added_account_id] = caccount['confirm_user_email']

        update = {}
        for caccount_idx, caccount in enumerate(confirmed_accounts):
            for account_idx, account in enumerate(caccount.get('accounts', [])):
                if account['account_id'] in accounts_added_by:
                    update[f"confirmed_accounts.{caccount_idx}.accounts.{account_idx}.added_by_contact"] = accounts_added_by[account['account_id']]

        for account_idx, account in enumerate(confirm_account.get('confirmed_accounts_last_save_all_accounts', [])):
            if account['account_id'] in accounts_added_by:
                update[f"confirmed_accounts_last_save_all_accounts.{account_idx}.added_by_contact"] = accounts_added_by[account['account_id']]

        # stamp = datetime.datetime.now()
        q = {'Id': confirm_account['Id']}
        update = {'$set': update}
        print(q, update)
        confirm_accounts.update_one(q, update)

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
