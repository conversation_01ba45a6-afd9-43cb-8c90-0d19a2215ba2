import csv
from langchain_openai import ChatOpenAI
from langchain_core.messages import ToolMessage, AIMessage, HumanMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from dotenv import load_dotenv

load_dotenv()

PROMPT = """
The following is a Jira ticket from a survey system for advertising companies.

The reporters do not have a good understanding of the system.

Identify which of the following themes the ticket is about:
* Bugs
* Bugs - SRP
* UX Issues
* Configuration
* New Feature
* Historical Data Migration Updates
* Data Import Request
* Client Mistake or Client Request
* Training
* Unknown

If you don't know, do say "Unknown" - don't attempt to guess.

Some common classifications to use:
* If the phrase "ability to" are included, it's likely going to be a new feature.
* If the phrase "client has asked" or "client wants to change" or "client wants to update" or "client wants to confirm or unconfirm" or "client made a mistake" show up then classify as client mistake.
* Permissions are likely going to be configuration issues.
* Phrases that include "historical data" are likely going to be Historical Data Migration updates.
* If the ticket mentions only salesforce but not client area or dashboards, then it's not going to be a UX Issue.
* If the ticket is a Bug and mentions "reportingplatform", it is a "Bugs - SRP".
* Training should never be used as a theme, except for the following cases:
    - If the ticket is asking for clarification on how something works or where they should go to for information or or "if it's ok to do something" and things like that, then they should be marked as Training.

The result must be a json structure as follows:
{
  "theme": <one of the themes as above>,
  "analysis": <a brief analysis of the ticket>
}
"""
# * Natalie Chadwick is extremely bad at writing tickets.

llm = ChatOpenAI(model="gpt-4o")
parser = JsonOutputParser()


inf = open('/home/<USER>/dev/crc-data/Jira Export CSV (all fields) 20250305095238.csv')
incsvdata = csv.reader(inf)
header_row = next(incsvdata)

outf = open('result.csv', 'w')
outcsvdata = csv.DictWriter(outf,
                            fieldnames=['Issue key',
                                        'Summary',
                                        'Description',
                                        'Reporter',
                                        'Custom field (Steps to recreate)',
                                        'Custom field (Expected Outcome)',
                                        'Custom field (Platform Area)',
                                        'Custom field (Journey Stage)',
                                        'Comment',
                                        'Status',
                                        'theme',
                                        'analysis'])
outcsvdata.writeheader()

def get_column_multivalue(row, header_row, column_name, multiple=False):
    values = []
    for i, header in enumerate(header_row):
        if header == column_name:
            v = row[i].strip()
            if v:
                if not multiple:
                    return v
                else:
                    values.append(v)

    return values

count = 0
for row in incsvdata:
    issue_id = get_column_multivalue(row, header_row, 'Issue key')
    summary = get_column_multivalue(row, header_row, 'Summary')
    description = get_column_multivalue(row, header_row, 'Description')
    reporter = get_column_multivalue(row, header_row, 'Reporter')
    recreate = get_column_multivalue(row, header_row, 'Custom field (Steps to recreate)')
    expected_outcome = get_column_multivalue(row, header_row, 'Custom field (Expected Outcome)')
    platform_area = get_column_multivalue(row, header_row, 'Custom field (Platform Area)')
    journey_stage = get_column_multivalue(row, header_row, 'Custom field (Journey Stage)')
    comments = get_column_multivalue(row, header_row, 'Comment', multiple=True)
    status = get_column_multivalue(row, header_row, 'Status')

    message = f"""
{PROMPT}
---
Summary: {summary}
Description: {description}
Reporter: {reporter}
How to Recreate: {recreate}
Expected Outcome: {expected_outcome}
Platform Area: {platform_area}
Process Stage: {journey_stage}
Comments: {"\n".join(comments)}
Ticket Status: {status}
"""

    in_messages = [HumanMessage(message)]
    out_message = llm.invoke(in_messages)
    result = parser.parse(out_message.content)
    result['Issue key'] = issue_id
    result['Summary'] = summary
    result['Description'] = description
    result['Reporter'] = reporter
    result['Custom field (Steps to recreate)'] = recreate
    result['Custom field (Expected Outcome)'] = expected_outcome
    result['Custom field (Platform Area)'] = platform_area
    result['Custom field (Journey Stage)'] = journey_stage
    result['Comment'] = "\n".join(comments)
    result['Status'] = status

    outcsvdata.writerow(result)

    # if count > 50:
    #     break
    count += 1
