import csv
import os
import openpyxl
import sys


filename = sys.argv[1]
pathname, leafname = os.path.split(filename)
leafnamestub, ext = os.path.splitext(leafname)

wb = openpyxl.open(filename, read_only=True)
for ws in wb.worksheets:
    ws_filename = os.path.join(pathname, leafname + '.' + ws.title.lower() + '.csv')
    with open(ws_filename, "w", encoding="utf-8") as f:
        writer = csv.writer(f, dialect='excel')
        for row in ws.iter_rows(values_only=True):
            outrow = []
            for cell in row:
                if cell is None:
                    outrow.append(None)

                elif isinstance(cell, float):
                    if cell == int(cell):
                        outrow.append(str(int(cell)))
                    else:
                        outrow.append(cell)

                else:
                    outrow.append(str(cell))

            writer.writerow(outrow)
