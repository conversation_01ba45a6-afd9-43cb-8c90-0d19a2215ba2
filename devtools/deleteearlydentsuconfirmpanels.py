import lib.portald<PERSON>pi as portaldbapi
import argparse


DENTSU_Q2_2025_CSR_ID = 'a02WT00000AhrWvYAJ'
CS_IDS = [
'a03WT00000FodleYAB',
'a03WT00000Fodo8YAB',
'a03WT00000G3t3bYAB',
'a03WT00000FoYgrYAF',
'a03WT00000GJYT5YAP',
'a03WT00000FodjvYAB',
'a03WT00000FoYgsYAF',
'a03WT00000FodkeYAB',
'a03WT00000FodkfYAB',
'a03WT00000FoYfrYAF',
'a03WT00000FoYfsYAF',
'a03WT00000FoYftYAF',
'a03WT00000FoYfuYAF',
'a03WT00000FoYgKYAV',
'a03WT00000FodnvYAB',
'a03WT00000FodkgYAB',
'a03WT00000FodnAYAR',
'a03WT00000FodnBYAR',
'a03WT00000FodnCYAR',
'a03WT00000FodnDYAR',
'a03WT00000FodncYAB',
'a03WT00000FodnXYAR',
'a03WT00000FodnEYAR',
'a03WT00000FodnaYAB',
'a03WT00000FodnbYAB',
'a03WT00000FodndYAB',
'a03WT00000FodneYAB',
'a03WT00000FodngYAB',
'a03WT00000FodnhYAB',
'a03WT00000FodnYYAR',
'a03WT00000FodnZYAR',
'a03WT00000FodnfYAB',
'a03WT00000FodkhYAB',
'a03WT00000Fodm0YAB',
'a03WT00000Fodm1YAB',
'a03WT00000Fodm4YAB',
'a03WT00000Fodm8YAB',
'a03WT00000Fodm9YAB',
'a03WT00000FodmCYAR',
'a03WT00000FodmFYAR',
'a03WT00000FodmGYAR',
'a03WT00000Fodm2YAB',
'a03WT00000Fodm3YAB',
'a03WT00000Fodm5YAB',
'a03WT00000Fodm6YAB',
'a03WT00000FodmAYAR',
'a03WT00000FodmBYAR',
'a03WT00000FodmDYAR',
'a03WT00000FodmEYAR',
'a03WT00000FodmHYAR',
'a03WT00000FodmiYAB',
'a03WT00000FoYgBYAV',
'a03WT00000FoYgCYAV',
'a03WT00000FoYgDYAV',
'a03WT00000FoYgEYAV',
'a03WT00000FoYgFYAV',
'a03WT00000FoYgGYAV',
'a03WT00000FoYgHYAV',
'a03WT00000FoYgIYAV',
'a03WT00000FoYgJYAV',
'a03WT00000FoYgdYAF',
'a03WT00000FoYgeYAF',
'a03WT00000FoYgfYAF',
'a03WT00000FoYggYAF',
'a03WT00000FoYghYAF',
'a03WT00000FoYgiYAF',
'a03WT00000FoYgjYAF',
'a03WT00000FoYgkYAF',
'a03WT00000FoYglYAF',
'a03WT00000FoYgmYAF',
'a03WT00000FoYgnYAF',
'a03WT00000FoYgoYAF',
'a03WT00000FoYgpYAF',
'a03WT00000FoYgqYAF',
'a03WT00000FoYgvYAF',
'a03WT00000FoYgwYAF',
'a03WT00000FoYgxYAF',
'a03WT00000FoYgtYAF',
'a03WT00000FoYguYAF',
'a03WT00000FoYgyYAF',
'a03WT00000FodjtYAB',
'a03WT00000FodjuYAB',
'a03WT00000FodnwYAB',
'a03WT00000FodnRYAR',
'a03WT00000FodnOYAR',
'a03WT00000FodnPYAR',
'a03WT00000FodnQYAR',
'a03WT00000FodnTYAR',
'a03WT00000FodnUYAR',
'a03WT00000FoYgLYAV',
'a03WT00000FoYgMYAV',
'a03WT00000FoYgNYAV',
'a03WT00000FoYgOYAV',
'a03WT00000FoYgPYAV',
'a03WT00000FoYgQYAV',
'a03WT00000FoYgRYAV',
'a03WT00000FoYgSYAV',
'a03WT00000FoYgTYAV',
'a03WT00000FoYgUYAV',
'a03WT00000FoYgVYAV',
'a03WT00000FoYgWYAV',
'a03WT00000FoYgXYAV',
'a03WT00000FoYgYYAV',
'a03WT00000FoYgZYAV',
'a03WT00000FoYgaYAF',
'a03WT00000FoYgbYAF',
'a03WT00000FoYgcYAF',
'a03WT00000FoYfiYAF',
'a03WT00000FodkkYAB',
'a03WT00000FodklYAB',
'a03WT00000FodkmYAB',
'a03WT00000FodknYAB',
'a03WT00000FodkoYAB',
'a03WT00000FodkpYAB',
'a03WT00000FodlSYAR',
'a03WT00000FodniYAB',
'a03WT00000FodnWYAR',
'a03WT00000FoYfcYAF',
'a03WT00000FoYfaYAF',
'a03WT00000FoYfbYAF',
'a03WT00000FoYfoYAF',
'a03WT00000FoYfpYAF',
'a03WT00000FoYfqYAF',
'a03WT00000FoYg2YAF',
'a03WT00000FoYg3YAF',
'a03WT00000FoYg4YAF',
'a03WT00000FoYg5YAF',
'a03WT00000FoYg6YAF',
'a03WT00000FoYg7YAF',
'a03WT00000FoYg8YAF',
'a03WT00000FoYg9YAF',
'a03WT00000FoYgAYAV',
'a03WT00000FodmmYAB',
'a03WT00000FodmnYAB',
'a03WT00000FodmoYAB',
'a03WT00000FodmpYAB',
'a03WT00000FodmqYAB',
'a03WT00000FodmrYAB',
'a03WT00000FodmsYAB',
'a03WT00000FodnjYAB',
'a03WT00000FodnkYAB',
'a03WT00000FodnlYAB',
'a03WT00000FodnmYAB',
'a03WT00000FodnnYAB',
'a03WT00000FodnoYAB',
'a03WT00000FodnxYAB',
'a03WT00000FodnyYAB',
'a03WT00000Fodl9YAB',
'a03WT00000FodlAYAR',
'a03WT00000FodlQYAR',
'a03WT00000FodlRYAR',
'a03WT00000FodnpYAB',
'a03WT00000FodnzYAB',
'a03WT00000Fodo0YAB',
'a03WT00000Fodo1YAB',
'a03WT00000Fodo2YAB',
'a03WT00000FodkrYAB',
'a03WT00000FodktYAB',
'a03WT00000FodkuYAB',
'a03WT00000FodkvYAB',
'a03WT00000FodkwYAB',
'a03WT00000FodkxYAB',
'a03WT00000FodkyYAB',
'a03WT00000FodkzYAB',
'a03WT00000FoYfmYAF',
'a03WT00000FoYfnYAF',
'a03WT00000FoYfjYAF',
'a03WT00000FoYfkYAF',
'a03WT00000FoYflYAF',
'a03WT00000FodjwYAB',
'a03WT00000FodkZYAR',
'a03WT00000Fodk1YAB',
'a03WT00000FodkUYAR',
'a03WT00000FodkVYAR',
'a03WT00000FodkWYAR',
'a03WT00000FodkXYAR',
'a03WT00000FodkYYAR',
'a03WT00000FodkaYAB',
'a03WT00000FodkbYAB',
'a03WT00000FodlVYAR',
'a03WT00000FodlWYAR',
'a03WT00000FodlXYAR',
'a03WT00000FodlYYAR',
'a03WT00000FodlZYAR',
'a03WT00000FodlaYAB',
'a03WT00000FodlbYAB',
'a03WT00000FodldYAB',
'a03WT00000FodlfYAB',
'a03WT00000FodlqYAB',
'a03WT00000FodlhYAB',
'a03WT00000FodliYAB',
'a03WT00000FodljYAB',
'a03WT00000FodlkYAB',
'a03WT00000FodllYAB',
'a03WT00000FodlmYAB',
'a03WT00000FodlgYAB',
'a03WT00000FodlnYAB',
'a03WT00000FodloYAB',
'a03WT00000FodlpYAB',
'a03WT00000FodlrYAB',
'a03WT00000FodlsYAB',
'a03WT00000FodltYAB',
'a03WT00000FodluYAB',
'a03WT00000FodlvYAB',
'a03WT00000FodlwYAB',
'a03WT00000FodlxYAB',
'a03WT00000FodoFYAR',
'a03WT00000Fodn8YAB',
'a03WT00000Fodn9YAB',
'a03WT00000FoYfeYAF',
'a03WT00000FoYffYAF',
'a03WT00000FoYfgYAF',
'a03WT00000FoYfhYAF',
'a03WT00000Fodo9YAB',
'a03WT00000Fodk2YAB',
'a03WT00000Fodk4YAB',
'a03WT00000Fodk5YAB',
'a03WT00000Fodk6YAB',
'a03WT00000Fodk7YAB',
'a03WT00000Fodk8YAB',
'a03WT00000FodkAYAR',
'a03WT00000FodkCYAR',
'a03WT00000FodkDYAR',
'a03WT00000FodkEYAR',
'a03WT00000FodkFYAR',
'a03WT00000FodkHYAR',
'a03WT00000FodkIYAR',
'a03WT00000FodkKYAR',
'a03WT00000FodkLYAR',
'a03WT00000FodkMYAR',
'a03WT00000FodlGYAR',
'a03WT00000FodnHYAR',
'a03WT00000FodnIYAR',
'a03WT00000FodnJYAR',
'a03WT00000FodnKYAR',
'a03WT00000FodnLYAR',
'a03WT00000FodnMYAR',
'a03WT00000FodnNYAR',
'a03WT00000FodnqYAB',
'a03WT00000FodnrYAB',
'a03WT00000FodnuYAB',
'a03WT00000Fodo3YAB',
'a03WT00000Fodo4YAB',
'a03WT00000Fodo5YAB',
'a03WT00000Fodo6YAB',
'a03WT00000Fodo7YAB',
'a03WT00000FodnsYAB',
'a03WT00000FodntYAB',
'a03WT00000FoYfvYAF',
'a03WT00000FoYfwYAF',
'a03WT00000FoYfxYAF',
'a03WT00000FoYfyYAF',
'a03WT00000FoYfzYAF',
'a03WT00000FoYg0YAF',
'a03WT00000FoYg1YAF',
'a03WT00000FodlDYAR',
'a03WT00000FodlEYAR',
'a03WT00000FodmLYAR',
'a03WT00000FodmMYAR',
'a03WT00000FodmIYAR',
'a03WT00000FodmJYAR',
'a03WT00000FodmKYAR',
'a03WT00000FodmNYAR',
'a03WT00000FodmOYAR',
'a03WT00000FodmPYAR',
'a03WT00000FodmQYAR',
'a03WT00000FodmRYAR',
'a03WT00000FodmSYAR',
'a03WT00000FodmTYAR',
'a03WT00000FodmUYAR',
'a03WT00000FodmVYAR',
'a03WT00000FodmWYAR',
'a03WT00000FodmXYAR',
'a03WT00000FodoCYAR',
'a03WT00000FodoEYAR',
'a03WT00000FodoDYAR',
'a03WT00000FodkOYAR',
'a03WT00000FodkPYAR',
'a03WT00000FodkQYAR',
'a03WT00000FodkRYAR',
'a03WT00000Fodl3YAB',
'a03WT00000Fodl0YAB',
'a03WT00000Fodl1YAB',
'a03WT00000Fodl2YAB',
'a03WT00000Fodl4YAB',
'a03WT00000Fodl5YAB',
'a03WT00000Fodl6YAB',
'a03WT00000Fodl7YAB',
'a03WT00000FodmfYAB',
'a03WT00000FodmdYAB',
'a03WT00000FodmcYAB',
'a03WT00000FodmeYAB',
'a03WT00000FodmgYAB',
'a03WT00000FodmhYAB',
'a03WT00000FodkSYAR',
'a03WT00000FodkTYAR',
'a03WT00000FodjyYAB',
'a03WT00000FodjzYAB',
'a03WT00000FodjxYAB',
'a03WT00000Fodk0YAB',
'a03WT00000Fodl8YAB',
'a03WT00000FodmvYAB',
'a03WT00000FodmjYAB',
'a03WT00000FodmkYAB',
'a03WT00000FodmlYAB',
'a03WT00000FodmtYAB',
'a03WT00000FodmuYAB',
'a03WT00000FodmwYAB',
'a03WT00000FodmxYAB',
'a03WT00000FodmyYAB',
'a03WT00000FodoAYAR',
'a03WT00000FodoBYAR',
'a03WT00000FodmbYAB',
'a03WT00000FodlyYAB',
'a03WT00000FodmaYAB',
'a03WT00000FodmYYAR',
'a03WT00000FodmZYAR',
'a03WT00000FodmzYAB',
'a03WT00000Fodn0YAB',
'a03WT00000Fodn1YAB',
'a03WT00000Fodn3YAB',
'a03WT00000Fodn4YAB',
'a03WT00000Fodn5YAB',
'a03WT00000Fodn7YAB',
'a03WT00000FodlHYAR',
'a03WT00000FodlIYAR',
'a03WT00000FodlJYAR',
'a03WT00000FodlKYAR',
'a03WT00000FodlLYAR',
'a03WT00000FodlMYAR',
'a03WT00000FodlNYAR',
'a03WT00000FodlPYAR',
'a03WT00000FodlzYAB',
]

def main():
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION]

    total_affected = 0
    total_successfully_deleted = 0
    find_query = {
        'customer_survey_round_id': DENTSU_Q2_2025_CSR_ID,
        'customer_survey_id': {'$in': CS_IDS},
        'deleted': True,
        '__dentsu_q2_early_sync_issue': True,
    }

    for confirm_panel in collection.find(find_query):
        total_affected += 1
        print(confirm_panel['Id'])

        delete_query = {
            'Id': confirm_panel['Id'],
        }
        result = collection.delete_one(delete_query)

        if result.modified_count > 0:
            total_successfully_deleted += 1

    print(f'Total affected: {total_affected}')
    print(f'Total successfully deleted: {total_successfully_deleted}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
