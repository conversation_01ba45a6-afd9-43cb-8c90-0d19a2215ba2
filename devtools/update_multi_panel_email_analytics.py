""" Tools to update the email analytics for panel members that are part of a multi-panel survey.
    There is currently a shortcoming in the email tracking that only tracks the first panel member
    that is part of a multi-panel survey. This tool will update the email analytics for all panel members
    that are part of the multi-panel survey based on the email analytics of the first panel member.
"""
import argparse
from datetime import datetime
from simple_salesforce import Salesforce
from typing import Union, Optional
from lib import sfapi, portaldbapi
from lib.settings import settings
from lib.portaldbapi import DocumentDB

import agents.sync_email_analytics_to_sf as libanalytics


def fetch_associated_panel_members(db: DocumentDB, panel_member_events: dict, customer_survey_round_id:str) -> dict[str, list]:
    # create a new processing dictionary for panel members associated with each PM
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)
    result = {}

    for survey_panel_member_id, email_events in panel_member_events.items():
        for event in email_events:
            event_template = event.get('template')
            # if event fron a non-prod environment env, strip the env suffix
            for suffix in ['_dev', '_stage']:
                if event_template.endswith(suffix):
                    event_template = event_template[:-len(suffix)]
                    break
            query = {'survey_panel_member_id': survey_panel_member_id, 'template_name': event_template}
            if customer_survey_round_id:
                query['customer_survey_round_id'] = customer_survey_round_id
            schedule_doc = collection.find_one(query)
            if schedule_doc:
                for pm_id in schedule_doc.get('survey_panel_member_ids', []):
                    if pm_id != survey_panel_member_id:
                        print(f'Found additional panel member {pm_id} for survey panel member {survey_panel_member_id} ({event.get('email')}) with template {event_template}')
                        tmp = result.setdefault(pm_id, [])
                        tmp.append(event)

    return result


def main(customer_survey_round_id:str, survey_panel_member_id:Optional[str] = None, starttime: Optional[str] = None, endtime: Optional[str] = None, writeit:bool = False) -> None:
    sf: Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db: DocumentDB = DocumentDB()

    if (starttime and not endtime) or (endtime and not starttime):
        raise ValueError("Both starttime and endtime must be specified if either is provided.")

    if starttime and endtime:
        starttime = datetime.fromisoformat(starttime)
        endtime = datetime.fromisoformat(endtime)

    survey_panel_members_updates: dict = {}

    event_count = 0
    panel_member_email_events = libanalytics.fetch_email_events(db, starttime, endtime, survey_panel_member_id)
    print(f"Found {len(panel_member_email_events.keys())} survey panel members with email events")
    # now we have the panel member ids, fetch their entries from the email log
    panel_member_email_events = fetch_associated_panel_members(db, panel_member_email_events, customer_survey_round_id)

    # iterate over the emails and determine what events have occured
    for survey_panel_member_id, email_events in panel_member_email_events.items():
        # store the status for each event
        email_event_status: dict[str, Union[bool,str]] = dict()

        # iterate over the events on the email and update the initial/default email event status
        for event in email_events:
            # we've seen the event, so update the status
            event_name = event.get('event')
            event_template = event.get('template')
            if event_name == 'open' and event.get('sg_machine_open'):
                # the email was opened by a machine, skip it
                continue
            # if event fron a non-prod environment env, strip the env suffix
            for suffix in ['_dev', '_stage']:
                if event_template.endswith(suffix):
                    event_template = event_template[:-len(suffix)]
                    break
            email_event_status[libanalytics.SG_EVENT_TO_SF_FIELD_MAP[event_name][event_template]] = True
            event_count += 1

        survey_panel_members_updates[survey_panel_member_id] = email_event_status
        # attach the survey panel member ID to the update
        survey_panel_members_updates[survey_panel_member_id]['id'] = survey_panel_member_id

    # update each SF survey panel member with their email activity
    if survey_panel_members_updates and writeit:
        libanalytics.update_sf_survey_panel_member(sf, survey_panel_members_updates)

    print(f"Processed {event_count} email events for {len(survey_panel_members_updates.keys())} survey panel members")


def lambda_handler(event, context):
    main(
        survey_panel_member_id=None,
        starttime=None,
        endtime=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("customer_survey_round_id", type=str, help="Specify a CSR")
    ap.add_argument("--survey_panel_member_id", type=str, help="The ID of the survey panel member to update")
    ap.add_argument("--starttime", type=str, help="The time to start looking for email events, e.g. 2011-11-04T00:05:23Z")
    ap.add_argument("--endtime", type=str, help="The time to stop looking for email events")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.survey_panel_member_id, args.starttime, args.endtime, args.writeit)
