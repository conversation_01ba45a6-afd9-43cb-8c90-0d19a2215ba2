import lib.portaldbapi as portaldbapi
import argparse
from lib.settings import settings


def main():
    portaldb = portaldbapi.DocumentDB()

    confirm_accounts = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]
    for confirm_account in confirm_accounts.find({'sfsync_date': None}):
        unset = {}
        for i in range(1, len(confirm_account.get('confirmed_accounts', []))):
            unset[f'confirmed_accounts.{i}.all_accounts'] = ''
        if not unset:
            continue

        q = {'Id': confirm_account['Id']}
        update = {'$unset': unset}

        print(q, update)
        confirm_accounts.update_one(q, update)
        # break


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
