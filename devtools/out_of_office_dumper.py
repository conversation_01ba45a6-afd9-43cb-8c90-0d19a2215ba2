import os
import json
import psycopg2
import argparse
import datetime
import re
import csv
from bs4 import BeautifulSoup
from lib.settings import settings
from lib import sfimport
import pytz


def make_timezone_aware(dt, timezone, hour, minute):
    if dt is None:
        return None

    try:
        timezone = pytz.timezone(timezone)
    except pytz.UnknownTimeZoneError:
        timezone = pytz.timezone('UTC')

    naive_datetime = datetime.datetime(dt.year, dt.month, dt.day, hour, minute)

    scheduled_date_local = timezone.localize(naive_datetime)
    return scheduled_date_local.astimezone(pytz.utc)


def main():
    conn = psycopg2.connect(settings.monitordb_email_url)
    cursor = conn.cursor()
    all_sfobjects = sfimport.load_sf_from_latest_dump(include_files={'survey_panel_member.csv',
                                                                     'survey_client.csv',
                                                                     'customer_survey.csv',
                                                                     'customer_survey_round.csv',
                                                                     'account.csv',
                                                                     'market.csv',
                                                                     'contact.csv',
                                                                     'survey_response.csv',
                                                                     'account_record_type.csv',})

    response_by_spm = {}
    for resp in all_sfobjects['SurveyResponse'].values():
        if resp.IsExtraQuestion:
            continue
        response_by_spm[resp.SurveyPanelMemberId] = resp

    survey_details_by_spm_id = {}
    for spm in all_sfobjects['SurveyPanelMember'].values():
        sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
        if sc is None:
            continue
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if cs is None:
            continue
        csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
        if csr is None:
            continue
        sc_account = all_sfobjects['Account'].get(sc.CustomersClientId)
        if sc_account is None:
            continue
        cs_account = all_sfobjects['Account'].get(cs.CustomerId)
        if cs_account is None:
            continue
        cs_account_market = all_sfobjects['Market'].get(cs_account.MarketId)
        csr_account = all_sfobjects['Account'].get(csr.AccountId)
        if csr_account is None:
            continue
        csr_account_type = all_sfobjects['AccountRecordType'].get(csr_account.RecordTypeId)
        contact = all_sfobjects['Contact'].get(spm.ContactId)
        if contact is None:
            continue
        contact_location = all_sfobjects['Market'].get(contact.Location)
        response = response_by_spm.get(spm.Id)

        timezone = None
        if csr_account_type.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING:
            timezone = cs_account_market.Timezone if cs_account_market else 'UTC'

        elif csr_account_type.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING:
            timezone = contact_location.Timezone if contact_location else 'UTC'

        response_datetime = response.ResponseDateTime.replace(tzinfo=datetime.UTC) if response and response.ResponseDateTime else None

        alldates = {
            'account_updates_start': make_timezone_aware(cs.AccountUpdatesStartDate, 'UTC', hour=0, minute=0),
            'account_updates_end': make_timezone_aware(cs.AccountUpdatesEndDate, 'UTC', hour=0, minute=0),
            'panel_updates_start': make_timezone_aware(cs.PanelUpdatesStartDate, 'UTC', hour=0, minute=0),
            'panel_updates_end': make_timezone_aware(cs.PanelUpdatesEndDate, 'UTC', hour=0, minute=0),
            'live_survey_start': make_timezone_aware(cs.LiveSurveyStartDate, 'UTC', hour=0, minute=0),
            'm1': make_timezone_aware(cs.LiveSurveyFirstRequest, timezone, hour=7, minute=30),
            'm2': make_timezone_aware(cs.LiveSurveySecondRequest, timezone, hour=7, minute=30),
            'm3': make_timezone_aware(cs.LiveSurveyThirdRequest, timezone, hour=7, minute=30),
            'm4': make_timezone_aware(cs.LiveSurveyFourthRequest, timezone, hour=7, minute=30),
            'survey_end': make_timezone_aware(cs.LiveSurveyEndDate, 'UTC', hour=0, minute=0),
            'insights_start': make_timezone_aware(cs.InsightsStartDate, 'UTC', hour=0, minute=0),
        }

        survey_details_by_spm_id[spm.Id] = {
            'SurveyPanelMember': spm,
            'SurveyClient': sc,
            'CustomerSurvey': cs,
            'CustomerSurveyRound': csr,
            'SurveyClientAccount': sc_account,
            'CustomerSurveyAccount': cs_account,
            'CustomerSurveyRoundAccount': csr_account,
            'response_datetime': response_datetime,
            'timezone': timezone,
            'dates': alldates,
        }

    f = open('out_of_office.csv', 'w')
    csvout = csv.DictWriter(f, fieldnames=[
        'email_id',
        'received_at',
        'from_email',
        'contact_id',
        'survey_panel_member_id',
        'survey_client_id',
        'survey_client_name',
        'customer_survey_id',
        'customer_survey_name',
        'agency_account_name',
        'top_level_agency_account_name',
        'top_level_customer_client_account_name',
        'event_date',
        'event_name',
        'account_updates_start',
        'account_updates_end',
        'panel_updates_start',
        'panel_updates_end',
        'live_survey_start',
        'm1',
        'm2',
        'm3',
        'm4',
        'survey_end',
        'insights_start',
        'response_datetime',
        'timezone',
    ])
    csvout.writeheader()

    email_select_sql = """
    SELECT email_id, received_at, triage_details, json
    FROM email
    WHERE triage_details->>'person_type' = 'customerclient'
    AND triage_details->>'out_of_office' = 'true'
    ORDER BY received_at DESC
    """
    cursor.execute(email_select_sql)
    for email_id, received_at, triage_details, json in cursor:
        spm_found = False
        for spm_id in triage_details['survey_panel_members']:
            cs_details = survey_details_by_spm_id.get(spm_id)
            if cs_details is None:
                continue

            closest_event_date = None
            closest_event = None
            for event_name, event_date in cs_details['dates'].items():
                if event_date is None:
                    continue

                if received_at >= event_date and (closest_event_date is None or event_date > closest_event_date):
                    closest_event_date = event_date
                    closest_event = event_name

            spm_found = True
            row = {
                'email_id': email_id,
                'received_at': received_at,
                'from_email': triage_details['from_email'],
                'contact_id': triage_details['from_contact_id'],
                'survey_panel_member_id': cs_details['SurveyPanelMember'].Id,
                'survey_client_id': cs_details['SurveyClient'].Id,
                'survey_client_name': cs_details['SurveyClient'].Name,
                'customer_survey_id': cs_details['CustomerSurvey'].Id,
                'customer_survey_name': cs_details['CustomerSurvey'].Name,
                'agency_account_name': cs_details['CustomerSurveyAccount'].Name,
                'top_level_agency_account_name': cs_details['CustomerSurveyRoundAccount'].Name,
                'top_level_customer_client_account_name': cs_details['SurveyClientAccount'].Name,
                'event_date': closest_event_date,
                'event_name': closest_event,
                'response_datetime': cs_details['response_datetime'],
                'timezone': cs_details['timezone'],
            }
            for event_name, event_date in cs_details['dates'].items():
                if event_date is None:
                    continue
                row[event_name] = event_date
            csvout.writerow(row)

        if not spm_found:
            row = {
                'email_id': email_id,
                'received_at': received_at,
                'from_email': triage_details['from_email'],
                'contact_id': triage_details['from_contact_id'],
                'top_level_agency_account_name': triage_details['top_level_agency_account_name'],
                'top_level_customer_client_account_name': triage_details['top_level_customer_client_account_name'],
            }
            csvout.writerow(row)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
