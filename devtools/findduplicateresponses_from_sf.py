""" Compare response ids from SF with what is in the responses collection to 
    identify any dupes that may have resulted from the issue with sync_surveyresponses_to_sf
    creating dupes when it re-runs after failure
"""
import argparse
import csv
import datetime
from pymongo import DeleteOne
from agents.sync_responses_to_portaldb import get_responses_by_survey_panel_member
from lib import sfapi, portaldbapi
from lib.settings import settings


def fetch_responses(
    responses_collection, 
    customer_survey_round_id: list | None,
    keys: list[str]
):
    query = {}
    if customer_survey_round_id:
        query = {'customer_survey_round_id': {'$in': customer_survey_round_id}}

    print('>> FETCHING DB RESPONSES')
    projection = {x:1 for x in keys}
    responses = list(responses_collection.find(query, projection))
    by_id = {}
    for response in responses:
        if response['Id'] not in by_id:
            by_id[response['Id']] = []
        by_id[response['Id']].append(response)
    return by_id


def main(customer_survey_round_id, writeit):
    # turn customer_survey_round_id param (which is a comma seperated string) into a list
    if customer_survey_round_id:
        customer_survey_round_id = customer_survey_round_id.split(',')

    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    responses_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_RESPONSES_COLLECTION]
    KEYS = [
        '_id', 'Id', 'client_id', 'client_name', 'contact_email', 'contact_name', 
        'customer_survey_id', 'customer_survey_round_id', 'customer_survey_round_name', 
        'rating', 'responded', 'response_id', 'response_date', 
        'is_extra_question', 'duplicate'
    ]

    print('>> FETCHING SF RESPONSES')
    sf_responses_by_survey_panel_member = get_responses_by_survey_panel_member(sf, customer_survey_round_id, None, True)
    db_responses_by_survery_panel_member = fetch_responses(
        responses_collection,
        customer_survey_round_id,
        KEYS
    )

    dupe_count = 0
    dupes = {}
    batch = []
    for pm_id, panel_member_responses in db_responses_by_survery_panel_member.items():
        sf_responses = sf_responses_by_survey_panel_member.get(pm_id, [])
        sf_responses_by_id = {r['Id']: r for r in sf_responses}
        has_dupe = False
        for response in panel_member_responses:
            if response['responded'] and (response['response_id'] not in sf_responses_by_id):
                has_dupe = True
                break
        if has_dupe:
            dupes[pm_id] = []
            for response in panel_member_responses:
                if response['responded'] and (response['response_id'] not in sf_responses_by_id):
                    response['duplicate'] = True
                    dupes[pm_id].append(response)
                    dupe_count += 1
                    batch.append(DeleteOne({'Id': response['Id'], 'response_id': response['response_id']}))
                else:
                    response['duplicate'] = False
                    dupes[pm_id].append(response)

    now = datetime.datetime.now()
    filename = f"/tmp/response_dupes_{now.isoformat()}.csv"
    with open(filename, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(KEYS)
        for _, pm_resps in dupes.items():
            for item in pm_resps:
                row = [str(item[key]) for key in KEYS]
                writer.writerow(row)

    if writeit:
        print(f'>> DELETING {len(batch)} DUPLICATE RESPONSES')
        responses_collection.bulk_write(batch)
    else:
        print(f'>> NOT DELETING {len(batch)} DUPLICATE RESPONSES')
        # for item in batch:
        #     print(item)

    print(f'Found {len(db_responses_by_survery_panel_member.keys())} panel members')
    print(f'Found {dupe_count} potential duplicate responses')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', default=None, help='comma seperated list of CSRs to run for')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(customer_survey_round_id=args.customer_survey_round_id,
         writeit=args.writeit)
