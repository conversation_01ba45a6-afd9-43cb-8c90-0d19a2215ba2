import bson.json_util
from lib import portaldbapi
import argparse
import bson


def main(collectionname, dirname):
    portaldb = portaldbapi.DocumentDB()
    collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][collectionname]

    for doc in collection.find():
        if 'Id' not in doc: # we use saleforce Id if its there
            _id = doc['_id']
        else:
            _id = doc['Id']

        with open(f'{dirname}/{_id}.json', 'w') as f:
            f.write(bson.json_util.dumps(doc, indent=2, sort_keys=True))


def lambda_handler(event, _):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('collectionname')
    ap.add_argument('dirname')
    args = ap.parse_args()

    main(args.collectionname, args.dirname)
