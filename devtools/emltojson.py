from email import policy
from email.parser import BytesParser
import argparse
import json
import base64


def main(raw_eml_file):
    with open(raw_eml_file, 'rb') as fp:
        msg = BytesParser(policy=policy.default).parse(fp)

    plain_text_content = msg.get_body(preferencelist=('plain'))
    if plain_text_content:
        plain_text_content = plain_text_content.get_content()
    html_content = msg.get_body(preferencelist=('html'))
    if html_content:
        html_content = html_content.get_content()

    data = {
        'from_email': msg['from'],
        'to_emails': msg['to'],
        'subject': msg['subject'],
        'plain_text_content': plain_text_content,
        'html_content': html_content,
        'attachments': [],
    }

    for part in msg.walk():
        if not part.get_content_disposition():
            continue

        attachment = {
            'file_content_base64': base64.b64encode(part.get_payload(decode=True)).decode(),
            'file_name': part.get_filename(),
            'file_type': part.get_content_type(),
            'disposition': part.get_content_disposition(),
            'content_id': part.get('Content-Id', '').strip('<>')
        }
        data['attachments'].append(attachment)

    print(json.dumps(data, indent=2))


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('eml_file')
    args = ap.parse_args()

    main(args.eml_file)
