import argparse
import copy
from bson import json_util, ObjectId
from lib import portaldbapi


def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    email = '<EMAIL>'

    q = {"account_managers.account_manager_email": email,
         "confirmed_accounts.0.confirm_user_email": email,
         "customer_survey_round_id": 'a02WT00000AhrWvYAJ'}

    updated_docs = set()
    for doc in confirm_accounts_collection.find(q):
        if len(doc['confirmed_accounts']) > 1:
            continue
        updated_docs.add(doc['_id'])

        in_round_accounts = {a['account_id']: a for a in doc['accounts'] if a['in_round']}
        latest_confirmed_accounts_object = doc['confirmed_accounts'][0]

        accounts_ids = {a['account_id'] for a in latest_confirmed_accounts_object['accounts']}
        all_accounts_ids = {a['account_id'] for a in latest_confirmed_accounts_object['all_accounts']}

        changed = False
        for in_round_account in in_round_accounts.values():
            if in_round_account['account_id'] not in accounts_ids:
                latest_confirmed_accounts_object['accounts'].append(copy.deepcopy(in_round_account))
                changed = True
            if in_round_account['account_id'] not in all_accounts_ids:
                latest_confirmed_accounts_object['all_accounts'].append(copy.deepcopy(in_round_account))
                changed = True

        updated_docs.add(doc['_id'])

        if not changed:
            continue

        print(doc['_id'])

        update = {
            "$set": {
                "confirmed_accounts.0.accounts": latest_confirmed_accounts_object['accounts'],
                "confirmed_accounts.0.all_accounts": latest_confirmed_accounts_object['all_accounts']
            }
        }
        confirm_accounts_collection.update_one({"_id": doc['_id']}, update)

    q = {"account_managers.account_manager_email": email,
         "confirmed_accounts.confirm_user_email": email,
         "customer_survey_round_id": 'a02WT00000AhrWvYAJ'}

    for doc in confirm_accounts_collection.find(q):
        if doc['_id'] not in updated_docs:
            print(doc['_id'], 'was missed')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
