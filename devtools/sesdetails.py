from typing import Any
from io import Bytes<PERSON>
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from lib.settings import settings
import boto3
import os
from dotenv import load_dotenv


load_dotenv()


def get_message_details(message_id: str):
    ses = boto3.client('sesv2',
                       aws_access_key_id=os.environ.get('SES_ACCESS_KEY_ID'),
                       aws_secret_access_key=os.environ.get('SES_SECRET_ACCESS_KEY'))

    print(ses.get_message_insights(MessageId=message_id))

get_message_details('010201962482ee4b-31cefe6c-74f9-42c9-b201-34abd9ae33cc-000000')
