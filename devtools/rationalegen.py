from langchain_openai import ChatOpenAI
from langchain_core.messages import ToolMessage, AIMessage, HumanMessage, SystemMessage
import psycopg2
import os
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import re


# Also determine a one word summary of the reason for being out of office from the following list:
# * maternity
# * paternity
# * vacation
# * sick
# * other

# Return only the the reason and the rationale as a json document as follows:
# {{"reason": "<reason>", "rationale": "<RATIONALE>"}}

# Never include any personally identifiable information (such names of emails).


load_dotenv()

def generate_rationale(llm, bucket, action, label, j):
    if not label:
        return  # email is not already categorized, so don't use it!

    subject = j['subject']
    outofoffice = label.lower() == 'out of office'

    soup = BeautifulSoup(j['body']['content'], "html.parser")
    text = soup.get_text(separator=" ").replace("\n", " ").replace("\r", " ")
    text = re.sub(r"\s+", " ", text).strip()

    prompt = [
        SystemMessage(f"""
You are an expert at training small language models."
"""),
        HumanMessage(f"""
The following is an email. It will be either:
* outofoffice
* other

Generate a short rationale for why this classification was chosen in a form shutable for training a small language model to do the same task.
Return only the rationale text and nothing else. Never include any personally identifiable information (such names of emails).

Subject: {subject}
Body: {text}
Classification: {'outofoffice' if outofoffice else 'other'}
""")
    ]

    response = llm.invoke(prompt)

    print()
    print("Subject:", subject)
    print("Body:", text)
    print("Classification:", label)
    print("Rationale:", response.content)


def main():
    llm = ChatOpenAI(model="gpt-4o")
    conn = psycopg2.connect(os.getenv("EMAIL_DATABASE_URL"))
    cursor = conn.cursor()

    email_select_sql = """
    SELECT email_id, bucket, action, label, json
    FROM email
    WHERE bucket != 'dmarc' AND action = 'received'
    ORDER BY received_at DESC
    """
    cursor.execute(email_select_sql)
    emails = cursor.fetchall()

    for email_id, bucket, action, label, j in emails:
        generate_rationale(llm, bucket, action, label, j)


if __name__ == "__main__":
    main()
