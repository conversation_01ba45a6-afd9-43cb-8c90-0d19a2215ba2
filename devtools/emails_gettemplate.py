import argparse
from lib.settings import settings
from sendgrid import SendGridAPIClient
import json
import requests
import csv
import time
import sys


def get_template_by_id(sg, template_id):
    response = requests.get(f'{sg.host}/v3/templates/{template_id}', headers={'Authorization': f'Bearer {sg.api_key}'})
    response.raise_for_status()
    return response.json()


def main():
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)

    response = requests.get(f'{sg.host}/v3/templates', headers={'Authorization': f'Bearer {sg.api_key}'})
    response.raise_for_status()
    print(response.json())

    template_id = 'd-8cfc7259fbe6412aa15e153fa8279256'
    template = get_template_by_id(sg, template_id)
    with open(f'{template_id}.json', 'w') as f:
        f.write(json.dumps(template, indent=2))

    latest_version = template['versions'][-1]
    with open(f'{template_id}.html', 'w') as f:
        f.write(latest_version['html_content'])
    with open(f'{template_id}.txt', 'w') as f:
        f.write(latest_version['plain_content'])


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()

