from lib.settings import settings
from lib import sfimport
import csv
import argparse
import datetime
import boto3
import zipfile
import io


def load_sf_from_dump(include_files=None, exclude_files=None, now=None):
    s3 = boto3.client('s3')

    if not now:
        now = datetime.date.today()

    prefix = f"crc-sf/yy={now:%Y}/mm={now:%m}/dd={now:%d}/"
    response = s3.list_objects_v2(Bucket=settings.SF_DATA_BUCKET, Prefix=prefix)
    if 'Contents' not in response:
        raise ValueError(f"No objects found in bucket {settings.SF_DATA_BUCKET} with prefix {prefix}")
    keys = sorted(response['Contents'], key=lambda x: x['LastModified'], reverse=True)
    latest_key = keys[0]['Key']
    print(latest_key)

    all_sfobjects = {}
    with sfimport.s3_cached_download(settings.SF_DATA_BUCKET, latest_key) as tmp_file:
        with zipfile.ZipFile(tmp_file.name, 'r') as zip_file:
            for csv_filename in zip_file.namelist():
                if include_files and csv_filename not in include_files:
                    continue
                if exclude_files and csv_filename in exclude_files:
                    continue

                sfobject = sfimport.SFAPI_TO_CSV_MAPPING[csv_filename]
                all_sfobjects.setdefault(sfobject.__name__, {})
                this_sfobjects = all_sfobjects[sfobject.__name__]

                with zip_file.open(csv_filename) as rawstream:
                    with io.TextIOWrapper(rawstream, encoding="utf-8") as textstream:
                        csv_reader = csv.DictReader(textstream)
                        for row in csv_reader:
                            o = sfobject.model_validate(row)
                            this_sfobjects[o.Id] = o

    return all_sfobjects


def main(customer_survey_round_id):
    all_sfobjects = sfimport.load_sf_from_latest_dump(exclude_files=['survey_response.csv'])

    for sc in all_sfobjects['SurveyClient'].values():
        sc_account = all_sfobjects['Account'].get(sc.CustomersClientId)
        if not sc_account:
            continue
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if not cs:
            continue
        agency_account = all_sfobjects['Account'].get(cs.CustomerId)
        if not agency_account:
            continue
        csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
        if not csr:
            continue
        if csr.Id != customer_survey_round_id:
            continue

        signature_contact = all_sfobjects['Contact'].get(sc.SignatoryId)
        if not signature_contact:
            continue
        print(signature_contact.Email, sc.Name, signature_contact.Signature)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_round_id')
    args = ap.parse_args()

    main(args.customer_survey_round_id)
