import csv
from datetime import datetime
from lib import portaldbapi


portaldb = portaldbapi.DocumentDB()
accesslog_collection = portaldb.accesslog

pipeline = [
    { 
        "$match": { 
            "type": "request", 
            "last_access": { "$elemMatch": { "$gte": datetime(2024, 12, 1) } } 
        }
    },
    { 
        "$project": { 
            "contact_id": 1, 
            "contact_email": 1, 
            "latest_last_access": { "$max": "$last_access" } 
        } 
    },
    {
        "$sort": { "latest_last_access": -1 }
    }
]

results = list(accesslog_collection.aggregate(pipeline))

with open("accesslogs.csv", mode="w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(["Contact ID", "Contact Email", "Latest Last Access"])

    for record in results:
        writer.writerow([
            record.get("contact_id", ""),
            record.get("contact_email", ""),
            record.get("latest_last_access", "").isoformat() if record.get("latest_last_access") else ""
        ])