import json
import copy


DASHBOARD = {
  "annotations": {
    "list": [
      {
        "builtIn": 1,
        "datasource": {
          "type": "grafana",
          "uid": "-- Grafana --"
        },
        "enable": True,
        "hide": True,
        "iconColor": "rgba(0, 211, 255, 1)",
        "name": "Annotations & Alerts",
        "type": "dashboard"
      }
    ]
  },
  "editable": True,
  "fiscalYearStartMonth": 0,
  "graphTooltip": 0,
  "id": 4,
  "links": [],
  "panels": [],
  "schemaVersion": 39,
  "tags": [],
  "templating": {
    "list": []
  },
  "time": {
    "from": "now-6h",
    "to": "now"
  },
  "timepicker": {},
  "timezone": "browser",
  "title": "Agents",
  "uid": "fehvlfq9vgu80f",
  "version": 6,
  "weekStart": ""
}

PANELS = [
    {
      "datasource": {
        "type": "grafana-postgresql-datasource",
        "uid": "eehtx33db981sc"
      },
      "description": "",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": False,
            "axisCenteredZero": False,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "legend": False,
              "tooltip": False,
              "viz": False
            },
            "insertNulls": False,
            "lineInterpolation": "linear",
            "lineStyle": {
              "fill": "solid"
            },
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "auto",
            "spanNulls": False,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": None
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "r"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "errors"
            },
            "properties": [
              {
                "id": "custom.axisPlacement",
                "value": "right"
              },
              {
                "id": "unit",
                "value": "e"
              },
              {
                "id": "color",
                "value": {
                  "fixedColor": "dark-red",
                  "mode": "fixed"
                }
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 11,
        "w": 9,
        "x": 0,
        "y": 0
      },
      "id": 1,
      "interval": "5m",
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": True
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "cloudwatch",
            "uid": "behlbjicq169se"
          },
          "dimensions": {
            "App": "d2bhczol2emhg7"
          },
          "editorMode": "code",
          "expression": "",
          "format": "time_series",
          "id": "",
          "label": "client area amplify",
          "logGroups": [],
          "matchExact": False,
          "metricEditorMode": 0,
          "metricName": "Requests",
          "metricQueryType": 0,
          "namespace": "AWS/AmplifyHosting",
          "period": "60s",
          "queryMode": "Metrics",
          "rawQuery": True,
          "rawSql": "select datetime as time, value, 'requests' as metric from timeseries where name='lambda-{agentlambdaname}-Invocations-Sum' and $__timeFilter(\"datetime\") order by datetime",
          "refId": "A",
          "region": "eu-west-1",
          "sql": {
            "from": {
              "property": {
                "name": "AWS/ApiGateway",
                "type": "string"
              },
              "type": "property"
            },
            "select": {
              "name": "SUM",
              "parameters": [
                {
                  "name": "5XXError",
                  "type": "functionParameter"
                }
              ],
              "type": "function"
            },
            "where": {
              "expressions": [
                {
                  "operator": {
                    "name": "=",
                    "value": "crc-api-clientarea-prod"
                  },
                  "property": {
                    "name": "ApiName",
                    "type": "string"
                  },
                  "type": "operator"
                }
              ],
              "type": "and"
            }
          },
          "sqlExpression": "SELECT SUM(\"5XXError\") FROM \"AWS/ApiGateway\" WHERE ApiName = 'crc-api-clientarea-prod'",
          "statistic": "Sum"
        },
        {
          "datasource": {
            "type": "grafana-postgresql-datasource",
            "uid": "eehtx33db981sc"
          },
          "editorMode": "code",
          "format": "time_series",
          "hide": False,
          "rawQuery": True,
          "rawSql": "select datetime as time, value, 'errors' as metric from timeseries where name='lambda-{agentlambdaname}-Errors-Sum' and $__timeFilter(\"datetime\") order by datetime",
          "refId": "B",
          "sql": {
            "columns": [
              {
                "parameters": [],
                "type": "function"
              }
            ],
            "groupBy": [
              {
                "property": {
                  "type": "string"
                },
                "type": "groupBy"
              }
            ],
            "limit": 50
          }
        }
      ],
      "title": "{agentprettyname}",
      "type": "timeseries"
    },
    {
      "datasource": {
        "type": "grafana-postgresql-datasource",
        "uid": "eehtx33db981sc"
      },
      "description": "",
      "fieldConfig": {
        "defaults": {
          "color": {
            "mode": "palette-classic"
          },
          "custom": {
            "axisBorderShow": False,
            "axisCenteredZero": False,
            "axisColorMode": "text",
            "axisLabel": "",
            "axisPlacement": "auto",
            "barAlignment": 0,
            "drawStyle": "line",
            "fillOpacity": 0,
            "gradientMode": "none",
            "hideFrom": {
              "legend": False,
              "tooltip": False,
              "viz": False
            },
            "insertNulls": False,
            "lineInterpolation": "linear",
            "lineStyle": {
              "fill": "solid"
            },
            "lineWidth": 1,
            "pointSize": 5,
            "scaleDistribution": {
              "type": "linear"
            },
            "showPoints": "always",
            "spanNulls": False,
            "stacking": {
              "group": "A",
              "mode": "none"
            },
            "thresholdsStyle": {
              "mode": "off"
            }
          },
          "mappings": [],
          "min": 0,
          "thresholds": {
            "mode": "absolute",
            "steps": [
              {
                "color": "green",
                "value": None
              },
              {
                "color": "red",
                "value": 80
              }
            ]
          },
          "unit": "ms"
        },
        "overrides": [
          {
            "matcher": {
              "id": "byName",
              "options": "throttles"
            },
            "properties": [
              {
                "id": "custom.axisPlacement",
                "value": "right"
              },
              {
                "id": "custom.drawStyle",
                "value": "line"
              },
              {
                "id": "unit",
                "value": "t"
              }
            ]
          }
        ]
      },
      "gridPos": {
        "h": 11,
        "w": 9,
        "x": 9,
        "y": 0
      },
      "id": 2,
      "interval": "5m",
      "options": {
        "legend": {
          "calcs": [],
          "displayMode": "list",
          "placement": "bottom",
          "showLegend": True
        },
        "tooltip": {
          "mode": "single",
          "sort": "none"
        }
      },
      "targets": [
        {
          "datasource": {
            "type": "grafana-postgresql-datasource",
            "uid": "eehtx33db981sc"
          },
          "editorMode": "code",
          "format": "time_series",
          "hide": False,
          "rawQuery": True,
          "rawSql": "select datetime as time, value, 'avglatency' as metric from timeseries where name='lambda-{agentlambdaname}-Duration-Average' and $__timeFilter(\"datetime\") order by datetime",
          "refId": "C",
          "sql": {
            "columns": [
              {
                "parameters": [],
                "type": "function"
              }
            ],
            "groupBy": [
              {
                "property": {
                  "type": "string"
                },
                "type": "groupBy"
              }
            ],
            "limit": 50
          }
        },
        {
          "datasource": {
            "type": "grafana-postgresql-datasource",
            "uid": "eehtx33db981sc"
          },
          "editorMode": "code",
          "format": "time_series",
          "hide": False,
          "rawQuery": True,
          "rawSql": "select datetime as time, value, 'maxlatency' as metric from timeseries where name='lambda-{agentlambdaname}-Duration-Maximum' and $__timeFilter(\"datetime\") order by datetime",
          "refId": "D",
          "sql": {
            "columns": [
              {
                "parameters": [],
                "type": "function"
              }
            ],
            "groupBy": [
              {
                "property": {
                  "type": "string"
                },
                "type": "groupBy"
              }
            ],
            "limit": 50
          }
        },
        {
          "datasource": {
            "type": "grafana-postgresql-datasource",
            "uid": "eehtx33db981sc"
          },
          "editorMode": "code",
          "format": "time_series",
          "hide": False,
          "rawQuery": True,
          "rawSql": "select datetime as time, value, 'throttles' as metric from timeseries where name='lambda-{agentlambdaname}-Throttles-Sum' and $__timeFilter(\"datetime\") order by datetime",
          "refId": "A",
          "sql": {
            "columns": [
              {
                "parameters": [],
                "type": "function"
              }
            ],
            "groupBy": [
              {
                "property": {
                  "type": "string"
                },
                "type": "groupBy"
              }
            ],
            "limit": 50
          }
        }
      ],
      "title": "{agentprettyname}",
      "type": "timeseries"
    }
]

LAMBDAS = [
    {'name': 'crc-agent-cognito_email_trigger-prod'},
    {'name': 'crc-agent-sync_sfschema_to_s3-prod'},
    {'name': 'crc-agent-email_scheduler_panel_users-prod'},
    {'name': 'crc-agent-sync_survey_access_audit_to_sf-prod'},
    {'name': 'crc-agent-email_scheduler_staff_users-prod'},
    {'name': 'crc-agent-sync_signatures_to_s3-prod'},
    {'name': 'crc-agent-sync_banners_to_s3-prod'},
    {'name': 'crc-agent-sync_markets_to_portaldb-prod'},
    {'name': 'crc-agent-send_email_to_sg-prod'},
    {'name': 'crc-agent-sync_finalpanel_to_portaldb-prod'},
    {'name': 'crc-agent-sync_panel_manager_activity_to_sf-prod'},
    {'name': 'crc-agent-sync_confirmpanel_audit_to_sf-prod'},
    {'name': 'crc-agent-sync_finalaccounts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_sfsimple_to_portaldb-prod'},
    {'name': 'crc-agent-survey_setup-prod'},
    {'name': 'crc-agent-email_sender_panel_users-prod'},
    {'name': 'crc-agent-slack_monitoring-prod'},
    {'name': 'crc-agent-sync_pendingcustomercontacts_to_sf-prod'},
    {'name': 'crc-agent-sync_optout_to_sf-prod'},
    {'name': 'crc-agent-sync_confirmpanel_to_sf-prod'},
    {'name': 'crc-agent-sync_reports_to_portaldb-prod'},
    {'name': 'crc-agent-sync_panel_member_status_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_sf_to_s3-prod'},
    {'name': 'crc-agent-sync_confirmpanel_to_portaldb-prod'},
    {'name': 'crc-agent-sync_customersurveyrounds_to_portaldb-prod'},
    {'name': 'crc-agent-sync_survey_updates_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_audit_to_sf-prod'},
    {'name': 'crc-agent-sync_surveyresponses_to_sf-prod'},
    {'name': 'crc-agent-sync_email_analytics_to_sf-prod'},
    {'name': 'crc-agent-sync_customercontacts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_account_manager_activity_to_sf-prod'},
    {'name': 'crc-agent-confirmaccounts_new_objects_daily_state-prod'},
    {'name': 'crc-agent-sync_srpreports_to_portaldb-prod'},
    {'name': 'crc-agent-sync_accesslog_to_sf-prod'},
    {'name': 'crc-agent-sync_sf_contacts_to_cognito-prod'},
    {'name': 'crc-agent-sync_responses_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_to_sf-prod'},
]


def _apply_doc_params(d, doc_params):
    if isinstance(d, str):
        return d.format(**doc_params)

    elif isinstance(d, dict):
        for key, value in d.items():
            d[key] = _apply_doc_params(value, doc_params)
        return d

    elif isinstance(d, list):
        for i, value in enumerate(d):
            d[i] = _apply_doc_params(value, doc_params)
        return d

    else:
        return d


def make_dash():
    global PANELS
    """
    Create a Grafana dashboard with panels for each lambda function.
    """
    dashboard = copy.deepcopy(DASHBOARD)
    for i, lambda_ in enumerate(LAMBDAS):
        lambdaname = lambda_['name']
        panels = _apply_doc_params(copy.deepcopy(PANELS),
                                   {'agentlambdaname': lambdaname,
                                    'agentprettyname': lambdaname.replace('crc-agent-', '').replace('-prod', ' ')})
        dashboard['panels'].extend(panels)
    return dashboard

print(json.dumps(make_dash(), indent=2))