import psycopg2
from lib.settings import settings
from lib import sfimport
import csv


def load_email_servers():
    results = {}
    with open('email_domains.csv') as f:
        csvin = csv.DictReader(f)
        for row in csvin:
            if row['error']:
                continue
            del row['error']
            results.setdefault(row['domain'], [])
            results[row['domain']].append(row)

    for domain, mx_records in results.items():
        mx_records = sorted(mx_records, key=lambda x: (x['preference'], x['exchange']))
        results[domain] = mx_records[0]
    return results


def get_customer_survey_dates(all_sfobjects):
    email_servers_by_domain = load_email_servers()

    f = open('rr.csv', 'w')
    csvout = csv.DictWriter(f, fieldnames=['contact_id',
                                           'contact_email',
                                           'survey_panel_member_id',
                                           'survey_panel_member_name',
                                           'survey_client_id',
                                           'survey_client_name',
                                           'customer_client_account_id',
                                           'customer_client_account_name',
                                           'customer_survey_id',
                                           'customer_survey_name',
                                           'agency_account_id',
                                           'agency_account_name',
                                           'customer_survey_round_id',
                                           'customer_survey_round_name',
                                           'responded',
                                           'domain', 'preference', 'exchange', 'provider'])
    csvout.writeheader()

    row = {}
    for spm in all_sfobjects['SurveyPanelMember'].values():
        contact = all_sfobjects['Contact'].get(spm.ContactId)
        if not contact:
            continue
        sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
        if not sc:
            continue
        sc_account = all_sfobjects['Account'].get(sc.CustomersClientId)
        if not sc_account:
            continue
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if not cs:
            continue
        agency_account = all_sfobjects['Account'].get(cs.CustomerId)
        if not agency_account:
            continue
        csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
        if not csr:
            continue
        mailbox, domain = contact.Email.lower().split('@')

        if 'Q1 2025' not in csr.Name:
            continue

        row = {
            'contact_id': spm.ContactId,
            'contact_email': contact.Email,
            'survey_panel_member_id': spm.Id,
            'survey_panel_member_name': spm.Name,
            'survey_client_id': sc.Id,
            'survey_client_name': sc.Name,
            'customer_client_account_id': sc_account.Id,
            'customer_client_account_name': sc_account.Name,
            'customer_survey_id': cs.Id,
            'customer_survey_name': cs.Name,
            'agency_account_id': agency_account.Id,
            'agency_account_name': agency_account.Name,
            'customer_survey_round_id': csr.Id,
            'customer_survey_round_name': csr.Name,
            'responded': True if spm.Responses else False,
        }

        mx_record = email_servers_by_domain.get(domain)
        if mx_record:
            row.update(mx_record)
            csvout.writerow(row)

        else:
            csvout.writerow(row)


def main():
    include_files={'customer_survey_round.csv',
                   'customer_survey.csv',
                   'survey_client.csv',
                   'account.csv',
                   'survey_panel_member.csv',
                   'contact.csv'}
    all_sfobjects = sfimport.load_sf_from_latest_dump(include_files=include_files)

    get_customer_survey_dates(all_sfobjects)


if __name__ == '__main__':
    main()
