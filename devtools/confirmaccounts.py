import lib.portaldbapi as portaldbapi
import argparse
from lib.settings import settings


def main(customer_survey_sfid: str, panel_manager_sfid: str):
    portaldb = portaldbapi.DocumentDB()

    confirm_accounts = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION].find_one({'Id': customer_survey_sfid})
    if not confirm_accounts:
        print("No confirm accounts found for id: ", customer_survey_sfid)
        return
    for account in confirm_accounts.get('accounts', []):
        account['in_round'] = True
        panel_manager = {
            'panel_manager_id': panel_manager_sfid,
            'panel_manager_email': f'{panel_manager_sfid}@clientrelationship.com',
            'panel_manager_name': f'Bodge Panel Manager {panel_manager_sfid}',
        }
        account['survey_panel_managers'] = [panel_manager]
    confirm_accounts['confirm_status'] = True

    del confirm_accounts['Id']
    del confirm_accounts['_id']

    portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION].update_one({'Id': customer_survey_sfid}, {'$set': confirm_accounts})


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_sfid', help='customer survey salesforce id')
    ap.add_argument('panel_manager_sfid', help='panel manager salesforce id')
    args = ap.parse_args()

    main(args.customer_survey_sfid, args.panel_manager_sfid)
