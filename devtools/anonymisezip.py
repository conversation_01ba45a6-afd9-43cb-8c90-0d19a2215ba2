import re
import zipfile
import csv
import os
import io
import sqlite3
import argparse
from random import randint


class QA:
    def __init__(self):
        self.contacts = []
        self.question = ""
        self.answer = ""

    @property
    def isempty(self):
        return not any([self.contacts, self.question, self.answer])


def parse_qa_file(text):
    qa = QA()

    for line in text.split('\n'):
        line = line.strip()
        if not line:
            if not qa.isempty:
                yield qa
            qa = QA()
            continue

        if line.startswith("Question:"):
            qa.question = line.replace("Question:", "").strip()

        elif line.startswith("Answer:"):
            qa.answer = line.replace("Answer:", "").strip()

        else:
            line = re.split(r'\s+', line)
            name = line[:-1]
            email = line[-1]
            if '@' not in email:
                raise ValueError(f"Invalid email: {email}")
            qa.contacts.append({'real_name': " ".join(name), 'real_email': email})

    if not qa.isempty:
        yield qa


KEEP_OBJECTS = {
    'account.csv',
    'record_type.csv',
    'contact.csv',
    'survey_round.csv',
    'customer_survey_round.csv',
    'customer_survey.csv',
    'survey_client.csv',
    'survey_panel_member.csv',
    'customer_client_relationship.csv',
    'account_contact_relation.csv',
    'contact_report_view.csv',
    'contact_key_account.csv',
    'contact_key_market.csv',
    'contact_key_customer.csv',
    'survey_panel_manager.csv',
    'survey_account_manager.csv',
    'market.csv',
    'team.csv',
}


def make_fake_name(all_first_names, all_last_names, names_lookup, real_first_name, real_last_name, real_email):
    real_names_lookup_key = ('real_email', real_email.lower())

    while True:
        fake_first_name = re.sub('[^a-zA-Z]', ' ', all_first_names[randint(0, len(all_first_names) - 1)]).strip()
        fake_last_name = re.sub('[^a-zA-Z]', ' ', all_last_names[randint(0, len(all_last_names) - 1)]).strip()

        if ' ' in fake_first_name or ' ' in fake_last_name or not fake_first_name or not fake_last_name:
            continue

        fake_email = f"{fake_first_name}.{fake_last_name}@fake.client-relationship.com".replace(' ', '_')
        fake_names_lookup_key = ('fake_email', fake_email.lower())
        if fake_names_lookup_key not in names_lookup:
            break

    names_lookup[real_names_lookup_key] = names_lookup[fake_names_lookup_key] = {
        'real_first_name': real_first_name,
        'real_last_name': real_last_name,
        'real_email': real_email,
        'fake_first_name': fake_first_name,
        'fake_last_name': fake_last_name,
        'fake_email': fake_email,
        'new': True
    }


def anonymise_contact(names_lookup, all_first_names, all_last_names, row):
    real_first_name = row['FirstName']
    real_last_name = row['LastName']
    real_email = row['Email']

    real_names_lookup_key = ('real_email', real_email.lower())
    if real_names_lookup_key not in names_lookup:
        make_fake_name(all_first_names, all_last_names, names_lookup, real_first_name, real_last_name, real_email)

    fake_first_name = names_lookup[real_names_lookup_key]['fake_first_name']
    fake_last_name = names_lookup[real_names_lookup_key]['fake_last_name']
    fake_email = names_lookup[real_names_lookup_key]['fake_email']

    # record this - we'll need to lookup by contactid later
    names_lookup[('contact_id', row['Id'])] = names_lookup[real_names_lookup_key]

    row['FirstName'] = fake_first_name
    row['LastName'] = fake_last_name
    row['Email'] = fake_email

    return row


def anonymise_survey_panel_member(names_lookup, all_first_names, all_last_names, row):
    real_names_lookup_key = ('contact_id', row['Contact__c'])
    if real_names_lookup_key in names_lookup:
        fake_email = names_lookup[real_names_lookup_key]['fake_email']

    else:
        fake_email = ""  # if we've got detached record somehow

    row['Name'] = fake_email
    row['Contact_Email__c'] = fake_email

    return row


def anonymise_survey_panel_manager(names_lookup, all_first_names, all_last_names, row):
    real_names_lookup_key = ('contact_id', row['Contact__c'])
    if real_names_lookup_key in names_lookup:
        fake_first_name = names_lookup[real_names_lookup_key]['fake_first_name']
        fake_last_name = names_lookup[real_names_lookup_key]['fake_last_name']

    else:
        fake_first_name = fake_last_name = ""  # if we've got detached record somehow

    row['First_Name__c'] = fake_first_name
    row['Last_Name__c'] = fake_last_name

    return row


def anonymise_file(filename, zipin, zipout, row_function, names_lookup, all_first_names, all_last_names):
    with zipin.open(filename, "r") as incsvfile:
        with zipout.open(filename, "w") as outcsvfile:
            csvin = csv.DictReader(io.TextIOWrapper(incsvfile))
            csvout = csv.DictWriter(io.TextIOWrapper(outcsvfile), fieldnames=csvin.fieldnames)
            csvout.writeheader()
            for row in csvin:
                row = row_function(names_lookup, all_first_names, all_last_names, row)
                csvout.writerow(row)


def load_namesdb(namescur):
    namescur.execute("SELECT real_first_name, real_last_name, real_email, fake_first_name, fake_last_name, fake_email FROM contacts")
    names_lookup = {}
    for real_first_name, real_last_name, real_email, fake_first_name, fake_last_name, fake_email in namescur.fetchall():
        real_key = ('real_email', real_email.lower())
        fake_key = ('fake_email', fake_email.lower())
        names_lookup[real_key] = names_lookup[fake_key] = {
            'real_first_name': real_first_name,
            'real_last_name': real_last_name,
            'real_email': real_email,
            'fake_first_name': fake_first_name,
            'fake_last_name': fake_last_name,
            'fake_email': fake_email,
        }
    return names_lookup


def anonymise_sf_zip_file(infilename):
    # From https://github.com/smashew/NameDatabases/tree/master/NamesDatabases
    all_first_names = open(os.path.expanduser('~/dev/crc-data/first_names.txt')).read().splitlines()
    all_last_names = open(os.path.expanduser('~/dev/crc-data/last_names.txt')).read().splitlines()

    namesdb = sqlite3.connect(os.path.expanduser('~/dev/crc-data/anonnames.db'))
    namescur = namesdb.cursor()
    namescur.execute("CREATE TABLE IF NOT EXISTS contacts (real_first_name TEXT, real_last_name TEXT, real_email TEXT, fake_first_name TEXT, fake_last_name TEXT, fake_email TEXT)")
    names_lookup = load_namesdb(namescur)

    outfilename = infilename.replace(".zip", ".anonymised.zip")

    with zipfile.ZipFile(infilename, "r") as zipin:
        with zipfile.ZipFile(outfilename, "w", compression=zipfile.ZIP_DEFLATED) as zipout:
            # do contact first so we can grab the contactid
            anonymise_file('contact.csv', zipin, zipout, anonymise_contact, names_lookup, all_first_names, all_last_names)
            anonymise_file('survey_panel_member.csv', zipin, zipout, anonymise_survey_panel_member, names_lookup, all_first_names, all_last_names)
            anonymise_file('survey_panel_manager.csv', zipin, zipout, anonymise_survey_panel_manager, names_lookup, all_first_names, all_last_names)

            for incsvname in zipin.namelist():
                if incsvname not in KEEP_OBJECTS:
                    continue

                if incsvname in {'contact.csv', 'survey_panel_member.csv', 'survey_panel_manager.csv'}:
                    continue
                else:
                    with zipin.open(incsvname, "r") as incsvfile:
                        with zipout.open(incsvname, "w") as outcsvfile:
                            outcsvfile.write(incsvfile.read())

    # preserve the mapping of real to fake names
    for item in names_lookup.values():
        if not item.get('new'):
            continue
        item.pop('new')
        namescur.execute("INSERT INTO contacts (real_first_name, real_last_name, real_email, fake_first_name, fake_last_name, fake_email) VALUES (?, ?, ?, ?, ?, ?)",
                        (item['real_first_name'], item['real_last_name'], item['real_email'], item['fake_first_name'], item['fake_last_name'], item['fake_email']))
    namescur.connection.commit()


def anonymise_qa_file(filename):

    text = open(filename).read()

    namesdb = sqlite3.connect(os.path.expanduser('~/dev/crc-data/anonnames.db'))
    namescur = namesdb.cursor()
    names_lookup = load_namesdb(namescur)

    for qa in parse_qa_file(text):
        for contact in qa.contacts:
            fake_deets = names_lookup.get(('real_email', contact['real_email'].lower()))
            if not fake_deets:
                raise ValueError(f"Missing contact: {contact['real_email']}")
            contact['fake_name'] = f"{fake_deets['fake_first_name']} {fake_deets['fake_last_name']}"
            contact['fake_email'] = fake_deets['fake_email']

        for contact in qa.contacts:
            qa.question = re.sub(contact['real_name'], contact['fake_name'], qa.question, flags=re.IGNORECASE)
            qa.answer = re.sub(contact['real_name'], contact['fake_name'], qa.answer, flags=re.IGNORECASE)

            print(f"{contact['fake_name']} {contact['fake_email']}")
        print(f"Question: {qa.question}")
        print(f"Answer: {qa.answer}")
        print()


def output_mapping(filename):
    namesdb = sqlite3.connect(os.path.expanduser('~/dev/crc-data/anonnames.db'))
    namescur = namesdb.cursor()

    with open(filename, "w") as f:
        csvout = csv.DictWriter(f, fieldnames=['real_first_name', 'real_last_name', 'real_email', 'fake_first_name', 'fake_last_name', 'fake_email'])
        csvout.writeheader()

        namescur.execute("SELECT real_first_name, real_last_name, real_email, fake_first_name, fake_last_name, fake_email FROM contacts")
        for real_first_name, real_last_name, real_email, fake_first_name, fake_last_name, fake_email in namescur.fetchall():
            csvout.writerow({
                'real_first_name': real_first_name,
                'real_last_name': real_last_name,
                'real_email': real_email,
                'fake_first_name': fake_first_name,
                'fake_last_name': fake_last_name,
                'fake_email': fake_email,
            })


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("action", help="The action to perform", choices=['zip', 'qa', 'map'])
    ap.add_argument("filename", help="The file to process")
    args = ap.parse_args()

    if args.action == 'zip':
        anonymise_sf_zip_file(args.filename)
    elif args.action == 'qa':
        anonymise_qa_file(args.filename)
    elif args.action == 'map':
        output_mapping(args.filename)
    else:
        raise ValueError(f"Unknown action: {args.action}")


if __name__ == "__main__":
    main()
