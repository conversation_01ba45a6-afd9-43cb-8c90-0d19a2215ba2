import json
from dotenv import load_dotenv
import openpyxl
import argparse
import os
import csv


load_dotenv()


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('xlsx')
    args = ap.parse_args()

    basename = os.path.splitext(os.path.basename(args.xlsx))[0]
    xlsx = openpyxl.load_workbook(args.xlsx)
    sheet = xlsx.active
    emails = set()
    for row in sheet:
        email = row[1].value
        if email and '@' in email:
            emails.add(email.lower())
    with open(f'{basename}.csv', 'w') as f:
        csvout = csv.writer(f)
        for email in emails:
            csvout.writerow([email])
