""" Test script to add a batch of emails to the scheduled emails collection in portaldb
    for scale testing purposes of SES integration.
    Panel member id is for <PERSON> in preprod sandbox: 
        https://clientrelationship--preprod.sandbox.lightning.force.com/lightning/r/Survey_Panel_Member__c/a07Qz00000ftZctIAE/view

"""
import argparse
import copy
import datetime
import pytz
from lib.settings import settings
from lib import portaldbapi
from pymongo import InsertOne


SF_BATCH_SIZE = 1000
DEFAULT_SENDER_MANAGERS = '<EMAIL>' # TODO: move to secrets
DEFAULT_SENDER_MEMBERS = '<EMAIL>' # TODO: move to secrets

EMAIL_TEMPLATE_NAMES = [
    "live_survey_participant",
    "live_survey_participant_reminder_1",
    "live_survey_participant_reminder_2",
    "live_survey_participant_reminder_3",
    "live_barometer_participant",
    "live_barometer_participant_reminder_1",
    "live_barometer_participant_reminder_2",
    "live_barometer_participant_reminder_3",
]

DOC_TEMPLATE = {
    "type" : "panel",
    "survey_id" : "1234",
    "contact_id" : "003Qz00000N5oBYIAZ",
    "template_name" : None,
    "banner_attachment" : None,
    "customer_survey_id" : "a03Qz00000N5odRIAR",
    "customer_survey_ids" : [
        "a03Qz00000N5odRIAR"
    ],
    "customer_survey_round_id" : "a02Qz000007rT36IAE",
    "language" : "en",
    # "last_modified" : ISODate("2025-05-12T10:25:39.700+0000"),
    "recipient" : "<EMAIL>",
    "scheduled_date_local" : "2025-05-14T07:30:00+00:00",
    "scheduled_date_timezone" : "",
    # "scheduled_date_utc" : ISODate("2025-05-14T16:20:00.000+0000"),
    # "sender_email" : "<EMAIL>",
    "sender_name" : "Iain Croft",
    "sf_spm_field" : "Survey_Email_Reminder_3__c",
    "signature_attachment" : {
        "disposition" : "inline",
        "key" : "003S900000rm24IIAQ.jpg",
        "file_type" : "image/jpeg",
        "content_id" : "003S900000rm24IIAQ"
    },
    "survey_client_id" : "a06Qz00000BFuGCIA1",
    "survey_client_ids" : [
        "a06Qz00000BFuGCIA1"
    ],
    "survey_panel_member_id" : "a07Qz00000ftZctIAE",
    "survey_panel_member_ids" : [
        "a07Qz00000ftZctIAE"
    ],
    "survey_round_id" : "a08S900000FGx8MIAT",
    "template_data" : {
        "FirstName" : "Colin",
        "LastName" : "Sleigh",
        "SurveyDate" : "2025-05-09",
        "LiveSurveyEndDate" : "2025-05-16",
        "SurveyLink" : "https://dev.d3fdli8xcck9no.amplifyapp.com/survey/1234",
        "Banner" : "",
        "Signatory" : "<p><img src=\"cid:003S900000rm24IIAQ\" alt=\"image.png\"></img></p><p>sig</p>",
        "AgencyBrand" : "TestAgency",
        "HoldingGroup" : "Vista Group",
        "OptOutLink" : "https://dev.d3fdli8xcck9no.amplifyapp.com/optout/003Qz00000N5oBYIAZ",
        "PrivacyPolicyLink" : "https://dev.d3fdli8xcck9no.amplifyapp.com/privacy/1234"
    },
    "template_metadata" : {
        "contact_id" : "003Qz00000N5oBYIAZ",
        "survey_panel_member_id" : "a07Qz00000ftZctIAE",
        "survey_client_id" : "a06Qz00000BFuGCIA1",
        "customer_survey_id" : "a03Qz00000N5odRIAR",
        "customer_survey_round_id" : "a02Qz000007rT36IAE",
        "tracked_template_name" : ""
    },
    "vertical" : "adv",
}

def main(email_count: int, provider: str, to_email: str, send_time: str, writeit: bool) -> None:
    default_timezone = pytz.utc
    now:datetime.datetime = datetime.datetime.now(datetime.UTC)

    naive_datetime:datetime.datetime = datetime.datetime.strptime(send_time, '%Y-%m-%d %H:%M:%S')
    scheduled_date_local = default_timezone.localize(naive_datetime)
    scheduled_date_utc = scheduled_date_local.astimezone(pytz.utc)
    split_email = to_email.split('@')

    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)

    batch = []
    for idx in range(email_count):
        doc = copy.deepcopy(DOC_TEMPLATE)
        template_name = EMAIL_TEMPLATE_NAMES[idx % len(EMAIL_TEMPLATE_NAMES)]
        recipient = f'{split_email[0]}+{idx}@{split_email[1]}'
        doc['recipient'] = recipient
        doc['template_name'] = template_name
        doc['template_metadata']['tracked_template_name'] = f"{template_name}_dev"
        doc['sender_email'] = DEFAULT_SENDER_MEMBERS
        doc['scheduled_date_local'] = scheduled_date_local
        doc['scheduled_date_utc'] = scheduled_date_utc
        doc['email_service_provider'] = provider
        doc['last_modified'] = now.isoformat()
        batch.append(InsertOne(doc))

    if writeit and batch:
        print(' # UPDATING PORTAL DB...')
        collection.bulk_write(batch)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('email_count', type=int, help='customer survey round id to run for')
    ap.add_argument('provider', type=str, choices=['sendgrid', 'ses'], help='Provider to use: sendgrid or ses')
    ap.add_argument('to_email', type=str, help='single survey round to run for')
    ap.add_argument('send_time', type=str, help='send time UTC in YYYY-MM-DD HH:MM:SS format')
    ap.add_argument('--writeit', action='store_true', help='Actually write data to portaldb')
    args = ap.parse_args()

    main(args.email_count, args.provider, args.to_email, args.send_time, writeit=args.writeit)


