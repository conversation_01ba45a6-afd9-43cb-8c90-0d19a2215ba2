import hashlib
import hmac
import datetime
import urllib.parse
import copy
import json
import requests
import os


def make_canonical_request(http_method: str,
                          http_url: str,
                          http_headers: dict[str, str],
                          http_payload: bytes):

    parsed_url = urllib.parse.urlparse(http_url)

    canonical_uri = urllib.parse.quote(parsed_url.path)

    canonical_querystring = []
    params = urllib.parse.parse_qs(parsed_url.query)
    params_keys = sorted(params.keys())
    for key in params_keys:
        value = params[key]
        canonical_querystring.append(f'{urllib.parse.quote(key)}={urllib.parse.quote_plus(value)}')
    canonical_querystring = '&'.join(canonical_querystring)

    canonical_headers = []
    local_headers = copy.copy(http_headers)
    local_headers['Host'] = parsed_url.netloc
    local_headers = {k.lower(): v for k, v in local_headers.items()}
    headers_keys = sorted(local_headers.keys())
    for key in headers_keys:
        value = local_headers[key].strip()
        while '  ' in value:
            value = value.replace('  ', ' ')
        canonical_headers.append(f'{key}:{value}\n')
    canonical_headers = ''.join(canonical_headers)

    signed_headers = ';'.join(headers_keys)

    payload_hash = hashlib.sha256(http_payload).hexdigest()

    canonical_request = '\n'.join([
        http_method,
        canonical_uri,
        canonical_querystring,
        canonical_headers,
        signed_headers,
        payload_hash
    ])
    return canonical_request, signed_headers


def sign_canonical_request(http_method: str,
                           http_url: str,
                           http_headers: dict[str, str],
                           http_payload: bytes,
                           aws_region: str,
                           aws_service: str,
                           aws_access_key_id: str,
                           aws_secret_access_key: str,
                           now: datetime.datetime):
    canonical_request, signed_headers = make_canonical_request(http_method, http_url, http_headers, http_payload)

    tosign = ["AWS4-HMAC-SHA256",
              now.strftime('%Y%m%dT%H%M%SZ'),
              f"{now.strftime('%Y%m%d')}/{aws_region}/{aws_service}/aws4_request",
              hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()]
    tosign = '\n'.join(tosign)

    key = f'AWS4{aws_secret_access_key}'.encode('utf-8')
    date_key = hmac.new(key, now.strftime('%Y%m%d').encode('utf-8'), hashlib.sha256).digest()
    date_region_key = hmac.new(date_key, aws_region.encode('utf-8'), hashlib.sha256).digest()
    date_region_service_key = hmac.new(date_region_key, aws_service.encode('utf-8'), hashlib.sha256).digest()
    signing_key = hmac.new(date_region_service_key, 'aws4_request'.encode('utf-8'), hashlib.sha256).digest()

    signature = hmac.new(signing_key, tosign.encode('utf-8'), hashlib.sha256).hexdigest()

    auth_header = f"AWS4-HMAC-SHA256 Credential={aws_access_key_id}/{now.strftime('%Y%m%d')}/{aws_region}/{aws_service}/aws4_request, SignedHeaders={signed_headers}, Signature={signature}"

    return auth_header


def send_sqs_message(aws_region,
                     aws_access_key_id,
                     aws_secret_access_key,
                     sqs_queue_url,
                     message_body):
    now = datetime.datetime.now(tz=datetime.UTC)
    now = datetime.datetime(2024,8,8,15,0,0)
    sqs_post_url = f'https://sqs.{aws_region}.amazonaws.com/'

    sqs_headers = {
        'Content-Type': 'application/x-amz-json-1.0',
        'X-Amz-Target': 'AmazonSQS.SendMessage',
        'X-Amz-Date': now.strftime('%Y%m%dT%H%M%SZ')
    }
    sqs_payload = json.dumps({
        'QueueUrl': sqs_queue_url,
        'MessageBody': message_body
    })
    auth_header = sign_canonical_request('POST',
                                         sqs_post_url,
                                         sqs_headers,
                                         sqs_payload.encode('utf-8'),
                                         'eu-west-1',
                                         'sqs',
                                         aws_access_key_id,
                                         aws_secret_access_key,
                                         now)
    sqs_headers['Authorization'] = auth_header
    resp = requests.post(sqs_post_url, headers=sqs_headers, data=sqs_payload)
    resp.raise_for_status()


send_sqs_message('eu-west-1',
                 os.environ.get('AWS_ACCESS_KEY_ID'),
                 os.environ.get('AWS_SECRET_ACCESS_KEY'),
                 'https://sqs.eu-west-1.amazonaws.com/891377123527/testqueue',
                 'HELLO')
