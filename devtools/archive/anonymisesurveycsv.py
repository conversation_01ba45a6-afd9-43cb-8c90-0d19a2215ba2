import argparse
import csv
import random
import datetime
import os
import json


<PERSON>_ROLES = [
    'Communications',
    'Media',
    'Marketing',
    'General',
    'Product',
    'Digital',
]

JOB_TITLES = [
    'Brand Director',
    'Head of Customer Acquisition & Campaigns',
    'Mktg Mgr',
    'VP & Dir of Mktg',
    'Marketing Exec',
    'CMO',
    'Marketing Director',
    'Marketing Manager',
    'Marketing Communications Manager',
    'Marketing Communications Exec',
]

FEEDBACKS = [
    'Great collaboration, always looking for innovative solutions to business challenges, team quick to respond',
    'I\'m lucky to work with a brilliant team of talented individuals. Rigorous planning, excellent account mgmt and ambitious strategic thinking - keep it up guys',
    'Their should have strong team to deliver complexed topics after quick start.',
    'The co-work between planning and trading team needs to be enhanced. The efficiency of resources pitching need to be enhanced.',
    'I think the team I/we work with are trying really hard to make a positive impact.  They are a good team to work with - lots of passion and good at what they do.',
    'Great partnership approach to doing business.',
    'I think it\'s a bit disappointing that the person in charge is constantly changing, which makes things less stable.',
    'Strong analytics and partnership. Strategic view and long term partner',
    '',
    ''
    '',
]

ATTRIBUTES = [
    [{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 38, "l1": "Relational", "l2": "The Experience", "l3": "Ownership", "pn": 1},{"id": 38, "l1": "Relational", "l2": "The Experience", "l3": "Ownership", "pn": 1},{"id": 38, "l1": "Relational", "l2": "The Experience", "l3": "Ownership", "pn": 1},{"id": 38, "l1": "Relational", "l2": "The Experience", "l3": "Ownership", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 26, "l1": "Relational", "l2": "Relationship", "l3": "Relationship", "pn": 1},{"id": 26, "l1": "Relational", "l2": "Relationship", "l3": "Relationship", "pn": 1},{"id": 26, "l1": "Relational", "l2": "Relationship", "l3": "Relationship", "pn": 1},{"id": 26, "l1": "Relational", "l2": "Relationship", "l3": "Relationship", "pn": 1},{"id": 77, "l1": "General Agency Capabilities", "l2": "", "l3": "Reputation", "pn": 1},{"id": 77, "l1": "General Agency Capabilities", "l2": "", "l3": "Reputation", "pn": 1},{"id": 77, "l1": "General Agency Capabilities", "l2": "", "l3": "Reputation", "pn": 1},{"id": 77, "l1": "General Agency Capabilities", "l2": "", "l3": "Reputation", "pn": 1}],
    [{"id": 58, "l1": "Relational", "l2": "Attitude", "l3": "Commitment", "pn": -1},{"id": 58, "l1": "Relational", "l2": "Attitude", "l3": "Commitment", "pn": -1},{"id": 58, "l1": "Relational", "l2": "Attitude", "l3": "Commitment", "pn": -1},{"id": 58, "l1": "Relational", "l2": "Attitude", "l3": "Commitment", "pn": -1},{"id": 120, "l1": "Transactional", "l2": "Expertise & Understanding", "l3": "Expertise/knowledge (general)", "pn": -1},{"id": 120, "l1": "Transactional", "l2": "Expertise & Understanding", "l3": "Expertise/knowledge (general)", "pn": -1},{"id": 120, "l1": "Transactional", "l2": "Expertise & Understanding", "l3": "Expertise/knowledge (general)", "pn": -1},{"id": 120, "l1": "Transactional", "l2": "Expertise & Understanding", "l3": "Expertise/knowledge (general)", "pn": -1},{"id": 102, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": -1},{"id": 102, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": -1},{"id": 102, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": -1},{"id": 102, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 108, "l1": "Transactional", "l2": "Financials", "l3": "Value for money", "pn": -1},{"id": 108, "l1": "Transactional", "l2": "Financials", "l3": "Value for money", "pn": -1},{"id": 108, "l1": "Transactional", "l2": "Financials", "l3": "Value for money", "pn": -1},{"id": 108, "l1": "Transactional", "l2": "Financials", "l3": "Value for money", "pn": -1},{"id": 52, "l1": "Relational", "l2": "People", "l3": "Resource Level", "pn": -1},{"id": 52, "l1": "Relational", "l2": "People", "l3": "Resource Level", "pn": -1},{"id": 52, "l1": "Relational", "l2": "People", "l3": "Resource Level", "pn": -1},{"id": 52, "l1": "Relational", "l2": "People", "l3": "Resource Level", "pn": -1},{"id": 3, "l1": "General Agency Capabilities", "l2": "", "l3": "Network, Scale & Structure", "pn": -1},{"id": 3, "l1": "General Agency Capabilities", "l2": "", "l3": "Network, Scale & Structure", "pn": -1},{"id": 3, "l1": "General Agency Capabilities", "l2": "", "l3": "Network, Scale & Structure", "pn": -1},{"id": 3, "l1": "General Agency Capabilities", "l2": "", "l3": "Network, Scale & Structure", "pn": -1}],
    [{"id": 101, "l1": "Transactional", "l2": "Effectiveness", "l3": "Performance", "pn": -1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1}],
    [{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 172, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": 1},{"id": 172, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": 1},{"id": 172, "l1": "Transactional", "l2": "Strategy", "l3": "Strategy", "pn": 1},{"id": 173, "l1": "Transactional", "l2": "Strategy", "l3": "Ideas", "pn": 1},{"id": 173, "l1": "Transactional", "l2": "Strategy", "l3": "Ideas", "pn": 1},{"id": 173, "l1": "Transactional", "l2": "Strategy", "l3": "Ideas", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 27, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": 1},{"id": 27, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": 1},{"id": 27, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": 1},{"id": 5, "l1": "Relational", "l2": "Attitude", "l3": "Attitude (general)", "pn": 1},{"id": 5, "l1": "Relational", "l2": "Attitude", "l3": "Attitude (general)", "pn": 1},{"id": 5, "l1": "Relational", "l2": "Attitude", "l3": "Attitude (general)", "pn": 1},{"id": 175, "l1": "Transactional", "l2": "Strategy", "l3": "Solutions", "pn": 1},{"id": 175, "l1": "Transactional", "l2": "Strategy", "l3": "Solutions", "pn": 1},{"id": 175, "l1": "Transactional", "l2": "Strategy", "l3": "Solutions", "pn": 1}],
    [{"id": 49, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": -1},{"id": 49, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": -1}],
    [{"id": 62, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": -1},{"id": 95, "l1": "Transactional", "l2": "Effectiveness", "l3": "Efficiency", "pn": -1}],
    [{"id": 37, "l1": "Relational", "l2": "The Experience", "l3": "Professional", "pn": 1}],
    [{"id": 37, "l1": "Relational", "l2": "The Experience", "l3": "Professional", "pn": 1},{"id": 14, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1}],
    [{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 34, "l1": "Relational", "l2": "The Experience", "l3": "Agile", "pn": 1},{"id": 5, "l1": "Relational", "l2": "Attitude", "l3": "Attitude (general)", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 20, "l1": "Relational", "l2": "People", "l3": "Individuals", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 16, "l1": "Relational", "l2": "Communication", "l3": "Listening", "pn": 1}],
    [{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 6, "l1": "Relational", "l2": "Attitude", "l3": "Friendly", "pn": 1},{"id": 27, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": 1},{"id": 27, "l1": "Relational", "l2": "Relationship", "l3": "Collaboration", "pn": 1},{"id": 180, "l1": "Transactional", "l2": "Effectiveness", "l3": "Improvement", "pn": 1},{"id": 180, "l1": "Transactional", "l2": "Effectiveness", "l3": "Improvement", "pn": 1},{"id": 143, "l1": "Transactional", "l2": "Product", "l3": "Quality of work", "pn": 1},{"id": 143, "l1": "Transactional", "l2": "Product", "l3": "Quality of work", "pn": 1}],
    [{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 15, "l1": "Relational", "l2": "Communication", "l3": "Available", "pn": 1},{"id": 15, "l1": "Relational", "l2": "Communication", "l3": "Available", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 36, "l1": "Relational", "l2": "The Experience", "l3": "Supportive", "pn": 1},{"id": 16, "l1": "Relational", "l2": "Communication", "l3": "Listening", "pn": 1},{"id": 16, "l1": "Relational", "l2": "Communication", "l3": "Listening", "pn": 1},{"id": 14, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": 1},{"id": 14, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": 1}],
    [{"id": 174, "l1": "Transactional", "l2": "Strategy", "l3": "Insights", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1}],
    [{"id": 8, "l1": "Relational", "l2": "Attitude", "l3": "Engagement", "pn": 1},{"id": 8, "l1": "Relational", "l2": "Attitude", "l3": "Engagement", "pn": 1},{"id": 8, "l1": "Relational", "l2": "Attitude", "l3": "Engagement", "pn": 1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 42, "l1": "Relational", "l2": "Communication", "l3": "Proactive", "pn": -1},{"id": 106, "l1": "Transactional", "l2": "Strategy", "l3": "Advice", "pn": -1},{"id": 106, "l1": "Transactional", "l2": "Strategy", "l3": "Advice", "pn": -1},{"id": 106, "l1": "Transactional", "l2": "Strategy", "l3": "Advice", "pn": -1}],
    [{"id": 13, "l1": "Relational", "l2": "Communication", "l3": "Responsive", "pn": 1},{"id": 139, "l1": "Transactional", "l2": "Expertise & Understanding", "l3": "Expertise/knowledge (general)", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1}],
    [{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 18, "l1": "Relational", "l2": "Communication", "l3": "Open & Honest", "pn": 1},{"id": 30, "l1": "Relational", "l2": "Relationship", "l3": "Trust", "pn": 1},{"id": 26, "l1": "Relational", "l2": "Relationship", "l3": "Relationship", "pn": 1},{"id": 56, "l1": "Relational", "l2": "Attitude", "l3": "Hard-working", "pn": -1},{"id": 64, "l1": "Relational", "l2": "Relationship", "l3": "Partnership", "pn": -1},{"id": 93, "l1": "Transactional", "l2": "Digital", "l3": "Integrated/360", "pn": -1}],
    [{"id": 7, "l1": "Relational", "l2": "Attitude", "l3": "Hard-working", "pn": 1},{"id": 7, "l1": "Relational", "l2": "Attitude", "l3": "Hard-working", "pn": 1},{"id": 7, "l1": "Relational", "l2": "Attitude", "l3": "Hard-working", "pn": 1},{"id": 178, "l1": "Transactional", "l2": "Effectiveness", "l3": "Effectiveness", "pn": 1},{"id": 178, "l1": "Transactional", "l2": "Effectiveness", "l3": "Effectiveness", "pn": 1},{"id": 178, "l1": "Transactional", "l2": "Effectiveness", "l3": "Effectiveness", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 21, "l1": "Relational", "l2": "People", "l3": "Talent", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 33, "l1": "Relational", "l2": "The Experience", "l3": "Easy to work with", "pn": 1},{"id": 11, "l1": "Relational", "l2": "Attitude", "l3": "Passion", "pn": 1},{"id": 11, "l1": "Relational", "l2": "Attitude", "l3": "Passion", "pn": 1},{"id": 11, "l1": "Relational", "l2": "Attitude", "l3": "Passion", "pn": 1}],
]

FIRST_NAMES = [
    "Alexander", "Olivia", "Liam", "Emma", "Noah", "Ava", "Isabella",
    "Mason", "Sophia", "Lucas", "Mia", "Elijah", "Amelia", "James",
    "Harper", "Benjamin", "Evelyn", "Jackson", "Abigail", "Aiden",
    "Charlotte", "Samuel", "Chloe", "Ethan", "Lily", "William",
    "Grace", "Jacob", "Ella", "Michael"
]

LAST_NAMES = [
    "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia",
    "Miller", "Davis", "Rodriguez", "Martinez", "Hernandez",
    "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
    "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez",
    "Thompson", "White", "Harris", "Sanchez", "Clark", "Ramirez",
    "Lewis", "Robinson"
]

US_BRANDS = [
    "Apple", "Microsoft", "Amazon", "Google", "Facebook", "Tesla", "Nike",
    "Coca-Cola", "Pepsi", "McDonald's", "Starbucks", "Walmart", "Target",
    "Home Depot", "Costco", "Boeing", "Intel", "Disney", "IBM", "AT&T",
    "Verizon", "Ford", "General Motors", "Chevrolet", "Chrysler", "Uber",
    "Airbnb", "PayPal", "American Express", "Goldman Sachs", "JPMorgan Chase",
    "Visa", "Mastercard", "FedEx", "UPS", "Delta Airlines", "Southwest Airlines",
    "ExxonMobil", "Chevron", "Pfizer", "Johnson & Johnson", "Procter & Gamble",
    "3M", "Cisco", "Oracle", "Salesforce", "HP", "Adobe", "Qualcomm",
    "LinkedIn", "Snapchat", "YouTube"
]


def make_anon(what, row_value, all_anonymised_map, anon_fn=None):
    if not row_value:
        return row_value

    anon_map = all_anonymised_map.setdefault(what, {})
    if row_value not in anon_map:
        if anon_fn:
            anon_value = anon_fn(row_value, len(anon_map) + 1)
        else:
            anon_value = f"{what} {len(anon_map) + 1}"

        anon_map[row_value] = anon_value
    return anon_map[row_value]


def parse_date(d):
    try:
        return datetime.date.fromisoformat(d.split(' ')[0])
    except ValueError:
        return datetime.datetime.strptime(d.split(' ')[0], '%d/%m/%Y').date()


def anonymise_csv(filename):
    all_anonymised_map = {}

    leafname = os.path.split(filename)[1]
    prefix, ext = os.path.splitext(leafname)
    outfilename = f'{prefix}.anon{ext}'

    with open(filename, 'r', encoding='utf-8-sig') as fin:
        csvin = csv.DictReader(fin)

        with open(outfilename, 'w', encoding='utf-8') as fout:
            csvout = csv.DictWriter(fout, fieldnames=csvin.fieldnames)
            csvout.writeheader()

            for row in csvin:
                survey_start_date = parse_date(row['survey_start_date'])
                if row['agency_brand'] != 'OMD' and row['market_name'] != 'US':
                    continue
                if survey_start_date > datetime.date(2024, 7, 1):
                    continue

                for idx, hierarchy_name in enumerate(['holding_group', 'network', 'sub_network', 'agency_brand', 'agency_brand_subsidiary']):
                    row_name = row[hierarchy_name]
                    anon_hierarchy_name = ['Holding Group', 'Network', 'Sub Network', 'Agency Brand', 'Agency Brand Subsidiary'][idx]
                    row[hierarchy_name] = make_anon(anon_hierarchy_name, row_name, all_anonymised_map)

                row['team_name'] = make_anon('Team', row['team_name'], all_anonymised_map)
                row['account_group_name'] = make_anon('Account Group', row['account_group_name'], all_anonymised_map)
                row['account_name'] = US_BRANDS[random.randint(0, len(US_BRANDS) - 1)]
                row['first_name'] = make_anon('First Name', row['first_name'], all_anonymised_map, lambda x, i: f"{FIRST_NAMES[random.randint(0, len(FIRST_NAMES) - 1)]} {i}")
                row['last_name'] = make_anon('Last Name', row['last_name'], all_anonymised_map, lambda x, i: f"{LAST_NAMES[random.randint(0, len(LAST_NAMES) - 1)]} {i}")
                row['email_address'] = make_anon('email_address', row['email_address'], all_anonymised_map, lambda x, i: f"{row['first_name']}.{row['last_name']}@example.com".lower().replace(' ', ''))
                row['job_role_name'] = JOB_ROLES[random.randint(0, len(JOB_ROLES) - 1)]
                row['job_title'] = JOB_TITLES[random.randint(0, len(JOB_TITLES) - 1)]
                row['rating_question_text'] = 'On a scale of 1 to 10, where 1 is low and 10 is high, how likely is it that you would recommend the agency to a friend or colleague?'
                row['feedback_question_text'] = 'What makes you feel this way about the agency?'
                row['feedback'] = FEEDBACKS[random.randint(0, len(FEEDBACKS) - 1)]
                row['feedback_attributes'] = json.dumps(ATTRIBUTES[random.randint(0, len(ATTRIBUTES) - 1)])

                row['feedback_translation'] = ''
                row['legacy_office_name'] = ''
                row['parent_account_name'] = ''
                row['contact_id'] = ''
                row['legacy_contact_division'] = ''
                row['legacy_account_category'] = ''
                row['survey_agency_name'] = ''
                row['hub_row_key'] = ''
                row['legacy_client_group_id'] = ''
                row['legacy_client_group'] = ''

                csvout.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('filename', help='CSV file to import')
    args = ap.parse_args()

    anonymise_csv(args.filename)

