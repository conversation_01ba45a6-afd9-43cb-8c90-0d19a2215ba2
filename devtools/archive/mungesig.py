import base64
from lib import sfapi
from lib.settings import settings



with open('sigs/4002.html') as f:
    html = f.read()

image = open('sigs/agencyLogos/IPG-Medediabrands-202.png', 'rb').read()
image = base64.b64encode(image).decode('utf-8')

html = html.replace('agencyLogos/IPG-Medediabrands-202.png', 'data:image/png;base64,' + image)

sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

sf.Contact.update('003WT000006kjjUYAQ', {'Signature__c': html})
