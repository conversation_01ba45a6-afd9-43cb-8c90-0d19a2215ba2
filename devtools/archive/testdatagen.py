import copy
import random
import csv
import json
import datetime
import os


ACCOUNT_COUNT = 10
RESPONDER_COUNT = 10
SURVEY_DURATION_DAYS = 7
CONSULTANT_RESPONSE_DAYS = 1
ACCOUNT_MANAGER_RESPONSE_DAYS = 1
PANEL_MANAGER_RESPONSE_DAYS = 1
DID_RESPOND_PROBABILITY = 0.8

SURVEY_LANGUAGES = {
    'en': 0.8,
    'fr': 0.1,
    'de': 0.05,
    'es': 0.05,
}

COUNTRIES = [
    'United States',
    'China',
    'Japan',
    'Germany',
    'United Kingdom',
    'India',
    'France',
    'Italy',
    'Canada',
    'South Korea',
    'Russia',
    'Australia',
    'Brazil',
    'Spain',
    'Mexico',
    'Indonesia',
    'Netherlands',
    'Saudi Arabia',
    'Turkey',
    'Switzerland'
]

REGIONS = [
    'EMEA',
    'APAC',
    'NA',
    'LATAM',
    'ANZ'
]

CONTACT_TYPES = [
    'pc',
    'cc',
    None
]

JOB_TITLES = [
    'Creative Director',
    'Senior Copywriter',
    'Graphic Designer',
    'Media Planner',
    'Lead Media Buyer',
    'Digital Marketing Specialist',
    'Social Media Manager',
    'Senior Content Strategist',
    'Brand Strategist',
    'Project Manager',
    'Senior Production Manager',
    'Client Services Manager',
    'Public Relations Specialist',
    'Junior Marketing Analyst',
    'Director of Marketing'
]

SENIORITIES = [
    'Senior',
    'Middle',
    'Junior'
]

DEPARTMENTS = [
    'Business Development',
    'Communications',
    'Communications/Corporate',
    'Communications/Media Relations',
    'Communications/Public Affairs/Relations',
    'Client & Relationship Management',
    'Data & Analytics',
    'Executive & Senior Management',
    'Finance',
    'Finance /Accounting',
    'Finance /Investor Relations',
    'Finance /Procurement & Purchasing',
    'Healthcare',
    'Human Resources & Talent Mgmt',
    'Information Technology',
    'Marketing',
    'Marketing/Brand',
    'Marketing/Creative',
    'Marketing/Campaign',
    'Marketing/Digital',
    'Marketing/Media',
    'Marketing/Omnichannel/Integrated',
    'Marketing/Partnerships & Sponsorships',
    'Marketing/Product',
    'Marketing/Production',
    'Marketing/Strategy & Planning',
    'Marketing/Traditional',
    'Operations',
    'Operations/Administration',
    'Operations/Supply Chain & Logistics',
    'Operations/Legal, Risk & Compliance',
    'Operations/Sustainability & CSR',
    'Operations/Quality Assurance',
    'Product Development',
    'Product Development/Innovation',
    'Product Development/Product Development',
    'Product Development/Research',
    'Sales & Commercial'
]

CLIENT_INDUSTRIES = [
    'Consultancy',
    'Creative',
    'Digital',
    'Direct-Integrated',
    'Marketing Implementation',
    'Media',
    'PR',
]

ACCOUNT_INDUSTRIES = [
    'Basic Materials',
    'Charity',
    'Chemicals',
    'Consumer Goods',
    'Consumer Services',
    'Energy & Utilities',
    'Financials',
    'Government, Social & Political',
    'Healthcare',
    'Industrials',
    'Technology',
    'Trade Association',
    'Packaging & Paper',
    'Pharma'
]

CLIENTSTREE = []
CLIENTSTREE += [{'name': f'TinyClient{i+1}', 'level': 'agency_brand'} for i in range(20)]

def genclient(basename, levels, labels):
    if not levels:
        return []

    levels = copy.copy(levels)
    cur_levelsize = levels.pop(0)
    labels = copy.copy(labels)
    cur_label = labels.pop(0)

    return [{'name': f'{basename}-{i+1}', 'level': cur_label, 'children': genclient(f'{basename}-{i+1}', levels, labels)} for i in range(cur_levelsize)]

CLIENTSTREE += genclient('LargeClientA', [1,3,3,2], ['holding_group', 'network', 'agency_brand', 'agency_brand_subsidiary_market'])
CLIENTSTREE += genclient('LargeClientB', [1,3,3,2], ['holding_group', 'network', 'agency_brand', 'agency_brand_subsidiary_market'])
CLIENTSTREE += genclient('LargeClientC', [1,3,3,2], ['holding_group', 'network', 'agency_brand', 'agency_brand_subsidiary_market'])

CLIENTSTREE += genclient('MediumClientA', [1,2,5], ['holding_group', 'agency_brand', 'agency_brand_subsidiary_market'])
CLIENTSTREE += genclient('MediumClientB', [1,2,5], ['holding_group', 'agency_brand', 'agency_brand_subsidiary_market'])

CLIENTSTREE += genclient('SmallClientA', [1,5], ['holding_group', 'agency_brand_subsidiary_market'])
CLIENTSTREE += genclient('SmallClientB', [1,5], ['holding_group', 'agency_brand_subsidiary_market'])


def flattenclienttree(client, l=None):
    if l is None:
        l = []
    l.append(client)

    result = []
    if client.get('children'):
        for child in client['children']:
            result += flattenclienttree(child, copy.deepcopy(l))

    else:
        result.append(l)

    return result


FLATCLIENTS = []
for curclient in CLIENTSTREE:
    FLATCLIENTS += flattenclienttree(curclient)


FEEDBACK_POOL = {}
with open('feedback.csv', 'r') as csvfile:
    csvin = csv.DictReader(csvfile)
    for row in csvin:
        theme = {'l1': row['lvl1Category'],
                 'l2': row['lvl2Category'],
                 'l3': row['lvl3Category'],
                 'id': row['attributeId'],
                 'pn': row['positiveNegative']}
        feedback = row['feedback']
        score = row['score']
        feedback_unique_id = row['feedback_unique_id']
        if feedback_unique_id not in FEEDBACK_POOL:
            FEEDBACK_POOL[feedback_unique_id] = {'feedback': feedback, 'score': score, 'themes': [theme]}
        else:
            FEEDBACK_POOL[feedback_unique_id]['themes'].append(theme)
FEEDBACK_POOL = FEEDBACK_POOL.values()

CSV_COLS = ['holding_group', 'network', 'sub_network', 'agency_brand', 'agency_brand_subsidiary_market',
            'holding_group_industry', 'network_industry', 'sub_network_industry', 'agency_brand_industry', 'agency_brand_subsidiary_market_industry',
            'survey_start_date', 'region_name', 'country_name', 'account_name', 'account_industry',
            'contact_type', 'job_title', 'first_name', 'last_name',
            'email_address', 'seniority_name', 'Department', 'survey_language',
            'response_offset_hours', 'person_responded', 'feedback', 'rating', 'themes']


def main():
    dirname = f'testdata-{datetime.datetime.now().strftime("%Y%m%d-%H%M%S")}'
    os.makedirs(dirname, exist_ok=True)

    with open(os.path.join(dirname, 'surveyresponses.csv'), 'w') as csvfile:
        csvout = csv.DictWriter(csvfile, CSV_COLS)
        csvout.writeheader()

        account_unique_id = 1
        for flatclient in FLATCLIENTS:
            for account_idx in range(ACCOUNT_COUNT):
                account_name = f'Account{account_unique_id}'
                account_unique_id += 1

                for responder_idx in range(RESPONDER_COUNT):
                    email = f'surveyrecipient+{account_name}_r{responder_idx+1}@clientrelationship.com'
                    survey_language = random.choices(list(SURVEY_LANGUAGES.keys()), weights=SURVEY_LANGUAGES.values(), k=1)[0]
                    response_offset_hours = random.randint(1, SURVEY_DURATION_DAYS * 24)
                    person_responded = random.random() < DID_RESPOND_PROBABILITY
                    feedback = random.choice(list(FEEDBACK_POOL))

                    row = {}
                    for x in flatclient:
                        row[x['level']] = x['name']
                        row[f'{x['level']}_industry'] = random.choice(CLIENT_INDUSTRIES)

                    row['survey_start_date'] = '2024-01-01'
                    if random.random() < 0.75:
                        row['country_name'] = random.choice(COUNTRIES)
                    else:
                        row['region_name'] = random.choice(REGIONS)
                    row['account_name'] = account_name
                    row['account_industry'] = random.choice(ACCOUNT_INDUSTRIES)
                    row['contact_type'] = random.choice(CONTACT_TYPES)
                    row['job_title'] = random.choice(JOB_TITLES)
                    row['first_name'] = 'surveyrecipient'
                    row['last_name'] = email.split('@')[0]
                    row['email_address'] = email
                    row['seniority_name'] = random.choice(SENIORITIES)
                    row['Department'] = random.choice(DEPARTMENTS)
                    row['survey_language'] = survey_language

                    row['response_offset_hours'] = response_offset_hours
                    row['person_responded'] = person_responded
                    row['feedback'] = feedback['feedback']
                    row['rating'] = feedback['score']
                    row['themes'] = json.dumps(feedback['themes'])
                    csvout.writerow(row)

    with open(os.path.join(dirname, 'consultants.csv'), 'w') as csvfile:
        csvout = csv.DictWriter(csvfile, ['client_name', 'first_name', 'last_name', 'email_address', 'response_offset_hours'])
        csvout.writeheader()

        for toplevel_client in CLIENTSTREE:
            response_offset_hours = random.randint(0, CONSULTANT_RESPONSE_DAYS * 24)
            client_name = toplevel_client['name']
            email_address = f'consultant+{client_name}@clientrelationship.com'

            row = {'client_name': client_name,  # always the top level client
                'first_name': 'consultant',
                'last_name':  email_address.split('@')[0],
                'email_address': email_address,
                'response_offset_hours': response_offset_hours,
                }
            csvout.writerow(row)

    with open(os.path.join(dirname, 'accountmanagers.csv'), 'w') as csvfile:
        csvout = csv.DictWriter(csvfile, ['client_name', 'first_name', 'last_name', 'email_address', 'response_offset_hours'])
        csvout.writeheader()

        for flatclient in FLATCLIENTS:
            response_offset_hours = random.randint(0, ACCOUNT_MANAGER_RESPONSE_DAYS * 24)
            client_name = "/".join([x['name'] for x in flatclient])
            email_address = f'accountmanager+{flatclient[-1]['name']}@clientrelationship.com'

            row = {'client_name': client_name,
                'first_name': 'accountmanager',
                'last_name':  email_address.split('@')[0],
                'email_address': email_address,
                'response_offset_hours': response_offset_hours,
                }
            csvout.writerow(row)

    with open(os.path.join(dirname, 'signatories.csv'), 'w') as csvfile:
        csvout = csv.DictWriter(csvfile, ['client_name', 'first_name', 'last_name', 'email_address', 'response_offset_hours'])
        csvout.writeheader()

        for flatclient in FLATCLIENTS:
            response_offset_hours = random.randint(0, PANEL_MANAGER_RESPONSE_DAYS * 24)
            client_name = "/".join([x['name'] for x in flatclient])
            email_address = f'signatory+{flatclient[-1]['name']}@clientrelationship.com'

            row = {'client_name': client_name,
                'first_name': 'signatory',
                'last_name':  email_address.split('@')[0],
                'email_address': email_address,
                'response_offset_hours': response_offset_hours,
                }
            csvout.writerow(row)

    with open(os.path.join(dirname, 'panelmanagers.csv'), 'w') as csvfile:
        csvout = csv.DictWriter(csvfile, ['client_name', 'first_name', 'last_name', 'email_address', 'response_offset_hours'])
        csvout.writeheader()

        for flatclient in FLATCLIENTS:
            response_offset_hours = random.randint(0, PANEL_MANAGER_RESPONSE_DAYS * 24)
            client_name = "/".join([x['name'] for x in flatclient])
            email_address = f'panelmanager+{flatclient[-1]['name']}@clientrelationship.com'

            row = {'client_name': client_name,
                'first_name': 'panelmanager',
                'last_name':  email_address.split('@')[0],
                'email_address': email_address,
                'response_offset_hours': response_offset_hours,
                }
            csvout.writerow(row)

    # output default test configuration
    config = {
        "dirname": dirname,
        "simulate_consultants": True,
        "simulate_accountmanagers": True,
        "simulate_panelmanagers": True,
        "simulate_surveyresponders": True,
    }
    configfilename = os.path.join(dirname, 'config.json')
    with open(configfilename, 'w') as f:
        json.dump(config, f, indent=2)

    # print this for other scripts
    print(dirname)


if __name__ == '__main__':
    main()
