import datetime
import lib.portaldbapi as portaldbapi
import lib.sfapi as sfapi
import csv
import lib.sfimport as sfimportlib
import lib.clientareaapi as clientareaapi
from lib.settings import settings
import argparse
import json
import os
from simple_salesforce import format_soql


def get_waiting_customer_surveys_from_portaldb(portaldb, now):
    q = {'ConfirmedStatus': False}
    for survey in portaldb.client.paneldb2.sfcustomersurveys.find(q):
        last_change = datetime.datetime.fromisoformat(survey['CustomerSurvey']['Last_Status_Change_Date__c'])
        if last_change <= now:
            yield survey


def get_waiting_survey_clients_from_portaldb(portaldb, now):
    q = {'ConfirmedStatus': False}
    for survey in portaldb.client.paneldb2.sfsurveyclients.find(q):
        last_change = datetime.datetime.fromisoformat(survey['SurveyClient']['Last_Status_Change_Date__c'])
        if last_change <= now:
            yield survey



def load_csvs(testconfig):
    consultants = {}
    with open(os.path.join(testconfig['dirname'], 'consultants.csv')) as f:
        csvin = csv.DictReader(f)

        for row in csvin:
            consultants[row['email_address'].lower()] = row

    accountmanagers = {}
    with open(os.path.join(testconfig['dirname'], 'accountmanagers.csv')) as f:
        csvin = csv.DictReader(f)

        for row in csvin:
            accountmanagers[row['email_address'].lower()] = row

    panelmanagers = {}
    panelmanagers_by_client_name = {}
    with open(os.path.join(testconfig['dirname'], 'panelmanagers.csv')) as f:
        csvin = csv.DictReader(f)

        for row in csvin:
            panelmanagers[row['email_address'].lower()] = row
            panelmanagers_by_client_name[row['client_name']] = row

    signatories = {}
    signatories_by_client_name = {}
    with open(os.path.join(testconfig['dirname'], 'signatories.csv')) as f:
        csvin = csv.DictReader(f)

        for row in csvin:
            signatories[row['email_address'].lower()] = row
            signatories_by_client_name[row['client_name']] = row

    surveyresponders = {}
    with open(os.path.join(testconfig['dirname'], 'surveyresponses.csv')) as f:
        csvin = csv.DictReader(f)

        for row in csvin:
            surveyresponders[row['email_address'].lower()] = row

    return consultants, accountmanagers, panelmanagers, panelmanagers_by_client_name, signatories, signatories_by_client_name, surveyresponders


def simulate_consultants(consultants, sf, now, immediate):
    accountRecordTypes = {x.Name: x for x in sfapi.AccountRecordType.get_all(sf)}
    approval_survey_round = list(sfapi.SurveyRound.get_by_status(sf, 'Consultant Approval'))
    if approval_survey_round:
        approval_survey_round = approval_survey_round[0]
        base_date = datetime.datetime.fromisoformat(approval_survey_round.LastStatusChangeDate)
        record_type_id=accountRecordTypes[sfimportlib.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id

        soql = format_soql(sfapi.Account.soql_fields("select FIELDS from Account where ParentId = NULL and RecordTypeId = {record_type_id} and Current_Round__c = false"), record_type_id=record_type_id)
        for row in sf.query_all_iter(soql):
            account =  sfapi.Account.model_validate(row)
            contact = sfapi.Contact.get_by_id(sf, account.ConsultantId)
            email_address = contact.Email
            csv_row = consultants.get(email_address.lower())
            if not csv_row:
                print("Failed to find consultant for email address: ", email_address)
                continue
            simulation_date = base_date + datetime.timedelta(hours=int(csv_row['response_offset_hours']))

            if simulation_date <= now or immediate:
                account.set_current_round(sf, account.Id, True)


def simulate_accountmanagers(accountmanagers, panelmanagers_by_client_name, signatories_by_client_name, portaldb, robbie, now, immediate):
    for customer_survey in get_waiting_customer_surveys_from_portaldb(portaldb, now):
        email_address = customer_survey['SurveyPanelManagerEmail']
        base_date = datetime.datetime.fromisoformat(customer_survey['CustomerSurvey']['Last_Status_Change_Date__c']).replace(tzinfo=datetime.UTC)
        accountmanager_csv_row = accountmanagers.get(email_address.lower())
        if accountmanager_csv_row is None:
            print("Failed to find account manager for email address: ", email_address, customer_survey.Id)
            continue
        simulation_date = base_date + datetime.timedelta(hours=int(accountmanager_csv_row['response_offset_hours']))
        client_name = accountmanager_csv_row['client_name']

        panelmanager_csv_row = panelmanagers_by_client_name.get(client_name)
        if panelmanager_csv_row is None:
            print("Failed to find panel manager for client: ", client_name, customer_survey.Id)
            continue
        panelmanager_contact = portaldb.client.paneldb2.sfcontacts.find_one({'Email': panelmanager_csv_row['email_address'].lower()})
        if panelmanager_contact is None:
            print("Failed to find panel manager contact for email address: ", panelmanager_csv_row['email_address'])
            continue

        signatory_csv_row = signatories_by_client_name.get(accountmanager_csv_row['client_name'])
        if signatory_csv_row is None:
            print("Failed to find signatory for client: ", client_name, customer_survey.Id)
            continue
        signatory_contact = portaldb.client.paneldb2.sfcontacts.find_one({'Email': signatory_csv_row['email_address'].lower()})
        if signatory_contact is None:
            print("Failed to find signatory contact for email address: ", signatory_csv_row['email_address'])
            continue

        if simulation_date <= now or immediate:
            deets = robbie.get_accounts(email_address)
            accounts = []
            for account in deets['accounts']:
                account['SurveyPanelManagerId'] = panelmanager_contact['Id']
                account['Panel_Manager_Id'] = panelmanager_contact['Id']
                account['Panel_Manager'] = panelmanager_contact['Email']
                account['Signatory_Id'] = signatory_contact['Id']
                account['Signatory'] = signatory_contact['Email']
                accounts.append(account)
            data = {
                customer_survey['Id']: accounts
            }
            robbie.confirm_accounts(email_address, data)


def simulate_panelmanagers(panelmanagers, portaldb, robbie, now, immediate):
    for survey_client in get_waiting_survey_clients_from_portaldb(portaldb, now):
        email_address = survey_client['PanelManagerEmail']
        base_date = datetime.datetime.fromisoformat(survey_client['SurveyClient']['Last_Status_Change_Date__c']).replace(tzinfo=datetime.UTC)
        panelmanager_csv_row = panelmanagers.get(email_address.lower())
        if panelmanager_csv_row is None:
            print("Failed to find account manager for email address: ", email_address, survey_client.Id)
            continue
        simulation_date = base_date + datetime.timedelta(hours=int(panelmanager_csv_row['response_offset_hours']))

        if simulation_date <= now or immediate:
            deets = robbie.get_panel(email_address)
            contacts = []
            for contact in deets['contacts']:
                if contact['AccountId'] == survey_client['SurveyClient']['Customers_Client__c']:
                    contacts.append(contact)
            data = {
                survey_client['Id']: contacts
            }
            robbie.confirm_panel(email_address, data)


def simulate_surveyresponders(sf, surveyresponders, now, immediate):
    soql = format_soql(sfapi.SurveyPanelMember.soql_fields("select Id, Contact__r.Email, Survey_Client__r.Last_Status_Change_Date__c from Survey_Panel_Member__c where Has_Responded__c = false AND Survey_Client__r.Status__c = 'Survey Out'"))
    for row in sf.query_all_iter(soql):
        survey_panel_member_id = row["Id"]
        email_address = row['Contact__r']['Email']
        base_date = datetime.datetime.fromisoformat(row['Survey_Client__r']['Last_Status_Change_Date__c']).replace(tzinfo=datetime.UTC)
        survey_responder_row = surveyresponders.get(email_address.lower())
        if not survey_responder_row:
            print("Failed to find survey responder for email address: ", email_address, survey_panel_member_id)
            continue
        simulation_date = base_date + datetime.timedelta(hours=int(survey_responder_row['response_offset_hours']))

        if not survey_responder_row['person_responded']:
            continue

        if simulation_date <= now or immediate:
            response = sfapi.SurveyResponse(Id="",
                                            Name=email_address,
                                            Survey_Panel_Member__c=survey_panel_member_id,
                                            Response_Date_Time__c=now.isoformat(),
                                            Score__c=survey_responder_row['rating'],
                                            Feedback__c=survey_responder_row['feedback'],
                                            Themes__c=survey_responder_row['themes'])
            response.create_in_sf(sf)


def main(testconfig: dict, immediate: bool):
    portaldb = portaldbapi.DocumentDB()
    sf = sfapi.get_sf_connection()
    now = datetime.datetime.now(datetime.UTC)
    robbie = clientareaapi.RobbieAPI()
    robbie.login(settings.CRCAPI_USER, settings.CRCAPI_PASSWORD)

    # load csvs
    consultants, accountmanagers, panelmanagers, panelmanagers_by_client_name, signatories, signatories_by_client_name, surveyresponders = load_csvs(testconfig)

    # consultant simulation
    if testconfig.get('simulate_consultants', False):
        simulate_consultants(consultants, sf, now, immediate)

    # account manager simulation
    if testconfig.get('simulate_accountmanagers', False):
        simulate_accountmanagers(accountmanagers, panelmanagers_by_client_name, signatories_by_client_name, portaldb, robbie, now, immediate)

    # panel manager simulation
    if testconfig.get('simulate_panelmanagers', False):
        simulate_panelmanagers(panelmanagers, portaldb, robbie, now, immediate)

    # survey responses simulation
    if testconfig.get('simulate_surveyresponders', False):
        simulate_surveyresponders(sf, surveyresponders, now, immediate)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('configfilename', help='Config filename')
    ap.add_argument('--immediate', action='store_true', help='Run immediately')
    args = ap.parse_args()

    with open(args.configfilename) as f:
        testconfig = json.load(f)

    main(testconfig, args.immediate)
