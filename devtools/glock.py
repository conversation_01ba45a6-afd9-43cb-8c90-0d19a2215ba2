import requests
import json
import os
from dotenv import load_dotenv

load_dotenv()


base_url = 'https://api.glockapps.com/gateway/spamtest-v2/api'


def get_projects(api_key):
    url = f"{base_url}/projects"
    headers = {
        "X-Api-Key": api_key,
        "Content-Type": "application/json"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json()['results']


def get_providers(api_key, project):
    url = f"{base_url}/projects/{project["id"]}/providers"
    headers = {
        "X-Api-Key": api_key,
        "Content-Type": "application/json"
    }
    response = requests.get(url, headers=headers)
    response.raise_for_status()
    return response.json()['results']

def create_manual_test(api_key,
                       project,
                       provider_group_ids,
                       test_note):
    url = f"{base_url}/projects/{project['id']}/manualTest"
    headers = {
        "X-Api-Key": api_key,
        "Content-Type": "application/json"
    }
    body = {
        "linkChecker": True,
        "note": test_note,
        "providerGroupIds": provider_group_ids,
        "testType": "ManualTest"
    }
    response = requests.post(url, headers=headers, json=body)
    response.raise_for_status()
    return response.json()['results']

def get_region_provider_group_ids(api_key, project, seedAccountRegionId):
    providers = get_providers(api_key, project)
    for provider in providers:
        if provider['seedAccountRegionId'] != seedAccountRegionId:
            continue

        for group in provider['seedAccountGroups']:
            for account in group['seedAccounts']:
                yield account['providerGroupId']

def get_folders(api_key, project, folderType):
    url = f"{base_url}/projects/{project['id']}/folders"
    headers = {
        "X-Api-Key": api_key,
        "Content-Type": "application/json"
    }
    response = requests.get(url, headers=headers, params={"folderType": folderType})
    response.raise_for_status()
    return response.json()['results']



api_key = os.environ.get('GLOCK_API_KEY')

projects = get_projects(api_key)
print(projects[0])
provider_group_ids = list(get_region_provider_group_ids(api_key), projects[0], '1')
folders = get_folders(api_key, projects[0], 'manualTestFolder')

test = create_manual_test(api_key, projects[0], provider_group_ids, 'test')
print(test)
