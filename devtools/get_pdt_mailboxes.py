import argparse
from lib import sfapi
from lib.settings import settings


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    soql = """
        SELECT External_Communication_Email_Address__c
        FROM  Customer_Survey__c
    """
    mailboxes = set()
    for row in sfapi.bulk_query(sf, soql):
        email = row['External_Communication_Email_Address__c']
        if not email:
            continue
        mailboxes.add(email.lower().strip())

    for m in sorted(mailboxes):
        print(m)



if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
