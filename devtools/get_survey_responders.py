import argparse
from itertools import islice
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
from lib.settings import settings
from pymongo import UpdateOne
import dns.resolver
import csv
import sys



def boolify(s):
    return s in {'true', 'True', 'TRUE', '1', 1, True}

def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    out = open('domainresult.csv', 'w')
    csvout = csv.DictWriter(out, fieldnames=['domain', 'mx_record', 'spm_count', 'email_count', 'responded_count', 'opened_count', 'clicked_count'])
    csvout.writeheader()

    # get the list of survey clients to process this time
    soql = """
        SELECT Contact__r.Email,
               Has_Responded__c,

               Survey_Email_Bounced__c,
               Survey_Email_Clicked__c,
               Survey_Email_Delivered__c,
               Survey_Email_Opened__c,
               Survey_Email_Triggered__c,

               Survey_Email_Reminder_1_Bounced__c,
               Survey_Email_Reminder_1_Clicked__c,
               Survey_Email_Reminder_1_Delivered__c,
               Survey_Email_Reminder_1_Opened__c,
               Survey_Email_Reminder_1__c,

               Survey_Email_Reminder_2_Bounced__c,
               Survey_Email_Reminder_2_Clicked__c,
               Survey_Email_Reminder_2_Delivered__c,
               Survey_Email_Reminder_2_Opened__c,
               Survey_Email_Reminder_2__c,

               Survey_Email_Reminder_3_Bounced__c,
               Survey_Email_Reminder_3_Clicked__c,
               Survey_Email_Reminder_3_Delivered__c,
               Survey_Email_Reminder_3_Opened__c,
               Survey_Email_Reminder_3__c

        FROM  Survey_Panel_Member__c
        WHERE Survey_Client__r.Customer_Survey__r.Stage__c IN ('Live Survey', 'Insights')
        AND Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name in ('Q1 2025')
    """
    email_status = {}
    for row in sfapi.bulk_query(sf, soql):
        email_address = row['Contact__r.Email'].lower().strip()
        email_scheduled = boolify(row['Survey_Email_Triggered__c']) or \
                          boolify(row['Survey_Email_Reminder_1__c']) or \
                          boolify(row['Survey_Email_Reminder_2__c']) or \
                          boolify(row['Survey_Email_Reminder_3__c'])

        email_opened =    boolify(row['Survey_Email_Opened__c']) or \
                          boolify(row['Survey_Email_Reminder_1_Opened__c']) or \
                          boolify(row['Survey_Email_Reminder_2_Opened__c']) or \
                          boolify(row['Survey_Email_Reminder_3_Opened__c'])

        email_clicked =   boolify(row['Survey_Email_Clicked__c']) or \
                          boolify(row['Survey_Email_Reminder_1_Clicked__c']) or \
                          boolify(row['Survey_Email_Reminder_2_Clicked__c']) or \
                          boolify(row['Survey_Email_Reminder_3_Clicked__c'])

        email_bounced =   boolify(row['Survey_Email_Bounced__c']) or \
                          boolify(row['Survey_Email_Reminder_1_Bounced__c']) or \
                          boolify(row['Survey_Email_Reminder_2_Bounced__c']) or \
                          boolify(row['Survey_Email_Reminder_3_Bounced__c'])

        email_delivered = boolify(row['Survey_Email_Delivered__c']) or \
                          boolify(row['Survey_Email_Reminder_1_Delivered__c']) or \
                          boolify(row['Survey_Email_Reminder_2_Delivered__c']) or \
                          boolify(row['Survey_Email_Reminder_3_Delivered__c'])

        responded = boolify(row['Has_Responded__c'])

        if not email_scheduled or not email_delivered or email_bounced:
            continue

        email_status.setdefault(email_address, {'spm_count': 0, 'responded_count': 0, 'opened_count': 0, 'clicked_count': 0})

        email_status[email_address]['spm_count'] += 1
        email_status[email_address]['responded_count'] += 1 if responded else 0
        email_status[email_address]['opened_count'] += 1 if email_opened else 0
        email_status[email_address]['clicked_count'] += 1 if email_clicked else 0

    domain_status = {}
    for cur_email_address, cur_email_status in email_status.items():
        cur_email_domain = cur_email_address.split('@')[1]

        domain_status.setdefault(cur_email_domain, {'spm_count': 0, 'responded_count': 0, 'opened_count': 0, 'clicked_count': 0, 'emails': set()})

        domain_status[cur_email_domain]['spm_count'] += cur_email_status['spm_count']
        domain_status[cur_email_domain]['responded_count'] += cur_email_status['responded_count']
        domain_status[cur_email_domain]['opened_count'] += cur_email_status['opened_count']
        domain_status[cur_email_domain]['clicked_count'] += cur_email_status['clicked_count']
        domain_status[cur_email_domain]['emails'].add(cur_email_address)

    for email_domain, domain_status in domain_status.items():
        spm_count = domain_status['spm_count']
        responded_count = domain_status['responded_count']
        opened_count = domain_status['opened_count']
        clicked_count = domain_status['clicked_count']
        email_count = len(domain_status['emails'])

        try:
            mx_records = dns.resolver.resolve(email_domain, 'MX')
            mx_record = mx_records[0].exchange
            # for mx in mx_records:
            #     print(f"MX record for {email_domain}: {mx.exchange} with priority {mx.preference}")
        except dns.resolver.NoAnswer:
            mx_record = 'Unknown'
            # print(f"No MX record found for {email_domain}")
        except dns.resolver.NXDOMAIN:
            mx_record = 'Unknown'
            # print(f"Domain {email_domain} does not exist")
        except Exception as e:
            mx_record = 'Unknown'
            # print(f"Error retrieving MX records for {email_domain}: {e}")

        row = {
            'domain': email_domain,
            'mx_record': mx_record,
            'spm_count': spm_count,
            'email_count': email_count,
            'responded_count': responded_count,
            'opened_count': opened_count,
            'clicked_count': clicked_count,
        }
        csvout.writerow(row)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
