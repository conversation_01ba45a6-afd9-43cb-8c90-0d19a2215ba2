from lib import sfimport
import json
import csv


RECORD_TYPE_ID_CUSTOMERS_CLIENT, RECORD_TYPE_ID_ADVERTISING, RECORD_TYPE_ID_MANUFACTURING = None, None, None


def find_account_record_types(all_sfobjects):
    global RECORD_TYPE_ID_CUSTOMERS_CLIENT, RECORD_TYPE_ID_ADVERTISING, RECORD_TYPE_ID_MANUFACTURING

    for o in all_sfobjects['AccountRecordType'].values():
        if o.Name == sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT:
            RECORD_TYPE_ID_CUSTOMERS_CLIENT = o.Id
        elif o.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING:
            RECORD_TYPE_ID_ADVERTISING = o.Id
        elif o.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING:
            RECORD_TYPE_ID_MANUFACTURING = o.Id

def main():
    all_sfobjects = sfimport.load_sf_from_latest_dump()
    find_account_record_types(all_sfobjects)

    rows = []
    cols = set()
    themecols = set()
    for response in all_sfobjects['SurveyResponse'].values():
        spm = all_sfobjects['SurveyPanelMember'].get(response.SurveyPanelMemberId)
        if spm is None:
            continue
        sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
        if sc is None:
            continue
        sc_account = all_sfobjects['Account'].get(sc.CustomersClientId)
        if sc_account is None:
            continue
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if cs is None:
            continue
        cs_account = all_sfobjects['Account'].get(cs.CustomerId)
        if cs_account is None:
            continue

        themes = {}
        if response.Themes:
            for jtheme in json.loads(response.Themes):
                pn = ""
                if jtheme['pn'] == 1:
                    pn = "POS/"
                elif jtheme['pn'] == -1:
                    pn = "NEG/"
                elif jtheme['pn'] == 0:
                    pn = ""
                elif jtheme['pn'] is None:
                    assert jtheme['l1'] is None
                    continue
                else:
                    raise ValueError(f"Unknown pn value: {jtheme['pn']}")

                full_name = f"{pn}{jtheme['l1']}/{jtheme['l2']}/{jtheme['l3']}:{jtheme['id']}"
                themes[full_name] = 1

        if cs_account.RecordTypeId == RECORD_TYPE_ID_ADVERTISING:
            vertical = 'adv'
        elif cs_account.RecordTypeId == RECORD_TYPE_ID_MANUFACTURING:
            vertical = 'mfv'
        else:
            raise ValueError(f"Unknown account record type: {cs_account.RecordTypeId}")

        row = {}
        row['feedback_question'] = response.FeedbackQuestion
        row['feedback_translated'] = response.FeedbackTranslated
        row['feedback'] = response.Feedback
        row['vertical'] = vertical
        row['score_question'] = response.ScoreQuestion
        row['is_extra_question'] = response.IsExtraQuestion
        row['score'] = response.Score
        row['survey_response_id'] = response.Id
        row['survey_panel_member_id'] = spm.Id
        row['contact_id'] = spm.ContactId
        row['contact_email'] = spm.ContactEmail
        row['survey_client_id'] = sc.Id
        row['survey_client_name'] = sc.Name
        row['survey_client_account_id'] = sc.CustomersClientId
        row['survey_client_account_name'] = sc_account.Name
        row['customer_survey_id'] = cs.Id
        row['customer_survey_name'] = cs.Name
        row['customer_survey_account_id'] = cs.CustomerId
        row['customer_survey_account_name'] = cs_account.Name
        cols.update(row.keys())

        row.update(themes)
        themecols.update(themes.keys())

        rows.append(row)

    with open('reginadata.csv', 'w') as f:
        allcols = list(cols) + list(sorted(themecols))
        csvout = csv.DictWriter(f, fieldnames=allcols)
        csvout.writeheader()
        csvout.writerows(rows)

if __name__ == "__main__":
    main()
