import argparse
import csv


def main():

    emails = set()
    # for file in ['dentsu permissions for Salesforce.template - dashboardsreports.csv',
    #              'dentsu permissions for Salesforce.account managers.csv',
    #              'dentsu permissions for Salesforce.survey panel managers.csv']:
    for file in ['Wirz Migration - New Staff CSV Structure  (1) (1).template - dashboardsreports.csv',
                 'Wirz Migration - New Staff CSV Structure  (1) (1).account managers.csv',
                 'Wirz Migration - New Staff CSV Structure  (1) (1).survey panel managers.csv',
                 'Wirz Migration - New Staff CSV Structure  (1) (1).signatories.csv']:
        with open(f'/home/<USER>/dev/crc-data/{file}') as f:
            csvin = csv.DictReader(f)

            for row in csvin:
                emails.add(row['Email Address'].lower())


    for email in emails:
        print(email)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
