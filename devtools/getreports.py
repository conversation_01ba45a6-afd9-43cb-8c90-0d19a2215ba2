import argparse
from itertools import islice
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
from lib.settings import settings
from pymongo import UpdateOne
import dns.resolver
import csv
import sys


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    out = open('reports.csv', 'w')
    csvout = csv.DictWriter(out, fieldnames=['Id',
                                             'ContentDocument.Title',
                                             'ContentDocumentId',
                                             'Owner.Name',
                                             'CreatedBy.Name',
                                             'CreatedDate',
                                             'LastModifiedBy.Name',
                                             'LastModifiedDate',
                                             'Share_in_Client_Area__c',
                                             'Report_Type__c',
                                             'ContactId',
                                             'ContactEmail'])
    csvout.writeheader()

    soql = """
    SELECT Id, Email
    FROM  Contact
    """
    contacts = {}
    for contact in sfapi.bulk_query(sf, soql):
        contacts[contact['Id']] = contact

    soql = """
Select Id,
        ContentDocument.Title,
        ContentDocumentId,
        Owner.Name,
        CreatedBy.Name,
        CreatedDate,
        LastModifiedBy.Name,
        LastModifiedDate,
        Share_in_Client_Area__c,
        Report_Type__c
From ContentVersion
    """
    content_versions = {}
    content_document_ids = set()
    for row in sfapi.bulk_query(sf, soql):
        content_versions.setdefault(row['ContentDocumentId'], []).append(row)
        content_document_ids.add(row['ContentDocumentId'])
    content_document_ids = list(content_document_ids)

    soql = """
SELECT Id,
        ContentDocumentId,
        LinkedEntityId
FROM ContentDocumentLink
WHERE ContentDocumentId IN {ids}
"""
    while content_document_ids:
        content_document_ids_batch = content_document_ids[:1000]
        content_document_ids = content_document_ids[1000:]

        batch_soql = format_soql(soql, ids=content_document_ids_batch)
        for row in sfapi.bulk_query(sf, batch_soql):

            document_content_versions = content_versions.get(row['ContentDocumentId'])
            if len(document_content_versions) > 1:
                raise Exception(f"Multiple content versions for {row['ContentDocumentId']}")
            document_content_version = document_content_versions[0]

            contact = contacts.get(row['LinkedEntityId'])
            if not contact:
                continue

            outrow = {
                'Id': document_content_version['Id'],
                'ContentDocument.Title': document_content_version['ContentDocument.Title'],
                'ContentDocumentId': document_content_version['ContentDocumentId'],
                'Owner.Name': document_content_version['Owner.Name'],
                'CreatedBy.Name': document_content_version['CreatedBy.Name'],
                'CreatedDate': document_content_version['CreatedDate'],
                'LastModifiedBy.Name': document_content_version['LastModifiedBy.Name'],
                'LastModifiedDate': document_content_version['LastModifiedDate'],
                'Share_in_Client_Area__c': document_content_version['Share_in_Client_Area__c'],
                'Report_Type__c': document_content_version['Report_Type__c'],
                'ContactId': contact['Id'],
                'ContactEmail': contact['Email'],
            }
            csvout.writerow(outrow)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
