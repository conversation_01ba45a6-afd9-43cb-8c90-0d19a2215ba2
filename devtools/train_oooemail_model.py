from datasets import load_dataset, DatasetDict, load_from_disk
from transformers import <PERSON>Tokenizer
from transformers import AutoModelForSequenceClassification, AutoConfig
from transformers import Trainer
import transformers
import numpy as np
import evaluate
import psycopg2
import datasets
import argparse
import os
from email.parser import BytesParser
from email import policy
from dotenv import load_dotenv
from bs4 import BeautifulSoup
import csv

load_dotenv()

# BASE_MODEL = "google-bert/bert-base-cased"
BASE_MODEL = "distilbert/distilbert-base-uncased"
ID_TO_LABEL = {
    "0": "not_out_of_office",
    "1": "out_of_office"
}

def generate_raw_dataset_csv(filename):
    if os.path.exists(filename):
        return

    conn = psycopg2.connect(os.getenv("EMAIL_DATABASE_URL"))
    cursor = conn.cursor()

    f = open(filename, 'w')
    csvout = csv.DictWriter(f, fieldnames=['email_id', 'from', 'subject', 'text', 'bucket', 'label'])
    csvout.writeheader()

    sql = """
SELECT email_id, json, eml, bucket, label
FROM email
WHERE action = 'received' AND bucket != 'dmarc'
AND label IS NOT NULL
ORDER BY email_id
"""
    cursor.execute(sql)
    for row in cursor:
        current_email_id, current_email_json, current_email_eml, bucket, label = row
        current_email_msg = BytesParser(policy=policy.default).parsebytes(current_email_eml.encode('utf-8'))

        fromEmails = set()
        fromEmails.add(current_email_json['sender']['emailAddress']['address'].lower())
        fromEmails.add(current_email_json['from']['emailAddress']['address'].lower())
        fromEmails = ",".join(fromEmails)

        body_html = current_email_msg.get_body(['html', 'plain']).get_content().strip()
        if not body_html:
            body_html = current_email_msg.get_body(['plain']).get_content().strip()
        soup = BeautifulSoup(body_html, "html.parser")
        text = soup.get_text(separator=" ").strip()

        subject = current_email_msg['subject']

        csvout.writerow({'email_id': str(current_email_id), 'from': fromEmails, 'subject': subject, 'text': text, 'bucket': bucket, 'label': label})


def generate_dataset_from_raw_csv(raw_csv_filename, dataset_name):
    if os.path.exists(dataset_name):
        return load_from_disk(dataset_name)

    ds = load_dataset("csv", data_files=raw_csv_filename)['train']

    # keep copy of original text
    ds = ds.rename_columns({'label': 'original_label', 'text': 'original_text'})

    # convert labels to numerics
    def label_to_numeric(label):
        if label == 'outofoffice':
            return 1

        else:
            return 0 # other
    ds = ds.map(lambda x: {'label': label_to_numeric(x['original_label'])})

    # prepend subject to body (and ensure there are no None texts)
    ds = ds.map(lambda x: {'text': f"{x['subject']} {x['original_text'] or ''}".strip().lower()})

    # drop any really short texts
    ds = ds.filter(lambda x: len(x['text']) > 10)

    # first of all, drop duplicate rows where the text and the label are identical to avoid cross-pollination between the different datasets
    df = ds.to_pandas()
    df_deduped = df.drop_duplicates(subset=['text', 'label'])

    # now drop all rows which still have duplicate text - this is where the text is identical, but the labels must be different!
    # no idea what "correct" is here, so drop them all on the floor
    df_without_conflicts = df_deduped.drop_duplicates(subset=['text'], keep=False)

    print(f"Original size {len(df)}, deduped size {len(df_deduped)}, conflictsremoved size {len(df_without_conflicts)}")
    print("Label distribution")
    print(df_without_conflicts.groupby('label').size())

    # create the final sets and save it
    ds = datasets.Dataset.from_pandas(df_without_conflicts)
    train_test_ds = ds.train_test_split(test_size=0.2)
    test_valid_ds = train_test_ds['test'].train_test_split(test_size=0.5)
    ds = DatasetDict({
        'train': train_test_ds['train'],
        'test': test_valid_ds['test'],
        'valid': test_valid_ds['train']
    })
    ds.save_to_disk(dataset_name)
    return ds


def train(ds, model_name):
    print("TRAINING")

    # remove any unnecessary columns
    cols = set(ds['train'].column_names)
    cols.remove('label')
    cols.remove('text')
    ds = ds.remove_columns(cols)

    label2id = {v: k for k, v in ID_TO_LABEL.items()}
    config = AutoConfig.from_pretrained(BASE_MODEL, label2id=label2id, id2label=ID_TO_LABEL)
    model = AutoModelForSequenceClassification.from_pretrained(BASE_MODEL,
                                                               config=config,
                                                               torch_dtype="auto")
    tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)

    def tokenize_function(examples):
        return tokenizer(examples["text"], padding="max_length", truncation=True)
    tokenized_ds = ds.map(tokenize_function, batched=True)

    metric = evaluate.load("accuracy")
    def compute_metrics(eval_pred):
        logits, labels = eval_pred
        predictions = np.argmax(logits, axis=-1)
        return metric.compute(predictions=predictions, references=labels)

    training_args = transformers.TrainingArguments(
        eval_strategy="epoch",
        output_dir=f"{model_name}-train-tmp",
    )
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_ds["train"],
        eval_dataset=tokenized_ds["test"],
        compute_metrics=compute_metrics,
    )
    trainer.train()

    tokenizer.save_pretrained(model_name)
    model.save_pretrained(model_name)


def validate(ds, model_name):
    print("VALIDATING")
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    def tokenize_function(examples):
        return tokenizer(examples["text"], padding="max_length", truncation=True, return_tensors="pt")
    tokenized_ds = ds.map(tokenize_function, batched=True)

    incorrect_labels = {}
    metric = evaluate.load("accuracy")
    def compute_metrics(eval_pred: transformers.trainer_utils.EvalPrediction):
        nonlocal incorrect_labels

        logits, labels = eval_pred
        predictions = np.argmax(logits, axis=-1)

        for i, (l, p) in enumerate(zip(labels, predictions)):
            if l != p:
                incorrect_labels[i] = p

        return metric.compute(predictions=predictions, references=labels)

    trainer = Trainer(
        model=model,
        eval_dataset=tokenized_ds["valid"],
        compute_metrics=compute_metrics,
    )
    metrics = trainer.evaluate()
    print("Validation", metrics)
    for i, p in incorrect_labels.items():
        print("----------------------")
        print(ds['valid'][i]['label'], p)
        print(ds['valid'][i]['subject'])


def main(out_base_name):
    generate_raw_dataset_csv(f'crc-oooemail-{out_base_name}-raw.csv')
    ds = generate_dataset_from_raw_csv(f"crc-oooemail-{out_base_name}-raw.csv", f'crc-oooemail-{out_base_name}-ds')
    train(ds, f'crc-oooemail-{out_base_name}-model')
    validate(ds, f'crc-oooemail-{out_base_name}-model')


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("out_base_name")
    args = ap.parse_args()

    main(args.out_base_name)
