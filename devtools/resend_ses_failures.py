""" Resend emails that failed to send via SES
"""
from typing import Any
import bson
from lib.settings import settings
import argparse
from datetime import datetime, UTC
from lib import emailapi, sfapi, portaldbapi, sqsapi
from itertools import islice
from pymongo import UpdateOne
import time


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch


def resend_emails(db, scheduled_emails):
    today:datetime.date = datetime.now(UTC)
    sqs:sqsapi.SQS = sqsapi.SQS()
    es:emailapi.EmailService = emailapi.EmailService(db, sqs)
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)

    batch_db = []
    # process the emails in batches of 10 to be super careful about SES rate limits (14 per sec)
    for email_batch in batch_iterable(scheduled_emails, 10):
        # add sleep for 1 second to avoid hitting SES rate limits
        time.sleep(1)
        print(f'Processing batch of {len(email_batch)} emails to resend...')
        for email in email_batch:
            id:bson.ObjectId = email.get('_id')
            spm_ids:list[str] = email.get('survey_panel_member_ids')
            sender_email:str = email.get('sender_email')
            sender_name:str = email.get('sender_name')
            recipient:str = email.get('recipient')
            email_template_name:str = email.get('template_name')
            template_data:dict = email.get('template_data')
            template_metadata:dict = email.get('template_metadata')
            send_at:datetime = email.get('scheduled_date_utc')
            language:str = email.get('language')
            vertical:str = email.get('vertical')
            banner_attachment:str = email.get('banner_attachment')
            signature_attachment:str = email.get('signature_attachment')
            sf_spm_field:str = email.get('sf_spm_field')
            portaldb_record_map:str = f'scheduledemails-{id}'
            email_service_provider:str = email.get('email_service_provider', 'sendgrid')

            # push email to SQS queue
            try:
                es.send(sender_email, 
                        recipient, 
                        email_template_name, 
                        template_data, 
                        template_metadata,
                        send_at=send_at,
                        from_name=sender_name,
                        language=language, 
                        vertical=vertical, 
                        banner_attachment=banner_attachment,
                        signature_attachment=signature_attachment,
                        portaldb_record_map=portaldb_record_map,
                        service_provider=email_service_provider)
        
                # update the scheduled email record in the portaldb to mark as sent
                batch_db.append(UpdateOne({
                    '_id': id
                }, {
                    '$set': {
                        'sent_to_sendgrid_date': today
                    }
                }))

                print(f'   :: Email Scheduled: scheduledemails record {id} for {recipient} at {send_at} UTC')
            except Exception as e:
                print(f"Failed to send email for record {id}: {e}")
    
    if batch_db:
        collection.bulk_write(batch_db)


def main(starttime: str, endtime:str, customer_survey_round_id:str|None = None, writeit:bool = False) -> None:
    starttime = datetime.fromisoformat(starttime)
    endtime = datetime.fromisoformat(endtime)

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    scheduledemails_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION]
    emaillog_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_EMAILLOG_COLLECTION]

    # look for entries in the email log where the ses_message_id is an empty string, indicating that the email failed to send via SES
    emaillog_query = {
        'timestamp': {
            '$gte': starttime,
            '$lte': endtime
        },
        'ses_message_id': '',
        'resent_at': {'$exists': False}
    }
    if customer_survey_round_id:
        emaillog_query['event_data.customer_survey_round_id'] = customer_survey_round_id
    email_count = 0
    error_count = 0
    batch = []
    scheduled_emails_to_resend = []
    for doc in emaillog_collection.find(emaillog_query):
        email_count += 1
        portaldb_record_map = doc.get('portaldb_record_map')
        scheduled_email_doc_id = portaldb_record_map.split('-')[1] if portaldb_record_map else None
        if not scheduled_email_doc_id:
            print(f"Skipping email log with no portaldb_record_map: {doc['_id']} for recipient {doc['recipient']}")
            error_count += 1
            continue
        scheduled_email_doc = scheduledemails_collection.find_one({'_id': bson.ObjectId(scheduled_email_doc_id)})
        if not scheduled_email_doc:
            print(f"Scheduled email doc not found for ID: {scheduled_email_doc_id} for recipient {doc['recipient']} with emaillog ID {doc['_id']}")
            error_count += 1
            continue

        print(f"Found scheduled email doc for ID: {scheduled_email_doc_id} for recipient {doc['recipient']} with emaillog ID {doc['_id']}")
        scheduled_emails_to_resend.append(scheduled_email_doc)
        batch.append(UpdateOne(
            {'_id': doc['_id']},
            {'$set': {
                'resent_at': datetime.now(UTC),
                'resend_reason': 'Failed to send via SES'
            }}
        ))

    if writeit:
        print(f"Resending {len(scheduled_emails_to_resend)} emails...")
        resend_emails(db, scheduled_emails_to_resend)
        emaillog_collection.bulk_write(batch)
    print(f"Found {email_count} emails to process, {error_count} errors encountered.")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("starttime", type=str, help="The time to start looking for email events, e.g. 2011-11-04T00:05:23Z")
    ap.add_argument("endtime", type=str, help="The time to stop looking for email events")
    ap.add_argument('--customer_survey_round_id', default=None, help='single survey round to run for')
    ap.add_argument('--writeit', action='store_true', help='Update the email schedule to send again')
    args = ap.parse_args()

    main(args.starttime, args.endtime, args.customer_survey_round_id, args.writeit)
