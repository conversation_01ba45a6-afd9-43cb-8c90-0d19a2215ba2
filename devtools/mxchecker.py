from lib import sfimport
import dns.resolver
import csv


def detect_provider(exchange):
    exchange = exchange.lower().strip()

    if exchange.endswith('.protection.outlook.com.'):
        return "Outlook"

    elif exchange.endswith('.mimecast.com.'):
        return "Mimecast"

    elif exchange.endswith('.mimecast.co.za.'):
        return "Mimecast"

    elif exchange.endswith('.pphosted.com.'):
        return "Proofpoint"

    elif exchange.endswith('.ppe-hosted.com.'):
        return "Proofpoint"

    elif exchange.endswith('.google.com.'):
        return "Google"

    elif exchange.endswith('.googlemail.com.'):
        return "Google"

    elif exchange.endswith('.iphmx.com.'):
        return "Cisco Secure Email"

    elif exchange.endswith('.trendmicro.com.'):
        return "Trend Micro"

    elif exchange.endswith('.trendmicro.au.'):
        return "Trend Micro"

    elif exchange.endswith('.trendmicro.eu.'):
        return "Trend Micro"

    elif exchange.endswith('.mailcontrol.com.'):
        return "Forcepoint One Mailcontrol"

    elif exchange.endswith('.messagelabs.com.'):
        return "MessageLabs/Symantec/Broadcom"

    elif exchange.endswith('.barracudanetworks.com.'):
        return "Barracuda Networks"

    elif exchange.endswith('.sophos.com.'):
        return "Sophos Antivirus"

    elif exchange.endswith('.hornetsecurity.com.'):
        return "Hornet Security"

    elif exchange.endswith('.secureserver.net.'):
        return "GoDaddy"

    elif exchange.endswith('.amazonaws.com.'):
        return "AWS"

    return None


def main():
    all_sfobjects = sfimport.load_sf_from_latest_dump(exclude_files={'survey_response.csv', 'survey_panel_member.csv'})

    resolver = dns.resolver.Resolver()
    resolver.lifetime = 10
    resolver.nameservers = ['1.1.1.1']

    with open('email_domains.csv', 'w') as f:
        csvout = csv.writer(f)
        csvout.writerow(['domain', 'exchange', 'preference', 'provider', 'error'])

        domains = set()
        for contact in all_sfobjects['Contact'].values():
            account = all_sfobjects['Account'].get(contact.AccountId)
            if not account:
                continue
            account_record_type = all_sfobjects['AccountRecordType'].get(account.RecordTypeId)
            if not account_record_type:
                continue

            user, domain = contact.Email.lower().split('@', 1)
            domains.add(domain)

        print(len(domains))

        domain_count = 0
        for domain in domains:
            try:
                mx_response = resolver.resolve(domain, 'MX')
            except (dns.resolver.NoAnswer, dns.resolver.LifetimeTimeout, dns.resolver.NXDOMAIN, dns.resolver.NoNameservers) as e:
                csvout.writerow([domain, None, None, None, str(e)])
                print("nope", domain, e)
                continue

            for mx in sorted(mx_response, key=lambda x: x.preference):
                provider = detect_provider(str(mx.exchange))
                csvout.writerow([domain, str(mx.exchange), str(mx.preference), provider])

                if provider:
                    domain_count += 1

            if (domain_count % 1000) == 0:
                print(domain_count)

if __name__ == "__main__":
    main()
