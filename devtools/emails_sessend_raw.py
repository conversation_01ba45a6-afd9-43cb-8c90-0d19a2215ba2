from typing import Any
from io import BytesIO
import base64
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from lib.settings import settings
import argparse
import json
import datetime
import boto3
import os
from lib import portaldbapi
import jinja2
from dotenv import load_dotenv

load_dotenv()

DEFAULT_FROM_NAME = "VerityRI"


def get_attachment_from_s3(attachment: str, s3_bucket: str):
    attachment_dict: dict[str, str] = json.loads(attachment)

    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=s3_bucket, Key=attachment_dict['key'])
    response_body = response['Body'].read()

    with BytesIO(response_body) as f:
        content = f.read()

    msg_att = MIMEApplication(content)
    msg_att.add_header('Content-Disposition',attachment_dict['disposition'], filename=attachment_dict['key'])
    msg_att.add_header('Content-Type', attachment_dict['file_type'])
    msg_att.add_header('Content-ID', attachment_dict['content_id'])
    return msg_att


def _get_template(template_name: str, template_ext: str):
    with open(f'emailtemplates/{template_name}.{template_ext}', 'r') as file:
        return jinja2.Template(file.read())


def send_ses_email(ses,
                from_email: str,
                to_email: str,
                template_name: str,
                template_data: str,
                custom_args: str,
                banner_attachment: str|None,
                signature_attachment: str|None,
                send_at: str|None,
                from_name: str|None,
                token: str|None,
                allow_nonvaul: bool|None) -> str:
    if not allow_nonvaul:
        assert to_email.endswith('@vaullabs.com')

    # convert template data to dict
    template_data_dict: dict[str, str] = json.loads(template_data)

    # correct any double slashes in URLs
    for k, v in template_data_dict.items():
        if v.startswith('https://'):
            template_data_dict[k] = v.replace('//', '/')

    # convert custom args to dict
    custom_args_dict: dict[str, str] = json.loads(custom_args)

    # convert send_at to datetime object (if provided)
    if send_at:
        send_at: datetime.datetime = datetime.datetime.strptime(send_at, '%Y-%m-%d %H:%M:%S')
        send_at: int = int(send_at.timestamp())

    # set default from name if not provided
    if not from_name:
        from_name = DEFAULT_FROM_NAME

    # build sender string
    sender = f"{from_name} <{from_email}>"

    # add stage to survey link so we can tell who it is
    if 'SurveyLink' in template_data_dict:
        template_data_dict['SurveyLink'] = template_data_dict['SurveyLink'] + f"?stage=4"

    # render the body
    body_html = _get_template(template_name, 'html').render(**template_data_dict).strip()
    body_text = _get_template(template_name, 'txt').render(**template_data_dict).strip()
    subject = _get_template(template_name, 'subject').render(**template_data_dict).strip()

    # tracking token for spam testing
    if token:
        body_text += f" {token}"

    # define initial message
    msg = MIMEMultipart('mixed')
    msg['Subject'] = subject
    msg['From'] = sender
    msg['To'] = to_email

    # Add the text and HTML parts to the child container.
    msg_body = MIMEMultipart('alternative')
    msg_body.attach(MIMEText(body_text.encode('utf-8'), 'plain', 'utf-8'))
    msg_body.attach(MIMEText(body_html.encode('utf-8'), 'html', 'utf-8'))
    msg.attach(msg_body)

    if banner_attachment:
        try:
            banner = get_attachment_from_s3(banner_attachment, settings.SF_BANNERS_BUCKET)
            msg.attach(banner)
        except Exception as e:
            print(f"Error fetching banner attachment from S3: {e}")
            raise Exception('Error fetching banner attachment from S3')

    if signature_attachment:
        try:
            signature = get_attachment_from_s3(signature_attachment, settings.SF_SIGNATURES_BUCKET)
            msg.attach(signature)
        except Exception as e:
            print(f"Error fetching signature attachment from S3: {e}")
            raise Exception('Error fetching signature attachment from S3')

    # # attach send_at to the message (if provided)
    # if send_at:
    #     message.send_at = send_at

    # # if we have custom args, append them to the message
    # if email.custom_args:
    #     metadata: list[CustomArg] = []
    #     for key, value in email.custom_args.items():
    #         metadata.append(CustomArg(key=key, value=value))
    #     message.custom_arg = metadata

    # sendit
    response = ses.send_email(
        FromEmailAddress=sender,
        Destination={
            'ToAddresses': [to_email],
        },
        Content={
            'Raw': {
                'Data': msg.as_bytes()
            },
        },
    )
    print(response)


def main(original_recipient: str, to_emails: str, template_name: str, token: str = None, allow_nonvaul: bool = False) -> None:
    ses = boto3.client('sesv2',
                       aws_access_key_id=os.environ.get('SES_ACCESS_KEY_ID'),
                       aws_secret_access_key=os.environ.get('SES_SECRET_ACCESS_KEY'))
    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()

    scheduledemails_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION]

    to_emails = to_emails.split(',')

    q = {
        'recipient': original_recipient,
        'deleted': {'$exists': False}
    }
    for doc in scheduledemails_collection.find(q):
        for to_email in to_emails:
            print(f"Sending email to {to_email} for {doc['_id']}")

            # get other gubbins
            template_data = json.dumps(doc.get('template_data', {}))
            banner_attachment = None
            if doc.get('banner_attachment'):
                banner_attachment = json.dumps(doc['banner_attachment'])
            signature_attachment = None
            if doc.get('signature_attachment'):
                signature_attachment = json.dumps(doc['signature_attachment'])

            send_ses_email(ses,
                       doc.get('sender_email'),
                       to_email,
                       template_name,
                       template_data,
                       '{}',   # never supply custom_args to prevent confusing the real system tracking
                       banner_attachment,
                       signature_attachment,
                       None, # always send now
                       doc.get('sender_name'),
                       token=token,
                       allow_nonvaul=allow_nonvaul)

        break


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('template_name')
    ap.add_argument('original_recipient')
    ap.add_argument('to_emails')
    ap.add_argument('--token')
    ap.add_argument('--allow-nonvaul', action='store_true', help='Allow sending to non-vaullabs.com email addresses')
    args = ap.parse_args()

    main(args.original_recipient, args.to_emails, args.template_name, args.token, args.allow_nonvaul)
