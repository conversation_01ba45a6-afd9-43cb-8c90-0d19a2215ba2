import csv
import argparse
import codecs
from sentence_transformers import SentenceTransformer, util
from nltk.translate.meteor_score import meteor_score
import nltk
from nltk import word_tokenize


SCORING_FIELD_NAMES = ['hc_google_cosine_score',
                       'hc_google_meteor_score',
                       'amazon_cosine_score',
                       'amazon_meteor_score',
                       'deepl_cosine_score',
                       'deepl_meteor_score']

def main(filename):
    # model = SentenceTransformer('multi-qa-MiniLM-L6-cos-v1')
    model = SentenceTransformer('all-MiniLM-L6-v2')
    nltk.download('punkt')
    nltk.download('wordnet')

    with open(filename, "r", encoding="utf-8-sig") as inf:
        csvin = csv.DictReader(inf)

        with open('scored.csv', 'w') as outf:
            outf.write(codecs.BOM_UTF8.decode('utf-8'))
            fieldnames = csvin.fieldnames + SCORING_FIELD_NAMES
            csvout = csv.DictWriter(outf, fieldnames=fieldnames)
            csvout.writeheader()

            for row in csvin:
                crc_translation_vector = model.encode(row['crc_translation'])
                crc_tokenized = word_tokenize(row['crc_translation'])

                for fieldprefix in ['hc_google', 'amazon', 'deepl']:
                    fieldprefix_translation_vector = model.encode(row[f'{fieldprefix}_translation'])

                    escore = float(util.cos_sim(crc_translation_vector, fieldprefix_translation_vector))
                    mscore = meteor_score([crc_tokenized], word_tokenize(row[f'{fieldprefix}_translation']))

                    row[f'{fieldprefix}_cosine_score'] = escore
                    row[f'{fieldprefix}_meteor_score'] = mscore

                csvout.writerow(row)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("filename")
    args = ap.parse_args()

    main(args.filename)
