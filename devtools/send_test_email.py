""" Send a test email using an existing scheduled email record as a template
"""
import bson
from lib.settings import settings
import argparse
from datetime import datetime, UTC
from lib import emailapi, sfapi, portaldbapi, sqsapi


def resend_email(db, email):
    sqs:sqsapi.SQS = sqsapi.SQS()
    es:emailapi.EmailService = emailapi.EmailService(db, sqs)

    id:bson.ObjectId = email.get('_id')
    # spm_ids:list[str] = email.get('survey_panel_member_ids')
    sender_email:str = email.get('sender_email')
    sender_name:str = email.get('sender_name')
    recipient:str = email.get('recipient')
    email_template_name:str = email.get('template_name')
    template_data:dict = email.get('template_data')
    template_metadata:dict = email.get('template_metadata')
    send_at:datetime = email.get('scheduled_date_utc')
    language:str = email.get('language')
    vertical:str = email.get('vertical')
    banner_attachment:str = email.get('banner_attachment')
    signature_attachment:str = email.get('signature_attachment')
    # sf_spm_field:str = email.get('sf_spm_field')
    portaldb_record_map:str = f'scheduledemails-{id}'
    email_service_provider:str = email.get('email_service_provider', 'sendgrid')

    # push email to SQS queue
    try:
        es.send(sender_email, 
                recipient, 
                email_template_name, 
                template_data, 
                template_metadata,
                send_at=send_at,
                from_name=sender_name,
                language=language, 
                vertical=vertical, 
                banner_attachment=banner_attachment,
                signature_attachment=signature_attachment,
                portaldb_record_map=portaldb_record_map,
                service_provider=email_service_provider)

        print(f'   :: Email Scheduled: scheduledemails record {id} for {recipient} at {send_at} UTC')
    except Exception as e:
        print(f"Failed to send email for record {id}: {e}")


def main(customer_survey_round_id:str, template_name:str, original_recipient:str, new_recipient:str, writeit:bool = False) -> None:

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    scheduledemails_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION]

    # look for entries in the email log where the ses_message_id is an empty string, indicating that the email failed to send via SES
    query = {
        'recipient': original_recipient,
        'template_name': template_name,
        'customer_survey_round_id': customer_survey_round_id,
    }

    docs = list(scheduledemails_collection.find(query))
    if len(docs) != 1:
        raise ValueError(f"Expected exactly one scheduled email record, found {len(docs)}")
    scheduled_email = docs[0]
    # update the recipient and the send time 
    # and change the tracked template name so we ignore email events for this test email
    scheduled_email['recipient'] = new_recipient
    scheduled_email['scheduled_date_utc'] = datetime.now(UTC)
    scheduled_email['template_metadata']['tracked_template_name'] = 'ignore_this_test_email'

    if writeit:
        print(f"Resending email to {new_recipient} using template {template_name}")
        resend_email(db, scheduled_email)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('customer_survey_round_id', type=str, help='single survey round to run for')
    ap.add_argument('template_name', type=str, help='The email template name')
    ap.add_argument('original_recipient', type=str, help='The original recipient email address')
    ap.add_argument('new_recipient', type=str, help='The new recipient email address to send the test email to')
    ap.add_argument('--writeit', action='store_true', help='Actually send the email')
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.template_name, args.original_recipient, args.new_recipient, args.writeit)
