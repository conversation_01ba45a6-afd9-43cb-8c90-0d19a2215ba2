import json
import base64
from PIL import Image
from io import BytesIO

def main():
    j = json.load(open('newtech.json'))

    j['html_content'] = open('jm_simplified.html').read()

    j['plain_text_content'] = open('jm_simplified.txt').read()

    for a in j['attachments']:
        content = base64.b64decode(a['file_content_base64'])
        im = Image.open(BytesIO(content))
        im = im.convert('RGB')
        temp = BytesIO()
        im.save(temp, format="JPEG")
        content = temp.getvalue()

        a['file_content_base64'] = base64.b64encode(content).decode()
        a['file_type'] = 'image/jpeg'
        a['file_name'] = a['file_name'].replace('.png', '.jpg')

    print(json.dumps(j, indent=2))

main()