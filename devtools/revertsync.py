import lib.portald<PERSON><PERSON> as portaldbapi
import argparse
from datetime import datetime, timezone


DENTSU_Q2_2025_CSR_ID = 'a02WT00000AhrWvYAJ'
ACCOUNTS_CONFIRMED_BY_DATE = datetime(2025, 4, 25, 0, 0, 0, tzinfo=timezone.utc)
SF_SYNC_DATE_START = datetime(2025, 4, 26, 0, 0, 0, tzinfo=timezone.utc)
SF_SYNC_DATE_END = datetime(2025, 4, 27, 0, 0, 0, tzinfo=timezone.utc)


def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]

    total_affected = 0
    total_successfully_updated = 0
    find_query = {
        'customer_survey_round_id': DENTSU_Q2_2025_CSR_ID,
        'confirm_status': {'$ne': False},
        'accounts_confirmed_by_date': ACCOUNTS_CONFIRMED_BY_DATE,
        'sfsync_date': {
            '$gte': SF_SYNC_DATE_START,
            '$lt': SF_SYNC_DATE_END,
        },
    }

    for confirm_account in confirm_accounts.find(find_query):
        total_affected += 1
        print(confirm_account['Id'])

        update_query = {
            'Id': confirm_account['Id'],
        }
        update = {
            '$set': {
                'sfsync_date': None,
                'confirm_status': False,
                'auto_confirmed': False,
                'final_confirmed_accounts': [],
            },
            '$unset': {
                'confirm_date': "",
            }
        }
        result = confirm_accounts.update_one(update_query, update)

        if result.modified_count > 0:
            total_successfully_updated += 1

    print(f'Total affected: {total_affected}')
    print(f'Total successfully updated: {total_successfully_updated}')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
