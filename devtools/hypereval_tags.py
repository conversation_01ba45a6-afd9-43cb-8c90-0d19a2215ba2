import csv
import pandas as pd

rows = []
header = None
count = 0
with open('/home/<USER>/dev/crc-data/hyper_eval/hypercube_feedback_processing/hypercube_feedback_attribution_2024-06-14.csv') as f:
    csvin = csv.DictReader(f)

    for row in csvin:
        row['source'] = 'hypercube'

        if not header:
            header = list(row.keys())
            header.append('hubTranslation')
        rows.append(row)

        # if count > 20000:
        #     break
        # count += 1

count = 0
with open('/home/<USER>/dev/crc-data/hyper_eval/hypercube_feedback_processing/codeit_feedback_attribution_2024-06-14.csv') as f:
    csvin = csv.DictReader(f)

    for row in csvin:
        row['source'] = 'codeit'
        rows.append(row)

        # if count > 20000:
        #     break
        # count += 1

cols = [
    'agencyId',
    'agencyName',
    'contactName',
    'email',
    'accountName',
    'clientGroupName',
    'agencyOfficeName',
    'customAgency',
    'feedbackId',
    'roundId',
    'roundNumber',
    'feedbackTimestamp',
    'rating',
    'attributeId',
    'lvl1Category',
    'lvl2Category',
    'lvl3Category',
    'positiveNegative',
]

rowsdf = pd.DataFrame(rows, dtype=str)
rowsdf = rowsdf.groupby(cols)

def longest(x):
    v = None
    for i in range(len(x)):
        cur = x.iloc[i]
        if type(cur) is not str:
            continue

        if v is None:
            v = cur

        elif cur is not None and len(cur) > len(v):
            v = cur
    return v

rowsdf = rowsdf.agg({'source': lambda x: ",".join(x),
                     'feedback': longest,
                     'hubTranslation': longest,
                     'hypercubeTranslation': longest,
                     })

rowsdf = rowsdf.sort_values(['feedbackId', 'lvl1Category', 'lvl2Category', 'lvl3Category', 'positiveNegative'])
rowsdf.to_csv('tags.csv')
