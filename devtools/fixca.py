import lib.portaldbapi as portaldbapi
import argparse
import copy
import datetime


##################################
# DEBUGGING FUNCTIONS
##################################
class PanelManagerDebugger:
    def __init__(self, enabled=True):
        self.enabled = enabled
        self.modified_accounts = set()
        self.original_panel_manager_ids_by_account = dict()


    @staticmethod
    def green(text: str) -> str:
        return f"\033[92m{text}\033[0m"


    @staticmethod
    def red(text: str) -> str:
        return f"\033[91m{text}\033[0m"


    @staticmethod
    def yellow(text: str) -> str:
        return f"\033[93m{text}\033[0m"


    @staticmethod
    def diff_panel_managers(before: list[dict], after: list[dict]) -> dict:
        before_ids = {pm['panel_manager_id'] for pm in before}
        after_ids = {pm['panel_manager_id'] for pm in after}

        added_ids = after_ids - before_ids
        removed_ids = before_ids - after_ids
        unchanged_ids = before_ids & after_ids

        added = [pm for pm in after if pm['panel_manager_id'] in added_ids]
        removed = [pm for pm in before if pm['panel_manager_id'] in removed_ids]

        return {
            'added': added,
            'removed': removed,
            'unchanged_count': len(unchanged_ids)
        }


    def debug_merge(
        self,
        existing: list[dict],
        incoming: list[dict],
        account_id: str,
        account_name: str,
        reverse_idx: int,
        merge_func
    ) -> list[dict]:
        before = copy.deepcopy(existing)
        merged = merge_func(existing, incoming)
        diff = self.diff_panel_managers(before, merged)

        if self.enabled:
            if diff['added'] or diff['removed']:
                print(f"\n{self.yellow(f'=== Merge Diff for {account_name} (ID: {account_id}) at Index {reverse_idx} ===')}")

            if diff['added']:
                print(self.green(f"Added Panel Managers:"))
                for pm in diff['added']:
                    print(f"  - ID: {pm['panel_manager_id']}, Email: {pm['panel_manager_email']}")
                self.modified_accounts.add(account_id)

            if diff['removed']:
                print(self.red(f"Removed Panel Managers (unexpected!):"))
                for pm in diff['removed']:
                    print(f"  - ID: {pm['panel_manager_id']}, Email: {pm['panel_manager_email']}")
                self.modified_accounts.add(account_id)

        return merged


    def capture_initial_panel_managers(self, master_account_list: dict):
        frozen_master = copy.deepcopy(master_account_list)

        self.original_panel_manager_ids_by_account = {}

        for account in frozen_master.values():
            if account.get('in_round', False):
                account_id = account['account_id']
                self.original_panel_manager_ids_by_account[account_id] = {
                    pm['panel_manager_id'] for pm in account.get('survey_panel_managers', [])
                }


    def show_final_survey_panel_managers(self, index0_account_list: list[dict]):
        if not self.enabled:
            return

        print(self.yellow("\n=== Final Survey Panel Managers for Modified Accounts ===\n"))

        for account in index0_account_list:
            account_id = account['account_id']
            if account_id not in self.modified_accounts:
                continue

            account_name = account.get('account_name', 'UNKNOWN')
            print(self.yellow(f"Account: {account_name} (ID {account_id})"))

            spm_list = account.get('survey_panel_managers', [])
            if not spm_list:
                print(self.red("  No survey panel managers found."))
                continue

            spm_list_sorted = sorted(spm_list, key=lambda pm: pm['panel_manager_email'].lower())

            for pm in spm_list_sorted:
                pm_id = pm['panel_manager_id']
                pm_email = pm['panel_manager_email']

                line = f"  - ID: {pm_id}, Email: {pm_email}"

                original_pm_ids = self.original_panel_manager_ids_by_account.get(account_id, set())

                if pm_id not in original_pm_ids:
                    print(self.yellow(line))
                else:
                    print(line)

            print()
##################################
# END OF DEBUGGING FUNCTIONS
##################################


def merge_panel_managers(master_panel_managers, account_panel_managers) -> list[dict]:
    master_panel_managers_by_id = {pm['panel_manager_id']: pm for pm in master_panel_managers}
    account_by_id = {pm['panel_manager_id']: pm for pm in account_panel_managers}
    existing_emails = {pm['panel_manager_email'] for pm in master_panel_managers_by_id.values()}

    for panel_manager_id, panel_manager in account_by_id.items():
        panel_manager_email = panel_manager['panel_manager_email']
        is_new = panel_manager_id.startswith('NEW')

        if (is_new and panel_manager_email not in existing_emails) or \
           (not is_new and panel_manager_id not in master_panel_managers_by_id):
            master_panel_managers_by_id[panel_manager_id] = panel_manager
            existing_emails.add(panel_manager_email)

    return list(master_panel_managers_by_id.values())


def build_master_list(confirmed_accounts:list, key:str, debugger: PanelManagerDebugger) -> dict[str, dict]|None:
    master_account_list = None
    confirmed_accounts_length = len(confirmed_accounts)
    for idx, caccount in enumerate(reversed(confirmed_accounts)):
        reverse_idx = confirmed_accounts_length - 1 - idx

        if caccount.get('confirm_user_id') in {'verityplatform'}:
            continue

        # have we located the base account?
        if caccount.get(key) and master_account_list is None:
            master_account_list = {a['account_id']: a for a in caccount[key]}

            debugger.capture_initial_panel_managers({a['account_id']: a for a in caccount[key]})

            # need to fix the master account list to remove any accounts that were not selected in the save
            caccount_accounts = {a['account_id']: a for a in caccount['accounts']}
            for account_id, account in master_account_list.items():
                if account_id not in caccount_accounts:
                    master_account_list[account_id]['removed'] = True

            continue

        # we've not found them yet, keep going
        if master_account_list is None:
            continue

        # grab the accounts saved in this confirmation (and group by id)
        caccount_accounts = {a['account_id']: a for a in caccount['accounts']}

        # copy any changes to existing accounts into the master list
        for account_id, account in caccount_accounts.items():
            if account_id in master_account_list:
                merged_panel_managers = merge_panel_managers(
                    master_account_list[account_id]['survey_panel_managers'],
                    account['survey_panel_managers']
                )

                #merged_panel_managers = debugger.debug_merge(
                #    master_account_list[account_id]['survey_panel_managers'],
                #    account['survey_panel_managers'],
                #    account_id=account_id,
                #    account_name=account.get('account_name', 'UNKNOWN'),
                #    reverse_idx=reverse_idx,
                #    merge_func=merge_panel_managers
                #)

                account['survey_panel_managers'] = merged_panel_managers
                master_account_list[account_id] = account

        # deal with any added accounts
        for added_deets in caccount.get('stats', {}).get('added', []):
            added_account_id = added_deets['id']
            account = caccount_accounts[added_account_id]

            # if this is a new account (not just a reselection), then there's a high chance it won't exist in the master list
            merged_panel_managers = merge_panel_managers(
                master_account_list.get(added_account_id, {}).get('survey_panel_managers', []),
                account['survey_panel_managers']
            )

            account['survey_panel_managers'] = merged_panel_managers
            master_account_list[added_account_id] = account

        # deal with any removed accounts
        for removed_deets in caccount.get('stats', {}).get('removed', []):
            removed_account_id = removed_deets['id']
            if removed_account_id in master_account_list:
                master_account_list[removed_account_id]['removed'] = True

    return master_account_list


def main():
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION]
    debugger = PanelManagerDebugger(enabled=False)
    total_count = 0

    for confirm_account in confirm_accounts.find({'sfsync_date': None}):
        print(confirm_account['Id'])
        total_count += 1

        confirmed_accounts = confirm_account.get('confirmed_accounts', [])
        if not confirmed_accounts:
            continue

        master_account_list:dict|None = build_master_list(copy.deepcopy(confirmed_accounts), 'all_accounts', debugger)
   
        # if the master account list is still None, then we actually have 2 scenerios:
        # 1. there is no confirmations
        # 2. all confirmations were made AFTER the release 
        if master_account_list is None:
            master_account_list:dict|None = build_master_list(copy.deepcopy(confirmed_accounts), 'accounts', debugger)

        # there truly was no confirmations, so default to empty dict
        if master_account_list is None:
            master_account_list = {}

        # index 0 save is the list of accounts saved for this save with anything excluded
        index0_account_list = [a for a in master_account_list.values() if not a.get('removed')]

        # spit out debug of final survey panel managers state
        debugger.show_final_survey_panel_managers(index0_account_list)

        # the master account list is all accounts that were in the round originally, PLUS any added accounts
        for account in confirm_account.get('accounts', []):
            if account['account_id'] not in master_account_list and account['in_round']:
                master_account_list[account['account_id']] = account

        stamp = datetime.datetime.now()
        q = {'Id': confirm_account['Id']}
        update = {
            '$set': {
                'confirmed_accounts_last_save_all_accounts': list(master_account_list.values()),
                'confirmed_accounts_last_save_date': stamp,
                'confirmed_accounts_last_save_user_id': 'verityplatform',
                'confirmed_accounts_last_save_user_email': '<EMAIL>',
                'confirmed_accounts_last_save_user_name': 'VerityRI Support'
            },
            '$push': {
                'confirmed_accounts': {
                    '$each': [{
                        'accounts': index0_account_list,
                        'confirm_date': stamp,
                        'confirm_user_id': 'verityplatform',
                        'confirm_user_email': '<EMAIL>',
                        'confirm_user_name': 'VerityRI Support',
                        'stats': {'added': [], 'removed': []}
                    }],
                    '$position': 0
                }
            }
        }
        confirm_accounts.update_one(q, update)  
    
    print(f"Total Confirm Accounts: {total_count}")

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
