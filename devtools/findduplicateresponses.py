import argparse
import csv
import datetime
from lib import portaldbapi


def main():
    portaldb = portaldbapi.DocumentDB()
    responses_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_RESPONSES_COLLECTION]

    # Aggregation pipeline to find duplicate Id values
    pipeline = [
        {
            "$group": {
                "_id": "$Id",
                "count": {"$sum": 1}
            }
        },
        {
            "$match": {
                "count": {"$gt": 1}
            }
        }
    ]
    duplicates = responses_collection.aggregate(pipeline)
    duplicate_ids = [doc['_id'] for doc in duplicates]

    KEYS = [
        '_id', 'Id', 'client_id', 'client_name', 'contact_email', 'contact_name', 
        'customer_survey_id', 'customer_survey_round_id', 'customer_survey_round_name', 
        'rating', 'responded', 'response_id', 'response_date', 
        'is_extra_question'
    ]
    query = {"Id": {"$in": duplicate_ids}}
    projection = {x:1 for x in KEYS}
    responses = list(responses_collection.find(query, projection))

    by_id = {}
    for response in responses:
        if response['Id'] not in by_id:
            by_id[response['Id']] = []
        by_id[response['Id']].append(response)
    
    dupes = []
    for _, panel_member_responses in by_id.items():
        extra_question_count = 0
        for response in panel_member_responses:
            if response['is_extra_question']:
                extra_question_count += 1
                break
        if extra_question_count == 0 or extra_question_count > 1:
            dupes.extend(panel_member_responses)

    now = datetime.datetime.now()
    filename = f"/tmp/response_dupes_{now.isoformat()}.csv"
    with open(filename, mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(KEYS)
        for item in responses:  # dupes:
            row = [str(item[key]) for key in KEYS]
            writer.writerow(row)

    print(f'Found {len(responses)} responses')
    print(f'Found {len(dupes)} potential duplicate responses')


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()