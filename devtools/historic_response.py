from lib.settings import settings
from lib import sfimport
import csv
import argparse
import datetime
import boto3
import zipfile
import io
import os
import re

def load_sf_from_dump(include_files=None, exclude_files=None, now=None):
    s3 = boto3.client('s3')

    if not now:
        now = datetime.date.today()

    prefix = f"crc-sf/yy={now:%Y}/mm={now:%m}/dd={now:%d}/"
    response = s3.list_objects_v2(Bucket=settings.SF_DATA_BUCKET, Prefix=prefix)
    if 'Contents' not in response:
        raise ValueError(f"No objects found in bucket {settings.SF_DATA_BUCKET} with prefix {prefix}")
    keys = sorted(response['Contents'], key=lambda x: x['LastModified'], reverse=True)
    latest_key = keys[0]['Key']
    print(latest_key)

    all_sfobjects = {}
    with sfimport.s3_cached_download(settings.SF_DATA_BUCKET, latest_key) as tmp_file:
        with zipfile.ZipFile(tmp_file.name, 'r') as zip_file:
            for csv_filename in zip_file.namelist():
                if include_files and csv_filename not in include_files:
                    continue
                if exclude_files and csv_filename in exclude_files:
                    continue

                sfobject = sfimport.SFAPI_TO_CSV_MAPPING[csv_filename]
                all_sfobjects.setdefault(sfobject.__name__, {})
                this_sfobjects = all_sfobjects[sfobject.__name__]

                with zip_file.open(csv_filename) as rawstream:
                    with io.TextIOWrapper(rawstream, encoding="utf-8") as textstream:
                        csv_reader = csv.DictReader(textstream)
                        for row in csv_reader:
                            o = sfobject.model_validate(row)
                            this_sfobjects[o.Id] = o

    m = re.match(r"crc-sf/yy=\d{4}/mm=\d{2}/dd=\d{2}/crc-sf-(\d{8}-\d{6})\.zip", latest_key)
    return all_sfobjects, m.group(1)


def main(customer_survey_round_id, when):
    all_sfobjects, stamp = load_sf_from_dump(exclude_files=['survey_responses.csv'], now=when)

    with open(f'historic_response-{stamp}.csv', 'w', newline='') as csvfile:
        csvout = csv.DictWriter(csvfile, fieldnames=[
            'customer_survey_round_name',
            'customer_survey_round_id',
            'customer_survey_name',
            'customer_survey_id',
            'survey_client_name',
            'survey_client_id',
            'survey_panel_member_id',
            'contact_id',
            'contact_email',
            'email_delivered',
            'email_bounced',
            'has_responded',
        ])
        csvout.writeheader()

        for spm in all_sfobjects['SurveyPanelMember'].values():
            contact = all_sfobjects['Contact'].get(spm.ContactId)
            if not contact:
                continue
            sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
            if not sc:
                continue
            sc_account = all_sfobjects['Account'].get(sc.CustomersClientId)
            if not sc_account:
                continue
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            if not cs:
                continue
            agency_account = all_sfobjects['Account'].get(cs.CustomerId)
            if not agency_account:
                continue
            csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
            if not csr:
                continue
            if csr.Id != customer_survey_round_id:
                continue

            row = {
                'customer_survey_round_name': csr.Name,
                'customer_survey_round_id': csr.Id,
                'customer_survey_name': cs.Name,
                'customer_survey_id': cs.Id,
                'survey_client_name': sc.Name,
                'survey_client_id': sc.Id,
                'survey_panel_member_id': spm.Id,
                'contact_id': contact.Id,
                'contact_email': spm.ContactEmail,
                'email_delivered': spm.SurveyEmailDelivered,
                'email_bounced': spm.SurveyEmailBounced,
                'has_responded': spm.HasResponded,
            }
            csvout.writerow(row)

if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('when')
    ap.add_argument('customer_survey_round_id')
    args = ap.parse_args()

    when = datetime.datetime.strptime(args.when, "%Y-%m-%dT%H:%M:%S")
    when = when.replace(tzinfo=datetime.timezone.utc)

    main(args.customer_survey_round_id, when)
