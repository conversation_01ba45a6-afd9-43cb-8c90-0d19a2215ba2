from typing import Any
from io import By<PERSON><PERSON>
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from lib.settings import settings
import json
import datetime
import boto3
import os
from lib import portaldbapi
import jinja2
from dotenv import load_dotenv
from sendgrid import SendGridAPIClient
from lib.emailapi import EmailServiceConfig
import requests
import argparse
import csv


load_dotenv()

TEMPLATES = {}

def get_attachment_from_s3(attachment: str, s3_bucket: str):
    attachment_dict: dict[str, str] = json.loads(attachment)

    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=s3_bucket, Key=attachment_dict['key'])
    response_body = response['Body'].read()

    with BytesIO(response_body) as f:
        content = f.read()

    msg_att = MIMEApplication(content)
    msg_att.add_header('Content-Disposition',attachment_dict['disposition'], filename=attachment_dict['key'])
    msg_att.add_header('Content-Type', attachment_dict['file_type'])
    msg_att.add_header('Content-ID', attachment_dict['content_id'])
    return msg_att


def _get_template(template_name: str, template_ext: str):
    with open(f'emailtemplates/{template_name}.{template_ext}', 'r') as file:
        return jinja2.Template(file.read())


def _make_template(body):
    body = body.replace('{{{', '{{').replace('}}}', '}}')
    template = jinja2.Template(body)
    return template


def send_ses_email(ses,
                default_from_name: str,
                from_email: str,
                to_email: str,
                body_html_template,
                body_text_template,
                subject_template,
                template_data: str,
                banner_attachment: str|None,
                signature_attachment: str|None,
                send_at: str|None,
                from_name: str|None,
                token: str|None) -> str:
    # convert template data to dict
    template_data_dict: dict[str, str] = json.loads(template_data)

    # correct any double slashes in URLs
    for k, v in template_data_dict.items():
        if v.startswith('https://'):
            template_data_dict[k] = v.replace('survey.thereferralrating.com//', 'survey.thereferralrating.com/')

    # convert send_at to datetime object (if provided)
    if send_at:
        send_at: datetime.datetime = datetime.datetime.strptime(send_at, '%Y-%m-%d %H:%M:%S')
        send_at: int = int(send_at.timestamp())

    # set default from name if not provided
    if not from_name:
        from_name = default_from_name

    # build sender string
    sender = f"{from_name} <{from_email}>"

    # add stage to survey link so we can tell who it is
    if 'SurveyLink' in template_data_dict:
        template_data_dict['SurveyLink'] = template_data_dict['SurveyLink'] + f"?stage=m2"

    # render the body
    body_html = body_html_template.render(**template_data_dict).strip()
    body_text = body_text_template.render(**template_data_dict).strip()
    subject = subject_template.render(**template_data_dict).strip()

    # tracking token for spam testing
    if token:
        body_text += f" {token}"

    # define initial message
    msg = MIMEMultipart('mixed')
    msg['Subject'] = subject
    msg['From'] = sender
    msg['To'] = to_email

    # Add the text and HTML parts to the child container.
    msg_body = MIMEMultipart('alternative')
    msg_body.attach(MIMEText(body_text.encode('utf-8'), 'plain', 'utf-8'))
    msg_body.attach(MIMEText(body_html.encode('utf-8'), 'html', 'utf-8'))
    msg.attach(msg_body)

    if banner_attachment:
        try:
            banner = get_attachment_from_s3(banner_attachment, settings.SF_BANNERS_BUCKET)
            msg.attach(banner)
        except Exception as e:
            print(f"Error fetching banner attachment from S3: {e}")
            raise Exception('Error fetching banner attachment from S3')

    if signature_attachment:
        try:
            signature = get_attachment_from_s3(signature_attachment, settings.SF_SIGNATURES_BUCKET)
            msg.attach(signature)
        except Exception as e:
            print(f"Error fetching signature attachment from S3: {e}")
            raise Exception('Error fetching signature attachment from S3')

    # return to_email, "FAKE"

    # sendit
    response = ses.send_email(
        FromEmailAddress=sender,
        Destination={
            'ToAddresses': [to_email],
        },
        Content={
            'Raw': {
                'Data': msg.as_bytes()
            },
        },
    )
    return to_email, response['MessageId']


def get_template(local_template_name: str, email_doc: str, es: EmailServiceConfig, sg) -> None:
    if local_template_name:
        raise Exception('Local template name not supported')

        # body_html_template = _get_template(local_template_name, 'html')
        # body_text_template = _get_template(local_template_name, 'txt')
        # subject_template = _get_template(local_template_name, 'subject')

    else:
        template_name: str = email_doc['template_name']
        if email_doc.get('vertical'):
            template_name = f"{email_doc['vertical'].lower()}:{template_name}"
        if email_doc.get('language'):
            template_name = f"{template_name}:{email_doc['language'].lower()}"
        template_id = es.templates.get(template_name)
        if not template_id:
            raise Exception(f"Template {template_name} does not exist")

        if template_id in TEMPLATES:
            active_version = TEMPLATES[template_id]

        else:
            response = requests.get(f'{sg.host}/v3/templates/{template_id}', headers={'Authorization': f'Bearer {sg.api_key}'})
            response.raise_for_status()
            template = response.json()
            active_version = [x for x in template['versions'] if x['active']][0]
            TEMPLATES[template_name] = active_version

        body_html_template = _make_template(active_version['html_content'])
        body_text_template = _make_template(active_version['plain_content'])
        subject_template = _make_template(active_version['subject'])

    return body_html_template, body_text_template, subject_template


def main(original_recipients: str,
         customer_survey_round_id: str,
         default_from_name: str,
         template_name: str,
         test_email: str,
         token: str = None) -> None:

    ses = boto3.client('sesv2',
                       aws_access_key_id=os.environ.get('SES_ACCESS_KEY_ID'),
                       aws_secret_access_key=os.environ.get('SES_SECRET_ACCESS_KEY'))
    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    es: EmailServiceConfig = EmailServiceConfig(**db.config.find_one({"_id": "email"}))

    scheduledemails_collection = db.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION]

    with open('emailresults.csv', 'w') as f:
        csvout = csv.writer(f)

        for original_recipient in original_recipients:
            original_recipient = original_recipient.strip()

            q = {
                'recipient': original_recipient,
                # 'deleted': {'$exists': False},
                'customer_survey_round_id': customer_survey_round_id,
                'template_name': template_name,
            }
            doc = sorted(scheduledemails_collection.find(q), key=lambda x: x['scheduled_date_utc'], reverse=True)
            if len(doc) == 0:
                print(f"!!!!No scheduled emails found for {original_recipient} - ignoring")
                continue
            elif len(doc) > 1:
                print(f"Multiple scheduled emails found for {original_recipient} - ignoring")
                continue
            doc = doc[0]

            body_html_template, body_text_template, subject_template = get_template(None, doc, es, sg)

            # THE SCARY BIT - SEND TO REAL PERSON
            # to_email = original_recipient

            # SEND TO TEST EMAIL ONLY
            to_email = test_email

            print(f"Sending email to {to_email}/{original_recipient} for {doc['_id']}")

            # extract template data
            template_data = doc.get('template_data', {})

            # add adhoc modifications here

            # get other gubbins
            template_data = json.dumps(template_data)
            banner_attachment = None
            if doc.get('banner_attachment'):
                banner_attachment = json.dumps(doc['banner_attachment'])
            signature_attachment = None
            if doc.get('signature_attachment'):
                signature_attachment = json.dumps(doc['signature_attachment'])

            to_email, messageid = send_ses_email(ses,
                        default_from_name,
                        doc.get('sender_email'),
                        to_email,
                        body_html_template,
                        body_text_template,
                        subject_template,
                        template_data,
                        banner_attachment,
                        signature_attachment,
                        None, # always send now
                        doc.get('sender_name'),
                        token=token)

            csvout.writerow([original_recipient, to_email, messageid, doc['_id']])


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('emailscsv')
    ap.add_argument('customer_survey_round_id')
    ap.add_argument('default_from_name')
    ap.add_argument('template_name')
    ap.add_argument('--testto', default='<EMAIL>')
    args = ap.parse_args()

    emails = set()
    with open(args.emailscsv, 'r') as f:
        csvin = csv.reader(f)
        for row in csvin:
            if row:
                emails.add(row[0].strip().lower())
    main(emails,
         args.customer_survey_round_id,
         args.default_from_name,
         args.template_name,
         args.testto)
