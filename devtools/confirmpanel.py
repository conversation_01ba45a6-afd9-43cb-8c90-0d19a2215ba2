import lib.portaldbapi as portaldbapi
import argparse
from lib.settings import settings
import copy
import datetime



def main(survey_client_sfid: str):
    portaldb = portaldbapi.DocumentDB()

    confirm_panel = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION].find_one({'Id': survey_client_sfid})
    if not confirm_panel:
        print("No confirm panel found for id: ", survey_client_sfid)
        return
    confirmed_panel = {}
    for panel_manager in confirm_panel.get('panel_managers', []):
        confirmed_panel_panel = []
        for contact in confirm_panel.get('panel', []):
            contact = copy.copy(contact)
            contact['in_round'] = True
            confirmed_panel_panel.append(contact)
        confirmed_panel[panel_manager['panel_manager_id']] = {
            'panel': confirmed_panel_panel,
            'confirm_date': datetime.datetime.now()
        }
    confirm_panel['confirmed_panel'] = confirmed_panel
    confirm_panel['confirm_status'] = True

    del confirm_panel['Id']
    del confirm_panel['_id']

    portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION].update_one({'Id': survey_client_sfid}, {'$set': confirm_panel})


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('survey_client_sfid', help='survey client salesforce id')
    args = ap.parse_args()

    main(args.survey_client_sfid)
