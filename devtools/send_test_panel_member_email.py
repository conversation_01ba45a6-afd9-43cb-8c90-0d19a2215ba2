""" Generate and send a test panel member email, substituing the recipient's email address
    Generates a scheduled email document based on a survey panel member's data (as per scheduler agent),
    then sends the email (as per the email sender agent).
"""
import argparse
import datetime
from simple_salesforce import Salesforce, format_soql
from lib.settings import settings
from lib import emailapi, sfapi, sqsapi, portaldbapi
from agents.email_scheduler_panel_users import group_survey_panel_members, process_participants


def get_panel_member_from_sf(sf:Salesforce, panel_member_id:str) -> dict:
    print(' >> FETCHING: SURVEY PANEL MEMBER FROM SF...')
    soql = """
    SELECT Id,
            Name,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.<PERSON>ail,
            Contact__r.Name,
            Contact__r.FirstName,
            Contact__r.LastName,
            Contact__r.Language__c,
            Contact__r.Location__r.Timezone__c,
            Survey_Email_Triggered__c,
            Survey_Email_Reminder_1__c,
            Survey_Email_Reminder_2__c,
            Survey_Email_Reminder_3__c,
            Survey_Type__c,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Signatory__r.Id,
            Survey_Client__r.Signatory__r.Name,
            Survey_Client__r.Signatory__r.Email,
            Survey_Client__r.Signatory__r.Title,
            Survey_Client__r.Signatory__r.Account.Name,
            Survey_Client__r.Signatory__r.Banner__c,
            Survey_Client__r.Signatory__r.Signature__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
    FROM  Survey_Panel_Member__c
    WHERE Id = {panel_member_id}
    """

    query:str = format_soql(soql, panel_member_id=panel_member_id)

    panel_member = None
    for spm in sfapi.bulk_query(sf, query):
        panel_member = spm

    return panel_member


def send_email(db, email):
    # code lifted from email_sender_panel_users agent
    sqs:sqsapi.SQS = sqsapi.SQS()
    es:emailapi.EmailService = emailapi.EmailService(db, sqs)

    # id:bson.ObjectId = email.get('_id')
    # spm_ids:list[str] = email.get('survey_panel_member_ids')
    sender_email:str = email.get('sender_email')
    sender_name:str = email.get('sender_name')
    recipient:str = email.get('recipient')
    email_template_name:str = email.get('template_name')
    template_data:dict = email.get('template_data')
    template_metadata:dict = email.get('template_metadata')
    send_at:datetime = email.get('scheduled_date_utc')
    language:str = email.get('language')
    vertical:str = email.get('vertical')
    banner_attachment:str = email.get('banner_attachment')
    signature_attachment:str = email.get('signature_attachment')
    # sf_spm_field:str = email.get('sf_spm_field')
    portaldb_record_map:str = None  #f'scheduledemails-{id}'
    email_service_provider:str = email.get('email_service_provider', 'sendgrid')

    # push email to SQS queue
    try:
        es.send(sender_email, 
                recipient, 
                email_template_name, 
                template_data, 
                template_metadata,
                send_at=send_at,
                from_name=sender_name,
                language=language, 
                vertical=vertical, 
                banner_attachment=banner_attachment,
                signature_attachment=signature_attachment,
                portaldb_record_map=portaldb_record_map,
                service_provider=email_service_provider)

        print(f'   :: Email Scheduled: for {recipient} at {send_at} UTC')
    except Exception as e:
        print(f"Failed to send email for record {id}: {e}")


def main(survey_panel_member_id:str, email_number:int, new_recipient:str, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()

    account_record_types:dict[str,dict] = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    spm = get_panel_member_from_sf(sf, survey_panel_member_id)
    if not spm:
        raise ValueError(f'No survey panel member found with ID {survey_panel_member_id}')
    
    csr_id:str = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id')
    grouped_panel_members:dict = group_survey_panel_members([spm])
    contact_profile_count:dict[str,set] = grouped_panel_members.get('contact_profile_count')
    contact_survey_names:dict[str,set] = grouped_panel_members.get('contact_survey_names')
    scheduled_emails = {}
    batch = process_participants(csr_id, [spm], contact_profile_count, contact_survey_names, email_number, account_record_types, scheduled_emails)
    assert len(batch) == 1, "Expected a single email batch for the test panel member"
    email_schedule_doc = batch[0]._doc['$set']
    # change the recipient email address to the new one
    email_schedule_doc['recipient'] = new_recipient
    # don't track this email
    email_schedule_doc['template_metadata']['tracked_template_name'] = 'ignore_this_test_email'
    # add the fields from $addToSet
    email_schedule_doc['survey_panel_member_ids'] = [spm.get('Id')]
    email_schedule_doc['survey_client_ids'] = [spm.get('Survey_Client__r.Id')]
    email_schedule_doc['customer_survey_ids'] = [spm.get('Survey_Client__r.Customer_Survey__r.Id')]
    # send the email now
    now:datetime.datetime = datetime.datetime.now(datetime.UTC)
    timezone = None
    scheduled_date_local = scheduled_date_utc = now
    email_schedule_doc['scheduled_date_local'] = scheduled_date_local.isoformat()
    email_schedule_doc['scheduled_date_utc'] = scheduled_date_utc
    email_schedule_doc['scheduled_date_timezone'] = timezone

    if writeit:
        print(' >> SENDING EMAIL')
        send_email(db, email_schedule_doc)
    else:
        print(' >> NOT SENDING EMAIL, just generating the document that would be sent')
        print(email_schedule_doc)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('survey_panel_member_id', default=None, help='single survey panel member to run for')
    ap.add_argument('email_number', type=int, choices=[1,2,3,4], help='Which email to send M: 1, 2, 3, or 4')
    ap.add_argument('new_recipient', type=str, help='The new recipient email address to send the test email to')
    ap.add_argument('--writeit', action='store_true', help='Actually send the email')
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.email_number, args.new_recipient, args.writeit)
