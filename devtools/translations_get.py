import deepl
import cfgsettings
import langdetect
import boto3
import csv
import codecs
import argparse


OUT_FIELDNAMES = ["srcrow",
                  "feedback_id",
                  "langdetect_lang",
                  "text",
                  "crc_translation", "crc_lang",
                  "hc_google_translation", "hc_google_lang",
                  "amazon_translation", "amazon_lang",
                  "deepl_translation", "deepl_lang"]

DEST_LANGAUGE = "en-us"

TX_BATCH_SIZE = 20

aws_session = boto3.Session()
# aws_session = boto3.Session(profile_name='vaul-crc-admin')
aws_translate_client = aws_session.client('translate')


def detect_language(text) -> str:
    return langdetect.detect(text)


def deepl_translate(text : str | list[str], dest_lang: str, source_lang: str | None=None) -> list[tuple[str, str]]:
    auth_key = cfgsettings.DEEPL_AUTH_KEY
    translator = deepl.Translator(auth_key)

    result = translator.translate_text(text, target_lang=dest_lang, source_lang=source_lang)
    if isinstance(result, list):
        return [(item.text, item.detected_source_lang.lower()) for item in result]
    else:
        return [(result.text, result.detected_source_lang.lower())]


# def google_translate(text : str | list[str], dest_lang: str, source_lang: str | None =None) -> list[tuple[str, str]]:

#     if isinstance(text, str):
#         text = [text]

#     # -H "X-goog-api-key: API_KEY" \

#     results = []
#     for cur_text in text:
#         params = {
#             "q": cur_text,
#             "target": dest_lang,
#             "key": cfgsettings.GOOGLE_TRANSLATE_API_KEY,
#             "format": "text",
#         }
#         if source_lang:
#             params["source"] = source_lang
#         response = requests.post("https://translation.googleapis.com/language/translate/v2", params=params)
#         response.raise_for_status()
#         tx_result = response.json()

#         results.append((tx_result["data"]["translations"][0]["translatedText"],
#                         tx_result["data"]["translations"][0]["detectedSourceLanguage"]))

#     return results


def amazon_translate(text: str | list[str], dest_lang, source_lang=None) -> list[tuple[str, str]]:
    if source_lang is None:
        source_lang = "auto"

    if isinstance(text, str):
        text = [text]

    result = []
    for cur_text in text:
        try:
            response = aws_translate_client.translate_text(
                Text=cur_text,
                SourceLanguageCode=source_lang,
                TargetLanguageCode=dest_lang,
            )
            result.append((response["TranslatedText"], response["SourceLanguageCode"]))
        except Exception as e:
            print("AMAZON API ERROR", e)
            result.append((f"API ERROR {e}", ""))
    return result


def translate(text: str | list[str], dest_lang: str):
    # google_result = google_translate(text, dest_lang)
    amazon_result = amazon_translate(text, dest_lang)
    deepl_result = deepl_translate(text, dest_lang)

    return {
        # "google": google_result,
        "amazon": amazon_result,
        "deepl": deepl_result,
    }

def translate_batch(tx_batch, csvout):
    print("TXBATCHSTART")
    texts = [item["text"] for item in tx_batch]
    tx_result = translate(texts, DEST_LANGAUGE)

    for idx in range(len(tx_batch)):
        outrow = tx_batch[idx]
        # outrow["google_translation"] = tx_result["google"][idx][0]
        # outrow["google_lang"] = tx_result["google"][idx][1]
        outrow["amazon_translation"] = tx_result["amazon"][idx][0]
        outrow["amazon_lang"] = tx_result["amazon"][idx][1]
        outrow["deepl_translation"] = tx_result["deepl"][idx][0]
        outrow["deepl_lang"] = tx_result["deepl"][idx][1]

        csvout.writerow(outrow)

    tx_batch.clear()
    print("TXBATCHDONE")
    return len(tx_batch)


def main(filename, start_row):
    with open(f'translated-{start_row}.csv', 'w') as outf:
        outf.write(codecs.BOM_UTF8.decode('utf-8'))
        csvout = csv.DictWriter(outf, fieldnames=OUT_FIELDNAMES)
        csvout.writeheader()

        tx_count = 0
        tx_batch = []
        i = 0
        langdist = {}
        with open(filename, "r", encoding='utf-8-sig') as f:
            csvin = csv.DictReader(f, dialect='excel', escapechar='\\')

            for row in csvin:
                print(row)
                # skip initial rows
                if i < start_row:
                    i += 1
                    continue

                # progress
                i += 1
                if (i % 1000) == 0:
                    print(i, tx_count)

                # skip rows with no feedback
                text = row['original'].strip()
                if not text or len(text) < 5:
                    continue

                # detect language
                try:
                    langdetect_lang = detect_language(text)
                except Exception as e:
                    print(f"LANGDETECT ERROR {e}")
                    langdetect_lang = "unknown"

                # basic output
                outrow = {
                    "srcrow": str(i),
                    "feedback_id": row['feedback_id'],
                    "text": text,
                    "langdetect_lang": langdetect_lang,
                    "crc_lang": row['hub_language'],
                    "crc_translation": row['hub_translation'],
                    "hc_google_translation": row['hc_translation'],
                    "hc_google_lang": row['detected_language'],
                }

                # note language distribution
                langdist.setdefault(langdetect_lang, 0)
                langdist[langdetect_lang] += 1

                # don't translate english
                if langdetect_lang in {'en'}:
                    csvout.writerow(outrow)
                    continue
                else:
                    tx_batch.append(outrow)

                # translate batch'o'stuff
                if len(tx_batch) > TX_BATCH_SIZE:
                    tx_count += translate_batch(tx_batch, csvout)

        if len(tx_batch):
            tx_count += translate_batch(tx_batch, csvout)

        print("TX COUNT", tx_count)
        print("LANG DIST", langdist)
        print("CSV ROW", i)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("filename")
    ap.add_argument("--start-row", type=int, default=0)
    args = ap.parse_args()

    main(args.filename, args.start_row)
