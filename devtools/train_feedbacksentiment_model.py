import csv
import os
import copy
import pandas as pd
import json
import numpy as np
import datasets
import datetime
import transformers
import argparse
from transformers import AutoTokenizer
from transformers import AutoModelForSequenceClassification, AutoConfig
from transformers import Trainer
import evaluate
import torch.nn


BASE_MODEL = "google-bert/bert-base-cased"

def _create_category_map(hub_category_ids, all_categories_by_hub_id):
    category_by_full_name = {}
    categories = []
    categories_by_hub_id = {}
    for hub_category_id in hub_category_ids:
        category_full_name = all_categories_by_hub_id[hub_category_id]['full_name']
        if category_full_name in category_by_full_name:
            category = category_by_full_name[category_full_name]

        else:
            category = copy.copy(all_categories_by_hub_id[hub_category_id])
            category['lm_id'] = len(categories)
            categories.append(category)
            category_by_full_name[category_full_name] = category

        categories_by_hub_id[hub_category_id] = category
    return categories, categories_by_hub_id


def load_feedback_dataset(dataset_name):
    if os.path.exists(dataset_name):
        with open(f"{dataset_name}/manufacturing_categories.json", 'r') as f:
            manufacturing_categories = json.loads(f.read())
        with open(f"{dataset_name}/advertising_categories.json", 'r') as f:
            advertising_categories = json.loads(f.read())

        return datasets.load_from_disk(dataset_name), manufacturing_categories, advertising_categories

    # parse the feedback attributes for categories
    all_categories_by_hub_id = {}
    with open(os.path.expanduser('~/dev/crc-data/hyper_eval/Hypercube Data Share/crc_feedbackAttributes_2023-10-13.csv'), 'r') as f:
        reader = csv.DictReader(f)

        for row in reader:
            if int(row['positiveNegative']) == 1:
                pn = '+'
            elif int(row['positiveNegative']) == -1:
                pn = '-'
            elif int(row['positiveNegative']) == 0:
                pn = ''
            else:
                raise ValueError(f"Unknown positiveNegative value {row['positiveNegative']}")

            hub_id = int(row['id'])
            full_name = f"{pn}{row['category'].strip()}/{row['subCategory'].strip()}/{row['name'].strip()}:{hub_id}"
            category = {
                'full_name': full_name,
                'category': row['category'].strip(),
                'sub_category': row['subCategory'].strip(),
                'name': row['name'].strip(),
                'code': row['code'],
                'hub_id': hub_id,
            }
            all_categories_by_hub_id[hub_id] = category

    # parse links from feedback -> categories
    feedback_categories = {}
    with open(os.path.expanduser('~/dev/crc-data/hyper_eval/Hypercube Data Share/crc_feedbackAttributeLinks_2023-10-13.csv'), 'r') as f:
        reader = csv.DictReader(f, escapechar='\\')
        for row in reader:
            feedback_categories.setdefault(int(row['feedbackId']), set()).add(int(row['attributeId']))

    # parse links from feedback -> vertical
    feedback_verticals = {}
    with open(os.path.expanduser('~/dev/crc-data/hyper_eval/Hypercube Data Share/feedbackVertical_2023-10-26.csv'), 'r') as f:
        reader = csv.reader(f, escapechar='\\')
        for feedback_id, vertical in reader:
            feedback_verticals[int(feedback_id)] = vertical

    # parse the feedback data, and figure out what categories belong to which vertical
    manufacturing_categories_hub_ids = set()
    advertising_categories_hub_ids = set()
    seen_categories_hub_ids = set()
    feedback = []
    with open(os.path.expanduser('~/dev/crc-data/hyper_eval/Hypercube Data Share/crc_rawFeedback_2023-10-13_v2.csv'), 'r') as f:
        reader = csv.DictReader(f, escapechar='\\')
        for row in reader:
            feedback_id = int(row.pop('id'))

            row['hub_feedback_id'] = feedback_id
            row['hub_contact_id'] = int(row.pop('contact'))
            row['hub_dataset_id'] = int(row.pop('dataset'))
            if row['response'] == 'NULL':
                row['score'] = None
            else:
                row['score'] = int(row['response'])
            row.pop('response')
            row['response_timestamp'] = datetime.datetime.fromisoformat(row.pop('responseTimestamp'))
            if row['translation']:
                row['text'] = row['translation'].strip()
            else:
                row['text'] = row['feedback'].strip()
            row['vertical'] = feedback_verticals.get(feedback_id, 'unknown')

            # skip rows we don't know the vertical for
            if row['vertical'] == 'unknown':
                continue

            # figure out the possible sets of categories in each vertical
            for hub_category_id in feedback_categories.get(feedback_id, set()):
                seen_categories_hub_ids.add(hub_category_id)
                if row['vertical'] == 'manufacturing':
                    manufacturing_categories_hub_ids.add(hub_category_id)
                elif row['vertical'] == 'advertising':
                    advertising_categories_hub_ids.add(hub_category_id)
                else:
                    raise ValueError(f"Unknown vertical {row['vertical']}")

            feedback.append(row)

    # warn if there are categories we simply didn't see at all!
    for hub_category_id, category in all_categories_by_hub_id.items():
        if hub_category_id not in seen_categories_hub_ids:
            print(f"WARNING: Category {category} not seen in feedback data")

    # setup category mappings for the two verticals
    # this will duplicate categories between the two verticals since some of them are shared and some are not. urgh
    manufacturing_categories, manufacturing_categories_by_hub_id = _create_category_map(manufacturing_categories_hub_ids, all_categories_by_hub_id)
    advertising_categories, advertising_categories_by_hub_id = _create_category_map(advertising_categories_hub_ids, all_categories_by_hub_id)

    # finally, generate the multi label binary vector for each feedback
    for row in feedback:
        feedback_id = row['hub_feedback_id']
        if row['vertical'] == 'manufacturing':
            vertical_categories = manufacturing_categories
            vertical_categories_by_hub_id = manufacturing_categories_by_hub_id

        elif row['vertical'] == 'advertising':
            vertical_categories = advertising_categories
            vertical_categories_by_hub_id = advertising_categories_by_hub_id

        else:
            raise ValueError(f"Unknown vertical {row['vertical']}")

        category_labels = [0.0] * len(vertical_categories)
        for category_hub_id in feedback_categories.get(feedback_id, set()):
            category = vertical_categories_by_hub_id[category_hub_id]
            category_labels[category['lm_id']] = 1.0
        row['labels'] = tuple(category_labels)

    df = pd.DataFrame(feedback)

    # first of all, drop duplicate rows where the text and the labels are identical to avoid cross-pollination between the different datasets
    df_deduped = df.drop_duplicates(subset=['vertical', 'text', 'labels'])

    # now drop all rows which STILL have duplicate text - this is where the text is identical, but the labels must be different!
    # no idea what "correct" is here, so drop them all on the floor
    df_without_conflicts = df_deduped.drop_duplicates(subset=['vertical', 'text'], keep=False)

    # log stuff!
    print(f"Original size {len(df)}, deduped size {len(df_deduped)}, conflictsremoved size {len(df_without_conflicts)}")

    # generate datasets for manufacturing
    df_manufacturing = df_without_conflicts[df_without_conflicts['vertical'] == 'manufacturing']
    ds_manufacturing = datasets.Dataset.from_pandas(df_manufacturing)
    train_test_ds_manufacturing = ds_manufacturing.train_test_split(test_size=0.2)
    test_valid_ds_manufacturing = train_test_ds_manufacturing['test'].train_test_split(test_size=0.5)

    # generate datasets for advertising
    df_advertising = df_without_conflicts[df_without_conflicts['vertical'] == 'advertising']
    ds_advertising = datasets.Dataset.from_pandas(df_advertising)
    train_test_ds_advertising = ds_advertising.train_test_split(test_size=0.2)
    test_valid_ds_advertising = train_test_ds_advertising['test'].train_test_split(test_size=0.5)

    # the final data set!
    ds = datasets.DatasetDict({
        'train_manufacturing': train_test_ds_manufacturing['train'],
        'test_manufacturing': test_valid_ds_manufacturing['test'],
        'valid_manufacturing': test_valid_ds_manufacturing['train'],

        'train_advertising': train_test_ds_advertising['train'],
        'test_advertising': test_valid_ds_advertising['test'],
        'valid_advertising': test_valid_ds_advertising['train'],
    })

    # persist and return
    ds.save_to_disk(dataset_name)
    with open(f"{dataset_name}/manufacturing_categories.json", 'w') as f:
        json.dump(manufacturing_categories, f, indent=4)
    with open(f"{dataset_name}/advertising_categories.json", 'w') as f:
        json.dump(advertising_categories, f, indent=4)
    return ds, manufacturing_categories, advertising_categories


def train(ds, category_by_lm_id, model_name):
    print("TRAINING")

    # remove any unnecessary columns
    cols = set(ds['train'].column_names)
    cols.remove('labels')
    cols.remove('text')
    ds = ds.remove_columns(cols)

    id2label = {str(k): v['full_name'] for k, v in category_by_lm_id.items()}
    label2id = {v: k for k, v in id2label.items()}
    config = AutoConfig.from_pretrained(BASE_MODEL, problem_type="multi_label_classification", label2id=label2id, id2label=id2label)
    model = AutoModelForSequenceClassification.from_pretrained(BASE_MODEL,
                                                               config=config,
                                                               torch_dtype="auto")
    tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL)

    def tokenize_function(examples):
        return tokenizer(examples["text"], padding="max_length", truncation=True)
    tokenized_ds = ds.map(tokenize_function, batched=True)

    def sigmoid(x):
        return 1/(1 + np.exp(-x))

    metric = evaluate.load("f1")
    def compute_metrics(eval_pred):
        logits, labels = eval_pred

        predictions = sigmoid(logits)
        predictions = (predictions > 0.5).astype(int).reshape(-1)
        return metric.compute(predictions=predictions, references=labels.astype(int).reshape(-1))

    def compute_loss(outputs, labels, num_items_in_batch=0):
        num_positives = torch.sum(labels, dim=0)
        num_negatives = labels.shape[0] - num_positives
        pos_weights = num_negatives / (num_positives + 1e-5)

        loss_fct = torch.nn.BCEWithLogitsLoss(pos_weight=pos_weights)
        loss = loss_fct(outputs.logits.view(-1, labels.shape[1]), labels.view(-1, labels.shape[1]))
        return loss

    training_args = transformers.TrainingArguments(
        eval_strategy="epoch",
        output_dir=f"{model_name}-train-tmp",
    )
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=tokenized_ds["train"],
        eval_dataset=tokenized_ds["test"],
        compute_metrics=compute_metrics,
        compute_loss_func=compute_loss,
    )
    trainer.train()

    tokenizer.save_pretrained(model_name)
    model.save_pretrained(model_name)


def validate(ds, model_name):
    print("VALIDATING")
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    def tokenize_function(examples):
        return tokenizer(examples["text"], padding="max_length", truncation=True, return_tensors="pt")
    tokenized_ds = ds.map(tokenize_function, batched=True)

    def sigmoid(x):
        return 1/(1 + np.exp(-x))

    metric = evaluate.load("f1")
    def compute_metrics(eval_pred):
        logits, labels = eval_pred

        predictions = sigmoid(logits)
        predictions = (predictions > 0.5).astype(int).reshape(-1)
        return metric.compute(predictions=predictions, references=labels.astype(int).reshape(-1))

    trainer = Trainer(
        model=model,
        eval_dataset=tokenized_ds["valid"],
        compute_metrics=compute_metrics,
    )
    metrics = trainer.evaluate()
    print("Validation", metrics)


def main(out_base_name, vertical):
    ds, manufacturing_categories, advertising_categories = load_feedback_dataset(f'crc-feedbacksentiment-{out_base_name}-ds')
    if vertical == 'manufacturing':
        category_by_lm_id = {v['lm_id']: v for v in manufacturing_categories}
        ds = datasets.DatasetDict({
            'train': ds['train_manufacturing'],
            'test': ds['test_manufacturing'],
            'valid': ds['valid_manufacturing'],
        })

    elif vertical == 'advertising':
        category_by_lm_id = {v['lm_id']: v for v in advertising_categories}
        ds = datasets.DatasetDict({
            'train': ds['train_advertising'],
            'test': ds['test_advertising'],
            'valid': ds['valid_advertising'],
        })

    else:
        raise ValueError(f"Unknown vertical {vertical}")

    train(ds, category_by_lm_id, f"crc-feedbacksentiment-{vertical}-{out_base_name}-model")
    validate(ds, f"crc-feedbacksentiment-{vertical}-{out_base_name}-model")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("out_base_name")
    ap.add_argument("--vertical", choices=['manufacturing', 'advertising'], default='advertising')
    args = ap.parse_args()

    main(args.out_base_name, args.vertical)
