import gradio as gr
import psycopg2
import os
from dotenv import load_dotenv
from email.parser import Bytes<PERSON>ars<PERSON>
from email import policy
from fastapi import Response, HTTPException

load_dotenv()


emails = []
categories = [
    "outofoffice",
    "acknowledgement",
    "codeit",
    "spam_ignore",

    "surveyprocess/cannotperformexpectedaction",  # user couldn't do something in the CA they expected to, or need permission changes/login to do it
    "surveyprocess/manualsetupchat",  # chat on manual setup by PDT, spreadsheets, lists of contacts/accounts, late requests etc
    "surveyprocess/emailsignature",  # stuff about getting people's email signatures
    "surveyprocess/feedback",  # feedback on the platform
    "surveyprocess/surveydeliveryissues",  # survey delivery issues only
    "surveyprocess/nonrelevantperson",  # person says they're not relevant for the survey setup
    "surveyprocess/other",  # anything else!

    "reporting/cannotperformexpectedaction",  # user couldn't do something in the CA they expected to, or need permission changes/login to do it
    "reporting/reviewmeeting",  # stuff about review of results meeting
    "reporting/feedback",  # feedback on the platform
    "reporting/other",  # anything else!

    "surveypanelmember/unabletocompletesurvey",  # says they can't complete the survey due to a technical issue (eg firewall, link blocker)
    "surveypanelmember/nonrelevantperson",  # person says they're not relevant for the survey
    "surveypanelmember/interestingissues_comments",  # anything else interesting (ignore things like "Yay I completed the survey!")

    # special commands for this tool only!
    "UNDO",
    "SKIP",
]

def main():
    conn = psycopg2.connect(os.getenv("EMAIL_DATABASE_URL"))
    cursor = conn.cursor()

    global emails
    email_select_sql = """
SELECT email_id, json, eml
FROM email
WHERE action = 'received' AND bucket != 'dmarc'
AND label IS NULL
ORDER BY received_at DESC
"""
    cursor.execute(email_select_sql)
    emails = cursor.fetchall()
    print(len(emails))

    current_email_index = -1
    inlines = {}
    attachments = []

    js_func = """
    function refresh() {
        const url = new URL(window.location);

        if (url.searchParams.get('__theme') !== 'light') {
            url.searchParams.set('__theme', 'light');
            window.location.href = url.href;
        }
    }
    """

    with gr.Blocks(js=js_func) as demo:
        subject = gr.Textbox(label="Subject")
        received = gr.Textbox(label="Received")
        from_ = gr.Textbox(label="From")
        to = gr.Textbox(label="To")
        attachments = gr.Textbox(label="Attachments")
        category_button = gr.Radio(categories, label="Category")
        body = gr.HTML(label="Body")

        def update_email(category):
            nonlocal current_email_index, inlines, attachments

            if current_email_index == -1:
                current_email_index = 0

            elif category == "UNDO":
                # if we're undoing, go back one email if we can
                if current_email_index > 0:
                    current_email_index -= 1

            elif category == "SKIP":
                # move onto next email
                current_email_index += 1

            else:
                email_id, current_email, _ = emails[current_email_index]
                cursor.execute("UPDATE email SET label = %s WHERE email_id = %s", (category, email_id))
                conn.commit()
                print(email_id, current_email['subject'], category)

                # move onto next email
                current_email_index += 1

            _, current_email_json, current_email_eml = emails[current_email_index]
            current_email_msg = BytesParser(policy=policy.default).parsebytes(current_email_eml.encode('utf-8'))

            # get body and make images work
            body_html = current_email_msg.get_body(['html', 'plain']).get_content().strip()
            if not body_html:
                body_html = current_email_msg.get_body(['plain']).get_content().strip()
            body_html = body_html.replace('cid:', '/inline/')

            # get attachments/ inlined things
            attachments = []
            inlines.clear()
            for part in current_email_msg.walk():
                if part.get_content_disposition() == 'attachment':
                    attachments.append(part)

                elif part.get_content_disposition() == 'inline':
                    content_id = part['Content-ID'].strip('<>')
                    inlines[content_id] = part

                else:
                    pass

            toEmails = set()
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['toRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['ccRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['bccRecipients']])
            toEmails = ",".join(toEmails)

            fromEmails = set()
            fromEmails.add(current_email_json['sender']['emailAddress']['address'].lower())
            fromEmails.add(current_email_json['from']['emailAddress']['address'].lower())
            fromEmails = ",".join(fromEmails)

            subject = current_email_msg['subject']
            received_at = current_email_json['receivedDateTime']

            result = [gr.update(value=subject),
                      gr.update(value=received_at),
                      gr.update(value=fromEmails),
                      gr.update(value=toEmails),
                      gr.update(value=f"{len(attachments)}"),
                      gr.update(value=f"<div style='border: solid 1px black'>{body_html}</div>"),
                      gr.update(value=None)]
            return result

        category_button.input(update_email,
                               inputs=[category_button],
                               outputs=[subject, received, from_, to, attachments, body, category_button])

    server_app, _, _ = demo.launch(prevent_thread_lock=True)

    @server_app.get("/inline/{cid}")
    def read_inline(cid):
        nonlocal inlines

        if cid not in inlines:
            raise HTTPException(status_code=404, detail="Item not found")
        else:
            inline = inlines[cid]
            return Response(content=inline.get_content(), media_type=inline.get_content_type())

    demo.block_thread()


if __name__ == "__main__":
    main()
