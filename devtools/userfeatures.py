import argparse
from lib.settings import settings
from lib import sfapi, portaldbapi


def main(email, assumed_role):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    features_collection = portaldb.features

    # get user from SF
    sf_contact: sfapi.Contact | None = sfapi.get_by_field(sf, sfapi.Contact, 'Email', email.lower())
    if not sf_contact:
        print('User not found in Salesforce')
        return

    # update role
    if assumed_role is True:
        features_collection.update_one({'contact_id': sf_contact.Id}, {'$set': {'features.assumed_role': True}}, upsert=True)
    elif assumed_role is False:
        features_collection.update_one({'contact_id': sf_contact.Id}, {'$set': {'features.assumed_role': False}}, upsert=True)

    # print out current state
    pdb_features = features_collection.find_one({'contact_id': sf_contact.Id})
    if not pdb_features:
        pdb_features = {'features': {}}
    print(pdb_features['features'])


def lambda_handler(event, context):
    main(True, False)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('email')
    ap.add_argument('--enable-assumed-role', action='store_true', dest='assumed_role', default=None, help='Enable assumed role')
    ap.add_argument('--disable-assumed-role', action='store_false', dest='assumed_role', default=None, help='Enable assumed role')
    args = ap.parse_args()

    main(args.email, args.assumed_role)
