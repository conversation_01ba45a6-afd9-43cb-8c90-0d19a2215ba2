import argparse
import os
from bson import json_util


def main(old_dirname: str, new_dirname: str):
    old_files = set(os.listdir(old_dirname))
    new_files = set(os.listdir(new_dirname))

    for filename in old_files | new_files:
        if os.path.exists(f'{old_dirname}/{filename}'):
            with open(f'{old_dirname}/{filename}', 'r') as f:
                old_data = json_util.loads(f.read())
        else:
            print("File in new but not in old:", filename)
            continue

        if os.path.exists(f'{new_dirname}/{filename}'):
            with open(f'{new_dirname}/{filename}', 'r') as f:
                new_data = json_util.loads(f.read())
        else:
            print("File in old but not in new:", filename)
            continue

        if old_data != new_data:
            print(f"File {filename} is different")





if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('old_dirname')
    ap.add_argument('new_dirname')
    args = ap.parse_args()

    main(args.old_dirname, args.new_dirname)
