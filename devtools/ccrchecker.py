import argparse
from itertools import islice
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
from lib.settings import settings
from pymongo import UpdateOne
import dns.resolver
import csv
import sys



def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    f = open('crreport.csv', 'w')
    csvout = csv.writer(f)

    soql = """
        SELECT  Survey_Client__c
        FROM  Survey_Panel_Member__c
    """
    spm_count_by_survey_client = {}
    for row in sfapi.bulk_query(sf, soql):
        spm_count_by_survey_client.setdefault(row['Survey_Client__c'], 0)
        spm_count_by_survey_client[row['Survey_Client__c']] += 1

    soql = """
        SELECT  Customers_Client__c,
                Customers_Client__r.Name,
                Customer_Account__c,
                Customer_Account__r.Name
        FROM  Customer_Client_Relationship__c
    """
    existing_ccrs = {}
    for row in sfapi.bulk_query(sf, soql):
        key = f"{row['Customers_Client__c']}_{row['Customer_Account__c']}"
        existing_ccrs.setdefault(key, []).append(row)

    soql = """
        SELECT Id,
               Customers_Client__c,
               Customers_Client__r.Name,
               Customer_Survey__r.Customer__c,
               Customer_Survey__r.Customer__r.Name,
               CreatedDate,
               CreatedBy.Name
        FROM  Survey_Client__c
    """
    seen_ccrs = {}
    for row in sfapi.bulk_query(sf, soql):
        key = f"{row['Customers_Client__c']}_{row['Customer_Survey__r.Customer__c']}"
        count = spm_count_by_survey_client.get(row['Id'], 0)

        if key not in existing_ccrs:
            csvout.writerow([
                'Missing CCR',
                row['Customers_Client__c'],
                row['Customers_Client__r.Name'],
                row['Customer_Survey__r.Customer__c'],
                row['Customer_Survey__r.Customer__r.Name'],
                row['CreatedDate'],
                row['CreatedBy.Name'],
                count
            ])
            print(f"Missing CCR {row['Customers_Client__c']}: {row['Customers_Client__r.Name']} -> {row['Customer_Survey__r.Customer__c']}: {row['Customer_Survey__r.Customer__r.Name']} by {row['CreatedDate']} {row['CreatedBy.Name']}, [{count} panel members]")
        seen_ccrs.setdefault(key, []).append(row)

    for k, v in existing_ccrs.items():
        if len(v) < 2:
            continue

        row = v[0]
        csvout.writerow([
            'Duplicate CCR',
            row['Customers_Client__c'],
            row['Customers_Client__r.Name'],
            row['Customer_Account__c'],
            row['Customer_Account__r.Name'],
            '',
            ''
        ])

        print(f"Duplicate CCR ({len(v)}) {row['Customers_Client__c']}: {row['Customers_Client__r.Name']} -> {row['Customer_Account__c']}: {row['Customer_Account__r.Name']}")


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
