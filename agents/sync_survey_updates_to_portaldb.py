import argparse
from lib import portaldbapi
from lib import sfapi
from datetime import datetime
from pymongo import UpdateOne
from simple_salesforce import Salesforce, format_soql
from lib.settings import settings


def get_customer_survey_rounds(sf: Salesforce, customer_survey_round_id:str|None = None) -> list[str]:
    customer_survey_round_ids:set = set()
    soql:str = '''
        WHERE Current_Round__c = True
    '''
    if customer_survey_round_id:
        soql:str = soql + " AND Id = {customer_survey_round_id}"
    
    query:str = format_soql(soql, customer_survey_round_id=customer_survey_round_id)

    for csr in sfapi.get_all(sf, sfapi.CustomerSurveyRound, bulk=True, query_suffix=query):
        customer_survey_round_ids.add(csr.Id)

    return list(customer_survey_round_ids)


def main(customer_survey_round_id:str|None = None, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    survey_collection:portaldbapi.DocumentDB = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    total_count:int = 0
    updated_count:int = 0
    batch:list[UpdateOne] = []

    # get all customer survey rounds for the current round
    customer_survey_round_ids:list[str] = get_customer_survey_rounds(sf, customer_survey_round_id)

    # get the survey panel member ids and their end dates
    soql:str = """
        SELECT  Id,
                Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM    Survey_Panel_Member__c
        WHERE   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c IN {customer_survey_round_ids}
    """
    soql:str = format_soql(soql, customer_survey_round_ids=customer_survey_round_ids)
    all_survey_panel_member_ids:set = set()
    survey_panel_member_dates:dict[str, datetime] = {}
    customer_survey_round_updated:set = set()
    for row in sfapi.bulk_query(sf, soql):
        all_survey_panel_member_ids.add(row['Id'])
        survey_panel_member_end_date:datetime = datetime.fromisoformat(row['Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c'])
        survey_panel_member_dates[row['Id']] = survey_panel_member_end_date

    for survey in survey_collection.find({'deleted': {'$ne': True}, 'response': None, 'sfsync_date': None}):
        total_count += 1
        panel_member_ids:set = set([
            member["survey_panel_member_id"]
            for question in survey["questions"]
            for member in question["panel_members"]
        ])

        # ok, is this survey relevant?
        if not panel_member_ids & all_survey_panel_member_ids:
            continue

        # figure out the max end date based on the panel members
        end_dates:list[datetime] = []
        for panel_member_id in panel_member_ids:
            if panel_member_id in survey_panel_member_dates:
                end_dates.append(survey_panel_member_dates[panel_member_id])
        end_date:datetime|None = max(end_dates) if end_dates else None
        if not end_date:
            #print("DEBUG: End date was unknown for survey", survey["_id"])
            continue

        # if the end date is already set to the correct end date, skip
        if survey['end_date'] == end_date:
            continue

        # ok, add the update to the batch
        updated_count += 1
        customer_survey_round_updated.update(survey.get('customer_survey_round_ids', []))
        batch.append(UpdateOne({'_id': survey['_id']}, {'$set': {'end_date': end_date}}))
        #print(f'DEBUG: Updating {survey['_id']}')

    if batch and writeit:
        survey_collection.bulk_write(batch)

    print(f'Total Surveys Found: {total_count}')
    print(f'Total Surveys Updated: {updated_count}')
    print(f'Customer Survey Rounds Updated: {list(customer_survey_round_updated)}')


def lambda_handler(event, context):
    main(
        customer_survey_round_id=None,
        writeit=True
    )


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', help='The customer survey round ID to update')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.customer_survey_round_id, args.writeit)
