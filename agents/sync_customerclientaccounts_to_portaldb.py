import datetime
import argparse
from collections import defaultdict
from itertools import islice
from simple_salesforce import format_soql, Salesforce
from pymongo import UpdateOne
from lib import sfapi, portaldbapi
from lib.settings import settings


SF_BATCH_SIZE = 3000


def nullempty(s):
    return s if s else None


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch


def get_related_customer_clients_metadata_from_sf(sf: Salesforce, account_ids:list) -> list:
    print(f'Fetching related customer clients for {len(account_ids)} accounts...')

    if not account_ids:
        return {}

    results:dict = defaultdict(lambda: defaultdict(list))

    for batch in batch_iterable(account_ids, SF_BATCH_SIZE):
        soql = format_soql("""
            SELECT Id,
                   Customers_Client__c,
                   Survey_Name__c,
                   Customer_Survey__r.Customer__c,
                   Customer_Survey__r.Customer__r.Name,
                   Customer_Survey__r.Team__c,
                   Customer_Survey__r.Team__r.Name,
                   Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c
            FROM   Survey_Client__c
            WHERE  Customers_Client__c in {batch}
            ORDER BY Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c DESC
            """, batch=batch)
        
        for row in sfapi.bulk_query(sf, soql):
            account_id:str = row['Customers_Client__c']
            client_id:str = row['Customer_Survey__r.Customer__c']
            client_name:str = row['Customer_Survey__r.Customer__r.Name']
            team_id:str = nullempty(row['Customer_Survey__r.Team__c'])
            team_name:str = nullempty(row['Customer_Survey__r.Team__r.Name'])
            survey_name:str = row['Survey_Name__c']
            survey_start_date_str:str = row['Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c']

            survey_start_date:datetime = (
                datetime.datetime.strptime(survey_start_date_str, "%Y-%m-%d") if survey_start_date_str else None
            )
            key:str = f"{client_id}-{team_id}"

            results[account_id][key].append({
                'client_id': client_id,
                'client_name': client_name,
                'team_id': team_id,
                'team_name': team_name,
                'survey_name': survey_name,
                'survey_start_date': survey_start_date,
            })
        
    final_output:dict = {}

    for account_id, clients in results.items():
        final_output[account_id] = []
        for key, records in clients.items():
            records.sort(key=lambda x: x['survey_start_date'] or datetime.datetime.min, reverse=True)
            latest:dict = records[0]
            del latest['survey_start_date']
            final_output[account_id].append(latest)

    return final_output


def get_customer_client_accounts_from_sf(sf: Salesforce, account_id:str|None):
    print('Fetching customer client accounts...')
    
    customer_client_accounts_by_id:dict = {}

    query_suffix:str = "WHERE RecordType.Name = 'Customer\\'s Client Account'"

    if account_id:
        query_suffix += f" AND Id = '{account_id}'"

    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=query_suffix):
        customer_client_accounts_by_id[account.Id] = account

    return customer_client_accounts_by_id


def main(account_id:str|None = None, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    portaldb_collection = portaldbapi.get_sf_collection(portaldb, 'customerclientaccounts')

    batch:list[UpdateOne] = []

    sync_date = datetime.datetime.now(tz=datetime.UTC)

    # Get all customer client accounts from SF
    customer_client_accounts_by_account_id:list = get_customer_client_accounts_from_sf(sf, account_id)

    # Get all related customer clients from SF
    related_customer_clients_by_account_id:dict = get_related_customer_clients_metadata_from_sf(sf, set(customer_client_accounts_by_account_id.keys()))

    for account in customer_client_accounts_by_account_id.values():
        res:dict = {
            'Id': account.Id,
            'name': account.Name,
            'related_customer_clients': related_customer_clients_by_account_id.get(account.Id, []),
            '_lastsynced': sync_date,
        }

        batch.append(UpdateOne(
            {'Id': account.Id},
            {
                '$set': res
            },
            upsert=True
        ))

    # write to the database
    if writeit and account_id is None and len(batch) > 0:
        print(f'Writing {len(batch)} customer client accounts to portaldb...')
        portaldb_collection.bulk_write(batch)
    
    # delete any accounts that haven't been updated in this sync (ie ones which no longer exist in salesforce)
    if writeit and account_id is None:
        portaldb_collection.delete_many({'_lastsynced': {'$ne': sync_date}})
    
    print(f'Done. Updated {len(batch)} customer client accounts')

        
def lambda_handler(event, context):
    main(
        account_id=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--account_id', default=None, help='single account to run for')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.account_id, args.writeit)
