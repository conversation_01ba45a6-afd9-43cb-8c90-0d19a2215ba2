import argparse
from typing import Optional
from lib import sfapi
from lib.settings import settings


def main(contact_id: Optional[str], survey_panel_member_id: Optional[str]):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    if contact_id:
        contact = {
            'Id': contact_id,
            'HasOptedOutOfEmail': True,
        }
        sfapi.bulk_update(sf, "Contact", [contact])
    if survey_panel_member_id:
        survey_panel_member = {
            'Id': survey_panel_member_id,
            'Opt_Out__c': True,
        }
        sfapi.bulk_update(sf, "Survey_Panel_Member__c", [survey_panel_member])


def lambda_handler(event, _):
    if event:
        for record in event["Records"]:
            contact_id = record['messageAttributes'].get('contactId', {}).get('stringValue', None)
            survey_panel_member_id = record['messageAttributes'].get('surveyPanelMemberId', {}).get('stringValue', None)
            main(contact_id, survey_panel_member_id)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--contact_id", help="The ID of the contact to opt-out")
    ap.add_argument("--survey_panel_member_id", help="The ID of the SPM to opt-out")
    args = ap.parse_args()

    main(args.contact_id, args.survey_panel_member_id)
