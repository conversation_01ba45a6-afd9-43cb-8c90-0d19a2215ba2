from __future__ import annotations
import logging
import datetime
import argparse
from typing import List, Dict
from lib.settings import settings
from lib.portaldbapi import DocumentDB
from lib import sfapi
import uuid
from typing import List, Optional, Union
from pydantic import BaseModel


logger = logging.getLogger(__name__)


class RawSalesforceReportResponse(BaseModel):
    factMap: dict
    reportMetadata: dict
    reportExtendedMetadata: dict

    @classmethod
    def parse(cls, raw_salesforce_report: dict) -> RawSalesforceReportResponse:
        return cls(
            factMap=raw_salesforce_report['factMap'],
            reportMetadata=raw_salesforce_report['reportMetadata'],
            reportExtendedMetadata=raw_salesforce_report['reportExtendedMetadata'],
        )


class UIReport(BaseModel):
    id: str
    name: str
    description: Optional[Union[None, str]] = ''
    columns: List[ReportColumn]
    rows: List


class Report(BaseModel):
    id: str
    name: str
    description: Optional[Union[None, str]] = ''
    columns: List[ReportColumn]
    rows: List[ReportRow]

    @classmethod
    def __decode_fact_map(cls, fact_map: dict):
        return list(fact_map.keys())

    @classmethod
    def parse(cls, raw_salesforce_report: dict) -> Report:
        parsed_report = RawSalesforceReportResponse.parse(raw_salesforce_report)
        report_metadata = parsed_report.reportMetadata
        report_extended_metadata = parsed_report.reportExtendedMetadata
        report_id = report_metadata['id']
        report_name = report_metadata['name']
        report_description = report_metadata['description']
        columns = []
        rows = []
        column_field_lookup = []

        for key, value in report_extended_metadata['detailColumnInfo'].items():
            columns.append(ReportColumn(
                header=value['label'],
                field=value['entityColumnName'],
                data_type=value['dataType'],
            ))
            # using `label` and not `entityColumnName` because `entityColumnName`, when it's a derived field, will not be unique
            column_field_lookup.append(value['label'])

        decoded_fact_map = cls.__decode_fact_map(parsed_report.factMap)

        for format_type in decoded_fact_map:
            for row in parsed_report.factMap.get(format_type, {}).get('rows', []):
                cells = []
                for idx, cell in enumerate(row['dataCells']):
                    cells.append(ReportRowCell(
                        label=cell['label'],
                        field=column_field_lookup[idx],
                        value=cell['value'],
                        uid=str(uuid.uuid4())
                    ))

                rows.append(ReportRow(cells=cells))

        return cls(
            id=report_id,
            name=report_name,
            description=report_description,
            columns=columns,
            rows=rows,
        )


class ReportColumn(BaseModel):
    header: str
    field: str
    data_type: str


class ReportRow(BaseModel):
    cells: List[ReportRowCell]

    def flatten_cells(self):
        cells = {}
        for cell in self.cells:
            cells['id'] = cell.uid
            cells[cell.field] = cell.label
        return cells


class ReportRowCell(BaseModel):
    uid: str
    label: str
    field: str
    value: str|float|bool|int|None


class ReportDefinition(BaseModel):
    id: str
    name: str
    contacts: List[str]
    accounts: List[str]


def get_exposed_reports(db: DocumentDB) -> List[str]:
    exposed_reports = db.config.find_one(
        {'_id': 'salesforce_reports'},
        {'_id': 0, 'reports': 1}
    )
    if exposed_reports is None:
        return []
    return exposed_reports.get('reports', [])

def get_report_definitions(
        salesforce: sfapi.Salesforce,
        exposed_reports: List[str]) -> List[ReportDefinition]:

    parsed_report_definitions = []

    for report in exposed_reports:
        report_id = report['report_id']
        report_accounts = report.get('accounts', [])
        report_contacts = report.get('contacts', [])

        # if we don't have any assigned contacts or accounts for this report, then just skip it - better safe than sorry!
        if not report_accounts and not report_contacts:
            logger.warning(f'No contacts or accounts assigned to report {report_id}. Skipping.')
            continue

        report_definition = salesforce.restful(f'analytics/reports/{report_id}/describe')
        parsed_report_definitions.append({**report_definition, **{'contacts': report_contacts, 'accounts': report_accounts}})

    return [
        ReportDefinition(**report['reportMetadata'], contacts=report['contacts'], accounts=report['accounts'])
        for report in parsed_report_definitions
    ]

def get_report(salesforce: sfapi.Salesforce, report_id: str) -> Dict:
    report = salesforce.restful(f'analytics/reports/{report_id}')
    parsed_report = Report.parse(report)

    flattened_rows = []
    for row in parsed_report.rows:
        cells = row.flatten_cells()
        flattened_rows.append(cells)

    parsed_report_dict = parsed_report.dict()

    return {
        'id': parsed_report_dict['id'],
        'name': parsed_report_dict['name'],
        'description': parsed_report_dict['description'],
        'columns': parsed_report_dict['columns'],
        'rows': flattened_rows,
    }

def cache_reports(db: DocumentDB, reports: List[Dict]):
    created_reports = db.reports.insert_many(reports).inserted_ids
    logger.info(f'Cached {len(created_reports)} reports to `sfreports`')

    db.reports.delete_many({'_id': {'$nin': created_reports}})

    return created_reports


def main(writeit):
    # reports that will be cached to the portal DB
    cached_reports = []

    # create a DocumentDB instance
    db = DocumentDB()

    # create Salesforce instance
    salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # get list of reports that have been flagged as exposable to the client area
    exposed_reports = get_exposed_reports(db)

    # if we have no reports, then let's just bail
    if not exposed_reports:
        logger.info("No reports found in `config.salesforce_reports` to cache")
        return

    # get report definitions from Salesforce
    report_definitions = get_report_definitions(salesforce, exposed_reports)

    # get the full report for each definition
    for report_definition in report_definitions:
        try:
            report = get_report(salesforce, report_definition.id)
            cache_report = {**report_definition.dict(), 'report': {**report}, 'last_updated': datetime.datetime.now()}
            cached_reports.append(cache_report)
        except Exception as e:
            logger.error(f'Failed to get report {report_definition.id} from Salesforce. Skipped.')
            continue

    # just bail if we have no reports to cache
    if not cached_reports:
        logger.info("No reports found in Salesforce to cache")
        return

    # cache the reports to the portal DB
    if writeit:
        cache_result = cache_reports(db, cached_reports)
        logger.info(f'Cache result: {cache_result}')


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
