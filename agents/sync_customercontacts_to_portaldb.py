import time
import datetime
import argparse
from pymongo import UpdateOne
from simple_salesforce import format_soql
from lib import sfapi, sfimport
from lib.portaldbapi import DocumentDB
from lib.settings import settings


INTERNAL_EMAIL_DOMAIN = ['clientrelationship.com', 'vaullabs.com', 'customer-relationship.com']
ORGANISATION_LEVEL_VERTICAL_MAP = {
    'Level 1': 'Holding Group',
    'Level 2': 'Network',
    'Level 3': 'Sub-network',
    'Level 4': 'Agency Brand',
    'Level 5': 'Agency Brand (Sub 1)',
    'Level 6': 'With Market',
    'Level 7': 'Office',
}


def map_office_account_to_market(account_orginsation_level:str):
    return ORGANISATION_LEVEL_VERTICAL_MAP.get(account_orginsation_level)


def boolify(s):
    return s in {'true', 'True', 'TRUE', '1', 1, True}


def generate_reporting_roles_lookup(sf):
    key_accounts_by_report_view_id = {}
    key_markets_by_report_view_id = {}
    key_customers_by_report_view_id = {}
    key_teams_by_report_view_id = {}
    report_views_by_contact_id = {}

    soql = """
    SELECT Account__c,
           Excluded__c,
           Contact_Report_View__c
    FROM Contact_Key_Account__c
    """
    for contact_key_account in sfapi.bulk_query(sf, soql):
        if not contact_key_account['Contact_Report_View__c']:
            continue

        if contact_key_account['Contact_Report_View__c'] not in key_accounts_by_report_view_id:
            key_accounts_by_report_view_id[contact_key_account['Contact_Report_View__c']] = []
        key_accounts_by_report_view_id[contact_key_account['Contact_Report_View__c']].append({'id': contact_key_account['Account__c'], 'excluded': boolify(contact_key_account['Excluded__c'])})

    soql = """
    SELECT Market__c,
           Excluded__c,
           Contact_Report_View__c
    FROM Contact_Key_Market__c
    """
    for contact_key_market in sfapi.bulk_query(sf, soql):
        if not contact_key_market['Contact_Report_View__c']:
            continue

        if contact_key_market['Contact_Report_View__c'] not in key_markets_by_report_view_id:
            key_markets_by_report_view_id[contact_key_market['Contact_Report_View__c']] = []
        key_markets_by_report_view_id[contact_key_market['Contact_Report_View__c']].append({'id': contact_key_market['Market__c'], 'excluded': boolify(contact_key_market['Excluded__c'])})

    soql = """
    SELECT Customer__c,
           Excluded__c,
           Contact_Report_View__c
    FROM Contact_Key_Customer__c
    """
    for contact_key_customer in sfapi.bulk_query(sf, soql):
        if not contact_key_customer['Contact_Report_View__c']:
            continue

        if contact_key_customer['Contact_Report_View__c'] not in key_customers_by_report_view_id:
            key_customers_by_report_view_id[contact_key_customer['Contact_Report_View__c']] = []
        key_customers_by_report_view_id[contact_key_customer['Contact_Report_View__c']].append({'id': contact_key_customer['Customer__c'], 'excluded': boolify(contact_key_customer['Excluded__c'])})

    soql = """
    SELECT Team__c,
           Excluded__c,
           Contact_Report_View__c
    FROM Contact_Key_Team__c
    """
    for contact_key_team in sfapi.bulk_query(sf, soql):
        if not contact_key_team['Contact_Report_View__c']:
            continue

        if contact_key_team['Contact_Report_View__c'] not in key_teams_by_report_view_id:
            key_teams_by_report_view_id[contact_key_team['Contact_Report_View__c']] = []
        key_teams_by_report_view_id[contact_key_team['Contact_Report_View__c']].append({'id': contact_key_team['Team__c'], 'excluded': boolify(contact_key_team['Excluded__c'])})
        
    soql = """
    SELECT Id,
           Role__c,
           Contact__c,
           Survey_Type__c
    FROM Contact_Report_View__c
    """
    for contact_report_view in sfapi.bulk_query(sf, soql):
        contact_id = contact_report_view['Contact__c']
        report_view_id = contact_report_view['Id']
        report_view_type = contact_report_view['Role__c']
        survey_type = contact_report_view['Survey_Type__c']

        if contact_id not in report_views_by_contact_id:
            report_views_by_contact_id[contact_id] = {}

        report_views_by_contact_id[contact_id][report_view_id] = {
            'type': report_view_type,
            'clients': [],
            'accounts': [],
            'markets': [],
            'customers': [],
            'teams': [],
            'excluded_accounts': [],
            'excluded_markets': [],
            'excluded_customers': [],
            'excluded_teams': [],
            'survey_type': survey_type,
        }

        key_accounts = key_accounts_by_report_view_id.get(report_view_id)
        key_markets = key_markets_by_report_view_id.get(report_view_id)
        key_customers = key_customers_by_report_view_id.get(report_view_id)
        key_teams = key_teams_by_report_view_id.get(report_view_id)

        if key_accounts:
            for account in key_accounts:
                if account['excluded']:
                    report_views_by_contact_id[contact_id][report_view_id]['excluded_accounts'].append(account['id'])
                else:
                    report_views_by_contact_id[contact_id][report_view_id]['accounts'].append(account['id'])
        if key_markets:
            for market in key_markets:
                if market['excluded']:
                    report_views_by_contact_id[contact_id][report_view_id]['excluded_markets'].append(market['id'])
                else:
                    report_views_by_contact_id[contact_id][report_view_id]['markets'].append(market['id'])
        if key_customers:
            for customer in key_customers:
                if customer['excluded']:
                    report_views_by_contact_id[contact_id][report_view_id]['excluded_customers'].append(customer['id'])
                else:
                    report_views_by_contact_id[contact_id][report_view_id]['customers'].append(customer['id'])
        if key_teams:
            for team in key_teams:
                if team['excluded']:
                    report_views_by_contact_id[contact_id][report_view_id]['excluded_teams'].append(team['id'])
                else:
                    report_views_by_contact_id[contact_id][report_view_id]['teams'].append(team['id'])

    return report_views_by_contact_id


def generate_panel_manager_report_lookup(sf):
    spm_by_contact_id_csr_id = {}
    spm_by_contact_id = {}
    csr = {}

    soql = """
    SELECT Survey_Client__c,
           Contact__c,
           Survey_Client__r.Customers_Client__c,
           Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
           Survey_Client__r.Customer_Survey__r.Customer__c,
           Survey_Client__r.Customer_Survey__r.Customer__r.Market__c,
           Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
           Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Type__c
    FROM   Survey_Panel_Manager__c
    WHERE  Survey_Client__r.Customer_Survey__r.Stage__c IN ('Live Survey', 'Insights')
    """
    for spm in sfapi.bulk_query(sf, soql):
        contact_id = spm['Contact__c']
        csr_id = spm['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id']
        csr_survey_date = spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c']
        account_id = spm['Survey_Client__r.Customers_Client__c']
        client_id = spm['Survey_Client__r.Customer_Survey__r.Customer__c']
        market_id = spm['Survey_Client__r.Customer_Survey__r.Customer__r.Market__c']
        survey_type = spm['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Type__c']

        if csr_id not in csr:
            csr[csr_id] = csr_survey_date
        else:
            if datetime.datetime.strptime(csr_survey_date, '%Y-%m-%d').date() < datetime.datetime.strptime(csr[csr_id], '%Y-%m-%d').date():
                csr[csr_id] = csr_survey_date

        if contact_id not in spm_by_contact_id_csr_id:
            spm_by_contact_id_csr_id[contact_id] = {}
        
        if csr_id not in spm_by_contact_id_csr_id[contact_id]:
            spm_by_contact_id_csr_id[contact_id][csr_id] = {
                'type': 'Panel Manager',
                'clients': [],
                'customers': [],
                'markets': [],
                'accounts': [],
                'teams': [],
                'survey_type': survey_type,
            }

        spm_by_contact_id_csr_id[contact_id][csr_id]['clients'].append(client_id)
        spm_by_contact_id_csr_id[contact_id][csr_id]['markets'].append(market_id)
        spm_by_contact_id_csr_id[contact_id][csr_id]['accounts'].append(account_id)

    # sort all our customer survey rounds by date (latest first)
    sorted_csr = dict(
        sorted(
            csr.items(),
            key=lambda item: datetime.datetime.strptime(item[1], '%Y-%m-%d'),
            reverse=True 
        )
    )

    # grab a list of the sorted customer survey round ids (by date, latest first)
    sorted_csr_ids = list(sorted_csr.keys())

    # create a map of the sorted customer survey round ids to their index in the list
    csr_ids_index_map = {val: idx for idx, val in enumerate(sorted_csr_ids)}

    # for each contact, grab only the latest customer survey round data (as that is the clients, markets and accounts we will use for the reporting role)
    for contact_id, data in spm_by_contact_id_csr_id.items():
        contact_csr_ids = list(data.keys())
        latest_csr = min(contact_csr_ids, key=lambda x: csr_ids_index_map[x])
        spm_by_contact_id[contact_id] = data[latest_csr]

    # dedupe the lists
    for contact_id, spm in spm_by_contact_id.items():
        spm['clients'] = sorted(set(spm['clients']))
        spm['markets'] = sorted(set(spm['markets']))
        spm['accounts'] = sorted(set(spm['accounts']))

    return spm_by_contact_id


def generate_contact_features_state(db, sync_date:datetime.datetime, contacts:list = [], writeit:bool = False):
    batch = []
    all_contact_features = {}

    for contact in contacts:
        contact_id = contact['Id']

        features = {
            'assumed_role': contact.get('internal', False),
            'dashboards': (contact.get('panel_manager', False) or contact.get('insights_viewer', False)),
            'dashboards_barometer': contact.get('barometer_viewer', False),
        }

        all_contact_features[contact_id] = features

        batch.append(UpdateOne({'contact_id': contact_id}, {'$set': {'contact_id': contact_id, 'features.assumed_role': features['assumed_role'], 'features.dashboards': features['dashboards'], 'features.dashboards_barometer': features['dashboards_barometer'], '_lastsync': sync_date}}, upsert=True))

    if writeit:
        if batch:
            db.features.bulk_write(batch)

    return all_contact_features


def _aggregate_descendant_accounts(account_details, account_forest_by_id, sector=None):
    # if we have a sector, nab it so we can pass it down the tree to anything which doesn't have it
    if account_details['account'].Sector:
        sector = account_details['account'].Sector

    account_details.setdefault('descendant_accounts', {})
    account_details.setdefault('descendant_customer_client_ids', set())
    account_details.setdefault('descendant_customer_client_key_account_ids', set())
    account_details.setdefault('descendant_team_ids', set())
    account_details['sector'] = sector

    for child_account in account_details['children']:
        child_account_details = account_forest_by_id[child_account.Id]
        _aggregate_descendant_accounts(child_account_details, account_forest_by_id, sector=sector)

        account_details['descendant_accounts'][child_account.Id] = child_account
        account_details['descendant_accounts'].update(child_account_details['descendant_accounts'])

        account_details['descendant_customer_client_ids'] |= child_account_details.get('customer_client_account_ids', set())
        account_details['descendant_customer_client_ids'] |= child_account_details['descendant_customer_client_ids']

        account_details['descendant_customer_client_key_account_ids'] |= child_account_details.get('customer_client_key_account_ids', set())
        account_details['descendant_customer_client_key_account_ids'] |= child_account_details['descendant_customer_client_key_account_ids']

        account_details['descendant_team_ids'] |= child_account_details.get('team_ids', set())
        account_details['descendant_team_ids'] |= child_account_details['descendant_team_ids']


def generate_accounts(sf) -> dict:
    # load all accounts into cache and generate flat account forest
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True))
    root_accounts, account_forest_by_id, _ = sfimport.generate_account_forest()

    # get and group teams by their linked account
    teams_by_account_id = {}
    for team in sfapi.get_all(sf, sfapi.Team, bulk=True):
        if team.AccountId not in teams_by_account_id:
            teams_by_account_id[team.AccountId] = set()
        teams_by_account_id[team.AccountId].add(team.Id)

    # now attach the customer client accounts to the relevant things in the tree
    for ccr in sfapi.get_all(sf, sfapi.CustomerClientRelationship, bulk=True):
        account_details = account_forest_by_id.get(ccr.CustomerAccountId)

        if not account_details:
            continue

        # We skip adding CCRs to intermediate accounts
        if account_details.get('children'):
            continue

        account_details.setdefault('team_ids', set()).update(teams_by_account_id.get(ccr.CustomerAccountId, set()))
        account_details.setdefault('customer_client_account_ids', set()).add(ccr.CustomersClientAccountId)
        if ccr.KeyAccount:
            account_details.setdefault('customer_client_key_account_ids', set()).add(ccr.CustomersClientAccountId)

    # aggregate all the lists of descendants in all accounts
    for root_account in root_accounts:
        root_account_details = account_forest_by_id[root_account.Id]
        _aggregate_descendant_accounts(root_account_details, account_forest_by_id)

    return account_forest_by_id


def _aggregate_descendant_markets(market_details, market_forest_by_id):
    market_details.setdefault('descendant_markets', {})

    for child_market in market_details['children']:
        child_market_details = market_forest_by_id[child_market.Id]
        _aggregate_descendant_markets(child_market_details, market_forest_by_id)

        market_details['descendant_markets'][child_market.Id] = child_market
        market_details['descendant_markets'].update(child_market_details['descendant_markets'])


def generate_markets(sf) -> dict:
    # get all markets from salesforce
    market_forest_by_id = {}
    for market in sfapi.get_all(sf, sfapi.Market, bulk=True):
        market_details = {'market': market, 'children': []}
        market_forest_by_id[market.Id] = market_details

    # generate children for each market
    root_markets = []
    for market_details in market_forest_by_id.values():
        market = market_details['market']
        if market.ParentMarketId:
            market_forest_by_id[market.ParentMarketId]['children'].append(market)
        else:
            root_markets.append(market)

    # generate all the descendant markets for each market
    for root_market in root_markets:
        root_market_details = market_forest_by_id[root_market.Id]
        _aggregate_descendant_markets(root_market_details, market_forest_by_id)

    return market_forest_by_id


def sync_contacts(sf, db,
                  sync_date: datetime.datetime,
                  contact_id: str|None,
                  account_record_types:dict,
                  account_forest_by_id: dict,
                  market_forest_by_id: dict,
                  survey_panel_managers_by_contact: dict,
                  contact_reporting_roles: dict,
                  writeit: bool):
    batch: list = []
    contacts: list = []

    soql = """
    SELECT Id,
           Internal_Id__c,
           FirstName,
           LastName,
           Email,
           Contact_Type__c,
           Seniority__c,
           Title,
           AccountId,
           Accounts_Manager__c,
           Panel_Manager__c,
           Signatory__c,
           Consultant__c,
           Pending_New_User__c,
           Reporting_Role__c,
           Insights_Viewer__c,
           Barometer_Viewer__c,
           Team__r.Id,
           Team__r.Name,
           Account.Id
    FROM  Contact
    WHERE Account.RecordType.Name in {record_type}
    """

    if contact_id:
        soql = soql + " AND Id = {contact_id}"
    query = format_soql(soql,
                        record_type = [sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING,
                                       sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING],
                        contact_id = contact_id)
    for c in sfapi.bulk_query(sf, query):
        # convert the CSV data from the bulk API to a dictionary
        # Empty strings become None, and boolean fields are converted to True/False
        contact_dict = {
            'Id': c['Id'],
            'InternalId': c['Internal_Id__c'] if c['Internal_Id__c'] else None,
            'FirstName': c['FirstName'],
            'LastName': c['LastName'],
            'Email': c['Email'],
            'Contact_Type__c': c['Contact_Type__c'] if c['Contact_Type__c'] else None,
            'Seniority__c': c['Seniority__c'] if c['Seniority__c'] else None,
            'Title': c['Title'] if c['Title'] else None,
            'AccountId': c['AccountId'],
            'Accounts_Manager__c': True if c['Accounts_Manager__c'] == 'true' else False,
            'Panel_Manager__c': True if c['Panel_Manager__c'] == 'true' else False,
            'Signatory__c': True if c['Signatory__c'] == 'true' else False,
            'Consultant__c': True if c['Consultant__c'] == 'true' else False,
            'Pending_New_User__c': True if c['Pending_New_User__c'] == 'true' else False,
            'Reporting_Role__c': c['Reporting_Role__c'],
            'Insights_Viewer__c': True if c['Insights_Viewer__c'] == 'true' else False,
            'Barometer_Viewer__c': True if c['Barometer_Viewer__c'] == 'true' else False,
            'Team__r.Id': c['Team__r.Id'] if c['Team__r.Id'] else None,
            'Team__r.Name': c['Team__r.Name'] if c['Team__r.Name'] else None,
        }
        contact = sfapi.Contact(**contact_dict)
        account_details = account_forest_by_id[c['Account.Id']]
        account: sfapi.Account = account_details['account']

        contact_report_view = contact_reporting_roles.get(contact.Id, {})
        panel_manager_report_view = survey_panel_managers_by_contact.get(contact.Id, {})
        reporting_roles = None
        report_views = {}
        internal = False

        # determine if the contact is internal (CRC)
        if contact.Email and contact.Email.split('@')[1] in INTERNAL_EMAIL_DOMAIN:
            internal = True

        # get all the clients and customer accounts this contact is able to see
        client_hierarchy = list(account_details['descendant_accounts'].values())
        client_hierarchy.append(account) # also include the account itself!
        client_hierarchy_ids = set([client.Id for client in client_hierarchy])
        account_hierarchy_ids = set(account_details['descendant_customer_client_ids']) | account_details.get('customer_client_account_ids', set())
        key_account_ids = set(account_details.get('descendant_customer_client_key_account_ids', set())) | account_details.get('customer_client_key_account_ids', set())
        team_ids = set(account_details.get('descendant_team_ids', set())) | account_details.get('team_ids', set()) | {None}

        # get the sector related to the contacts linked account
        sector = account_details['sector']

        # get account veritical
        account_vertical = None
        if account.RecordTypeId == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id:
            account_vertical = 'adv'
        elif account.RecordTypeId == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING].Id:
            account_vertical = 'mfv'

        # get account organisation level
        # NOTE: remove this mapping one Office has rolled out for all accounts in SF
        account_organisation_level = map_office_account_to_market(account.CalculatedOrganisationalLevel)

        # generate reporting views
        if contact_report_view:
            for report_view_id, report_view in contact_report_view.items():
                report_views[report_view_id] = {
                    'type': report_view['type'],
                    'clients': [],
                    'accounts': [],
                    'markets': [],
                    'customers': [],
                    'teams': [],
                    'survey_type': report_view['survey_type'],
                }

                # Accounts
                included_accounts = set(report_view['accounts'])
                excluded_accounts = set(report_view['excluded_accounts'])

                # Explode out accounts to include all children (if available)
                included_accounts_with_children = set()
                for included_account_id in included_accounts:
                    included_accounts_with_children.add(included_account_id)
                    included_accounts_with_children |= set([child_account.Id for child_account in account_forest_by_id.get(included_account_id, {}).get('descendant_accounts', {}).values()])
                excluded_accounts_with_children = set()
                for excluded_account_id in excluded_accounts:
                    excluded_accounts_with_children.add(excluded_account_id)
                    excluded_accounts_with_children |= set([child_account.Id for child_account in account_forest_by_id.get(excluded_account_id, {}).get('descendant_accounts', {}).values()])

                if len(included_accounts_with_children) == 0:
                    included_accounts_with_children = set(account_hierarchy_ids)

                reporting_accounts = [account for account in included_accounts_with_children if account not in excluded_accounts_with_children]

                # Customers
                included_customers = set(report_view['customers'])
                excluded_customers = set(report_view['excluded_customers'])

                # TODO: flatten out customers to be a list of "with market" accounts and default to client hierarchy if none

                reporting_customers = [client for client in included_customers if client not in excluded_customers]

                # Markets
                included_markets = set(report_view['markets'])
                excluded_markets = set(report_view['excluded_markets'])

                if len(included_markets) == 0:
                    included_markets = set([client.MarketId for client in client_hierarchy if client.MarketId is not None])

                all_included_markets = set()
                for market_id in included_markets:
                    all_included_markets.add(market_id)
                    all_included_markets |= set(market_forest_by_id.get(market_id, {}).get('descendant_markets', {}).keys())
                all_excluded_markets = set()
                for market_id in excluded_markets:
                    all_excluded_markets.add(market_id)
                    all_excluded_markets |= set(market_forest_by_id.get(market_id, {}).get('descendant_markets', {}).keys())

                reporting_markets = [market for market in all_included_markets if market not in all_excluded_markets]

                # Teams
                included_teams = set(report_view['teams'])
                excluded_teams = set(report_view['excluded_teams'])

                if len(included_teams) == 0:
                    included_teams = team_ids

                reporting_teams = [team for team in included_teams if team not in excluded_teams]

                # Extra careful permissions filtering
                #reporting_accounts = [account for account in reporting_accounts if account in account_hierarchy_ids]
                reporting_customers = [client for client in reporting_customers if client in client_hierarchy_ids]
                reporting_key_accounts = [account for account in reporting_accounts if account in key_account_ids]
                reporting_teams = [team for team in reporting_teams if team in team_ids]

                report_views[report_view_id]['clients'] = sorted(client_hierarchy_ids)
                report_views[report_view_id]['accounts'] = sorted(reporting_accounts)
                report_views[report_view_id]['key_accounts'] = sorted(reporting_key_accounts)
                report_views[report_view_id]['customers'] = sorted(reporting_customers)
                report_views[report_view_id]['markets'] = sorted(reporting_markets)
                report_views[report_view_id]['teams'] = sorted(reporting_teams, key=lambda x: (x is None, x))

        reporting_roles = set([report_view['type'] for report_view in report_views.values()])

        # generate panel manager reporting view (if the user does not have any specific contact report views setup)
        if not reporting_roles and panel_manager_report_view:
            reporting_roles.add('Panel Manager')

            # Extra careful permissions filtering
            panel_manager_report_view['clients'] = sorted([client for client in panel_manager_report_view['clients'] if client in client_hierarchy_ids])
            panel_manager_report_view['accounts'] = sorted([account for account in panel_manager_report_view['accounts'] if account in account_hierarchy_ids])
            panel_manager_report_view['key_accounts'] = sorted([account for account in panel_manager_report_view['accounts'] if account in key_account_ids])

            report_views['panel_manager'] = panel_manager_report_view

        # generate contacts features
        features = {
            'assumed_role': internal,
            'dashboards': (contact.PanelManager or contact.InsightsViewer),
            'dashboards_barometer': contact.BarometerViewer,
        }

        result = dict(
            # Internal
            internal_id=contact.InternalId,
            # Contact
            Id=contact.Id,
            internal=internal,
            email=contact.Email,
            name=f'{contact.FirstName} {contact.LastName}',
            contact_type=contact.ContactType,
            seniority=contact.Seniority,
            title=contact.JobTitle,
            # Roles
            panel_manager=contact.PanelManager,
            account_manager=contact.AccountsManager,
            signatory=contact.Signatory,
            insights_viewer=contact.InsightsViewer,
            barometer_viewer=contact.BarometerViewer,
            consultant=contact.Consultant,
            # Features
            features=features,
            # Account (linked)
            account_id=account.Id,
            account_name=account.Name,
            account_sector=sector,
            account_vertical=account_vertical,
            account_organisation_level=account_organisation_level,
            # Team
            team_id=contact_dict.get('Team__r.Id'),
            team_name=contact_dict.get('Team__r.Name'),
            # Reporting
            reporting_role=contact.ReportingRole,
            reporting_roles=sorted(reporting_roles),
            client_hierarchy=sorted(client_hierarchy_ids),
            account_hierarchy=sorted(account_hierarchy_ids),
            key_accounts=sorted(key_account_ids),
            reporting_views=report_views,
            _lastsynced=sync_date,
        )

        # dump out to file for testing
        # from bson import json_util
        # with open(f'new_customer_contacts/{result["Id"]}.json', 'w') as f:
        #    del result['_lastsynced']
        #    result['account_hierarchy'].sort()
        #    result['client_hierarchy'].sort()
        #    result['reporting_roles'].sort()
        #    for k, v in result['reporting_views'].items():
        #        v['accounts'].sort()
        #        v['clients'].sort()
        #        v['customers'].sort()
        #        v['key_accounts'].sort()
        #        v['markets'].sort()
        #    f.write(json_util.dumps(result, indent=2, sort_keys=True))

        batch.append(UpdateOne({'Id': contact.Id}, {'$set': result}, upsert=True))
        contacts.append(result)

    if writeit and batch:
        db.customercontacts.bulk_write(batch)

    print(f'{len(contacts)} Contacts Synced')

    # delete any contacts that haven't been updated in this sync (ie ones which no longer exist in salesforce)
    # assuming we're not running for a single contact though....
    if writeit and contact_id is None:
        db.customercontacts.delete_many({'_lastsynced': {'$ne': sync_date}})

    return contacts


def main(contact_id:str|None = None, writeit:bool = False):
    start_time_all = time.time()
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db = DocumentDB()

    sync_date = datetime.datetime.now(tz=datetime.UTC)
    account_record_types = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}
    account_forest_by_id = generate_accounts(sf)
    contact_reporting_roles = generate_reporting_roles_lookup(sf)
    survey_panel_managers_by_contact = generate_panel_manager_report_lookup(sf)
    market_forest_by_id = generate_markets(sf)
    print(f"LOOKUP RUN TIME: {(time.time() - start_time_all):.4f} seconds")

    contacts = sync_contacts(sf,
                             db,
                             sync_date,
                             contact_id,
                             account_record_types,
                             account_forest_by_id,
                             market_forest_by_id,
                             survey_panel_managers_by_contact,
                             contact_reporting_roles,
                             writeit)

    # TODO: move features to `customercontacts` and deprecate `features` collection
    features = generate_contact_features_state(db,
                                               sync_date,
                                               contacts,
                                               writeit)

    elapsed_time_all = time.time() - start_time_all
    print(f"TOTAL RUN TIME: {elapsed_time_all:.4f} seconds")


def lambda_handler(event, context):
    main(contact_id=None, writeit=True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--contact_id', default='ALL', help='single contact to run for (use ALL for all contacts)')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    if args.contact_id == "ALL":
        main(contact_id=None, writeit=args.writeit)
    else:
        main(contact_id=args.contact_id, writeit=args.writeit)