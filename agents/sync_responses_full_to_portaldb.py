""" Syncs all responses from Survey Panel Members in recent customer survey rounds to the portal database.
    This script retrieves customer survey rounds that have started in the last quarter and syncs their responses to
    the portal database.
    If any Survey Panel Members have been deleted in SF, it will also delete their responses from the portal database.

DATA:
    FROM: Salesforce (Customer_Survey__c, Customer_Survey_Round__c, Survey_Panel_Member__c, Survey_Response__c)
          PortalDB (responses, responsesbarometer)
    TO:   PortalDB (responses, responsesbarometer)
"""
from datetime import datetime, date, UTC
import argparse
from typing import List
from lib import sfapi
from lib.settings import settings
from simple_salesforce import Salesforce, format_soql
from agents import sync_responses_to_portaldb as lib_sync_responses


def today() -> date:
    # wrap in function for easier mocking in tests
    return datetime.now(UTC).date()


def get_start_date() -> date:
    now = today()
    # Calculate start date as first of month in last quarter
    month = now.month
    year = now.year
    if month <= 3:
        month = 10
        year -= 1
    elif month <= 6:
        month = 1
    elif month <= 9:
        month = 4
    else:
        month = 7
    start_date = now.replace(day=1, month=month, year=year)
    return start_date


def get_recent_customer_survey_rounds(sf: Salesforce) -> List[str]:
    soql = """
    SELECT Id,
           Name,
           Live_Survey_Start_Date__c,
           Customer_Survey_Round__c,
           Customer_Survey_Round__r.Name
    FROM Customer_Survey__c
    WHERE Stage__c IN ('Live Survey', 'Insights')
    AND Live_Survey_Start_Date__c >= {start_date}
    """
    start_date = get_start_date()
    query:str = format_soql(soql, start_date=start_date)

    csr_names = {}
    for cs in sfapi.bulk_query(sf, query):
        csr_names[cs['Customer_Survey_Round__c']] = cs['Customer_Survey_Round__r.Name']
    # iterate in order of names for consistent output
    customer_survey_rounds = sorted(csr_names.keys(), key=lambda csr_id: csr_names[csr_id])
    for csr_id in customer_survey_rounds:
        print(f"Found Customer Survey Round: {csr_id} {csr_names[csr_id]}")
    return customer_survey_rounds


def main(writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    print(">> FINDING RECENT CUSTOMER SURVEY ROUNDS")
    customer_survey_round_ids = get_recent_customer_survey_rounds(sf)
    if not customer_survey_round_ids:
        print("No recent customer survey rounds found.")
        return
    print(f">> PROCESSING {len(customer_survey_round_ids)} recent customer survey rounds.")
    lib_sync_responses.main(customer_survey_round_ids=','.join(customer_survey_round_ids), survey_panel_member_id=None, current_round=False, writeit=writeit, forceall=True)


def lambda_handler(event, context):
    main(writeit=True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(writeit=args.writeit)
