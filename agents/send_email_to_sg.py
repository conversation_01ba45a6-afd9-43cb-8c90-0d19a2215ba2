from __future__ import annotations
import json
import datetime
import argparse
import boto3
import base64
from io import BytesIO
from lib.settings import settings
from pydantic import BaseModel, EmailStr
from sendgrid import SendGridAPIClient
from sendgrid.helpers.mail import Mail, CustomArg, Attachment
from lib import portaldbapi


DEFAULT_FROM_NAME = "VerityRI"


class Email(BaseModel):
    from_email: EmailStr
    from_name: str|None = None
    to_email: EmailStr
    template_id: str
    template_data: dict[str, str|None]|None = {}
    custom_args: dict[str, str|None]|None = {}
    sent_at: int|None = None


class EmailServiceConfig(BaseModel):
    enabled: bool = False
    restricted_sender_domains: list[str] = []
    restricted_domains: list[str] = []
    templates: dict[str, str] = {}


def get_attachment_from_s3(attachment: str, s3_bucket: str) -> Attachment:
    attachment_dict: dict[str, str] = json.loads(attachment)

    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=s3_bucket, Key=attachment_dict['key'])
    response_body = response['Body'].read()
            
    with BytesIO(response_body) as f:
        content = f.read()
            
    return Attachment(
        disposition=attachment_dict['disposition'],
        file_name=attachment_dict['key'],
        file_type=attachment_dict['file_type'],
        file_content=base64.b64encode(content).decode(),
        content_id=attachment_dict['content_id']
    )


def main(from_email: str, 
         to_email: str, 
         template_id: str, 
         template_data: str, 
         custom_args: str, 
         banner_attachment: str|None, 
         signature_attachment: str|None, 
         send_at: str|None, 
         from_name: str|None,
         portaldb_record_map: str|None=None) -> str:
    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    es: EmailServiceConfig = EmailServiceConfig(**db.config.find_one({"_id": "email"}))
    collection: portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_EMAILLOG_COLLECTION)

    # check if email service is enabled
    if not es.enabled:
        raise Exception('Email service is disabled')

    # check if there is restriced recipient domains enabled and the `to_email` is in the list
    if es.restricted_domains and to_email.split('@')[1] not in es.restricted_domains:
        raise Exception('Domain is not permitted to receive emails')
    
    # check if there is restriced sender domains enabled and the `from_email` is in the list
    if es.restricted_sender_domains and from_email.split('@')[1] not in es.restricted_sender_domains:
        raise Exception(f'Domain is not permitted to send emails: {from_email} {from_email.split("@")[1]} {', '.join(es.restricted_sender_domains)} {to_email} {template_id}')

    # check if the template supplied exists
    if template_id not in [id for _, id  in es.templates.items()]:
        raise Exception(f"Template ID {template_id} does not exist")

    # convert template data to dict
    template_data_dict: dict[str, str] = json.loads(template_data)

    # convert custom args to dict
    custom_args_dict: dict[str, str] = json.loads(custom_args)

    # convert send_at to datetime object (if provided)
    if send_at:
        send_at: datetime.datetime = datetime.datetime.strptime(send_at, '%Y-%m-%d %H:%M:%S')
        send_at: int = int(send_at.timestamp())
    
    # set default from name if not provided
    if not from_name:
        from_name = DEFAULT_FROM_NAME
    
    # build sender string
    sender = f"{from_name} <{from_email}>"

    # create instance of Email for validation
    email: Email = Email(
        from_email=from_email,
        from_name=from_name,
        to_email=to_email,
        template_id=template_id,
        template_data=template_data_dict,
        custom_args=custom_args_dict,
        sent_at=send_at
    )

    # define initial SG message
    message: Mail = Mail(
        from_email=sender,
        to_emails=[to_email]
    )

    # attach template id to the message
    message.template_id = template_id

    # attach template data to the message
    message.dynamic_template_data = template_data_dict

    # attach attachment to the message (if provided)
    attachments: list[Attachment] = []

    if banner_attachment:
        try:
            banner = get_attachment_from_s3(banner_attachment, settings.SF_BANNERS_BUCKET)
            attachments.append(banner)
        except Exception as e:
            print(f"Error fetching banner attachment from S3: {e}")
            raise Exception('Error fetching banner attachment from S3')

    if signature_attachment:
        try:
            signature = get_attachment_from_s3(signature_attachment, settings.SF_SIGNATURES_BUCKET)
            attachments.append(signature)
        except Exception as e:
            print(f"Error fetching signature attachment from S3: {e}")
            raise Exception('Error fetching signature attachment from S3')

    # if we have any attachments, add them to the mail
    if attachments:
        message.attachment = attachments

    # attach send_at to the message (if provided)
    if send_at:
        message.send_at = send_at

    # if we have custom args, append them to the message
    if email.custom_args:
        metadata: list[CustomArg] = []
        for key, value in email.custom_args.items():
            metadata.append(CustomArg(key=key, value=value))
        message.custom_arg = metadata

    # attempt to send the email
    status_code:int = 0
    try:
        response = sg.send(message)
        status_code, body, headers = response.status_code, response.body, response.headers
        print(f"Response status_code: {status_code}")
        print(f"Recipient: {to_email}")
        print(f"Template ID: {template_id}")
    except Exception as e:
       print("Error: {0}".format(e))

    # log the email in the portaldb
    log = {
        'from_email': from_email,
        'from_name': from_name,
        'sender': sender,
        'recipient': to_email,
        'template': template_id,
        'template_data': template_data_dict,
        'event_data': custom_args_dict,
        'send_at': send_at,
        'timestamp': datetime.datetime.now(),
    }
    if portaldb_record_map:
        log['portaldb_record_map'] = portaldb_record_map
    collection.insert_one(log)

    return str(status_code)


def lambda_handler(event, _) -> None:
    if event:
        for record in event["Records"]:
            from_email: str = record['messageAttributes']['fromEmail']['stringValue']
            to_email: str = record['messageAttributes']['toEmail']['stringValue']
            template_id: str = record['messageAttributes']['templateId']['stringValue']
            template_data: str = record['messageAttributes']['templateData']['stringValue']
            custom_args: str = record['messageAttributes']['customArgs']['stringValue']
            banner_attachment: str = record['messageAttributes'].get('banner_attachment', {}).get('stringValue', None)
            signature_attachment: str = record['messageAttributes'].get('signature_attachment', {}).get('stringValue', None)
            sendAt: str = record['messageAttributes'].get('sendAt', {}).get('stringValue', None)
            fromName: str|None = record['messageAttributes'].get('fromName', {}).get('stringValue', None)
            portaldbRecordMap: str = record['messageAttributes'].get('portaldbRecordMap', {}).get('stringValue', None)
            main(
                from_email,
                to_email,
                template_id,
                template_data,
                custom_args,
                banner_attachment,
                signature_attachment,
                sendAt,
                fromName,
                portaldbRecordMap
            )


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--from_email", required=True, help="Email address to send the email from")
    ap.add_argument("--to_email", required=True, help="Email address to send the email to")
    ap.add_argument("--template_id", required=True, help="Sendgrid dynamic template ID")
    ap.add_argument("--template_data", required=True, help="Sendgrid dynamic template data for insertion")
    ap.add_argument("custom_args", help="Metadata to embed into the email for analytics")
    ap.add_argument("banner_attachment", help="Banner to include in the email")
    ap.add_argument("signature_attachment", help="Signature to include in the email")
    ap.add_argument("send_at", help="Time to send the email")
    ap.add_argument("from_name", help="Name to display in the email")
    ap.add_argument("portaldb_record_map", help="Mapping ID of record to match log too")
    args = ap.parse_args()

    main(
        args.from_email,
        args.to_email,
        args.template_id,
        args.template_data,
        args.custom_args,
        args.banner_attachment,
        args.signature_attachment,
        args.send_at,
        args.from_name,
        args.portaldb_record_map
    )
