import datetime
import argparse
from lib import portaldbapi, sfapi, sfimport
from lib.settings import settings
from simple_salesforce import Salesforce
from lib.portaldbapi import DocumentDB
from pymongo import UpdateOne


def _aggregate_descendant_accounts(account_details, account_forest_by_id):
    account_details.setdefault('descendant_accounts', {})

    for child_account in account_details['children']:
        child_account_details = account_forest_by_id[child_account.Id]
        _aggregate_descendant_accounts(child_account_details, account_forest_by_id)

        account_details['descendant_accounts'][child_account.Id] = child_account
        account_details['descendant_accounts'].update(child_account_details['descendant_accounts'])


def nullempty(s):
    return s if s else None


def boolify(value):
    return value in {'true', 'True', 'TRUE', '1', 1, True}


def main(writeit):
    sf: Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb: DocumentDB = DocumentDB()
    surveyrounds_collection = portaldbapi.get_sf_collection(portaldb, 'surveyrounds')
    sync_date = datetime.datetime.now(tz=datetime.UTC)

    # load all agency accounts into cache and generate flat account forest
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True))
    root_accounts, account_forest_by_id, _ = sfimport.generate_account_forest()

    # aggregate descendant accounts into each parent account
    for root_account in root_accounts:
        _aggregate_descendant_accounts(account_forest_by_id[root_account.Id], account_forest_by_id)

    # process all customer survey rounds in SF
    batch = []
    soql = """
    SELECT  Id,
            Name,
            Account__c,
            Current_Round__c,
            Live_Survey_Start_Date__c,
            Live_Survey_End_Date__c,
            Stage__c,
            State__c,
            Survey_Type__c,
            Linked_TRR_Survey__c,
            Progress__c
    FROM  Customer_Survey_Round__c
    WHERE Live_Survey_Start_Date__c != null 
    AND   Live_Survey_End_Date__c != null
    """
    for row in sfapi.bulk_query(sf, soql):
        round_start_date = datetime.datetime.strptime(row['Live_Survey_Start_Date__c'], '%Y-%m-%d')
        round_end_date = datetime.datetime.strptime(row['Live_Survey_End_Date__c'], '%Y-%m-%d')

        permissions = set(account_forest_by_id[row['Account__c']]['descendant_accounts'].keys())
        permissions.add(row['Account__c'])

        doc = dict(
            name=row['Name'],
            account_id=row['Account__c'],
            round_start_date=round_start_date,
            round_end_date=round_end_date,
            stage=nullempty(row['Stage__c']),
            state=nullempty(row['State__c']),
            survey_type=nullempty(row['Survey_Type__c']),
            linked_trr_survey_id=nullempty(row['Linked_TRR_Survey__c']),
            progress=nullempty(row['Progress__c']),
            current_round=boolify(row['Current_Round__c']),
            _permissions=sorted(permissions),
            _lastsynced=sync_date
        )
        batch.append(UpdateOne({"Id": row['Id']}, {"$set": doc}, upsert=True))

        # dump out to file for testing
        # from bson import json_util
        # doc['Id'] = row['Id']
        # with open(f'new_csr/{row["Id"]}.json', 'w') as f:
        #     f.write(json_util.dumps(doc, indent=2, sort_keys=True))

    # write 'em out and cleanups
    if writeit and batch:
        surveyrounds_collection.bulk_write(batch)

        # clean up any defunct things
        surveyrounds_collection.delete_many({'_lastsynced': {'$ne': sync_date}})


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
