import argparse
from pymongo import UpdateOne
from lib import sfapi, portaldbapi
from lib.settings import settings


def get_all_descendants(market_id, children_map):
    all_descendants = []
    direct_children = children_map.get(market_id, [])
    
    for child_id in direct_children:
        all_descendants.append(child_id)
        all_descendants.extend(get_all_descendants(child_id, children_map))
    
    return all_descendants


def main(writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    portaldb_collection = portaldbapi.get_sf_collection(portaldb, 'markets')

    soql = """
    SELECT Id,
           Name,
           Market_Type__c,
           Parent_Market__c
    FROM Market__c
    """

    data = []
    batch = []
    all_markets = []
    all_markets_in_sf_ids = set()
    all_markets_in_portaldb = set()
    for market in sf.query_all_iter(soql):
        if not market:
            continue
            
        all_markets.append(market)

    all_markets_in_sf_ids = set([record['Id'] for record in all_markets])
    all_markets_in_portaldb = set([record['Id'] for record in portaldb_collection.find({}, {'Id': 1})])
    data = [dict(record) for record in all_markets]

    # Create a lookup to map each market's Id to a list of its child Ids
    children_map = {}
    for record in data:
        parent_id = record['Id']
        children_map[parent_id] = []
    
    for record in data:
        parent_id = record.get('Parent_Market__c')
        if parent_id:
            children_map[parent_id].append(record['Id'])
    
    # Add the children to each record with all possible descendants
    for record in data:
        record['children'] = get_all_descendants(record['Id'], children_map)

    # Generate the mongo record for each market
    for record in data:
        market = dict(
            Id=record['Id'],
            name=record['Name'],
            type=record['Market_Type__c'],
            parent_market_id=record['Parent_Market__c'],
            children=record['children']
        )
        batch.append(UpdateOne({'Id': record['Id']}, {'$set': market}, upsert=True))

    # Calculate stale markets in portaldb
    stale_market_ids = all_markets_in_portaldb - all_markets_in_sf_ids
    
    if writeit:
        if batch:
            portaldb_collection.bulk_write(batch)
        
        if stale_market_ids:
            portaldb_collection.delete_many({'Id': {'$in': list(stale_market_ids)}})

        
def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
