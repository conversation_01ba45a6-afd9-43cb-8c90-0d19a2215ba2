import argparse
from lib import sfapi, portaldbapi
from lib.settings import settings


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    # get all active panel managers
    soql = """
        SELECT  Id,
                Contact__c,
                Survey_Client__c,
                Has_Responded__c
        FROM  Survey_Panel_Manager__c
        WHERE Survey_Client__r.Current_Round__c = TRUE AND Survey_Client__r.Customer_Survey__r.Stage__c = 'Survey Setup'
    """
    survey_client_ids = set()
    panel_managers_by_survey_client_id = {}
    for row in sfapi.bulk_query(sf, soql):
        survey_client_id = row["Survey_Client__c"]
        survey_client_ids.add(survey_client_id)
        panel_managers_by_survey_client_id.setdefault(survey_client_id, {})[row['Contact__c']] = row

    # now figure out SF updates for people who have responded
    batch = []
    for pdb_survey_client in confirm_panel_collection.find({'Id': {'$in': list(survey_client_ids)}}):
        sf_panel_managers = panel_managers_by_survey_client_id.get(pdb_survey_client['Id'], {})
        if not sf_panel_managers:
            continue

        for panel in pdb_survey_client.get('confirmed_panel', []):
            pm_contact_id = panel.get('confirm_user_id')
            sf_pm = sf_panel_managers.get(pm_contact_id)
            if not sf_pm:
                continue

            if sf_pm['Has_Responded__c'].lower() in {'false', None, ''}:
                batch.append({'Id': sf_pm['Id'], 'Has_Responded__c': True})

    if batch and writeit:
        sfapi.bulk_update(sf, 'Survey_Panel_Manager__c', batch)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
