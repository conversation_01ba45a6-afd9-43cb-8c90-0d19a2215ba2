from lib import sfapi, portaldbapi
import datetime
import argparse
from lib.settings import settings


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    accesslog_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_ACCESSLOG_COLLECTION]

    query_date = datetime.datetime.now() - datetime.timedelta(days=1)
    query = {
        "type": "request",
        "$expr": {
            "$gt": [
                {"$max": "$last_access"},
                query_date
            ]
        },
        "deleted": {"$ne": True},
    }
    accesslog_cursor = accesslog_collection.find(query)

    accesslogs_to_update: dict[str, dict] = {}
    for accesslog in accesslog_cursor:
        accesslogs_to_update[accesslog['contact_id']] = {
            'Id': accesslog['contact_id'],
            "Last_Access_Client_Area__c": accesslog['last_access'][-1].isoformat(),
        }

    if accesslogs_to_update and writeit:
        sfapi.bulk_update(sf, "Contact", accesslogs_to_update.values())


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
