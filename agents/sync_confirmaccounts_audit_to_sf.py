import argparse
from lib import sfapi, portaldbapi
from simple_salesforce import format_soql, Salesforce
import datetime
from lib.settings import settings



def format_date_key(date: datetime.datetime) -> str:
    return date.strftime('%Y%m%d%H%M%S')


def preload_sf_data(sf: Salesforce, customer_survey_ids: set[str]) -> dict[str, sfapi.AccountConfirmationAudit]:
    # get all relevant customer surveys
    all_acount_confirmation_audit_by_id = {}
    soql = format_soql("WHERE Customer_Survey__c IN {customer_survey_ids}", customer_survey_ids=customer_survey_ids)
    for aca in sfapi.get_all(sf, sfapi.AccountConfirmationAudit, bulk=True, query_suffix=soql):
        confirm_date = datetime.datetime.strptime(aca.ConfirmedDate, '%Y-%m-%dT%H:%M:%S.%fZ')
        key = f'{aca.CustomerSurveyId}-{aca.AccountManagerId}-{format_date_key(confirm_date)}'
        all_acount_confirmation_audit_by_id[key] = aca

    return all_acount_confirmation_audit_by_id


def main(customer_survey_id:str|None = None, forceall:bool = False, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    all_confirm_accounts: list[portaldbapi.ConfirmAccounts] = []
    customer_survey_ids = set()
    new_account_confirmation_audit = []

    now = datetime.datetime.now(datetime.timezone.utc)
    today_start = datetime.datetime(now.year, now.month, now.day)
    yesterday_start = today_start - datetime.timedelta(days=1)

    query = {
        "$or": [
            {"sfsync_date": None},
            {"sfsync_date": {"$gte": yesterday_start, "$lt": now}}
        ],
        "deleted": { "$ne": True },
    }
    if customer_survey_id:
        query["Id"] = customer_survey_id
    if forceall:
        query = {}

    # grab all the records
    confirm_accounts_records = confirm_accounts_collection.find(query)

    # gather information on what we're about to sync
    for pdb_customer_survey in confirm_accounts_records:
        pdb_customer_survey = portaldbapi.ConfirmAccounts(**pdb_customer_survey)
        all_confirm_accounts.append(pdb_customer_survey)
        customer_survey_ids.add(pdb_customer_survey.Id)

    # if there's nothing to do, exit early
    if not all_confirm_accounts:
        return
    
    # get necessary data from salesforce now we know we have something to do
    all_acount_confirmation_audit_by_id = preload_sf_data(sf, customer_survey_ids)

    # check each confirm account to see if there is new saves needing synced
    for pdb_customer_survey in all_confirm_accounts:
        
        # get the latest count of confirmed accounts
        if len(pdb_customer_survey.confirmed_accounts):
            for idx, confirmed_account in enumerate(pdb_customer_survey.confirmed_accounts):
                confirmed_by_contact_id = confirmed_account.confirm_user_id
                confirmed_by_contact_date = confirmed_account.confirm_date
                account_confirmation_count = len(confirmed_account.accounts)
                account_confirmation_names = ', '.join([account.account_name for account in confirmed_account.accounts])
                accounts_added_count = len(confirmed_account.stats['added'])
                accounts_added_names = ', '.join([account['name'] for account in confirmed_account.stats['added']])
                accounts_removed_count = len(confirmed_account.stats['removed'])
                accounts_removed_names = ', '.join([account['name'] for account in confirmed_account.stats['removed']])
                sf_cache_key = f'{pdb_customer_survey.Id}-{confirmed_by_contact_id}-{format_date_key(confirmed_by_contact_date)}'
                is_latest_save = (idx == 0)

                if sf_cache_key not in all_acount_confirmation_audit_by_id:
                    new_account_confirmation_audit.append({
                        "Customer_Survey__c": pdb_customer_survey.Id,
                        "Account_Manager__c": confirmed_by_contact_id,
                        "Confirmed_Date__c": confirmed_by_contact_date.isoformat(),
                        "Confirmed_Accounts_Count__c": account_confirmation_count,
                        "Confirmed_Accounts__c": account_confirmation_names,
                        "Accounts_Added_Count__c": accounts_added_count,
                        "Accounts_Added__c": accounts_added_names,
                        "Accounts_Removed_Count__c": accounts_removed_count,
                        "Accounts_Removed__c": accounts_removed_names,
                        "Latest_Save__c": is_latest_save,
                    })

    if writeit:
        if new_account_confirmation_audit:
            sfapi.bulk_insert(sf, "Account_Confirmation_Audit__c", new_account_confirmation_audit)
    
    updates = set([p["Customer_Survey__c"] for p in new_account_confirmation_audit])
    print(f"Updated {len(updates)} customer surveys")
    print(updates)


def lambda_handler(event, context):
    main(
        customer_survey_id=None, 
        forceall=False, 
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_id', help='ID of the CS to process')
    ap.add_argument("--forceall", action="store_true", help="Attempt to resync audit history for all customer surveys")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(
        customer_survey_id=args.customer_survey_id, 
        forceall=args.forceall, 
        writeit=args.writeit
    )
