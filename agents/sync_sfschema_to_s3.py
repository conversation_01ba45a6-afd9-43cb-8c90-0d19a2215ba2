import tempfile
import zipfile
import argparse
import json
import datetime
from lib import sfapi
from lib.settings import settings
import urllib
import boto3

def fetch_object_definitions(sf):
    # Fetch all object names
    object_definitions = {}

    object_names = sf.describe()['sobjects']
    for obj in object_names:
        obj_name = obj['name']
        obj_description = sf.__getattr__(obj_name).describe()
        object_definitions[obj_name] = obj_description

    return object_definitions


def fetch_flows(sf):

    flows = {}

    results = sf.toolingexecute(f'query/?q=Select+Id+From+FlowDefinition')
    for cur_record in results['records']:
        flow_definition_url = f"sobjects/FlowDefinition/{cur_record['Id']}"
        flow_definition = sf.toolingexecute(flow_definition_url)
        active_version_id = flow_definition['ActiveVersionId']

        if active_version_id:
            active_flow_url = f"sobjects/Flow/{active_version_id}"
            active_flow = sf.toolingexecute(active_flow_url)
            flow_definition['active_flow'] = active_flow

        flows[flow_definition['FullName']] = flow_definition

    return flows


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    object_definitions = fetch_object_definitions(sf)
    flow_definitions = fetch_flows(sf)

    outfile = tempfile.NamedTemporaryFile(suffix='.zip')
    with zipfile.ZipFile(outfile, "w", compression=zipfile.ZIP_DEFLATED) as zipout:
        # output object definitions
        for objname, objdef in object_definitions.items():
            zip_path = zipfile.Path(zipout, f"objects/{objname}.json")
            with zip_path.open('w') as fileout:
                fileout.write(json.dumps(objdef, indent=2))

        # output flows
        for flowname, flowdef in flow_definitions.items():
            zip_path = zipfile.Path(zipout, f"flows/{flowname}.json")
            with zip_path.open('w') as fileout:
                fileout.write(json.dumps(flowdef, indent=2))

    # upload zip to s3
    now = datetime.datetime.now(datetime.UTC)
    s3_filename = f"crc-sf-schema/yy={now:%Y}/mm={now:%m}/dd={now:%d}/crc-sf-schema-{now:%Y%m%d-%H%M%S}.zip"
    s3_client = boto3.client('s3')
    s3_client.upload_file(outfile.name, settings.SF_DATA_BUCKET, s3_filename)

    return f"s3://{settings.SF_DATA_BUCKET}/{s3_filename}"


def lambda_handler(event, context):
    return main()


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    print(main())
