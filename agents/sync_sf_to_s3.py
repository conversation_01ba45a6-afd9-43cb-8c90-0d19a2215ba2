import csv
from lib import sfapi, portaldbapi
import argparse
import re
import zipfile
import tempfile
import datetime
import boto3
from lib.settings import settings

OBJNAMES = sfapi.SFAPI_OBJECT_NAMES + sfapi.SFAPI_KEY_OBJECT_NAMES

def export_csv(sf, zipout, snakecase, objname):
    sfobject = getattr(sfapi, objname)
    fieldnames = sfobject.model_json_schema()['properties'].keys()

    if snakecase:
        snakeobjname = re.sub(r'(?<!^)(?=[A-Z])', '_', objname).lower()
        filename = f"{snakeobjname}.csv"
    else:
        filename = f"{objname}.csv"

    zip_path = zipfile.Path(zipout, filename)
    with zip_path.open('w') as fileout:
        csvout = csv.DictWriter(fileout, fieldnames=fieldnames)
        csvout.writeheader()

        for item in sfapi.get_all(sf, sfobject, bulk=True, is_export=True):
            row = item.model_dump(by_alias=True)
            csvout.writerow(row)


def main(snakecase):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # create indices in portaldb
    portaldb = portaldbapi.DocumentDB()
    portaldbapi.make_indices(portaldb)

    # output to a temporary zip file
    outfile = tempfile.NamedTemporaryFile(suffix='.zip')
    with zipfile.ZipFile(outfile, "w", compression=zipfile.ZIP_DEFLATED) as zipout:
        for objname in OBJNAMES:
            export_csv(sf, zipout, snakecase, objname)
    outfile.flush()

    # upload zip to s3
    now = datetime.datetime.now(datetime.UTC)
    s3_filename = f"crc-sf/yy={now:%Y}/mm={now:%m}/dd={now:%d}/crc-sf-{now:%Y%m%d-%H%M%S}.zip"
    s3_client = boto3.client('s3')
    s3_client.upload_file(outfile.name, settings.SF_DATA_BUCKET, s3_filename)

    # upload zip to crc s3 if buckets present
    if settings.SF_ADH_BUCKET:
        crc_s3_filename = datetime.datetime.now().strftime('salesforce-data-%Y-%m-%d-%H-%M-%S.zip')
        for bucket_name in settings.SF_ADH_BUCKET.split(','):
            s3_client.upload_file(outfile.name, bucket_name, crc_s3_filename)

    return f"s3://{settings.SF_DATA_BUCKET}/{s3_filename}"


def lambda_handler(event, context):
    return main(True)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    print(main(True))
