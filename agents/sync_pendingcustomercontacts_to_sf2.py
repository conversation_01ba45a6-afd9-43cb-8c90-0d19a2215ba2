from lib import sfapi, portaldbapi
from lib.settings import settings
from pymongo import UpdateOne
import argparse
import datetime


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    pending_contacts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_PENDING_CUSTOMER_CONTACTS_COLLECTION)

    # find all pending contacts that are unsynced
    query = {
        "sfsync_date": None,
        'deleted': {'$ne': True}
    }
    portaldb_pending_contacts = pending_contacts_collection.find(query)

    # sync them
    sf_pending_contacts_to_create = []
    for portaldb_pending_contact in portaldb_pending_contacts:
        portaldb_pending_contact = portaldbapi.PendingCustomerContact(**portaldb_pending_contact)
        new_pending_contact = sfapi.Contact(
            Id=f"Contact-{portaldb_pending_contact.internal_id}",
            FirstName=portaldb_pending_contact.first_name,
            LastName=portaldb_pending_contact.last_name,
            Email=portaldb_pending_contact.email,
            Internal_Id__c=portaldb_pending_contact.internal_id,
            Pending_New_User__c=True,
        )
        sf_pending_contacts_to_create.append(new_pending_contact)

    # FIXME: try and get 'em from salesforce to prevent dupes
    existing_sf_contacts_by_email = {}

    # FIXME: now go through each and new one check if it already exists in salesforce to prevent dupes
    sf_pending_contacts_to_create_deduped = []
    sf_pending_contacts_to_create_dupes = []
    for sf_pending_contact in sf_pending_contacts_to_create:
        if sf_pending_contact.Email.lower() in existing_sf_contacts_by_email:
            sf_pending_contacts_to_create_dupes.append(sf_pending_contact)
        else:
            sf_pending_contacts_to_create_deduped.append(sf_pending_contact)

    # write to salesforce
    if writeit:
        created_pending_customer_contacts: dict[str, sfapi.Contact] = {}
        sfapi.bulk_import_batch_to_sf(sf, None, created_pending_customer_contacts, sf_pending_contacts_to_create_deduped, sfapi.Contact)

        # now write the salesforce id back onto portaldb objects
        pdb_updates = []
        for sf_contact in created_pending_customer_contacts.values():
            pdb_updates.append(
                UpdateOne(
                    {"internal_id": sf_contact.InternalId},
                    {"$set": {"Id": sf_contact.Id, "sfsync_date": datetime.datetime.now(datetime.UTC)}},
                )
            )
        if pdb_updates:
            pending_contacts_collection.bulk_write(pdb_updates)

        # auto complete any that were dupes in salesforce
        pending_contacts_collection.update_many({"internal_id": {"$in": [sf_contact.InternalId for sf_contact in sf_pending_contacts_to_create_dupes]}},
                                                {"$set": {"sfsync_date": datetime.datetime.now(datetime.UTC), "deleted": True}})


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
