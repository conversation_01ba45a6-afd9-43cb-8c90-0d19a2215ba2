import json
import datetime
import argparse
from pymongo import UpdateOne
from json.decoder import JSONDecodeError
from lib import sfapi, portaldbapi, sfimport
from lib.settings import settings
from simple_salesforce import format_soql


def format_round_name(round_date: str) -> str:
    return round_date.strftime('%B %Y')


def calculate_team_market_group(team_market_type: str) -> str:
    if team_market_type in ['Country', None]:
        return 'Local'
    elif team_market_type in ['Business Region', 'Key Region', 'Region']:
        return 'Regional'
    elif team_market_type in ['Global']:
        return 'Global'


def generate_client_organisation_level_lookup(sf):
    print('START: PROCESSING ORGANISATION LEVEL LOOKUP')

    result = {}
    all_accounts = []

    soql = """
    SELECT Id,
           Name,
           Organisation_Level__c,
           Calculated_Organisation_Level__c,
           Industry,
           Supersector__c,
           Sector__c,
           Subsector__c,
           Parent.Id,
           Parent.Name,
           Market__c,
           Market__r.Name,
           Market__r.Market_Type__c
    FROM Account
    """

    for account in sf.query_all_iter(soql):
        if not account:
            continue

        all_accounts.append(account)

    # Create a lookup dictionary for quick access by 'id'
    accounts_by_id = {account['Id']: account for account in all_accounts}

    # Iterate over each 'With Market' or 'Office' account
    for account in all_accounts:
        pa = account.get('Parent', {}) or {}

        if account['Calculated_Organisation_Level__c'] in {'Level 6', 'Level 7'}:
            with_market = {}
            agency_brand_sub_1 = {}
            agency_brand = {}
            sub_network = {}
            network = {}
            holding_group = {}
            parent_id = pa.get('Id')

            # Traverse up the parent hierarchy
            while parent_id is not None:
                parent = accounts_by_id.get(parent_id)
                pa2 = parent.get('Parent', {}) or {}

                if not parent:
                    break

                market_name = market_type = None
                if parent['Market__r']:
                    market_name = parent['Market__r']['Name']
                    market_type = parent['Market__r']['Market_Type__c']

                obj = {
                    'id': parent['Id'],
                    'name': parent['Name'],
                    'industry': parent['Industry'],
                    'supersector': parent['Supersector__c'],
                    'sector': parent['Sector__c'],
                    'subsector': parent['Subsector__c'],
                    'market_id': parent['Market__c'],
                    'market_name': market_name,
                    'market_type': market_type,
                }

                if parent['Calculated_Organisation_Level__c'] == 'Level 6' and not with_market:
                    with_market = obj
                if parent['Calculated_Organisation_Level__c'] == 'Level 5' and not agency_brand_sub_1:
                    agency_brand_sub_1 = obj
                if parent['Calculated_Organisation_Level__c'] == 'Level 4' and not agency_brand:
                    agency_brand = obj
                if parent['Calculated_Organisation_Level__c'] == 'Level 3' and not sub_network:
                    sub_network = obj
                if parent['Calculated_Organisation_Level__c'] == 'Level 2' and not network:
                    network = obj
                if parent['Calculated_Organisation_Level__c'] == 'Level 1' and not holding_group:
                    holding_group = obj

                parent_id = pa2.get('Id')

            result[account['Id']] = {
                'with_market': with_market,
                'agency_brand_sub_1': agency_brand_sub_1,
                'agency_brand': agency_brand,
                'sub_network': sub_network,
                'network': network,
                'holding_group': holding_group,
            }

    print('END: PROCESSING ORGANISATION LEVEL LOOKUP')

    return result


def generate_survey_round_name_lookup(sf):
    print('START: PROCESSING SURVEY ROUNDS NAME LOOKUP')

    customer_survey_round_start_dates = {}
    customer_survey_round_labels = {}

    soql = """
    SELECT Id,
           Live_Survey_Start_Date__c,
           Customer_Survey_Round__r.Id,
           Customer_Survey_Round__r.Live_Survey_Start_Date__c
    FROM Customer_Survey__c
    """

    for customer_survey in sf.query_all_iter(soql):
        if not customer_survey:
            continue

        csr = customer_survey.get('Customer_Survey_Round__r', {}) or {}

        customer_survey_round_id = csr.get('Id')
        customer_survey_round_live_survey_start_date = csr.get('Live_Survey_Start_Date__c')
        customer_survey_live_survey_start_date = customer_survey.get('Live_Survey_Start_Date__c')

        # if whatever reason we don't have a CSR, skip
        if not customer_survey_round_id:
            continue

        # if this is the first time we're seeing this CSR, add it to the dict
        if customer_survey_round_id not in customer_survey_round_start_dates:
            customer_survey_round_start_dates[customer_survey_round_id] = []

        # if the duplication down to the CS in SF hasn't worked, we can default to the CSR date
        if not customer_survey_live_survey_start_date:
            customer_survey_live_survey_start_date = customer_survey_round_live_survey_start_date

        # if have a date, add it to the list
        if customer_survey_live_survey_start_date:
            customer_survey_round_start_dates[customer_survey_round_id].append(customer_survey_live_survey_start_date)

    for customer_survey_round_id, dates in customer_survey_round_start_dates.items():
        # if we have no dates, skip
        if not dates:
            continue

        # Parse the date strings into datetime objects
        parsed_dates = [datetime.datetime.strptime(date_str, '%Y-%m-%d') for date_str in dates]

        # Find the earliest date
        earliest_date = min(parsed_dates)

        # Get the full month name of the earliest date
        earliest_month_name = format_round_name(earliest_date)

        customer_survey_round_labels[customer_survey_round_id] = earliest_month_name

    print('END: PROCESSING SURVEY ROUNDS NAME LOOKUP')

    return customer_survey_round_labels


def propagate_customer_survey_rounds(account_forest_by_id, account_id):
    # find the details
    account_deets = account_forest_by_id[account_id]
    survey_rounds = account_deets.get('customer_survey_rounds', {})

    # propagate up from children
    for child_account in account_deets.get('children', []):
        child_account_deets = account_forest_by_id[child_account.Id]
        survey_rounds.update(propagate_customer_survey_rounds(account_forest_by_id, child_account_deets['account'].Id))

    # generate a lookup of survey rounds
    sorted_survey_rounds = sorted(survey_rounds.items(), key=lambda x: x[1], reverse=True)
    account_deets['customer_survey_rounds'] = {x[0]: {'start_date': x[1], 'index': index} for index, x in enumerate(sorted_survey_rounds)}
    return survey_rounds


def generate_survey_rounds_index_lookup(sf):
    print('START: PROCESSING SURVEY ROUNDS INDEX')

    # load all agency accounts into cache and generate account forest
    soql = format_soql("where RecordType.Name = {record_type}", record_type=sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql))
    account_forest_roots, account_forest_by_id, account_forest_by_name = sfimport.generate_account_forest()

    # now load all customer surveys and attach their dates to the accounts
    soql = """
    SELECT Customer_Survey_Round__c,
           Customer__c,
           Customer_Survey_Round__r.Survey_Round__r.Round_Date__c
    FROM Customer_Survey__c
    """
    for cs in sfapi.bulk_query(sf, soql):
        customer_survey_round_id = cs['Customer_Survey_Round__c']
        agency_account_id = cs['Customer__c']
        round_start_date = cs['Customer_Survey_Round__r.Survey_Round__r.Round_Date__c']

        account_forest_by_id[agency_account_id].setdefault('customer_survey_rounds', {})[customer_survey_round_id] = round_start_date

    # now, propagate the customer survey rounds up the tree and finalise the lists of customer survey rounds
    for root_acount in account_forest_roots:
        propagate_customer_survey_rounds(account_forest_by_id, root_acount.Id)

    print('END: PROCESSING SURVEY ROUNDS INDEX')

    return account_forest_roots, account_forest_by_id, account_forest_by_name


def sync_responses(sf,
                   customer_survey_round_id:list|None = None,
                   survey_panel_member_id:str|None = None,
                   organisation_level_lookup: dict = {},
                   survey_round_name_lookup: dict = {},
                   survey_round_lookup: dict = {},
                   writeit: bool = False,
                   forceall:bool = False) -> int:
    print('START: PROCESSING RESPONSES')

    portaldb = portaldbapi.DocumentDB()
    portaldb_collection = portaldbapi.get_sf_collection(portaldb, 'responses')
    batch = []
    total_record_count = 0
    sync_date = datetime.datetime.now(tz=datetime.UTC)

    soql = """
    SELECT  Id,
            Contact_Email__c,
            Contact_Type__c,
            Contact_Division__c,
            Contact_Title__c,
            Has_Responded__c,
            Opt_Out__c,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.Name,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customers_Client_Name__c,
            Survey_Client__r.Customers_Client__r.Name,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Parent_Customer_Survey__c,
            Survey_Client__r.Customer_Survey__r.Insights_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Market_Type__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Legacy_Sector__c,
            Survey_Client__r.Customer_Survey__r.Team__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Name,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Name,
            Survey_Client__r.Customer_Survey__r.Team__r.Market__r.Market_Type__c,
            Survey_Client__r.Customer_Survey__r.Team__r.Type__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_Start_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Round_Date__c,
            (SELECT Id,
                    Score__c,
                    Feedback__c,
                    Feedback_Translated__c,
                    Themes__c,
                    Is_Extra_Question__c,
                    Response_Date_Time__c
            FROM Survey_Responses__r)
    FROM Survey_Panel_Member__c
    """

    if not forceall:
        soql = soql + " WHERE LastModifiedDate >= YESTERDAY"
    if customer_survey_round_id:
        soql = soql + " WHERE Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id IN {customer_survey_round_id}"
    if survey_panel_member_id:
        soql = soql + " WHERE Id = {survey_panel_member_id}"

    query = format_soql(soql, customer_survey_round_id = customer_survey_round_id, survey_panel_member_id = survey_panel_member_id)

    for spm in sf.query_all_iter(query):
        if not spm:
            continue

        spm_r = spm.get('Survey_Responses__r', {}) or {}
        r = {}
        sc = spm.get('Survey_Client__r', {}) or {}
        cs = sc.get('Customer_Survey__r', {}) or {}
        csr = cs.get('Customer_Survey_Round__r', {}) or {}
        sr = csr.get('Survey_Round__r', {}) or {}
        t = cs.get('Team__r', {}) or {}
        tm = t.get('Market__r', {}) or {}
        account = sc.get('Customers_Client__r', {}) or {}
        client = cs.get('Customer__r', {}) or {}
        contact = spm.get('Contact__r', {}) or {}
        market = client.get('Market__r', {}) or {}

        # if we're missing being attached to a customer survey round, skip
        if not sr.get('Id') or not csr.get('Id'):
            continue

        # Get only the non-extra question response
        survey_response = [record for record in spm_r.get('records', []) if not record.get('Is_Extra_Question__c')]

        # Ignore extra questions (for now)
        if len(survey_response) > 1:
            continue

        # Get the response (if we have one)
        if len(survey_response):
            r = survey_response[0]

        # Get the client hierarchy
        agency_brand_sub_1 = organisation_level_lookup.get(client.get('Id'), {}).get('agency_brand_sub_1', {})
        agency_brand = organisation_level_lookup.get(client.get('Id'), {}).get('agency_brand', {})
        sub_network = organisation_level_lookup.get(client.get('Id'), {}).get('sub_network', {})
        network = organisation_level_lookup.get(client.get('Id'), {}).get('network', {})
        holding_group = organisation_level_lookup.get(client.get('Id'), {}).get('holding_group', {})
        with_market = organisation_level_lookup.get(client.get('Id'), {}).get('with_market', {})

        if not with_market:  # ie the leaf node was a with market
            with_market = {'id': client.get('Id'),
                           'name': client.get('Name'),
                           'market_id': client.get('Market__c'),
                           'market_name': market.get('Name'),
                           'market_type': market.get('Market_Type__c')}
            office = {}

        else:  # ie the leaf node was an office.
            office = {'id': client.get('Id'),
                      'name': client.get('Name')}

        # Get account name
        account_name = account.get('Name')
        account_name_custom = sc.get('Customers_Client_Name__c')
        if account_name_custom:
            account_name = account_name_custom

        # Get market from the With Market agency
        market_id = with_market.get('market_id')
        market_name = None
        market_type = None
        if market_id:
            market_name = with_market.get('market_name')
            market_type = with_market.get('market_type')

        # Get themes (they could initially be none)
        themes = []
        if r.get('Themes__c'):
            try:
                themes = json.loads(r.get('Themes__c'))
            except JSONDecodeError as e:
                print(f'Error decoding themes for response {r.get('Id')}: {e}')
                themes = []

        # Get theme-specific stuff
        sentiment_count = {
            'p': 0,
            'n': 0,
            'm': 0,
        }
        theme_categories = set()
        for theme in themes:
            # Gather list of relational and transactional themes
            category_value = theme['l3']
            theme_categories.add(category_value)

            # Determine response overall sentiment
            if not theme.get('pn'):
                continue
            if int(theme['pn']) > 0:
                sentiment_count['p'] += 1
            elif int(theme['pn']) < 0:
                sentiment_count['n'] += 1
            else:
                sentiment_count['m'] += -1
        theme_categories = list(theme_categories)
        sentiment = max(sentiment_count, key=sentiment_count.get)
        if sentiment_count[sentiment] == 0:
            sentiment = None

        # Get (legacy) sector
        sector = None
        if client.get('Legacy_Sector__c') is not None:
            legacy_sectors = client.get('Legacy_Sector__c', '').split(',')
            if len(legacy_sectors) > 0:
                sector = legacy_sectors[0]

        # Determine survey start and end date
        survey_start_date = cs.get('Live_Survey_Start_Date__c')
        survey_end_date = cs.get('Live_Survey_End_Date__c')
        if not survey_start_date:
            survey_start_date = csr.get('Live_Survey_Start_Date__c')
        if not survey_end_date:
            survey_end_date = csr.get('Live_Survey_End_Date__c')

        # Get the survey round index (per organisation level)
        office_round_index = survey_round_lookup.get(office.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        with_market_round_index = survey_round_lookup.get(with_market.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        agency_brand_sub_1_round_index = survey_round_lookup.get(agency_brand_sub_1.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        agency_brand_round_index = survey_round_lookup.get(agency_brand.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        sub_network_round_index = survey_round_lookup.get(sub_network.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        network_round_index = survey_round_lookup.get(network.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')
        holding_group_round_index = survey_round_lookup.get(holding_group.get('id'), {}).get('customer_survey_rounds', {}).get(csr.get('Id'), {}).get('index')

        # Get the round name
        round_name = survey_round_name_lookup.get(csr.get('Id'), '')
        if not round_name:
            round_name = format_round_name(datetime.datetime.strptime(sr.get('Round_Date__c'), '%Y-%m-%d'))

        # Determine if this is a "team" customer survey, mark and so, and roll-up back to the parent customer survey
        customer_survey_id = cs.get('Id')
        team_id = cs.get('Team__c')
        team_name = None
        team_type = None
        team_market_id = None
        team_market_name = None
        team_market_type = None
        team_market_group = None
        team_customer_survey_id = None
        if team_id:
            team_name = cs.get('Team__r', {}).get('Name')
            team_type = cs.get('Team__r', {}).get('Type__c')
            team_market_id = cs.get('Team__r', {}).get('Market__c')
            team_market_name = tm.get('Name')
            team_market_type = tm.get('Market_Type__c')
            team_market_group = calculate_team_market_group(tm.get('Market_Type__c'))
            team_customer_survey_id = cs.get('Id')
            customer_survey_id = cs.get('Parent_Customer_Survey__c')

        # Determine the reporting role market filter fields
        market_view_filter_id = None
        market_view_filter_name = None
        market_view_filter_type = None
        account_view_filter_id = None
        account_view_filter_name = None
        account_view_filter_type = None
        if team_market_name:
            account_view_filter_id = team_market_id
            account_view_filter_name = team_market_name
            account_view_filter_type = team_market_type
        else:
            account_view_filter_id = market_id
            account_view_filter_name = market_name
            account_view_filter_type = market_type
        if team_market_name and team_market_name == market_name:
            market_view_filter_id = team_market_id
            market_view_filter_name = team_market_name
            market_view_filter_type = team_market_type
        elif team_market_name and team_market_name != market_name:
            market_view_filter_id = None
            market_view_filter_name = None
            market_view_filter_type = None
        else:
            market_view_filter_id = market_id
            market_view_filter_name = market_name
            market_view_filter_type = market_type

        response = dict(
            Id=spm.get('Id'),
            # Location
            market_id=market_id,
            market_name=market_name,
            market_type=market_type,
            market_view_filter_id=market_view_filter_id,
            market_view_filter_name=market_view_filter_name,
            market_view_filter_type=market_view_filter_type,
            account_view_filter_id=account_view_filter_id,
            account_view_filter_name=account_view_filter_name,
            account_view_filter_type=account_view_filter_type,
            # Client
            client_id=client.get('Id'),
            client_name=client.get('Name'),
            office_id=office.get('id'),
            office_name=office.get('name'),
            with_market_id=with_market.get('id'),
            with_market_name=with_market.get('name'),
            agency_brand_sub_1_id=agency_brand_sub_1.get('id'),
            agency_brand_sub_1_name=agency_brand_sub_1.get('name'),
            agency_brand_id=agency_brand.get('id'),
            agency_brand_name=agency_brand.get('name'),
            sub_network_id=sub_network.get('id'),
            sub_network_name=sub_network.get('name'),
            network_id=network.get('id'),
            network_name=network.get('name'),
            holding_group_id=holding_group.get('id'),
            holding_group_name=holding_group.get('name'),
            agencies=[agency_brand.get('id'), agency_brand_sub_1.get('id')],
            # Sector
            sector=sector,
            # Account
            account_id=sc.get('Customers_Client__c'),
            account_name=account_name,
            # Contact
            contact_id=contact.get('Id'),
            contact_name=contact.get('Name'),
            contact_email=spm.get('Email'),
            contact_type=spm.get('Contact_Type__c'),
            contact_job_title=spm.get('Job_Title__c'),
            contact_division=spm.get('Contact_Division__c'),
            contact_opt_out=spm.get('Opt_Out__c'),
            # Team
            team_id=team_id,
            team_name=team_name,
            team_market_id=team_market_id,
            team_market_name=team_market_name,
            team_market_type=team_market_type,
            team_market_group=team_market_group,
            team_type=team_type,
            # Survey
            survey_id=spm.get('SurveyJsId__c'),
            survey_name=sc.get('Survey_Name__c'),
            round_id = sr.get('Id'),
            round_name = round_name,
            customer_survey_round_id=csr.get('Id'),
            customer_survey_round_name=sr.get('Name'),
            customer_survey_id=customer_survey_id,
            team_customer_survey_id=team_customer_survey_id,
            survey_start_date=survey_start_date,
            survey_end_date=survey_end_date,
            insights_start_date=cs.get('Insights_Start_Date__c'),
            # Panel Member
            survey_panel_member_id=spm.get('Id'),
            # Round
            office_round_index = office_round_index,
            with_market_round_index = with_market_round_index,
            agency_brand_sub_1_round_index = agency_brand_sub_1_round_index,
            agency_brand_round_index = agency_brand_round_index,
            sub_network_round_index = sub_network_round_index,
            network_round_index = network_round_index,
            holding_group_round_index = holding_group_round_index,
            insights_date=cs.get('Insights_Start_Date__c'),
            # Response
            responded=bool(r.get('Id')),
            response_id=r.get('Id'),
            rating=r.get('Score__c'),
            feedback=r.get('Feedback__c'),
            feedback_translated=r.get('Feedback_Translated__c'),
            response_date=r.get('Response_Date_Time__c'),
            themes=themes,
            sentiment=sentiment,
            theme_categories=theme_categories,
            # Sync Info
            _lastsynced=sync_date,
        )
        batch.append(UpdateOne({'Id': spm['Id']}, {'$set': response}, upsert=True))
        total_record_count += 1

        if writeit:
            if len(batch) > portaldbapi.MONGO_BATCH_SIZE:
                portaldb_collection.bulk_write(batch)
                batch.clear()

    if writeit:
        if batch:
            portaldb_collection.bulk_write(batch)

    print('END: PROCESSING RESPONSES')

    return total_record_count


def main(customer_survey_round_id:str|None = None, survey_panel_member_id:str|None = None, writeit:bool = False, forceall:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # turn customer_survey_round_id param (which is a comma seperated string) into a list
    if customer_survey_round_id:
        customer_survey_round_id = customer_survey_round_id.split(',')

    _, account_forest_by_id, _ = generate_survey_rounds_index_lookup(sf)

    organisation_level_lookup = generate_client_organisation_level_lookup(sf)

    survey_round_name_lookup = generate_survey_round_name_lookup(sf)

    responses = sync_responses(sf,
                               customer_survey_round_id,
                               survey_panel_member_id,
                               organisation_level_lookup,
                               survey_round_name_lookup,
                               account_forest_by_id,
                               writeit,
                               forceall)

    print(f'{responses} RESPONSES SYNCED')


def lambda_handler(event, context):
    main(writeit=True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', default=None, help='comma seperated list of CSRs to run for')
    ap.add_argument('--survey_panel_member_id', default=None, help='single SPM to run for')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    ap.add_argument("--forceall", action="store_true", help="Resync ALL responses")
    args = ap.parse_args()

    main(customer_survey_round_id=args.customer_survey_round_id,
         survey_panel_member_id=args.survey_panel_member_id,
         writeit=args.writeit,
         forceall=args.forceall)
