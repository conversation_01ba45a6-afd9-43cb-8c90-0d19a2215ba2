import json
import datetime
import argparse
from lib import sfapi
from lib.settings import settings
from lib.portaldbapi import DocumentDB
from sendgrid import SendGridAPIClient
from simple_salesforce import Salesforce
import requests
import time


SG_EVENT_TO_SF_FIELD_MAP = {
    'delivered': 'Survey_Email_Delivered__c',
    'open': 'Survey_Email_Opened__c',
    'click': 'Survey_Email_Clicked__c',
    'bounce': 'Survey_Email_Bounced__c',
}


def update_sf_survey_panel_member(sf: Salesforce, survey_panel_members_updates: dict[str, dict]) -> None:
    statuses = sfapi.bulk_update(sf, "Survey_Panel_Member__c", survey_panel_members_updates.values())
    for status in statuses:
        sfapi.detect_bulk2_errors(status)


def get_email_metadata_from_sg(sg: SendGridAPIClient, email_id: str) -> dict:
    # fetch the more granular email activity metadata from SG
    retrycount = 0

    while True:
        response = requests.get(f'{sg.host}/v3/messages/{email_id}', headers={'Authorization': f'Bearer {sg.api_key}'})

        if response.status_code == 200:
            return response.json()
        elif response.status_code > 428:
            if retrycount > 5:
                raise Exception(f"Too many retries from sendgrid API, giving up")
            
            retrycount += 1

            retrydelay = (int(response.headers.get('x-ratelimit-reset', 1)) + 1)
            print("Rate limited, delay retrying for", retrydelay)
            time.sleep(retrydelay)
        else:
            raise Exception(f"HTTP Error {response.status_code}: {response.text}")
        

def get_emails_by_template_id_from_sg(sg: SendGridAPIClient, template_id: str) -> list[str]:
    # define query date/hour range for emails
    filter_end_date: datetime = datetime.datetime.now(tz=datetime.UTC)
    filter_start_date: datetime = filter_end_date - datetime.timedelta(minutes=settings.SENDGRID_DATE_RANGE_OFFSET)
    filter_start_date = filter_start_date.isoformat()
    filter_end_date = filter_end_date.isoformat()

    # fetch all the emails from SG
    emails = sg.client.messages.get(
        query_params={
            'limit': settings.SENDGRID_API_RETURN_LIMIT,
            'query': f'template_id="{template_id}" AND (last_event_time BETWEEN TIMESTAMP "{filter_start_date}" AND TIMESTAMP "{filter_end_date}") AND (Contains(events, "bounce"))'
        }
    )

    # if the request fails, raise an exception
    if emails.status_code != 200:
        raise Exception(f"Error: {emails.status_code}")

    # parse the response
    emails_json: dict = json.loads(emails.body)

    return [message.get('msg_id') for message in emails_json.get('messages', [])]


def get_survey_email_template(db: DocumentDB) -> str:
    # get the survey templates from the config collection
    survey_template_id =  db.config.find_one({ '_id': 'email' })

    # if no survey template is found, raise an exception
    if not survey_template_id:
        raise Exception("Survey template not found")

    return survey_template_id.get('templates', {}).get('live_survey_participant')


def main(writeit):
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    sf: Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db: DocumentDB = DocumentDB()

    survey_panel_members_updates: dict = {}

    # get the SG template ID for the survey email
    survey_template_id: str = get_survey_email_template(db)

    # get all the emails sent for the survey template
    emails_by_template_id: list[str] = get_emails_by_template_id_from_sg(sg, survey_template_id)

    # iterate over the emails and determine what events have occured
    for email_id in emails_by_template_id:

        # define the initial/default response for each event
        email_event_status: dict[str, bool] = dict(
            delivered=False,
            open=False,
            click=False,
            bounce=False,
        )

        # fetch the more granular email activity metadata from SG
        email_metadata = get_email_metadata_from_sg(sg, email_id)

        # get the survey panel member ID from the custom args
        survey_panel_member_id: str = email_metadata.get('unique_args', {}).get('survey_panel_member_id')

        # if no survey panel member ID is found on the custom args, skip this email
        if not survey_panel_member_id:
            continue

        # iterate over the events on the email and update the initial/default email event status
        for event in email_metadata.get('events', []):
            event_name: str = event.get('event_name')

            # if the event name is not one we currently track, skip it
            if event_name not in SG_EVENT_TO_SF_FIELD_MAP.keys():
                continue

            # we've seen the event, so update the status
            email_event_status[event_name] = True

        # roll-up and group the events by servey panel member id and map to SF fields
        survey_panel_members_updates[survey_panel_member_id] = {
            SG_EVENT_TO_SF_FIELD_MAP[event]: value
            for event, value in email_event_status.items()
        }

        # attach the survey panel member ID to the update
        survey_panel_members_updates[survey_panel_member_id]['id'] = survey_panel_member_id

    # update each SF survey panel member with their email activity
    if survey_panel_members_updates and writeit:
        update_sf_survey_panel_member(sf, survey_panel_members_updates)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
