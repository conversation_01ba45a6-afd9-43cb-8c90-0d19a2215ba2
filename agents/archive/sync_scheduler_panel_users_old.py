""" Send panel member survey emails for live survey rounds

DATA:
    FROM: SF (Customer_Survey_Round__c, Survey_Panel_Member__c)
    TO: Sendgrid
        Salesforce (Customer_Survey_Round__c, Survey_Panel_Member__c)

"""
import argparse
import datetime
import re
import json
from simple_salesforce import format_soql
from lib.settings import settings
from lib import emailapi, sfapi, portaldbapi, sqsapi, locale as liblocale, sfimport


SF_BATCH_SIZE = 1000
SF_RESOURCE_URL = 'file.force.com'
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets


def nullempty(value):
    return value if value else None


def boolify(value):
    return value in {'true', 'True', 'TRUE', '1', 1, True}


def determine_vertical(account_record_types:dict[str, str], record_type_id:str):
    vertical:str = 'adv'
    if record_type_id == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING].Id:
        vertical = 'adv'
    elif record_type_id == account_record_types[sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING].Id:
        vertical = 'mfv'
    return vertical


def get_contact_from_spm(spm):
    contact = dict(
        Id=spm['Contact__r.Id'],
        Email=spm['Contact__r.Email'],
        Name=spm['Contact__r.Name'],
        FirstName=spm['Contact__r.FirstName'],
        LastName=spm['Contact__r.LastName'],
        Language__c=spm['Contact__r.Language__c'],
    )
    return contact


def get_customer_survey_from_spm(spm):
    customer_survey = dict(
        External_Communication_Email_Address__c=spm['Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c'],
        Live_Survey_Start_Date__c=spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c'],
        Live_Survey_End_Date__c=spm['Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c'],
    )
    return customer_survey


def get_signatory_from_spm(spm):
    signatory = dict(
        Id=spm['Survey_Client__r.Signatory__r.Id'],
        Name=spm['Survey_Client__r.Signatory__r.Name'],
        Email=spm['Survey_Client__r.Signatory__r.Email'],
        Title=spm['Survey_Client__r.Signatory__r.Title'],
        Banner__c=spm['Survey_Client__r.Signatory__r.Banner__c'],
        Signature__c=spm['Survey_Client__r.Signatory__r.Signature__c'],
    )
    return signatory


def get_panel_members(sf, csr_id):
    print(' >> FETCHING: LIVE SURVEY PARTICIPANTS...')
    soql = """
    SELECT Id,
            Name,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.Email,
            Contact__r.Name,
            Contact__r.FirstName,
            Contact__r.LastName,
            Contact__r.Language__c,
            Survey_Email_Triggered__c,
            Survey_Email_Reminder_1__c,
            Survey_Email_Reminder_2__c,
            Survey_Email_Reminder_3__c,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Signatory__r.Id,
            Survey_Client__r.Signatory__r.Name,
            Survey_Client__r.Signatory__r.Email,
            Survey_Client__r.Signatory__r.Title,
            Survey_Client__r.Signatory__r.Account.Name,
            Survey_Client__r.Signatory__r.Banner__c,
            Survey_Client__r.Signatory__r.Signature__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
            Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
    FROM  Survey_Panel_Member__c
    WHERE SurveyJsId__c != NULL
    AND   Has_Responded__c = False
    AND   Contact__r.HasOptedOutOfEmail = False
    AND   Contact__r.Disable_External_Communication__c = False
    AND   Opt_Out__c = False
    AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
    AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
    AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
    AND   ((Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c = TODAY) OR 
           (Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c = TODAY) OR
           (Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c = TODAY) OR
           (Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c = TODAY))
    AND   (Survey_Email_Triggered__c = False OR Survey_Email_Reminder_1__c = False OR Survey_Email_Reminder_2__c = False OR Survey_Email_Reminder_3__c = False)
    """
    first_email_panel_members = []
    reminder_1_email_panel_members = []
    reminder_2_email_panel_members = []
    reminder_3_email_panel_members = []
    contact_profile_count = {}
    contact_survey_names:dict[str, set] = {}
    query = format_soql(soql, csr_id=csr_id)
    for spm in sfapi.bulk_query(sf, query):
        if not spm:
            continue

        contact_id = spm.get('Contact__r.Id')

        # determine agency brand id based on org-level (and handle accounts that do not support office yet)
        agency_brand_id = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id')
        agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name')
        if spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c') == "Level 6": # "Level 6 == With Market"
            agency_brand_id = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id')
            agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name')

        if contact_id not in contact_profile_count:
            contact_profile_count[contact_id] = set()

        contact_profile_count[contact_id].add(agency_brand_id)

        # collect survey names for each contact
        survey_name = spm.get('Survey_Client__r.Survey_Name__c')
        if not survey_name:
            survey_name = agency_brand_name
        contact_survey_names_key = f'{contact_id}-{spm.get("SurveyJsId__c")}'
        contact_survey_names.setdefault(contact_survey_names_key, set()).add(survey_name)

        # split panel members into 1st email and 1st/2nd/3rd reminder email groups
        today_str = datetime.date.today().strftime('%Y-%m-%d')
        first_email_sent = boolify(spm.get('Survey_Email_Triggered__c'))
        first_email_trigger_date = spm['Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c']
        reminder_1_sent = boolify(spm.get('Survey_Email_Reminder_1__c'))
        reminder_1_trigger_date = spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c']
        reminder_2_sent = boolify(spm.get('Survey_Email_Reminder_2__c'))
        reminder_2_trigger_date = spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c']
        reminder_3_sent = boolify(spm.get('Survey_Email_Reminder_3__c'))
        reminder_3_trigger_date = spm['Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c']

        if(first_email_sent == False and first_email_trigger_date == today_str):
            first_email_panel_members.append(spm)
        if(reminder_1_sent == False and reminder_1_trigger_date == today_str):
            reminder_1_email_panel_members.append(spm)
        if(reminder_2_sent == False and reminder_2_trigger_date == today_str):
            reminder_2_email_panel_members.append(spm)
        if(reminder_3_sent == False and reminder_3_trigger_date == today_str):
            reminder_3_email_panel_members.append(spm)

    print(f' >> FETCHED: LIVE SURVEY PARTICIPANTS... First Email: {len(first_email_panel_members)}, Reminder 1: {len(reminder_1_email_panel_members)}, Reminder 2: {len(reminder_2_email_panel_members)}, Reminder 3: {len(reminder_3_email_panel_members)}')

    result = dict(
        first_email_panel_members=first_email_panel_members, 
        reminder_1_email_panel_members=reminder_1_email_panel_members, 
        reminder_2_email_panel_members=reminder_2_email_panel_members, 
        reminder_3_email_panel_members=reminder_3_email_panel_members, 
        contact_profile_count=contact_profile_count,
        contact_survey_names=contact_survey_names,
    )
    return result
    

def process_participants(sf, email_service, csr_id, panel_members, contact_profile_count, contact_survey_names, reminder_number, emails_scheduled, account_record_types:dict[str, str]):
    batch_spm = []
    has_been_sent = set()
    email_template_name = 'live_survey_participant'
    spm_property_name = 'Survey_Email_Triggered__c'
    csr_property_name = 'Email_Live_Survey_First_Request__c'
    if reminder_number == 1:
        email_template_name = 'live_survey_participant_reminder_1'
        spm_property_name = 'Survey_Email_Reminder_1__c'
        csr_property_name = 'Email_Live_Survey_Second_Request__c'
    elif reminder_number == 2:
        email_template_name = 'live_survey_participant_reminder_2'
        spm_property_name = 'Survey_Email_Reminder_2__c'
        csr_property_name = 'Email_Live_Survey_Third_Request__c'
    elif reminder_number == 3:
        email_template_name = 'live_survey_participant_reminder_3'
        spm_property_name = 'Survey_Email_Reminder_3__c'
        csr_property_name = 'Email_Live_Survey_Fourth_Request__c'

    for spm in panel_members:
        contact = get_contact_from_spm(spm)
        signatory = get_signatory_from_spm(spm)
        customer_survey = get_customer_survey_from_spm(spm)
        holding_group_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name', '')
        vertical = determine_vertical(account_record_types, spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId'))

        external_communication_email = customer_survey.get('External_Communication_Email_Address__c')
        banner_attachment = None
        signature_attachment = None
        banner = signatory.get('Banner__c')
        signature = signatory.get('Signature__c')
        has_been_sent_key = f'{contact.get("Id")}-{spm.get('SurveyJsId__c')}'
        is_multi_profile = len(contact_profile_count.get(contact.get('Id'), [])) > 1

        # determine agency brand name based on org-level (and handle accounts that do not support office yet)
        agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name')
        if spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c') == "Level 6": # "Level 6 == With Market"
            agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name')

        if is_multi_profile:
            agency_brand_name = holding_group_name

        # format survey names and fallback to agency brand name if there's none
        # FIXME: would prefer not to re-use `agency_brand_name` here, but requires a mass template update.
        survey_name = ', '.join(contact_survey_names.get(has_been_sent_key, []))
        if survey_name:
            agency_brand_name = survey_name

        sent_date_property = f'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.{csr_property_name}'
        last_email_sent_date = nullempty(spm.get(sent_date_property))
        if last_email_sent_date:
            last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

        if signatory and banner and SF_RESOURCE_URL in banner:
            cid = f'cid:{signatory.get("Id")}-banner'
            banner = re.sub(r'src="[^"]+"', f'src="{cid}"', banner)
            banner_attachment = {
                'disposition': 'inline',
                'key': f'{signatory.get("Id")}-banner.png',
                'file_type': 'image/png',
                'content_id': f'{signatory.get("Id")}-banner',
            }

        if signatory and signature and SF_RESOURCE_URL in signature:
            cid = f'cid:{signatory.get("Id")}'
            signature = re.sub(r'src="[^"]+"', f'src="{cid}"', signature)
            signature_attachment = {
                'disposition': 'inline',
                'key': f'{signatory.get("Id")}.png',
                'file_type': 'image/png',
                'content_id': signatory.get('Id'),
            }

        # FIXME: this is a fallback as the SF work never got completed in time
        if signatory and not signature:
            signature_name = '' if signatory.get('Name') == None else signatory.get('Name', '')
            signature_title = '' if signatory.get('Title') == None else signatory.get('Title', '')
            signature_account = spm.get('Survey_Client__r.Signatory__r.Account.Name') if spm.get('Survey_Client__r.Signatory__r.Account.Name') else ''
            signature_email = '' if signatory.get('Email') == None else signatory.get('Email', '')
            signature = f'<p><strong>{signature_name}</strong></p><p>{signature_title}</p><p>{signature_account}</p><p>{signature_email}</p>'

        sender = external_communication_email if external_communication_email else DEFAULT_SENDER
        recipient = contact.get('Email')
        locale = liblocale.get_sf_language_to_locale_formatted(contact.get('Language__c'))
        template_data = {
            'FirstName': contact.get('FirstName'),
            'LastName': contact.get('LastName'),
            'SurveyDate': customer_survey.get('Live_Survey_Start_Date__c'),
            'LiveSurveyEndDate': customer_survey.get('Live_Survey_End_Date__c'),
            'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
            'Banner': banner,
            'Signatory': signature,
            'AgencyBrand': agency_brand_name,
            'HoldingGroup': holding_group_name,
            'OptOutLink': f'{settings.survey_ui_link}/optout/{contact.get('Id')}',
            'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{spm.get('SurveyJsId__c')}',
        }
        template_metadata = {
            'contact_id': contact.get('Id'),
            'survey_panel_member_id': spm.get('Id'),
            'survey_client_id': spm.get('Survey_Client__r.Id'),
            'customer_survey_id': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
            'customer_survey_round_id': csr_id,
            # this property is used to filter sendgrid email events, see: api/agents/src/handlers/sendgrid/event_handler.py
            # suffix appended in non-prod environments to prevent tracking of test emails by prod
            'tracked_template_name': email_template_name if not settings.TRACKED_TEMPLATE_SUFFIX else f'{email_template_name}_{settings.TRACKED_TEMPLATE_SUFFIX}',
        }
        try:
            if has_been_sent_key not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                email_service.send(sender, 
                                   recipient, 
                                   email_template_name, 
                                   template_data, 
                                   template_metadata, 
                                   language=locale, 
                                   vertical=vertical, 
                                   banner_attachment=banner_attachment,
                                   signature_attachment=signature_attachment)
                emails_scheduled.append(f'{email_template_name}-{recipient}')
                print(f'   :: Email Scheduled: {email_template_name} ({locale}) to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            else:
                print(f'   :: Email Skipped: {email_template_name} already sent to {recipient} today for Survey_Panel_Member__c {spm.get("Id")}')
            has_been_sent.add(has_been_sent_key)
            batch_spm.append({
                'Id': spm.get('Id'),
                spm_property_name: True
            })
        except Exception as ex:
            print(f'   :: ERROR: {ex}')

        if len(batch_spm) > SF_BATCH_SIZE:
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
            batch_spm.clear()

    if len(batch_spm):
        sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
        batch_spm.clear()

    sf.Customer_Survey_Round__c.update(csr_id, {csr_property_name: datetime.date.today().strftime('%Y-%m-%d')})


def main(round_id = None):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    print('START: EMAIL SCHEDULER PANEL USERS')

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sqs: sqsapi.SQS = sqsapi.SQS()
    es: emailapi.EmailService = emailapi.EmailService(db, sqs)

    emails_scheduled = []
    account_record_types = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    soql = """
    SELECT Id,
           Name,
           Disable_External_Communication__c,
           Email_Round_Scheduled__c,
           Stage__c,
           Account_Updates_Start_Date__c,
           Account_Updates_End_Date__c,
           Panel_Updates_Start_Date__c,
           Panel_Updates_End_Date__c,
           Live_Survey_Start_Date__c,
           Live_Survey_First_Request__c,
           Live_Survey_Second_Request__c,
           Live_Survey_Third_Request__c,
           Live_Survey_Fourth_Request__c,
           Live_Survey_End_Date__c,
           Email_Live_Survey_First_Request__c,
           Email_Live_Survey_Second_Request__c,
           Email_Live_Survey_Third_Request__c,
           Email_Live_Survey_Fourth_Request__c,
           Account__r.Name,
           Account__r.Consultant__r.Email,
           Survey_Round__r.Name,
           Survey_Round__r.Disable_External_Communication__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """
    if round_id:
        soql = soql + " AND Id = {round_id}"
    query = format_soql(soql, round_id=round_id)
    for csr in sfapi.bulk_query(sf, query):
        if not csr:
            continue

        csr_id = csr.get('Id')
        cs_name = csr.get('Name')
        cs_comms_disabled = boolify(csr.get('Disable_External_Communication__c'))
        sr_comms_disabled = boolify(csr.get('Survey_Round__r.Disable_External_Communication__c'))

        print(f'# START: Customer Survey Round: {cs_name} ({csr_id})')

        # if customer survey round has comms disabled, skip it
        if sr_comms_disabled or cs_comms_disabled:
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) has comms disabled')
            continue

        # if survey emails have run today for this customer survey round, skip it
        if (csr.get('Email_Live_Survey_First_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Second_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Third_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Fourth_Request__c') == datetime.date.today().strftime('%Y-%m-%d')):
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) emails already sent today')
            continue

        grouped_panel_members = get_panel_members(sf, csr_id)
        contact_profile_count = grouped_panel_members.get('contact_profile_count')
        contact_survey_names = grouped_panel_members.get('contact_survey_names')
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT...')
        process_participants(sf, es, csr_id, grouped_panel_members['first_email_panel_members'], contact_profile_count, contact_survey_names, 0, emails_scheduled, account_record_types)
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 1...')
        process_participants(sf, es, csr_id, grouped_panel_members['reminder_1_email_panel_members'], contact_profile_count, contact_survey_names, 1, emails_scheduled, account_record_types)
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 2...')
        process_participants(sf, es, csr_id, grouped_panel_members['reminder_2_email_panel_members'], contact_profile_count, contact_survey_names, 2, emails_scheduled, account_record_types)
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 3...')
        process_participants(sf, es, csr_id, grouped_panel_members['reminder_3_email_panel_members'], contact_profile_count, contact_survey_names, 3, emails_scheduled, account_record_types)

        print(f'# END: Customer Survey Round: {cs_name} ({csr_id})')

    print(f'END: EMAIL SCHEDULER PANEL USERS: {len(emails_scheduled)} Emails Scheduled')

    return emails_scheduled


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--round_id', default=None, help='single survey round to run for')
    args = ap.parse_args()

    main(round_id=args.round_id)
