import pymongo.collection
import pymongo
from lib import sfapi, portaldbapi
from simple_salesforce import Salesforce
import datetime
from lib.settings import settings


TRANSLATION_BATCH_SIZE = 100

DEEPL_LANGUAGES = {
    "French",
    "Spanish",
    "German",
    "Italian",
    "Dutch",
    "Polish",
    "Russian",
    "Chinese",
    "Portuguese"
}


def translate_deepl_batch(batch: list[dict]) -> None:
    pass


def translate_google_batch(batch: list[dict]) -> None:
    pass


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    soql = """
    SELECT  Id,
            Feedback__c
            Contact__r.Language__c
    from Survey_Response__c
    where Contact__r.Language__c != "English" and Feedback_Translated__c = null
    """

    # get all the stuff we need to translate
    batch = []
    for to_translate in sf.query_all_iter(soql):
        batch.append(to_translate)

        if len(batch) > TRANSLATION_BATCH_SIZE:
            translate_batch(batch)
            batch.clear()


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    main()
