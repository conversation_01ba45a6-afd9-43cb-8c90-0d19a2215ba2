from lib import sfapi
import datetime
import argparse
from lib.settings import settings

def main(survey_round_id: str):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # get the survey round
    survey_round: sfapi.SurveyRound | None = sfapi.get_by_id(sf, sfapi.SurveyRound, survey_round_id)
    if not survey_round:
        raise ValueError(f"Survey round {survey_round_id} not found")

    # get the dates of the survey round
    survey_round_start_date = survey_round.RoundDate
    survey_round_end_date = survey_round.RoundEndDate
    survey_round_active_duration_days = survey_round.SurveyActiveDurationDays

    # the date the last survey should start on, allowing time for it to complete before end of the survey round
    last_survey_start_date = survey_round_end_date - datetime.timedelta(days=survey_round_active_duration_days)
    if last_survey_start_date < survey_round_start_date:
        raise ValueError("Last survey start date is before survey round start date")

    # figure out how many working days we have available in the period (assume mon-fri are working days)
    d = survey_round_start_date
    working_days = 0
    while d < last_survey_start_date:
        if d.weekday() != 5 and d.weekday() != 6:
            working_days += 1

        d += datetime.timedelta(days=1)

    # ensure we have some working days to schedule on!
    if working_days == 0:
        raise ValueError("No days available to schedule on!")

    # get all the customer surveys we need to scheudule for this survey round
    customer_survey_rounds = list(sfapi.CustomerSurveyRound.get_by_survey_round_id(sf, survey_round_id))

    # ensure we have something to do!
    if len(customer_survey_rounds) == 0:
        print(f"No customer survey rounds found for survey round {survey_round_id}")
        return

    # figure out how many surveys we can schedule per day, rounding up
    surveys_per_day = int((len(customer_survey_rounds) / working_days) + 1)

    # now iterate over the customer surveys, and set their start dates
    d = survey_round_start_date
    surveys_scheduled_this_day = 0
    customer_survey_rounds_updates = []
    while len(customer_survey_rounds) > 0:
        # if we've scheduled enough surveys for today, move on to the next day
        if surveys_scheduled_this_day > surveys_per_day:
            surveys_scheduled_this_day = 0
            d += datetime.timedelta(days=1)
            continue

        # if its the weekend, move on to the next day
        if d.weekday() == 5 or d.weekday() == 6:
            surveys_scheduled_this_day = 0
            d += datetime.timedelta(days=1)
            continue

        # schedule this survey!
        customer_survey_round = customer_survey_rounds.pop(0)
        print(f"Scheduling survey {customer_survey_round.Id} for {d} with duration {survey_round_active_duration_days}")
        customer_survey_rounds_updates.append({
            "Id": customer_survey_round.Id,
            "Round_Date__c": d,
            "Survey_Round_Active_Duration_Days__c": survey_round_active_duration_days,
            "Stage__c": "Start Round",
        })
        surveys_scheduled_this_day += 1

    # bulk update salesforce
    statuses = sfapi.bulk_update(sf, "Customer_Survey_Round__c", customer_survey_rounds_updates)
    for status in statuses:
        sfapi.detect_bulk2_errors(status)


def lambda_handler(event, _):
    if event:
        for record in event["Records"]:
            survey_round_id = record['messageAttributes']['SurveyRoundId']['stringValue']
            main(survey_round_id)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("survey_round_id", help="The ID of the survey round to schedule")
    args = ap.parse_args()

    main(args.survey_round_id)
