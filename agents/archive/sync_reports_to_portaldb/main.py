import logging
import datetime
from typing import List, Dict
from lib.settings import settings
from lib.portaldbapi import DocumentDB
from lib import sfapi
from agents.sync_reports_to_portaldb.reports import Report, ReportDefinition


logger = logging.getLogger(__name__)


def get_exposed_reports(db: DocumentDB) -> List[str]:
    exposed_reports = db.config.find_one(
        {'_id': 'salesforce_reports'},
        {'_id': 0, 'reports': 1}
    )
    if exposed_reports is None:
        return []
    return exposed_reports.get('reports', [])

def get_report_definitions(
        salesforce: sfapi.Salesforce,
        exposed_reports: List[str]) -> List[ReportDefinition]:

    parsed_report_definitions = []

    for report in exposed_reports:
        report_id = report['report_id']
        report_accounts = report.get('accounts', [])

        # if we don't have any assigned accounts for this report, then just skip it - better safe than sorry!
        if not report_accounts:
            logger.warning(f'No accounts assigned to report {report_id}. Skipping.')
            continue

        report_definition = salesforce.restful(f'analytics/reports/{report_id}/describe')
        parsed_report_definitions.append({**report_definition, **{'accounts': report_accounts}})

    return [
        ReportDefinition(**report['reportMetadata'], accounts=report['accounts'])
        for report in parsed_report_definitions
    ]

def get_report(salesforce: sfapi.Salesforce, report_id: str) -> Dict:
    report = salesforce.restful(f'analytics/reports/{report_id}')
    parsed_report = Report.parse(report)

    flattened_rows = []
    for row in parsed_report.rows:
        cells = row.flatten_cells()
        flattened_rows.append(cells)

    parsed_report_dict = parsed_report.dict()

    return {
        'id': parsed_report_dict['id'],
        'name': parsed_report_dict['name'],
        'description': parsed_report_dict['description'],
        'columns': parsed_report_dict['columns'],
        'rows': flattened_rows,
    }

def cache_reports(db: DocumentDB, reports: List[Dict]):
    created_reports = db.reports.insert_many(reports).inserted_ids
    logger.info(f'Cached {len(created_reports)} reports to `sfreports`')

    db.reports.delete_many({'_id': {'$nin': created_reports}})

    return created_reports

def main():
    # reports that will be cached to the portal DB
    cached_reports = []

    # create a DocumentDB instance
    db = DocumentDB()

    # create Salesforce instance
    salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    # get list of reports that have been flagged as exposable to the client area
    exposed_reports = get_exposed_reports(db)

    # if we have no reports, then let's just bail
    if not exposed_reports:
        logger.info("No reports found in `config.salesforce_reports` to cache")
        return

    # get report definitions from Salesforce
    report_definitions = get_report_definitions(salesforce, exposed_reports)

    # get the full report for each definition
    for report_definition in report_definitions:
        try:
            report = get_report(salesforce, report_definition.id)
            cache_report = {**report_definition.dict(), 'report': {**report}, 'last_updated': datetime.datetime.now()}
            cached_reports.append(cache_report)
        except Exception as e:
            logger.error(f'Failed to get report {report_definition.id} from Salesforce. Skipped.')
            continue

    # just bail if we have no reports to cache
    if not cached_reports:
        logger.info("No reports found in Salesforce to cache")
        return

    # cache the reports to the portal DB
    cache_result = cache_reports(db, cached_reports)

    logger.info(f'Cache result: {cache_result}')


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    main()
