from __future__ import annotations
import uuid
from typing import List, Optional, Union
from pydantic import BaseModel


class RawSalesforceReportResponse(BaseModel):
    factMap: dict
    reportMetadata: dict
    reportExtendedMetadata: dict

    @classmethod
    def parse(cls, raw_salesforce_report: dict) -> RawSalesforceReportResponse:
        return cls(
            factMap=raw_salesforce_report['factMap'],
            reportMetadata=raw_salesforce_report['reportMetadata'],
            reportExtendedMetadata=raw_salesforce_report['reportExtendedMetadata'],
        )


class UIReport(BaseModel):
    id: str
    name: str
    description: Optional[Union[None, str]] = ''
    columns: List[ReportColumn]
    rows: List


class Report(BaseModel):
    id: str
    name: str
    description: Optional[Union[None, str]] = ''
    columns: List[ReportColumn]
    rows: List[ReportRow]

    @classmethod
    def __decode_fact_map(cls, fact_map: dict):
        return list(fact_map.keys())

    @classmethod
    def parse(cls, raw_salesforce_report: dict) -> Report:
        parsed_report = RawSalesforceReportResponse.parse(raw_salesforce_report)
        report_metadata = parsed_report.reportMetadata
        report_extended_metadata = parsed_report.reportExtendedMetadata
        report_id = report_metadata['id']
        report_name = report_metadata['name']
        report_description = report_metadata['description']
        columns = []
        rows = []
        column_field_lookup = []

        for key, value in report_extended_metadata['detailColumnInfo'].items():
            columns.append(ReportColumn(
                header=value['label'],
                field=value['entityColumnName'],
                data_type=value['dataType'],
            ))
            # using `label` and not `entityColumnName` because `entityColumnName`, when it's a derived field, will not be unique
            column_field_lookup.append(value['label'])

        decoded_fact_map = cls.__decode_fact_map(parsed_report.factMap)

        for format_type in decoded_fact_map:
            for row in parsed_report.factMap.get(format_type, {}).get('rows', []):
                cells = []
                for idx, cell in enumerate(row['dataCells']):
                    cells.append(ReportRowCell(
                        label=cell['label'],
                        field=column_field_lookup[idx],
                        value=cell['value'],
                        uid=str(uuid.uuid4())
                    ))

                rows.append(ReportRow(cells=cells))

        return cls(
            id=report_id,
            name=report_name,
            description=report_description,
            columns=columns,
            rows=rows,
        )


class ReportColumn(BaseModel):
    header: str
    field: str
    data_type: str


class ReportRow(BaseModel):
    cells: List[ReportRowCell]

    def flatten_cells(self):
        cells = {}
        for cell in self.cells:
            cells['id'] = cell.uid
            cells[cell.field] = cell.label
        return cells


class ReportRowCell(BaseModel):
    uid: str
    label: str
    field: str
    value: str


class ReportDefinition(BaseModel):
    id: str
    name: str
    accounts: List[str]