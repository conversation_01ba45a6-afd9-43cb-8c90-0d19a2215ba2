def exec_level(
        accounts: list = [], 
        key_acounts: list = [], 
        key_markets: list = [],
        excluded_accounts: list = [],
        excluded_markets: list = []):
    result = set()
    for account in accounts:
        if account.Id in key_acounts and account.Id not in excluded_accounts and account.MarketValue in key_markets and account.MarketValue not in excluded_markets:
            result.add(account.Id)
    return result

def global_account(
        accounts: list = [], 
        key_acounts: list = [], 
        key_markets: list = [],
        excluded_accounts: list = [],
        excluded_markets: list = []):
    result = set()
    for account in accounts:
        if account.Id in key_acounts and account.Id not in excluded_accounts:
            result.add(account.Id)
    return result

def global_market(
        accounts: list = [], 
        key_acounts: list = [], 
        key_markets: list = [],
        excluded_accounts: list = [],
        excluded_markets: list = []):
    result = set()
    #print(key_markets)
    for a in accounts:
        account = a['account']
        client = a['client']
        print(account)
        print(client)
        print(key_markets)
        if client.MarketValue in key_markets and client.MarketValue not in excluded_markets:
            result.add(account.Id)
    return result

def local_account(
        accounts: list = [], 
        key_acounts: list = [], 
        key_markets: list = [],
        excluded_accounts: list = [],
        excluded_markets: list = []):
    result = set()
    for account in accounts:
        if account.Id in key_acounts and account.Id not in excluded_accounts:
            result.add(account.Id)
    return result

def local_agency(
        accounts: list = [], 
        key_acounts: list = [], 
        key_markets: list = [],
        excluded_accounts: list = [],
        excluded_markets: list = []):
    result = set()
    for account in accounts:
        if account.Id in key_acounts and account.Id not in excluded_accounts and account.MarketValue.Id in key_markets and account.MarketValue not in excluded_markets:
            result.add(account.Id)
    return result