import datetime
import argparse
from lib import sfapi
from lib.settings import settings
import agents.sync_permissions_to_portaldb.reportingroles as rr
from lib.portaldbapi import DocumentDB


SFAPI_OBJECT_NAMES = {
    'CustomerClientRelationship': {},
    'ContactKeyAccount': {},
    'ContactKeyMarket': {},
    'Account': {},
    'Contact': { 'query_suffix': "WHERE RecordType.Name = 'Customer\\'s Client Account'" },
}
REPORTING_ROLE_FILTERS = {
    'Exec Level View': rr.exec_level,
    'Global Account View': rr.global_account,
    'Global Market View': rr.global_market,
    'Local Account View': rr.local_account,
    'Local Agency View': rr.local_agency
}


def find_contacts(sfcache, sfobjcache, contact_id):
    contact = []
    if contact_id is not None:
        contact.append(sfcache[contact_id])
        return contact
    else:
        return sfobjcache[sfapi.Contact]


def find_associated_accounts(sfcache, sfobjcache, account):
    accounts = []

    def get_ccr_accounts(account):
        child_account_count = account.NoOfChildAccounts

        if child_account_count == 0:
            for ccr in find_objects_in_cache(sfobjcache, sfapi.CustomerClientRelationship, 'CustomerAccountId', account.Id):
                client = sfcache[ccr.CustomerAccountId]
                client_account = sfcache[ccr.CustomersClientAccountId]
                accounts.append({'account': client_account, 'client': client})
        else:
            for childAccount in find_objects_in_cache(sfobjcache, sfapi.Account, 'ParentId', account.Id):
                get_ccr_accounts(childAccount)

    get_ccr_accounts(account)

    return accounts


def write_contacts_db(records: list = []):
    print('START: Writing contacts to PortalDB...')
    db = DocumentDB()
    db.contacts.drop()
    db.contacts.insert_many(records)
    print('End: Writing contacts to PortalDB...')


def find_cls_in_cache(sfobjcache, cls):
    for i in sfobjcache.get(cls, {}):
        yield i


def find_objects_in_cache(sfobjcache, cls, fieldname, fieldvalue):
    for i in sfobjcache.get(cls, {}):
        if getattr(i, fieldname) == fieldvalue:
            yield i


def main(contact_id = None):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    records = []
    sfcache = {}
    sfobjcache = {}

    print('START: Fetching data from Salesforce...')
    for objname in SFAPI_OBJECT_NAMES:
        sfobj = getattr(sfapi, objname)
        for item in sfapi.get_all(sf, sfobj):
            sfobjcache.setdefault(getattr(sfapi, objname), []).append(item)
            sfcache[item.Id] = item
    print('END: Fetching data from Salesforce...')

    for sfobject in sfapi.get_all(sf, sfapi_object, bulk=True, query_suffix=query_suffix):

    for contact in find_contacts(sfcache, sfobjcache, contact_id):
        key_accounts = []
        key_markets = []
        excluded_accounts = []
        excluded_markets = []
        reporting_role_associated_accounts = []

        # get the reporting role assigned to the contact
        reporting_role = contact.ReportingRole

        # get direct linked client/account on contact
        account = sfcache[contact.AccountId]

        # if no reporting role or client has been assigned to the contact, skip them
        if reporting_role is None or account is None:
            continue

        # get key accounts (included and excluded)
        for key_account in find_objects_in_cache(sfobjcache, sfapi.ContactKeyAccount, 'ContactId', contact.Id):
            if key_account.Excluded:
                excluded_accounts.append(key_account.AccountId)
            else:
                key_accounts.append(key_account.AccountId)

        # get key markets (included and excluded)
        for key_market in find_objects_in_cache(sfobjcache, sfapi.ContactKeyMarket, 'ContactId', contact.Id):
            if key_market.Excluded:
                excluded_markets.append(key_market.Market)
            else:
                key_markets.append(key_market.Market)

        # get all accounts this contact would be able to access (traverse down the SF hierachy)
        #account_hierarchy = find_associated_accounts(sfcache, sfobjcache, account)

        # filter the accounts based on the reporting role
        #reporting_role_associated_accounts = REPORTING_ROLE_FILTERS[reporting_role](associated_accounts, key_accounts, key_markets, excluded_accounts, excluded_markets)

        records.append(dict(
            # Contact
            Id=contact.Id,
            email=contact.Email,
            name=contact.Name,
            contact_type=contact.ContactType,
            seniority=contact.Seniority,
            title=contact.Title,
            # Roles
            panel_manager=contact.PanelManager,
            account_manager=contact.AccountManager,
            signatory=contact.Signatory,
            consultant=contact.Consultant,
            # Account (linked)
            account_id=account.Id,
            account_name=account.Name,
            # Reporting
            reporting_role=reporting_role,
            client_hierarchy=[],
            account_hierarchy=[],
            key_accounts=key_accounts,
            key_markets=key_markets,
            excluded_accounts=excluded_accounts,
            excluded_markets=excluded_markets,
            #account_hierarchy=[account.Id for account in associated_accounts],
            #key_accounts=list(reporting_role_associated_accounts),
            _lastsynced=datetime.datetime.now(),
        ))

    if records:
        write_contacts_db(records)


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('contact_id', help='single contact to run for (use ALL for all contacts)')
    args = ap.parse_args()

    if args.contact_id == "ALL":
        main()
    else:
        main(contact_id=args.contact_id)
