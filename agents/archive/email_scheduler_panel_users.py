import argparse
import datetime
import re
import json
from simple_salesforce import format_soql
from lib.settings import settings
from lib import emailapi, sfapi, portaldbapi, sqsapi, locale as liblocale


SF_BATCH_SIZE = 1000
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets
SIGNATURE_SF_RESOURCE_URL = 'file.force.com'

def main(round_id = None):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    print('START: EMAIL SCHEDULER PANEL USERS')

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sqs: sqsapi.SQS = sqsapi.SQS()
    es: emailapi.EmailService = emailapi.EmailService(db, sqs)

    emails_scheduled = []

    soql = """
    SELECT Id,
           Name,
           Disable_External_Communication__c,
           Email_Round_Scheduled__c,
           Stage__c,
           Account_Updates_Start_Date__c,
           Account_Updates_End_Date__c,
           Panel_Updates_Start_Date__c,
           Panel_Updates_End_Date__c,
           Live_Survey_Start_Date__c,
           Live_Survey_First_Request__c,
           Live_Survey_Second_Request__c,
           Live_Survey_Third_Request__c,
           Live_Survey_Fourth_Request__c,
           Live_Survey_End_Date__c,
           Email_Live_Survey_First_Request__c,
           Email_Live_Survey_Second_Request__c,
           Email_Live_Survey_Third_Request__c,
           Email_Live_Survey_Fourth_Request__c,
           Account__r.Name,
           Account__r.Consultant__r.Email,
           Survey_Round__r.Name,
           Survey_Round__r.Disable_External_Communication__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """
    if round_id:
        soql = soql + " AND Id = {round_id}"
    query = format_soql(soql, round_id=round_id)
    for csr in sf.query_all_iter(query):
        if not csr:
            continue

        sr = csr.get('Survey_Round__r', {}) or {}

        csr_id = csr.get('Id')
        cs_name = csr.get('Name')
        cs_comms_disabled = csr.get('Disable_External_Communication__c')
        sr_comms_disabled = sr.get('Disable_External_Communication__c')

        print(f'# START: Customer Survey Round: {cs_name} ({csr_id})')

        # if customer survey round has comms disabled, skip it
        if sr_comms_disabled or cs_comms_disabled:
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) has comms disabled')
            continue

        # if survey emails have run today for this customer survey round, skip it
        if (csr.get('Email_Live_Survey_First_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Second_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Third_Request__c') == datetime.date.today().strftime('%Y-%m-%d') and
            csr.get('Email_Live_Survey_Fourth_Request__c') == datetime.date.today().strftime('%Y-%m-%d')):
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) emails already sent today')
            continue

        # ==============================================================================================================
        # Email: Take the survey #1
        # Recipients: Panel Member
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Contact__r.Language__c,
               Survey_Client__r.Signatory__r.Id,
               Survey_Client__r.Signatory__r.Name,
               Survey_Client__r.Signatory__r.Email,
               Survey_Client__r.Signatory__r.Title,
               Survey_Client__r.Signatory__r.Account.Name,
               Survey_Client__r.Signatory__r.Signature__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
        FROM  Survey_Panel_Member__c
        WHERE SurveyJsId__c != NULL
        AND   Survey_Email_Triggered__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Contact__r.Disable_External_Communication__c = False
        AND   Opt_Out__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_spm = []
        has_been_sent = set()
        panel_members = []
        contact_profile_count = {}
        query = format_soql(soql, csr_id=csr_id)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}

            panel_members.append(spm)
            contact_id = c.get('Id')
            agency_brand_id = ba.get('Id')

            if contact_id not in contact_profile_count:
                contact_profile_count[contact_id] = set()

            contact_profile_count[contact_id].add(agency_brand_id)

        for spm in panel_members:
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            s = sc.get('Signatory__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            csri = cs.get('Customer_Survey_Round__r', {}) or {}
            sa = s.get('Account', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}
            a = csri.get('Account__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            attachment = None
            agency_brand_name = ba.get('Name')
            signature = s.get('Signature__c')
            has_been_sent_key = f'{c.get("Id")}-{spm.get('SurveyJsId__c')}'
            is_multi_profile = len(contact_profile_count.get(c.get('Id'), [])) > 1

            if is_multi_profile:
                agency_brand_name = a.get('Name')

            last_email_sent_date = csri.get('Email_Live_Survey_First_Request__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            if s and signature and SIGNATURE_SF_RESOURCE_URL in signature:
                cid = f'cid:{s.get("Id")}'
                signature = re.sub(r'src="[^"]+"', f'src="{cid}"', signature)
                attachment = {
                    'disposition': 'inline',
                    'key': f'{s.get("Id")}.png',
                    'file_type': 'image/png',
                    'content_id': s.get('Id'),
                }

            # FIXME: this is a fallback as the SF work never got completed in time
            if s and not signature:
                signature_name = '' if s.get('Name') == None else s.get('Name', '')
                signature_title = '' if s.get('Title') == None else s.get('Title', '')
                signature_account = '' if sa.get('Name') == None else sa.get('Name', '')
                signature_email = '' if s.get('Email') == None else s.get('Email', '')
                signature = f'<p><strong>{signature_name}</strong></p><p>{signature_title}</p><p>{signature_account}</p><p>{signature_email}</p>'

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            locale = liblocale.get_sf_language_to_locale_formatted(c.get('Language__c'))
            template_data = {
                'FirstName': c.get('FirstName'),
                'LastName': c.get('LastName'),
                'SurveyDate': cs.get('Live_Survey_Start_Date__c'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'Signatory': signature,
                'AgencyBrand': agency_brand_name,
                'HoldingGroup': a.get('Name'),
                'OptOutLink': f'{settings.survey_ui_link}/optout/{c.get('Id')}',
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{spm.get('SurveyJsId__c')}',
            }
            template = 'live_survey_participant'
            template_metadata = {
                'survey_panel_member_id': spm.get('Id'),
                'tracked_template_name': template,
            }
            try:
                if has_been_sent_key not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, template_metadata, language=locale, attachment=attachment)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} ({locale}) to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Member__c {spm.get("Id")}')
                has_been_sent.add(has_been_sent_key)
                batch_spm.append({
                    'Id': spm.get('Id'),
                    'Survey_Email_Triggered__c': True
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
                batch_spm.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
            batch_spm.clear()

        sfapi.bulk_update(sf, "Customer_Survey_Round__c", [{ 'Id': csr_id, 'Email_Live_Survey_First_Request__c': datetime.date.today().strftime('%Y-%m-%d') }])

        # ==============================================================================================================
        # Email: Take the Survey #2
        # Recipients: Panel Member
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 1...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Contact__r.Language__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name
        FROM  Survey_Panel_Member__c
        WHERE SurveyJsId__c != NULL
        AND   Survey_Email_Reminder_1__c = False
        AND   Has_Responded__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Contact__r.Disable_External_Communication__c = False
        AND   Opt_Out__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_spm = []
        has_been_sent = set()
        panel_members = []
        contact_profile_count = {}
        query = format_soql(soql, csr_id=csr_id)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}

            panel_members.append(spm)
            contact_id = c.get('Id')
            agency_brand_id = ba.get('Id')

            if contact_id not in contact_profile_count:
                contact_profile_count[contact_id] = set()

            contact_profile_count[contact_id].add(agency_brand_id)

        for spm in panel_members:
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            csri = cs.get('Customer_Survey_Round__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}
            a = csri.get('Account__r', {}) or {}
            s = sc.get('Signatory__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            agency_brand_name = ba.get('Name')
            has_been_sent_key = f'{c.get("Id")}-{spm.get('SurveyJsId__c')}'
            is_multi_profile = len(contact_profile_count.get(c.get('Id'), [])) > 1

            if is_multi_profile:
                agency_brand_name = a.get('Name')

            last_email_sent_date = csri.get('Email_Live_Survey_Second_Request__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            locale = liblocale.get_sf_language_to_locale_formatted(c.get('Language__c'))
            template_data = {
                'FirstName': c.get('FirstName'),
                'LastName': c.get('LastName'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'AgencyBrand': agency_brand_name,
                'HoldingGroup': a.get('Name'),
                'OptOutLink': f'{settings.survey_ui_link}/optout/{c.get('Id')}',
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{spm.get('SurveyJsId__c')}',
            }
            template_metadata = {
                'survey_panel_member_id': spm.get('Id'),
            }
            template = 'live_survey_participant_reminder_1'
            try:
                if has_been_sent_key not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, template_metadata, language=locale)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} ({locale}) to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Member__c {spm.get("Id")}')
                has_been_sent.add(has_been_sent_key)
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_1__c': True
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
                batch_spm.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
            batch_spm.clear()

        sfapi.bulk_update(sf, "Customer_Survey_Round__c", [{ 'Id': csr_id, 'Email_Live_Survey_Second_Request__c': datetime.date.today().strftime('%Y-%m-%d') }])

        # ==============================================================================================================
        # Email: Take the Survey #3
        # Recipients: Panel Member
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 2...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Contact__r.Language__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name
        FROM  Survey_Panel_Member__c
        WHERE SurveyJsId__c != NULL
        AND   Survey_Email_Reminder_2__c = False
        AND   Has_Responded__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Contact__r.Disable_External_Communication__c = False
        AND   Opt_Out__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_spm = []
        has_been_sent = set()
        panel_members = []
        contact_profile_count = {}
        query = format_soql(soql, csr_id=csr_id)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}

            panel_members.append(spm)
            contact_id = c.get('Id')
            agency_brand_id = ba.get('Id')

            if contact_id not in contact_profile_count:
                contact_profile_count[contact_id] = set()

            contact_profile_count[contact_id].add(agency_brand_id)

        for spm in panel_members:
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            csri = cs.get('Customer_Survey_Round__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}
            a = csri.get('Account__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            agency_brand_name = ba.get('Name')
            has_been_sent_key = f'{c.get("Id")}-{spm.get('SurveyJsId__c')}'
            is_multi_profile = len(contact_profile_count.get(c.get('Id'), [])) > 1

            if is_multi_profile:
                agency_brand_name = a.get('Name')

            last_email_sent_date = csri.get('Email_Live_Survey_Third_Request__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            locale = liblocale.get_sf_language_to_locale_formatted(c.get('Language__c'))
            template_data = {
                'FirstName': c.get('FirstName'),
                'LastName': c.get('LastName'),
                'AgencyBrand': agency_brand_name,
                'HoldingGroup': a.get('Name'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
                'OptOutLink': f'{settings.survey_ui_link}/optout/{c.get('Id')}',
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{spm.get('SurveyJsId__c')}',
            }
            template_metadata = {
                'survey_panel_member_id': spm.get('Id'),
            }
            template = 'live_survey_participant_reminder_2'
            try:
                if has_been_sent_key not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, template_metadata, language=locale)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} ({locale}) to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Member__c {spm.get("Id")}')
                has_been_sent.add(has_been_sent_key)
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_2__c': True
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
                batch_spm.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
            batch_spm.clear()

        sfapi.bulk_update(sf, "Customer_Survey_Round__c", [{ 'Id': csr_id, 'Email_Live_Survey_Third_Request__c': datetime.date.today().strftime('%Y-%m-%d') }])

        # ==============================================================================================================
        # Email: Take the Survey #4
        # Recipients: Panel Member
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 3...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Contact__r.Language__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Signatory__r.Id,
               Survey_Client__r.Signatory__r.Name,
               Survey_Client__r.Signatory__r.Email,
               Survey_Client__r.Signatory__r.Title,
               Survey_Client__r.Signatory__r.Account.Name,
               Survey_Client__r.Signatory__r.Signature__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
               Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name
        FROM  Survey_Panel_Member__c
        WHERE SurveyJsId__c != NULL
        AND   Survey_Email_Reminder_3__c = False
        AND   Has_Responded__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Contact__r.Disable_External_Communication__c = False
        AND   Opt_Out__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        """
        batch_spm = []
        has_been_sent = set()
        panel_members = []
        contact_profile_count = {}
        query = format_soql(soql, csr_id=csr_id)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}

            panel_members.append(spm)
            contact_id = c.get('Id')
            agency_brand_id = ba.get('Id')

            if contact_id not in contact_profile_count:
                contact_profile_count[contact_id] = set()

            contact_profile_count[contact_id].add(agency_brand_id)

        for spm in panel_members:
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}
            csri = cs.get('Customer_Survey_Round__r', {}) or {}
            s = sc.get('Signatory__r', {}) or {}
            sa = s.get('Account', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}
            a = csri.get('Account__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            attachment = None
            agency_brand_name = ba.get('Name')
            signature = s.get('Signature__c')
            has_been_sent_key = f'{c.get("Id")}-{spm.get('SurveyJsId__c')}'
            is_multi_profile = len(contact_profile_count.get(c.get('Id'), [])) > 1

            if is_multi_profile:
                agency_brand_name = a.get('Name')

            last_email_sent_date = csri.get('Email_Live_Survey_Fourth_Request__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            if not signature:
                signature = s.get('Name')

            if s and signature and SIGNATURE_SF_RESOURCE_URL in signature:
                cid = f'cid:{s.get("Id")}'
                signature = re.sub(r'src="[^"]+"', f'src="{cid}"', signature)
                attachment = {
                    'disposition': 'inline',
                    'key': f'{s.get("Id")}.png',
                    'file_type': 'image/png',
                    'content_id': s.get('Id'),
                }

            # FIXME: this is a fallback as the SF work never got completed in time
            if s and not signature:
                signature_name = '' if s.get('Name') == None else s.get('Name', '')
                signature_title = '' if s.get('Title') == None else s.get('Title', '')
                signature_account = '' if sa.get('Name') == None else sa.get('Name', '')
                signature_email = '' if s.get('Email') == None else s.get('Email', '')
                signature = f'<p><strong>{signature_name}</strong></p><p>{signature_title}</p><p>{signature_account}</p><p>{signature_email}</p>'

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            locale = liblocale.get_sf_language_to_locale_formatted(c.get('Language__c'))
            template_data = {
                'FirstName': c.get('FirstName'),
                'LastName': c.get('LastName'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'AgencyBrand': agency_brand_name,
                'HoldingGroup': a.get('Name'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
                'Signatory': signature,
                'OptOutLink': f'{settings.survey_ui_link}/optout/{c.get('Id')}',
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{spm.get('SurveyJsId__c')}',
            }
            template_metadata = {
                'survey_panel_member_id': spm.get('Id'),
            }
            template = 'live_survey_participant_reminder_3'
            try:
                if has_been_sent_key not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, template_metadata, language=locale, attachment=attachment)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} ({locale}) to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Member__c {spm.get("Id")}')
                has_been_sent.add(has_been_sent_key)
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_3__c': True
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
                batch_spm.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch_spm)
            batch_spm.clear()

        sfapi.bulk_update(sf, "Customer_Survey_Round__c", [{ 'Id': csr_id, 'Email_Live_Survey_Fourth_Request__c': datetime.date.today().strftime('%Y-%m-%d') }])

        print(f'# END: Customer Survey Round: {cs_name} ({csr_id})')

    print(f'END: EMAIL SCHEDULER PANEL USERS: {len(emails_scheduled)} Emails Scheduled')

    return emails_scheduled


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--round_id', default=None, help='single survey round to run for')
    args = ap.parse_args()

    main(round_id=args.round_id)
