import argparse
import datetime
from lib import sfapi, portaldbapi
from lib.settings import settings


def convert_date(date):
    return datetime.datetime.combine(date, datetime.datetime.min.time())


def sync_dates_to_portaldb(sf, confirm_account_collection, confirm_panel_collection, customer_survey_id: str):
    customer_survey: sfapi.CustomerSurvey | None = sfapi.get_by_id(sf, sfapi.CustomerSurvey, customer_survey_id)
    if not customer_survey:
        raise Exception(f"Unable to find customer survey: {customer_survey_id}")

    # grab the confirmation end dates
    accounts_confirmation_start_date = convert_date(customer_survey.AccountUpdatesStartDate)
    accounts_confirmation_end_date = convert_date(customer_survey.AccountUpdatesEndDate)
    panel_confirmation_start_date = convert_date(customer_survey.PanelUpdatesStartDate)
    panel_confirmation_end_date = convert_date(customer_survey.PanelUpdatesEndDate)
    live_survey_start_date = convert_date(customer_survey.LiveSurveyStartDate)
    live_survey_first_request_date = convert_date(customer_survey.LiveSurveyFirstRequest)
    live_survey_second_request_date = convert_date(customer_survey.LiveSurveySecondRequest)
    live_survey_third_request_date = convert_date(customer_survey.LiveSurveyThirdRequest)
    live_survey_fourth_request_date = convert_date(customer_survey.LiveSurveyFourthRequest)
    live_survey_end_date = convert_date(customer_survey.LiveSurveyEndDate)

    # get the confirm account for the customer survey and update the end date
    confirm_account_collection.find_one_and_update(
        {"Id": customer_survey_id},
        {"$set": {
            "accounts_confirmed_start_date": accounts_confirmation_start_date,
            "accounts_confirmed_by_date": accounts_confirmation_end_date,
            "panel_confirmed_start_date": panel_confirmation_start_date,
            "panel_confirmed_by_date": panel_confirmation_end_date,
            "live_survey_start_date": live_survey_start_date,
            "live_survey_first_request_date": live_survey_first_request_date,
            "live_survey_second_request_date": live_survey_second_request_date,
            "live_survey_third_request_date": live_survey_third_request_date,
            "live_survey_fourth_request_date": live_survey_fourth_request_date,
            "live_survey_end_date": live_survey_end_date,
        }}
    )

    # get the confirm panels for the customer survey round and update the end date
    confirm_panel = confirm_panel_collection.find({"customer_survey_id": customer_survey_id})
    if confirm_panel:
        confirm_panel_ids = [confirm_panel["Id"] for confirm_panel in confirm_panel]
        confirm_panel_collection.update_many(
            {"Id": {"$in": confirm_panel_ids}},
            {"$set": {
                "accounts_confirmed_start_date": accounts_confirmation_start_date,
                "accounts_confirmed_by_date": accounts_confirmation_end_date,
                "panel_confirmed_start_date": panel_confirmation_start_date,
                "panel_confirmed_by_date": panel_confirmation_end_date,
                "live_survey_start_date": live_survey_start_date,
                "live_survey_first_request_date": live_survey_first_request_date,
                "live_survey_second_request_date": live_survey_second_request_date,
                "live_survey_third_request_date": live_survey_third_request_date,
                "live_survey_fourth_request_date": live_survey_fourth_request_date,
                "live_survey_end_date": live_survey_end_date,
            }}
        )


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_account_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    soql = """
SELECT Id
FROM  Customer_Survey__c
WHERE Current_Round__c = TRUE AND (Stage__c = 'Survey Setup' OR Stage__c = 'Live Survey') AND Requires_Portaldb_Sync__c = TRUE
"""

    # keep note of all the survey clients we've dealt with
    batch_update = []

    # get all the surveys we need to update
    for row in sf.query_all_iter(soql):
        customer_survey_id = row["Id"]
        sync_dates_to_portaldb(sf, confirm_account_collection, confirm_panel_collection, customer_survey_id)

        batch_update.append({
            'Id': customer_survey_id,
            'Requires_Portaldb_Sync__c': False
        })

    if len(batch_update):
        sfapi.bulk_update(sf, "Customer_Survey__c", batch_update)


def lambda_handler(event, _):
    main()


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
