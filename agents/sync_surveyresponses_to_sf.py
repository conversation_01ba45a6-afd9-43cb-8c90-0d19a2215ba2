import pymongo.collection
import pymongo
import argparse
from lib import sfapi, portaldbapi
from simple_salesforce import Salesforce
import datetime
from lib.settings import settings


SF_BATCH_SIZE = 10000

def sync_batch_to_sf(sf: Salesforce,
                     sf_survey_responses_to_create: list[sfapi.SurveyResponse],
                     survey_collection: pymongo.collection.Collection,
                     processed_survey_oids: set[str]) -> None:

    # write them to salesforce
    sfapi.bulk_import_batch_to_sf(sf, None, {}, sf_survey_responses_to_create, sfapi.SurveyResponse)

    # mark them as synced in portaldb
    update = {
        "$set": {
            "sfsync_date": datetime.datetime.now(datetime.UTC),
        }
    }
    query = {
        "_id": {"$in": list(processed_survey_oids)},
        "sfsync_date": None
    }
    survey_collection.update_many(query, update)


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    survey_collection = portaldb.client[portaldbapi.PORTALDB_DATABASE][portaldbapi.PORTALDB_SURVEY_COLLECTION]

    query = {
        "response": {"$ne": None},
        "sfsync_date": None,
        "deleted": {"$ne": True},
    }
    survey_cursor = survey_collection.find(query)

    sf_survey_responses_to_create = []
    processed_survey_oids = set()
    for survey in survey_cursor:
        survey_oid = survey["_id"]
        survey = portaldbapi.Survey(**survey)

        for survey_question in survey.questions:
            # NOTE IMPORTANT: This short block is dealing with data from the end user.
            # It needs carefully sanitised to ensure we cannot have any injection attacks.
            try:
                score = int(survey.response.get(survey_question.score_question_id, "")) # force it to be an int
            except ValueError:
                score = None
            feedback = str(survey.response.get(survey_question.feedback_question_id, "")) # force it to be a string

            # create sfresponse objects
            for panel_member in survey_question.panel_members:
                sfresponse = sfapi.SurveyResponse(
                    Id="NEW",
                    Name=panel_member.survey_panel_member_name,
                    Survey_Panel_Member__c=panel_member.survey_panel_member_id,
                    Score_Question__c=survey_question.score_question,
                    Score_Question_EN__c=survey_question.score_question_en,
                    Score__c=score,
                    Feedback_Question__c=survey_question.feedback_question,
                    Feedback_Question_EN__c=survey_question.feedback_question_en,
                    Feedback__c=feedback,
                    Themes__c=None,
                    Response_Date_Time__c=survey.response_date,
                    Is_Extra_Question__c=survey_question.is_extra_question,
                )
                sf_survey_responses_to_create.append(sfresponse)
            processed_survey_oids.add(survey_oid)

        # NOTE: this is intentionally in the outer loop so we only ever create complete sets of survey responses
        if len(sf_survey_responses_to_create) >= SF_BATCH_SIZE:
            if writeit:
                sync_batch_to_sf(sf, sf_survey_responses_to_create, survey_collection, processed_survey_oids)
            sf_survey_responses_to_create.clear()
            processed_survey_oids.clear()

    # remember to write the last batch
    if len(sf_survey_responses_to_create) > 0:
        if writeit:
            sync_batch_to_sf(sf, sf_survey_responses_to_create, survey_collection, processed_survey_oids)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
