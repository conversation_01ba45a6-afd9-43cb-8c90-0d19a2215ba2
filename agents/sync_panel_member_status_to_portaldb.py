import argparse
from pymongo import UpdateOne
from lib import sfapi, portaldbapi
from lib.settings import settings


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    portaldb_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    # get all updates from SF
    panel_members_by_survey_client_id: dict[str, dict[str, dict]] = {}
    soql = """
    SELECT  Id,
            Contact__c,
            Has_Responded__c,
            Survey_Email_Bounced__c,
            Survey_Client__c
    FROM  Survey_Panel_Member__c
    WHERE (Has_Responded__c = True OR Survey_Email_Bounced__c = True)
    AND   Survey_Client__r.Current_Round__c = True
    AND   LastModifiedDate >= YESTERDAY
    """
    for r in sfapi.bulk_query(sf, soql):
        survey_client_panel = panel_members_by_survey_client_id.setdefault(r['Survey_Client__c'], {})
        survey_client_panel[r['Contact__c']] = {
            'responded': r['Has_Responded__c'].lower() in {'true'},
            'bounced': r['Survey_Email_Bounced__c'].lower() in {'true'}
        }
    survey_client_ids = list(panel_members_by_survey_client_id.keys())

    # generate updates to confirmed panel members in portaldb
    batch = []
    for pdb_confirm_panel in portaldb_collection.find({'Id': {'$in': survey_client_ids}}):
        sf_panel_members = panel_members_by_survey_client_id.get(pdb_confirm_panel['Id'], {})
        if not sf_panel_members:
            continue

        set_updates = {}
        for cp_idx, panel in enumerate(pdb_confirm_panel.get('confirmed_panel', [])):
            panel_deets = panel.get('panel', [])
            for pm_idx, panel_member in enumerate(panel_deets):
                sf_panel_member_status = sf_panel_members.get(panel_member['contact_id'], {})
                if not sf_panel_member_status:
                    continue

                if panel_member['response_status'] != sf_panel_member_status['responded']:
                    set_updates[f"confirmed_panel.{cp_idx}.panel.{pm_idx}.response_status"] = sf_panel_member_status['responded']
                if panel_member['bounced_status'] != sf_panel_member_status['bounced']:
                    set_updates[f"confirmed_panel.{cp_idx}.panel.{pm_idx}.bounced_status"] = sf_panel_member_status['bounced']

        if set_updates:
            batch.append(UpdateOne({'Id': pdb_confirm_panel['Id']}, {'$set': set_updates}))

    # update 'em all in mongo
    if writeit:
        if batch:
            portaldb_collection.bulk_write(batch)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    ap.add_argument("--forceall", action="store_true", help="Resync ALL responses")
    args = ap.parse_args()

    main()
