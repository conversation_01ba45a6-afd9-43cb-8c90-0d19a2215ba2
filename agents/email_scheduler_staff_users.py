import argparse
import datetime
from sendgrid import SendGridAPIClient
from simple_salesforce import format_soql
from lib.settings import settings
from lib import emailapi, sfapi, portaldbapi, sqsapi
from agents.email_sender_panel_users import cache_sendgrid_template


SF_BATCH_SIZE = 1000
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets


def get_email_service_provider(csr) -> str:
    # check the CSR first
    ses_checked = csr.get('Send_Emails_Via_SES__c')
    if not ses_checked:
        # check the survey round
        sr = csr.get('Survey_Round__r', {}) or {}
        ses_checked = sr.get('Send_Emails_Via_SES__c')
    if ses_checked:
        return 'ses'
    return 'sendgrid'


def main(round_id = None):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    print('START: EMAIL SCHEDULER STAFF USERS')

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sqs: sqsapi.SQS = sqsapi.SQS()
    es: emailapi.EmailService = emailapi.EmailService(db, sqs)
    esc: emailapi.EmailServiceConfig = emailapi.EmailServiceConfig(**db.config.find_one({"_id": "email"}))
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)

    emails_scheduled = []
    cached_template_id_tracker:set[str] = set()

    soql = """
    SELECT Id,
           Name,
           Disable_External_Communication__c,
           Send_Emails_Via_SES__c,
           Email_Round_Scheduled__c,
           Stage__c,
           Account_Updates_Start_Date__c,
           Account_Updates_End_Date__c,
           Panel_Updates_Start_Date__c,
           Panel_Updates_End_Date__c,
           Live_Survey_Start_Date__c,
           Live_Survey_First_Request__c,
           Live_Survey_Second_Request__c,
           Live_Survey_Third_Request__c,
           Live_Survey_Fourth_Request__c,
           Live_Survey_End_Date__c,
           Account__r.Name,
           Account__r.Consultant__r.Email,
           Survey_Round__r.Name,
           Survey_Round__r.Disable_External_Communication__c,
           Survey_Round__r.Send_Emails_Via_SES__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """
    if round_id:
        soql = soql + " AND Id = {round_id}"
    query = format_soql(soql, round_id=round_id)
    for csr in sf.query_all_iter(query):
        if not csr:
            continue

        sr = csr.get('Survey_Round__r', {}) or {}

        csr_id = csr.get('Id')
        cs_name = csr.get('Name')
        cs_comms_disabled = csr.get('Disable_External_Communication__c')
        sr_comms_disabled = sr.get('Disable_External_Communication__c')
        email_service_provider = get_email_service_provider(csr)

        print(f'# START: Customer Survey Round: {cs_name} ({csr_id})')

        # if customer survey round has comms disabled, skip it
        if sr_comms_disabled or cs_comms_disabled:
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) has comms disabled')
            continue

        # ==============================================================================================================
        # Email: Survey has been scheduled
        # Recipients: Consultants
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: SURVEY SCHEDULED...')
        batch = []
        a = csr.get('Account__r', {}) or {}
        sr = csr.get('Survey_Round__r', {}) or {}
        c = a.get('Consultant__r', {}) or {}

        if not csr.get('Email_Round_Scheduled__c') and csr.get('Stage__c') == 'Start Round' and csr.get('Live_Survey_Start_Date__c') and csr.get('Live_Survey_End_Date__c'):
            sender = DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'Company': a.get('Name'),
                'Round': sr.get('Name'),
                'AccountUpdateStartDate': csr.get('Account_Updates_Start_Date__c'),
                'AccountUpdateEndDate': csr.get('Account_Updates_End_Date__c'),
                'PanelUpdateStartDate': csr.get('Panel_Updates_Start_Date__c'),
                'PanelUpdateEndDate': csr.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': csr.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': csr.get('Live_Survey_End_Date__c'),
            }
            template = 'round_scheduled'
            try:
                es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id': csr.get('Id'),
                    'Email_Round_Scheduled__c': True
                })
                print(f'   :: Email Scheduled: {template} to {recipient} for Customer_Survey_Round__c {csr.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch):
                sfapi.bulk_update(sf, "Customer_Survey_Round__c", batch)
                batch.clear()
        '''

        # ==============================================================================================================
        # Email: Confirm your Accounts
        # Recipients: Account Manager
        print(' >> PROCESSING: ACCOUNT UPDATES...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Confirm_Your_Accounts__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.External_Communication_Email_Address__c,
               Customer_Survey__r.Account_Updates_End_Date__c,
               Customer_Survey__r.Panel_Updates_End_Date__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_End_Date__c,
               Customer_Survey__r.Customer_Survey_Round__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Confirm_Your_Accounts__c = False
        AND   Customer_Survey__r.Stage__c = 'Survey Setup'
        AND   Customer_Survey__r.Account_Updates_Start_Date__c = TODAY
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}
            a = csr.get('Account__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Confirm_Your_Accounts__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/accounts',
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': cs.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'account_updates'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Confirm_Your_Accounts__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Accounts__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Confirm your Accounts Reminder
        # Recipients: Account Manager
        print(' >> PROCESSING: ACCOUNT UPDATES REMINDER...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Confirm_Your_Accounts_Reminder__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.External_Communication_Email_Address__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Account_Updates_End_Date__c,
               Customer_Survey__r.Panel_Updates_End_Date__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Confirm_Your_Accounts__c = True
        AND   Email_Confirm_Your_Accounts_Reminder__c = False
        AND   Customer_Survey__r.Account_Updates_End_Date__c = TOMORROW
        AND   Customer_Survey__r.Stage__c = 'Survey Setup'
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Confirm_Your_Accounts_Reminder__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/accounts',
            }
            template = 'account_updates_reminder'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Confirm_Your_Accounts_Reminder__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Accounts_Reminder__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Account Delegation
        # Recipients: Account Manager
        '''
        print(' >> PROCESSING: ACCOUNT DELEGATION...')
        confirm_accounts = list(db.confirmaccounts.find({'customer_survey_round_id': csr_id}, {'Id': 1, 'delegation': 1, 'client_name': 1, 'customer_survey_round_id': 1, 'accounts_confirmed_by_date': 1}))
        for ca in confirm_accounts:
            delegation = ca.get('delegation', {})
            if not delegation:
                continue

            for d in delegation:
                if d.get('email_account_delegation_sent'):
                    continue

                sender = ca.get('email_sender', DEFAULT_SENDER)
                recipient = d.get('delegate_email')
                template_data = {
                    'AccountName': ca.get('client_name'),
                    'AccountUpdateEndDate': ca.get('accounts_confirmed_by_date').date().strftime('%Y-%m-%d'),
                    'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{ca['customer_survey_round_id']}/accounts',
                }
                template = 'account_delegation'

                try:
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')

                    print(f'   :: Email Scheduled: {template} to {recipient} for confirmaccounts (delegation) {ca.get("Id")}')
                except Exception as e:
                    print(f'   :: ERROR: {e}')

                db.confirmaccounts.update_one({'Id': ca['Id'], 'delegation.delegate_id': d['delegate_id']}, {'$set': {'delegation.$.email_account_delegation_sent': True}})
        '''
        # ==============================================================================================================
        # Email: Confirm your Panel
        # Recipients: Panel Manager(s)
        print(' >> PROCESSING: PANEL UPDATES...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Confirm_Your_Panel__c,
               Survey_Client__r.Customers_Client_Name__c,
               Survey_Client__r.Panel_Confirmed_By_Date__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c,
               Survey_Client__r.Customer_Survey__r.Account_Updates_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Confirm_Your_Panel__c = False
        AND   Survey_Client__r.State__c = 'Panel Pending'
        AND   Survey_Client__r.Customer_Survey__r.Panel_Updates_Start_Date__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Confirm_Your_Panel__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': cs.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'panel_updates'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Confirm_Your_Panel__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Panel__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Confirm your Panel Reminder
        # Recipients: Panel Manager(s)
        print(' >> PROCESSING: PANEL UPDATES REMINDER...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Confirm_Your_Panel_Reminder__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c,
               Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Confirm_Your_Panel__c = True
        AND   Email_Confirm_Your_Panel_Reminder__c = False
        AND   Has_Responded__c = False
        AND   Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c = TOMORROW
        AND   Survey_Client__r.State__c = 'Panel Pending'
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Confirm_Your_Panel_Reminder__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
            }
            template = 'panel_updates_reminder'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Confirm_Your_Panel_Reminder__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Panel_Reminder__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Panels Successfully Confirmed
        # Recipients: Panel Manager(s)
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: PANEL UPDATES SUCCESS...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Panel_Confirmation__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Panel_Confirmation__c = False
        AND   Has_Responded__c = True
        AND   Survey_Client__r.State__c = 'Panel Pending'
        AND   Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c >= TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Panel_Confirmation__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('FirstName'),
                'FirstRequestDate': cs.get('Live_Survey_First_Request__c'),
                'SecondRequestDate': cs.get('Live_Survey_Second_Request__c'),
                'ThirdRequestDate': cs.get('Live_Survey_Third_Request__c'),
                'LastRequestDate': cs.get('Live_Survey_Fourth_Request__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'panel_updates_confirmed'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Panel_Confirmation__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Panel_Confirmation__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()
        '''

        # ==============================================================================================================
        # Email: Survey is ready to go
        # Recipients: Account Manager
        '''
        print(" >> PROCESSING: SURVEY READY TO GO...")
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Survey_Ready_To_Go__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.Name,
               Customer_Survey__r.External_Communication_Email_Address__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_First_Request__c,
               Customer_Survey__r.Live_Survey_Second_Request__c,
               Customer_Survey__r.Live_Survey_Third_Request__c,
               Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Customer_Survey__r.Live_Survey_End_Date__c,
               Customer_Survey__r.Customer__r.Parent.Name
        FROM  Survey_Account_Manager__c
        WHERE Email_Survey_Ready_To_Go__c = False
        AND   Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Customer_Survey__r.Live_Survey_Start_Date__c = TOMORROW
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}
            ma = cs.get('Customer__r', {}) or {}
            ba = ma.get('Parent', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            agency_brand_name = ba.get('Name')

            if agency_brand_name:
                agency_brand_name = agency_brand_name.lower().replace(' ', '')

            last_email_sent_date = c.get('Email_Survey_Ready_To_Go__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'AgencyName': agency_brand_name,
                'FirstRequestDate': cs.get('Live_Survey_First_Request__c'),
                'SecondRequestDate': cs.get('Live_Survey_Second_Request__c'),
                'ThirdRequestDate': cs.get('Live_Survey_Third_Request__c'),
                'LastRequestDate': cs.get('Live_Survey_Fourth_Request__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'panel_updates_finalised'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Survey_Ready_To_Go__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Survey_Ready_To_Go__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()
        '''
        # ==============================================================================================================
        # Email: Survey is live
        # Recipients: Account Manager
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: LIVE SURVEY NOTICE...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.Email,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email_Live_Survey__c,
               Customer_Survey__r.External_Communication_Email_Address__c,
               Customer_Survey__r.Customer_Survey_Round__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Live_Survey__c = False
        AND   Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Customer_Survey__r.Live_Survey_Start_Date__c = YESTERDAY
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Live_Survey__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
            }
            template = 'live_survey_notice'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Live_Survey__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Live_Survey__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()
        '''

        # ==============================================================================================================
        # Email: Address Bounce Backs
        # Recipients: Panel Manager(s)
        print(' >> PROCESSING: ADDRESS BOUNCE BACKS...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Address_Bounce_Backs__c,
               Survey_Client__r.No_of_Emails_Bounced__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Address_Bounce_Backs__c = False
        AND   Survey_Client__r.No_of_Emails_Bounced__c > 0
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c = YESTERDAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')
            bounce_back_count = sc.get('No_of_Emails_Bounced__c')

            if bounce_back_count and isinstance(bounce_back_count, (int, float)):
                bounce_back_count = str(int(bounce_back_count))

            last_email_sent_date = c.get('Email_Address_Bounce_Backs__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'NoBounceBacks': bounce_back_count,
                'FirstRequestDate': cs.get('Live_Survey_First_Request__c'),
                'SecondRequestDate': cs.get('Live_Survey_Second_Request__c'),
                'ThirdRequestDate': cs.get('Live_Survey_Third_Request__c'),
                'LastRequestDate': cs.get('Live_Survey_Fourth_Request__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel'
            }
            template = 'bounce_backs'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Address_Bounce_Backs__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Address_Bounce_Backs__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Monitor response rate
        # Recipients: Account Manager
        print(' >> PROCESSING: MONITOR RESPONSE RATE...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.Email,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email_Monitor_Response_Rate__c,
               Customer_Survey__r.External_Communication_Email_Address__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Live_Survey_First_Request__c,
               Customer_Survey__r.Live_Survey_Second_Request__c,
               Customer_Survey__r.Live_Survey_Third_Request__c,
               Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Monitor_Response_Rate__c = False
        AND   Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Customer_Survey__r.Live_Survey_Second_Request__c = YESTERDAY
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        query = format_soql(soql, csr_id=csr_id)
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Monitor_Response_Rate__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'SenderEmail': sender,
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'FirstRequestDate': cs.get('Live_Survey_First_Request__c'),
                'SecondRequestDate': cs.get('Live_Survey_Second_Request__c'),
                'ThirdRequestDate': cs.get('Live_Survey_Third_Request__c'),
                'LastRequestDate': cs.get('Live_Survey_Fourth_Request__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'monitor_response_rate'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, template, None, None, cached_template_id_tracker)
                    es.send(sender, recipient, template, template_data, {}, service_provider=email_service_provider)
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Monitor_Response_Rate__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Monitor_Response_Rate__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Survey is Closed
        # Recipients: Account Managers
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: SURVEY IS CLOSED (ACCOUNT MANAGER)...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.Email,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email_Survey_Closed__c,
               Customer_Survey__r.External_Communication_Email_Address__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Survey_Closed__c = False
        AND   Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Customer_Survey__r.Live_Survey_End_Date__c = YESTERDAY
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        query = format_soql(soql, csr_id=csr_id)
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Survey_Closed__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/dashboards/overview',
            }
            template = 'survey_closed'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Survey_Closed__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Survey_Closed__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()
        '''
        # ==============================================================================================================
        # Email: Survey is Closed
        # Recipients: Panel Managers
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: SURVEY IS CLOSED (PANEL MANAGER)...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.Email,
               Contact__r.FirstName,
               Contact__r.Email_Survey_Closed__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Survey_Closed__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c = YESTERDAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        query = format_soql(soql, csr_id=csr_id)
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Survey_Closed__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/dashboards/overview',
            }
            template = 'survey_closed'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id': spm.get('Id'),
                    'Email_Survey_Closed__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Survey_Closed__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()
        '''
        # ==============================================================================================================
        # Email: Insights Ready
        # Recipients: Panel Managers
        # NOTE: This was requested to be disabled for Q4 2024 round
        '''
        print(' >> PROCESSING: INSIGHTS READY...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Insights_Ready__c,
               Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Insights_Ready__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Insights'
        AND   Survey_Client__r.Customer_Survey__r.Insights_Start_Date__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            external_communication_email = cs.get('External_Communication_Email_Address__c')

            last_email_sent_date = c.get('Email_Insights_Ready__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = external_communication_email if external_communication_email else DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/dashboards/overview',
            }
            template = 'insights_ready'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    #es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id': spm.get('Id'),
                    'Email_Insights_Ready__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Insights_Ready__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                #sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                #sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            #sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            #sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()
        '''

        print(f'# END: Customer Survey Round: {cs_name} ({csr_id})')

    print(f'END: EMAIL SCHEDULER STAFF USERS: {len(emails_scheduled)} Emails Scheduled')

    return emails_scheduled


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--round_id', default=None, help='single survey round to run for')
    args = ap.parse_args()

    main(round_id=args.round_id)
