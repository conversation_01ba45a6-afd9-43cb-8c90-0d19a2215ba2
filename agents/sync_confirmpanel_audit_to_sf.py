""" Sync panel confirmation audit information to Salesforce
    The audit information is used to track changes to a Survey Client's panel by panel managers.

DATA:
    FROM: PortalDB (confirmpanel)
    TO: Salesforce (Panel_Confirmation_Audit__c)
"""
import argparse
from lib import sfapi, portaldbapi
from simple_salesforce import format_soql, Salesforce
import datetime
from lib.settings import settings


SF_BATCH_SIZE = 3000


def format_date_key(date:datetime.datetime) -> str:
    return date.strftime('%Y%m%d%H%M%S')


def preload_sf_data(sf:Salesforce, survey_client_ids:set[str]) -> dict[str, sfapi.PanelConfirmationAudit]:
    # get all relevant survey clients 
    all_panel_confirmation_audit_by_id = {}
    for batch in sfapi.batch_iterable(survey_client_ids, SF_BATCH_SIZE):
        soql = format_soql("WHERE Survey_Client__c IN {batch}", batch=batch)
        for apa in sfapi.get_all(sf, sfapi.PanelConfirmationAudit, bulk=True, query_suffix=soql):
            confirm_date = datetime.datetime.strptime(apa.ConfirmedDate, '%Y-%m-%dT%H:%M:%S.%fZ')
            key = f'{apa.SurveyClientId}-{apa.PanelManagerId}-{format_date_key(confirm_date)}'
            all_panel_confirmation_audit_by_id[key] = apa

    return all_panel_confirmation_audit_by_id


def main(survey_client_id:str|None = None, forceall:bool = False, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    all_confirm_panel: list[portaldbapi.ConfirmPanel] = []
    survey_client_ids = set()
    new_panel_confirmation_audit = []

    now = datetime.datetime.now(datetime.timezone.utc)
    today_start = datetime.datetime(now.year, now.month, now.day)
    yesterday_start = today_start - datetime.timedelta(days=1)

    query = {
        "$or": [
            {"sfsync_date": None},
            {"sfsync_date": {"$gte": yesterday_start, "$lt": now}}
        ],
        "deleted": { "$ne": True },
    }
    if survey_client_id:
        query["Id"] = survey_client_id
    if forceall:
        query = {}

    # grab all the records
    confirm_panel_records = confirm_panel_collection.find(query)

    # gather information on what we're about to sync
    for pdb_survey_client in confirm_panel_records:
        pdb_survey_client = portaldbapi.ConfirmPanel(**pdb_survey_client)
        all_confirm_panel.append(pdb_survey_client)
        survey_client_ids.add(pdb_survey_client.Id)

    # if there's nothing to do, exit early
    if not all_confirm_panel:
        return
    
    # get necessary data from salesforce now we know we have something to do
    all_panel_confirmation_audit_by_id = preload_sf_data(sf, survey_client_ids)

    # check each confirm panel to see if there is new saves needing synced
    for pdb_survey_client in all_confirm_panel:
        
        # get the latest count of confirmed panel
        if len(pdb_survey_client.confirmed_panel):
            for idx, confirmed_panel in enumerate(pdb_survey_client.confirmed_panel):
                confirmed_by_contact_id = confirmed_panel.confirm_user_id
                confirmed_by_contact_date = confirmed_panel.confirm_date
                panel_confirmation_count = len(confirmed_panel.panel)
                panel_confirmation_names = ', '.join([panel_member.contact_name for panel_member in confirmed_panel.panel])
                panel_added_count = len(confirmed_panel.stats['added'])
                panel_added_names = ', '.join([panel_member['name'] for panel_member in confirmed_panel.stats['added']])
                panel_removed_count = len(confirmed_panel.stats['removed'])
                panel_removed_names = ', '.join([panel_member['name'] for panel_member in confirmed_panel.stats['removed']])
                sf_cache_key = f'{pdb_survey_client.Id}-{confirmed_by_contact_id}-{format_date_key(confirmed_by_contact_date)}'
                is_latest_save = (idx == 0)

                if sf_cache_key not in all_panel_confirmation_audit_by_id:
                    new_panel_confirmation_audit.append({
                        "Survey_Client__c": pdb_survey_client.Id,
                        "Panel_Manager__c": confirmed_by_contact_id,
                        "Confirmed_Date__c": confirmed_by_contact_date.isoformat(),
                        "Confirmed_Panel_Count__c": panel_confirmation_count,
                        "Confirmed_Panel__c": panel_confirmation_names,
                        "Panel_Added_Count__c": panel_added_count,
                        "Panel_Added__c": panel_added_names,
                        "Panel_Removed_Count__c": panel_removed_count,
                        "Panel_Removed__c": panel_removed_names,
                        "Latest_Save__c": is_latest_save,
                    })

    if writeit:
        if new_panel_confirmation_audit:
            sfapi.bulk_insert(sf, "Panel_Confirmation_Audit__c", new_panel_confirmation_audit)

    updates = set([p["Survey_Client__c"] for p in new_panel_confirmation_audit])
    print(f"Updated {len(updates)} survey clients")
    print(updates)


def lambda_handler(event, context):
    main(
        survey_client_id=None, 
        forceall=False, 
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_client_id', help='ID of the SC to process')
    ap.add_argument("--forceall", action="store_true", help="Attempt to resync audit history for all survey clients")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(
        survey_client_id=args.survey_client_id, 
        forceall=args.forceall, 
        writeit=args.writeit
    )
