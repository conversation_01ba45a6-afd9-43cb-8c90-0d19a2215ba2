from lib import portaldbapi, sfapi
from lib.settings import settings

def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()

    portaldbapi.sync_sfsimple_to_portaldb(sf, portaldb, "sfcustomersurveyround", sfapi.CustomerSurveyRound)
    portaldbapi.sync_sfsimple_to_portaldb(sf, portaldb, "sfcustomersurvey", sfapi.CustomerSurvey)
    portaldbapi.sync_sfsimple_to_portaldb(sf, portaldb, "sfsurveyclient", sfapi.SurveyClient)
    portaldbapi.sync_sfsimple_to_portaldb(sf, portaldb, "sfcontacts", sfapi.Contact)
    portaldbapi.sync_sfsimple_to_portaldb(sf, portaldb, "sfcustomerclientaccounts", sfapi.Account, "WHERE RecordType.Name = 'Customer\\'s Client Account'")


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    main()
