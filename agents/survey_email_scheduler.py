import argparse
import datetime
from simple_salesforce import format_soql
from lib.settings import settings
from lib import emailapi, sfapi, portaldbapi, sqsapi


SF_BATCH_SIZE = 1000
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets

def main(round_id = None):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    print('START: EMAIL SCHEDULER')

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sqs: sqsapi.SQS = sqsapi.SQS()
    es: emailapi.EmailService = emailapi.EmailService(db, sqs)

    emails_scheduled = []

    soql = """
    SELECT Id,
           Name,
           Disable_External_Communication__c,
           Email_Round_Scheduled__c,
           Stage__c,
           Account_Updates_Start_Date__c,
           Account_Updates_End_Date__c,
           Panel_Updates_Start_Date__c,
           Panel_Updates_End_Date__c,
           Live_Survey_Start_Date__c,
           Live_Survey_First_Request__c,
           Live_Survey_Second_Request__c,
           Live_Survey_Third_Request__c,
           Live_Survey_Fourth_Request__c,
           Live_Survey_End_Date__c,
           Account__r.Name,
           Account__r.Consultant__r.Email,
           Survey_Round__r.Name,
           Survey_Round__r.Disable_External_Communication__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """
    if round_id:
        soql = soql + " AND Id = {round_id}"
    query = format_soql(soql, round_id=round_id)
    for csr in sf.query_all_iter(query):
        if not csr:
            continue

        sr = csr.get('Survey_Round__r', {}) or {}

        csr_id = csr.get('Id')
        cs_name = csr.get('Name')
        cs_comms_disabled = csr.get('Disable_External_Communication__c')
        sr_comms_disabled = sr.get('Disable_External_Communication__c')

        print(f'# START: Customer Survey Round: {cs_name} ({csr_id})')

        # if customer survey round has comms disabled, skip it
        if sr_comms_disabled or cs_comms_disabled:
            print(f' >> SKIPPED: Customer Survey Round: {cs_name} ({csr_id}) has comms disabled')
            continue

        # ==============================================================================================================
        # Email: Survey has been scheduled
        # Recipients: Consultants
        # NOTE: This was requested to be disabled for Q4 2024 round (they couldn't agree who to send it too)
        '''
        print(' >> PROCESSING: SURVEY SCHEDULED...')
        batch = []
        a = csr.get('Account__r', {}) or {}
        sr = csr.get('Survey_Round__r', {}) or {}
        c = a.get('Consultant__r', {}) or {}

        if not csr.get('Email_Round_Scheduled__c') and csr.get('Stage__c') == 'Start Round' and csr.get('Live_Survey_Start_Date__c') and csr.get('Live_Survey_End_Date__c'):
            sender = DEFAULT_SENDER
            recipient = c.get('Email')
            template_data = {
                'Company': a.get('Name'),
                'Round': sr.get('Name'),
                'AccountUpdateStartDate': csr.get('Account_Updates_Start_Date__c'),
                'AccountUpdateEndDate': csr.get('Account_Updates_End_Date__c'),
                'PanelUpdateStartDate': csr.get('Panel_Updates_Start_Date__c'),
                'PanelUpdateEndDate': csr.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': csr.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': csr.get('Live_Survey_End_Date__c'),
            }
            template = 'round_scheduled'
            try:
                es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id': csr.get('Id'),
                    'Email_Round_Scheduled__c': True
                })
                print(f'   :: Email Scheduled: {template} to {recipient} for Customer_Survey_Round__c {csr.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch):
                sfapi.bulk_update(sf, "Customer_Survey_Round__c", batch)
                batch.clear()
        '''

        # ==============================================================================================================
        # Email: Confirm your accounts
        # Recipients: Account Manager
        print(' >> PROCESSING: ACCOUNT UPDATES...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Confirm_Your_Accounts__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.Email__c,
               Customer_Survey__r.Account_Updates_End_Date__c,
               Customer_Survey__r.Panel_Updates_End_Date__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_End_Date__c,
               Customer_Survey__r.Customer_Survey_Round__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Confirm_Your_Accounts__c = False
        AND   Account_Manager__r.HasOptedOutOfEmail = False
        AND   Customer_Survey__r.Stage__c = 'Survey Setup'
        AND   Customer_Survey__r.Account_Updates_Start_Date__c = TODAY
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}
            a = csr.get('Account__r', {}) or {}

            last_email_sent_date = c.get('Email_Confirm_Your_Accounts__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/accounts',
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': cs.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'account_updates'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Confirm_Your_Accounts__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Accounts__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Confirm your accounts reminder
        # Recipients: Account Manager
        print(' >> PROCESSING: ACCOUNT UPDATES REMINDER...')
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Confirm_Your_Accounts_Reminder__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.Email__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Account_Updates_End_Date__c,
               Customer_Survey__r.Panel_Updates_End_Date__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Confirm_Your_Accounts__c = True
        AND   Email_Confirm_Your_Accounts_Reminder__c = False
        AND   Account_Manager__r.HasOptedOutOfEmail = False
        AND   Customer_Survey__r.Account_Updates_End_Date__c = TOMORROW
        AND   Customer_Survey__r.Stage__c = 'Survey Setup'
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            last_email_sent_date = c.get('Email_Confirm_Your_Accounts_Reminder__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/accounts',
            }
            template = 'account_updates_reminder'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Confirm_Your_Accounts_Reminder__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Accounts_Reminder__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Account Delegation
        # Recipients: Account Manager
        print(' >> PROCESSING: ACCOUNT DELEGATION...')
        confirm_accounts = list(db.confirmaccounts.find({'customer_survey_round_id': csr_id}, {'Id': 1, 'delegation': 1, 'client_name': 1, 'customer_survey_round_id': 1, 'accounts_confirmed_by_date': 1}))
        for ca in confirm_accounts:
            delegation = ca.get('delegation', {})
            if not delegation:
                continue

            for d in delegation:
                if d.get('email_account_delegation_sent'):
                    continue

                sender = ca.get('email_sender', DEFAULT_SENDER)
                recipient = d.get('delegate_email')
                template_data = {
                    'AccountName': ca.get('client_name'),
                    'AccountUpdateEndDate': ca.get('accounts_confirmed_by_date').date().strftime('%Y-%m-%d'),
                    'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{ca['customer_survey_round_id']}/accounts',
                }
                template = 'account_delegation'

                try:
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')

                    print(f'   :: Email Scheduled: {template} to {recipient} for confirmaccounts (delegation) {ca.get("Id")}')
                except Exception as e:
                    print(f'   :: ERROR: {e}')

                db.confirmaccounts.update_one({'Id': ca['Id'], 'delegation.delegate_id': d['delegate_id']}, {'$set': {'delegation.$.email_account_delegation_sent': True}})

        # ==============================================================================================================
        # Email: Confirm your panel
        # Recipients: Panel Manager(s)
        print(' >> PROCESSING: PANEL UPDATES...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Confirm_Your_Panel__c,
               Survey_Client__r.Customers_Client_Name__c,
               Survey_Client__r.Panel_Confirmed_By_Date__c,
               Survey_Client__r.Customer_Survey__r.Email__c,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c,
               Survey_Client__r.Customer_Survey__r.Account_Updates_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Confirm_Your_Panel__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Client__r.State__c = 'Panel Pending'
        AND   Survey_Client__r.Customer_Survey__r.Panel_Updates_Start_Date__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            last_email_sent_date = c.get('Email_Confirm_Your_Panel__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'AccountUpdateEndDate': cs.get('Account_Updates_End_Date__c'),
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
                'LiveSurveyStartDate': cs.get('Live_Survey_Start_Date__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'panel_updates'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Confirm_Your_Panel__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Panel__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Confirm your panel reminder
        # Recipients: Panel Manager(s)
        '''
        print(' >> PROCESSING: PANEL UPDATES REMINDER...')
        soql = """
        SELECT Id,
               Contact__r.Id,
               Contact__r.FirstName,
               Contact__r.Email,
               Contact__r.Email_Confirm_Your_Panel_Reminder__c,
               Survey_Client__r.Customer_Survey__r.Email__c,
               Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c,
               Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c
        FROM  Survey_Panel_Manager__c
        WHERE Email_Confirm_Your_Panel__c = True
        AND   Email_Confirm_Your_Panel_Reminder__c = False
        AND   Has_Responded__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Client__r.Customer_Survey__r.Panel_Updates_End_Date__c = TOMORROW
        AND   Survey_Client__r.State__c = 'Panel Pending'
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_spm = []
        batch_c = []
        has_been_sent = set()
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            last_email_sent_date = c.get('Email_Confirm_Your_Panel_Reminder__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'FirstName': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'PanelUpdateEndDate': cs.get('Panel_Updates_End_Date__c'),
            }
            template = 'panel_updates_reminder'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Manager__c {spm.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Panel_Manager__c {spm.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_spm.append({
                    'Id':spm.get('Id'),
                    'Email_Confirm_Your_Panel_Reminder__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Confirm_Your_Panel_Reminder__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_spm) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_spm.clear()
                batch_c.clear()

        if len(batch_spm):
            sfapi.bulk_update(sf, "Survey_Panel_Manager__c", batch_spm)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_spm.clear()
            batch_c.clear()
        '''
        # ==============================================================================================================
        # Email: Panels successfully confirmed
        # Recipients: Panel Manager(s)
        '''
        print(' >> PROCESSING: PANEL UPDATES SUCCESS...')
        confirm_panels = list(db.confirmpanel.find({'customer_survey_round_id': csr_id}, {'Id': 1, 'panel_manager_id': 1, 'panel_managers': 1, 'confirmed_panel': 1}))
        for cp in confirm_panels:
            pms_ids = [d['panel_manager_id'] for d in cp.get('panel_managers', [])]
            pm = {panel_manager['panel_manager_id']: panel_manager for panel_manager in cp.get('panel_managers', [])}

            for id, cpm in cp.get('confirmed_panel',{}).items():
                if id not in pms_ids:
                    continue

                if cpm.get('email_account_panel_update_success'):
                    continue

                sender = cpm.get('email_sender', DEFAULT_SENDER)
                recipient = pm.get(id).get('panel_manager_email')
                template_data = {
                    'Name': pm.get(id).get('panel_manager_name'),
                    'FirstRequestDate': cpm.get('live_survey_first_request_date'),
                    'SecondRequestDate': cpm.get('live_survey_second_request_date'),
                    'ThirdRequestDate': cpm.get('live_survey_third_request_date'),
                    'LastRequestDate': cpm.get('live_survey_fourth_request_date'),
                    'LiveSurveyEndDate': cpm.get('live_survey_end_date'),
                }
                template = 'panel_updates_confirmed'
                try:
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')

                    print(f'   :: Email Scheduled: {template} to {recipient} for confirmpanel {cp.get("Id")}')
                except Exception as e:
                    print(f'   :: ERROR: {e}')

                set_path  = f'confirmed_panel.{id}.email_account_panel_update_success'
                db.confirmpanel.update_one({'Id': cp.get('Id')}, {'$set': {set_path: True}})
        '''
        '''
        # ==============================================================================================================
        # Email: Survey is ready to go
        # Recipients: Account Manager
        print(" >> PROCESSING: SURVEY READY TO GO...")
        soql = """
        SELECT Id,
               Account_Manager__r.Id,
               Account_Manager__r.FirstName,
               Account_Manager__r.Email,
               Account_Manager__r.Email_Survey_Ready_To_Go__c,
               Customer_Survey__r.Id,
               Customer_Survey__r.Name,
               Customer_Survey__r.Email__c,
               Customer_Survey__r.Customer_Survey_Round__c,
               Customer_Survey__r.Live_Survey_Start_Date__c,
               Customer_Survey__r.Live_Survey_First_Request__c,
               Customer_Survey__r.Live_Survey_Second_Request__c,
               Customer_Survey__r.Live_Survey_Third_Request__c,
               Customer_Survey__r.Live_Survey_Fourth_Request__c,
               Customer_Survey__r.Live_Survey_End_Date__c
        FROM  Survey_Account_Manager__c
        WHERE Email_Survey_Ready_To_Go__c = False
        AND   Account_Manager__r.HasOptedOutOfEmail = False
        AND   Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Customer_Survey__r.Live_Survey_Start_Date__c = TOMORROW
        AND   Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch_sam = []
        batch_c = []
        has_been_sent = set()
        for sam in sf.query_all_iter(query):
            if not sam:
                continue

            c = sam.get('Account_Manager__r', {}) or {}
            cs = sam.get('Customer_Survey__r', {}) or {}

            last_email_sent_date = c.get('Email_Survey_Ready_To_Go__c', None)
            if last_email_sent_date:
                last_email_sent_date = datetime.datetime.strptime(last_email_sent_date, "%Y-%m-%d").date()

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('FirstName'),
                'ClientAreaLink': f'{settings.client_area_ui_link}/surveys/{cs.get('Customer_Survey_Round__c')}/panel',
                'FirstRequestDate': cs.get('Live_Survey_First_Request__c'),
                'SecondRequestDate': cs.get('Live_Survey_Second_Request__c'),
                'ThirdRequestDate': cs.get('Live_Survey_Third_Request__c'),
                'LastRequestDate': cs.get('Live_Survey_Fourth_Request__c'),
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
            }
            template = 'panel_updates_finalised'
            try:
                if c.get('Id') not in has_been_sent and (last_email_sent_date is None or last_email_sent_date < datetime.date.today()):
                    es.send(sender, recipient, template, template_data, {})
                    emails_scheduled.append(f'{template}-{recipient}')
                    print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Account_Manager__c {sam.get("Id")}')
                else:
                    print(f'   :: Email Skipped: {template} already sent to {recipient} today for Survey_Account_Manager__c {sam.get("Id")}')
                has_been_sent.add(c.get('Id'))
                batch_sam.append({
                    'Id': sam.get('Id'),
                    'Email_Survey_Ready_To_Go__c': True
                })
                batch_c.append({
                    'Id': c.get('Id'),
                    'Email_Survey_Ready_To_Go__c': datetime.date.today().strftime('%Y-%m-%d')
                })
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch_sam) > SF_BATCH_SIZE:
                sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
                sfapi.bulk_update(sf, "Contact", batch_c)
                batch_sam.clear()
                batch_c.clear()

        if len(batch_sam):
            sfapi.bulk_update(sf, "Survey_Account_Manager__c", batch_sam)
            sfapi.bulk_update(sf, "Contact", batch_c)
            batch_sam.clear()
            batch_c.clear()

        # ==============================================================================================================
        # Email: Take the survey
        # Recipients: Panel Member
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Survey_Client__r.Signature__c,
               Survey_Client__r.Customer_Survey__r.Email__c
        FROM  Survey_Panel_Member__c
        WHERE Survey_Email_Triggered__c = False
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch = []
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('Name'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'Signatory': sc.get('Signature__c'),
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{sc.get('Customers_Client__c')}',
            }
            template = 'live_survey_participant'
            try:
                #es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Triggered__c': True
                })

                print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch) > SF_BATCH_SIZE:
                #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
                batch.clear()

        if len(batch):
            #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
            batch.clear()

        # ==============================================================================================================
        # Email: Take the Survey Reminder 1
        # Recipients: Panel Member
        # Questions:
        # > What is ask about?
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 1...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Survey_Client__r.Signature__c,
               Survey_Client__r.Customers_Client__c,
               Survey_Client__r.Customer_Survey__r.Email__c
        FROM  Survey_Panel_Member__c
        WHERE Survey_Email_Triggered__c = True
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Email_Delivered__c = True
        AND   Survey_Email_Reminder_1__c = False
        AND   Has_Responded__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch = []
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('Name'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'AskAbout': '',
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{sc.get('Customers_Client__c')}',
            }
            template = 'live_survey_participant_reminder_1'
            try:
                #es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_1__c': True
                })

                print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch) > SF_BATCH_SIZE:
                #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
                batch.clear()

        if len(batch):
            #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
            batch.clear()

        # ==============================================================================================================
        # TODO
        # Email: Monitor response rate
        # Logic:
        # Recipients: Account Manager
        # Questions:
        # > What even is this?

        # ==============================================================================================================
        # Email: Take the Survey Reminder 2
        # Recipients: Panel Member
        # Questons:
        # > What is ask about?
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 2...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Survey_Client__r.Signature__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Email__c
        FROM  Survey_Panel_Member__c
        WHERE Survey_Email_Triggered__c = True
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Email_Delivered__c = True
        AND   Survey_Email_Reminder_2__c = False
        AND   Has_Responded__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        AND   Survey_Client__r.Customer_Survey__r.Disable_External_Communication__c = False
        """
        query = format_soql(soql, csr_id=csr_id)
        batch = []
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('Name'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'AskAbout': '',
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{sc.get('Customers_Client__c')}',
            }
            template = 'live_survey_participant_reminder_2'
            try:
                #es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_2__c': True
                })

                print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch) > SF_BATCH_SIZE:
                #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
                batch.clear()

        if len(batch):
            #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
            batch.clear()

        # ==============================================================================================================
        # Email: Take the Survey Reminder 3
        # Recipients: Panel Member
        # Questons:
        # > What is ask about?
        print(' >> PROCESSING: LIVE SURVEY PARTICIPANT Reminder 3...')
        soql = """
        SELECT Id,
               Name,
               SurveyJsId__c,
               Contact__r.Email,
               Contact__r.Name,
               Contact__r.FirstName,
               Contact__r.LastName,
               Survey_Client__r.Signature__c,
               Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
               Survey_Client__r.Customer_Survey__r.Email__c
        FROM  Survey_Panel_Member__c
        WHERE Survey_Email_Triggered__c = True
        AND   Contact__r.HasOptedOutOfEmail = False
        AND   Survey_Email_Delivered__c = True
        AND   Survey_Email_Reminder_3__c = False
        AND   Has_Responded__c = False
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey'
        AND   Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c = TODAY
        AND   Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__c = {csr_id}
        """
        query = format_soql(soql, csr_id=csr_id)
        batch = []
        for spm in sf.query_all_iter(query):
            if not spm:
                continue

            c = spm.get('Contact__r', {}) or {}
            sc = spm.get('Survey_Client__r', {}) or {}
            cs = sc.get('Customer_Survey__r', {}) or {}

            sender = cs.get('Email__c', DEFAULT_SENDER)
            recipient = c.get('Email')
            template_data = {
                'Name': c.get('Name'),
                'SurveyLink': f'{settings.survey_ui_link}/survey/{spm.get('SurveyJsId__c')}',
                'AskAbout': '',
                'LiveSurveyEndDate': cs.get('Live_Survey_End_Date__c'),
                'Signatory': sc.get('Signature__c'),
                'PrivacyPolicyLink': f'{settings.survey_ui_link}/privacy/{sc.get('Customers_Client__c')}',
            }
            template = 'live_survey_participant_reminder_3'
            try:
                #es.send(sender, recipient, template, template_data, {})
                emails_scheduled.append(f'{template}-{recipient}')
                batch.append({
                    'Id':spm.get('Id'),
                    'Survey_Email_Reminder_3__c': True
                })

                print(f'   :: Email Scheduled: {template} to {recipient} for Survey_Panel_Member__c {spm.get("Id")}')
            except Exception as e:
                print(f'   :: ERROR: {e}')

            if len(batch) > SF_BATCH_SIZE:
                #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
                batch.clear()

        if len(batch):
            #sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)
            batch.clear()

        # ==============================================================================================================
        # TODO
        # Email: Survey is closed/complete
        # Questions:
        # > When is this sent?

        print(f'# END: Customer Survey Round: {cs_name} ({csr_id})')
    '''
    print(f'END: EMAIL SCHEDULER: {len(emails_scheduled)} Emails Scheduled')

    return emails_scheduled


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--round_id', default=None, help='single survey round to run for')
    args = ap.parse_args()

    main(round_id=args.round_id)