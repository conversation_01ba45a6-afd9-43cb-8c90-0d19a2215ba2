from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql
import argparse
from lib.settings import settings
from pymongo import UpdateOne


def nullempty(s):
    return s if s else None


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_account_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)

    # get all customer client accounts by name
    all_customer_clients_by_name = {}
    soql = format_soql("where RecordType.Name = {record_type}", record_type=sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_customer_clients_by_name[account.Name.lower()] = account

    # first of, all find out which account managers chose to which account in each customer survey
    accounts_and_account_managers_by_customer_survey_id = {}
    for confirmaccount in confirm_account_collection.find({'deleted': False}):
        confirmaccount = portaldbapi.ConfirmAccounts(**confirmaccount)
        for confirmed_accounts in confirmaccount.confirmed_accounts:
            account_manager_id = confirmed_accounts.confirm_user_id
            for confirmed_account in confirmed_accounts.accounts:
                account_id = confirmed_account.account_id
                if account_id.startswith('NEW'):
                    resolved_account = all_customer_clients_by_name.get(confirmed_account.account_name.lower())
                    if not resolved_account:
                        print(f"Unable to resolve account {confirmed_account.account_name}")
                        continue
                    account_id = resolved_account.Id

                accounts_and_account_managers_by_customer_survey_id.setdefault(confirmaccount.Id, {}).setdefault(account_id, set()).add(account_manager_id)

    # get the list of panel members to process
    soql = """
        SELECT Survey_Client__c,
                Contact__c,
                Contact_Email__c,
                Contact_Type__c,
                Contact_Seniority__c,
                Contact_Title__c,
                Contact_Job_Function__c,
                Contact_Division__c,
                Survey_Panel_Manager__c,
                Has_Responded__c,
                Survey_Email_Bounced__c,
                Contact__r.Name,
                Contact__r.Email,
                Contact__r.Title,
                Contact__r.Contact_Type__c,
                Contact__r.Legacy_Division__c,
                Contact__r.Seniority__c,
                Contact__r.Location__r.Id,
                Contact__r.Location__r.Name,
                Survey_Client__r.Survey_Name__c,
                Survey_Client__r.Customers_Client__c,
                Survey_Client__r.Customer_Survey__c
        FROM  Survey_Panel_Member__c
        WHERE Survey_Client__r.Current_Round__c = TRUE AND Survey_Client__r.State__c IN ('Panel Agreed', 'Survey Ready', 'Survey Out')
    """
    final_account_managers_by_survey_client_id = {}
    final_panels_by_survey_client_id = {}
    for row in sfapi.bulk_query(sf, soql):
        pdb_panel_member = portaldbapi.ConfirmPanelMember(
                contact_id=row['Contact__c'],
                contact_name=row['Contact__r.Name'],
                contact_email=row['Contact__r.Email'],
                contact_type=row['Contact_Type__c'],
                contact_seniority=row['Contact_Seniority__c'],
                contact_job_function=row['Contact_Job_Function__c'],
                contact_job_title=row['Contact_Title__c'],
                contact_division=row['Contact_Division__c'],
                contact_location=nullempty(row['Contact__r.Location__r.Name']),
                contact_location_id=nullempty(row['Contact__r.Location__r.Id']),
                account_id=None,
                serial_non_responder=False,
                in_round=True,
                survey_panel_manager_id=row['Survey_Panel_Manager__c'],
                response_status=row['Has_Responded__c'],
                bounced_status=row['Survey_Email_Bounced__c'],
                survey_name=row['Survey_Client__r.Survey_Name__c'])

        final_panels_by_survey_client_id.setdefault(row["Survey_Client__c"], {})[row['Contact__c']] = pdb_panel_member

        final_account_managers_by_survey_client_id[row['Survey_Client__c']] = accounts_and_account_managers_by_customer_survey_id.get(row['Survey_Client__r.Customer_Survey__c'], {}).get(row['Survey_Client__r.Customers_Client__c'], [])

    # now generate updates to the confirmpanels objects
    batch = []
    for survey_client_id, panel_members in final_panels_by_survey_client_id.items():
        panel_members_json = [member.model_dump(by_alias=True) for member in panel_members.values()]
        account_managers_json = final_account_managers_by_survey_client_id.get(survey_client_id, [])

        batch.append(UpdateOne({"Id": survey_client_id},
                               {"$set": {"final_confirmed_panel": panel_members_json,
                                         "final_account_managers": list(account_managers_json)}}))

    if writeit:
        confirm_panel_collection.bulk_write(batch)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
