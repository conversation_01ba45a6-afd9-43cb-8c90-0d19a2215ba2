import argparse
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
import datetime
import requests
from pymongo import UpdateOne
from lib.settings import settings


def preload_sf_data(sf: Salesforce, customer_survey_ids, agency_ids):
    # preload all survey clients for the affected customer surveys into the cache
    soql = format_soql("WHERE Customer_Survey__c IN {customer_survey_ids}", customer_survey_ids=customer_survey_ids)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.SurveyClient, bulk=True, query_suffix=soql))

    # preload all relevant customer client relationships into the cache
    soql = format_soql("WHERE Customer_Account__c IN {agency_ids}", agency_ids=agency_ids)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.CustomerClientRelationship, bulk=True, query_suffix=soql))

    # preload all relevant panel managers into the cache
    soql = format_soql("WHERE Survey_Client__r.Customer_Survey__r.Customer__c IN {agency_ids}", agency_ids=agency_ids)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.SurveyPanelManager, bulk=True, query_suffix=soql))

    # get all relevant customer surveys
    all_customer_surveys_by_id = {}
    soql = format_soql("WHERE Id IN {customer_survey_ids}", customer_survey_ids=customer_survey_ids)
    for cs in sfapi.get_all(sf, sfapi.CustomerSurvey, bulk=True, query_suffix=soql):
        all_customer_surveys_by_id[cs.Id] = cs
        sfimport.sfid_mapping[cs.Id] = cs.Id
        sfimport.sfcache[cs.business_key2()] = cs

    # get all contacts
    all_contacts_by_id = {}
    all_contacts_by_email = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        all_contacts_by_id[contact.Id] = contact
        all_contacts_by_email[contact.Email.lower()] = contact
        sfimport.sfid_mapping[contact.Id] = contact.Id
        sfimport.sfcache[contact.business_key2()] = contact

    # get all customer client accounts by name
    all_customer_clients_by_id = {}
    all_customer_clients_by_name = {}
    soql = format_soql("where RecordType.Name = {record_type}", record_type=sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_customer_clients_by_id[account.Id] = account
        all_customer_clients_by_name[account.Name.lower()] = account
        sfimport.sfid_mapping[account.Id] = account.Id
        sfimport.sfcache[account.business_key2()] = account

    # get all agency accounts by id
    all_agencies_by_id = {}
    soql = format_soql("where Id in {agency_ids}", agency_ids=agency_ids)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_agencies_by_id[account.Id] = account
        sfimport.sfid_mapping[account.Id] = account.Id
        sfimport.sfcache[account.business_key2()] = account

    return all_contacts_by_email, all_contacts_by_id, all_customer_clients_by_name, all_customer_clients_by_id, all_customer_surveys_by_id, all_agencies_by_id


def slack_resolution_errors(messages):
    body = {"blocks": []}
    body["blocks"].append({"type": "rich_text", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sync_confirmaccounts_to_sf"}]}]})
    for m in messages:
        body["blocks"].append({"type": "rich_text", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": m}]}]})
    resp = requests.post(settings.sns_slack_resolution_error_webhook_url, json=body)
    resp.raise_for_status()


def main(customer_survey_id:str|None = None, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    # find all customersurveys that have been confirmed or need auto-confirmed and unsynced
    today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0)

    # we only want to sync confirmations when the `accounts_confirmed_by_date` has passed
    query = {
        "sfsync_date": None,
        "deleted": { "$ne": True },
        "account_managers.0": { "$exists" : True },
        "accounts_confirmed_by_date": { "$lt": today }
    }
    if customer_survey_id:
        query["Id"] = customer_survey_id

    all_confirm_accounts: list[portaldbapi.ConfirmAccounts] = []
    customer_survey_ids = set()
    agency_ids = set()

    # gather information on what we're about to sync
    for pdb_customer_survey in confirm_accounts_collection.find(query):
        pdb_customer_survey = portaldbapi.ConfirmAccounts(**pdb_customer_survey)
        all_confirm_accounts.append(pdb_customer_survey)
        customer_survey_ids.add(pdb_customer_survey.Id)
        agency_ids.add(pdb_customer_survey.client_id)

    # if there's nothing to do, exit early
    if not all_confirm_accounts:
        return

    # get necessary data from salesforce now we know we have something to do
    all_contacts_by_email, all_contacts_by_id, all_customer_clients_by_name, all_customer_clients_by_id, all_customer_surveys_by_id, all_agencies_by_id = preload_sf_data(sf, customer_survey_ids, agency_ids)

    # sync it!
    resolution_errors = []
    sf_customer_survey_updates = {}
    pdb_customer_survey_updates = {}
    for pdb_customer_survey in all_confirm_accounts:
        # merge all the accounts from multiple account managers stuff into a single account
        confirmed_accounts: dict[str, portaldbapi.ConfirmAccountsAccount] = {}
        resolution_failed = False
        if len(pdb_customer_survey.confirmed_accounts) > 0:
            # NOTE: MIG-01: this is a temporary fix to get around the fact that the confirmed_accounts_last_save_all_accounts is not always populated
            # whilst the data is being migrated
            pdb_customer_survey_all_accounts = pdb_customer_survey.confirmed_accounts_last_save_all_accounts
            if pdb_customer_survey_all_accounts is None:
                pdb_customer_survey_all_accounts = pdb_customer_survey.confirmed_accounts[0].all_accounts

            pdb_customer_survey_confirmed_accounts = pdb_customer_survey.confirmed_accounts[0]
            survey_account_manager_id = pdb_customer_survey_confirmed_accounts.confirm_user_id

            # we want to process all confirrmed accounts, but also want to attempt to resolve any NEW accounts that were 
            # added by the user but not confirmed
            user_confirmed_accounts_ids = [
                account.account_id 
                for account in pdb_customer_survey_confirmed_accounts.accounts
            ]
            user_added_accounts = [
                account.model_copy(update={'not_confirmed_resolving_required': True})
                for account in pdb_customer_survey_all_accounts
                if account.account_id.startswith('NEW') and account.account_id not in user_confirmed_accounts_ids
            ]

            # merge the confirmed accounts with the user added accounts
            all_confirmed_accounts = pdb_customer_survey_confirmed_accounts.accounts + user_added_accounts

            for confirmed_account in all_confirmed_accounts:
                # resolve account if new
                if confirmed_account.account_id.startswith('NEW'):
                    resolved_account = all_customer_clients_by_name.get(confirmed_account.account_name.lower())
                    if not resolved_account:
                        message = f"{pdb_customer_survey.customer_survey_name} ({pdb_customer_survey.Id}) non-auto-resolvable account {confirmed_account.account_name} ({confirmed_account.account_id})"
                        print(message)
                        resolution_errors.append(message)
                        resolution_failed = True
                        continue

                    # patch it
                    confirmed_account.account_id = resolved_account.Id

                # if this was not a selected account for confirmation but was resolvable, then we can skip the rest of the processing
                if confirmed_account.not_confirmed_resolving_required:
                    continue

                # merge/resolve the account
                prior_confirmed_account = confirmed_accounts.get(confirmed_account.account_id)
                if not prior_confirmed_account:
                    confirmed_account.account_manager_id = survey_account_manager_id
                    confirmed_accounts[confirmed_account.account_id] = confirmed_account
                    prior_confirmed_account = confirmed_account

                # now merge/resolve the panel managers
                prior_panel_managers = set([x.panel_manager_id for x in prior_confirmed_account.survey_panel_managers])
                for panel_manager in confirmed_account.survey_panel_managers:
                    # resolve panel manager if new
                    if panel_manager.panel_manager_id.startswith('NEW'):
                        resolved_contact = all_contacts_by_email.get(panel_manager.panel_manager_email.lower())
                        if not resolved_contact:
                            message = f"{pdb_customer_survey.customer_survey_name} ({pdb_customer_survey.Id}) {confirmed_account.account_name} ({confirmed_account.account_id}) non-auto-resolvable panel manager: {panel_manager}"
                            print(message)
                            resolution_errors.append(message)
                            resolution_failed = True
                            continue

                        # patch it
                        panel_manager.panel_manager_id = resolved_contact.Id

                    # skip if we've already got this one
                    if panel_manager.panel_manager_id in prior_panel_managers:
                        continue

                    # otherwise, add it to the list!
                    prior_confirmed_account.survey_panel_managers.append(panel_manager)
                    prior_panel_managers.add(panel_manager.panel_manager_id)

        # skip this one if its not resolvable
        if resolution_failed:
            continue

        # if there were no confirmed_accounts at all (ie no account manager did anything), we just re-use the ones we used last time and auto-confirm them
        was_auto_confirmed = False
        if not pdb_customer_survey.confirmed_accounts:
            was_auto_confirmed = True

            for account in pdb_customer_survey.accounts:
                if not account.in_round:
                    continue

                confirmed_accounts[account.account_id] = account

        # sort out survey clients for each account now we're processed them
        sf_customer_survey = all_customer_surveys_by_id[pdb_customer_survey.Id]

        for confirmed_account in confirmed_accounts.values():
            sf_customer_client = all_customer_clients_by_id[confirmed_account.account_id]

            sc = sfimport.get_or_create_cached_survey_client(sf_customer_survey, sf_customer_client, 'Panel Pending', confirmed_account.survey_name)
            sc.SignatoryId = confirmed_account.signatory_id  # NOTE: this will not affect the SC if it already exists

            survey_panel_manager_id = None
            for survey_panel_manager in confirmed_account.survey_panel_managers:
                sf_contact = all_contacts_by_id[survey_panel_manager.panel_manager_id]
                sfimport.get_or_create_cached_survey_panel_manager(sc, sf_contact)

                if not survey_panel_manager_id:
                    survey_panel_manager_id = survey_panel_manager.panel_manager_id

            # make sure there's a CCR between agency and client
            agency_account = all_agencies_by_id[sf_customer_survey.CustomerId]
            ccr = sfimport.get_or_create_cached_customer_client_relationship(agency_account, sf_customer_client, 'Client')

            # NOTE: following only affects the CCR if it doesn't already exist
            ccr.SurveyPanelManagerId = survey_panel_manager_id
            ccr.StartDate = datetime.date.today()

        # figure out the updates we need to make
        final_confimed_accounts = [x.model_dump(by_alias=True) for x in confirmed_accounts.values()]

        sf_customer_survey_updates[pdb_customer_survey.Id] = {"Id": pdb_customer_survey.Id,
                                                              "State__c": "Survey Clients Agreed",
                                                              "Accounts_Auto_Confirmed__c": was_auto_confirmed}
        pdb_customer_survey_updates[pdb_customer_survey.Id] = UpdateOne({"Id": pdb_customer_survey.Id},
                                                                        {"$set": {"sfsync_date": datetime.datetime.now(datetime.UTC),
                                                                                  "confirm_status": True,
                                                                                  "auto_confirmed": was_auto_confirmed,
                                                                                  "confirm_date": datetime.datetime.now(datetime.UTC),
                                                                                  'final_confirmed_accounts': final_confimed_accounts}})

    if writeit:
        # commit to salesforce
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_customer_client_relationships, sfapi.CustomerClientRelationship)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_clients, sfapi.SurveyClient)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_managers, sfapi.SurveyPanelManager)

        # update the customer surveys to indicate the accounts are agreed
        if sf_customer_survey_updates:
            for status in sfapi.bulk_update(sf, "Customer_Survey__c", list(sf_customer_survey_updates.values())):
                sfapi.detect_bulk2_errors(status)

        # reflect that back onto the portaldb confirm accounts now its all written to sf
        if pdb_customer_survey_updates:
            confirm_accounts_collection.bulk_write(list(pdb_customer_survey_updates.values()))

    # let us know if something needs our attention!
    if writeit:
        if resolution_errors:
            print("Resolution failures occurred, manual intervention required")
            # NOTE: might get rate-limited here, so need to keep an eye on this
            for i in range(0, len(resolution_errors), 10):
                slack_resolution_errors(resolution_errors[i:i+10])

    else:
        print('dry run, nothing written to SF')
        print('new ccrs', len(sfimport.new_customer_client_relationships))
        print('new survey clients', len(sfimport.new_survey_clients))
        print('new survey panel managers', len(sfimport.new_survey_panel_managers))
        print('patched customer surveys', len(sf_customer_survey_updates))
        print('resolution errors', len(resolution_errors))


def lambda_handler(event, context):
    main(
        customer_survey_id=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_id', default=None, help='single customer survey to run for')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.customer_survey_id, args.writeit)
