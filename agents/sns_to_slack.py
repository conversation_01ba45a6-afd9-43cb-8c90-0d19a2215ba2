from typing import Any
import argparse
from lib.settings import settings
import requests
import json

def main(record):
    message = json.dumps(record)

    # try and treat as an SNS message
    try:
        message = record["Sns"]["Message"]
        jmessage = json.loads(message)
        message = jmessage['AlarmName']
    except:
        pass

    # post it to slack
    body = {
            "blocks": [
                {
                    "type": "rich_text",
                    "elements": [
                        {
                            "type": "rich_text_section",
                            "elements": [
                                {
                                    "type": "text",
                                    "text": message,
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    resp = requests.post(settings.sns_slack_webhook_url, json=body)
    resp.raise_for_status()


def lambda_handler(event, _):
    if event:
        for record in event["Records"]:
            main(record)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
