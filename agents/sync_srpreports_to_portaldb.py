import time
import datetime
import argparse
import boto3
from itertools import islice
from pymongo import UpdateOne
from simple_salesforce import format_soql, SalesforceGeneralError
from lib import sfapi, portaldbapi, sfimport
from lib.settings import settings


SF_BATCH_SIZE = 3000


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch


def generate_customer_survey_rounds_lookup(sf):
    customer_survey_rounds = {}

    soql = """
    SELECT Id,
           Name
    FROM   Customer_Survey_Round__c
    """

    for csr in sfapi.bulk_query(sf, soql):
        customer_survey_rounds[csr['Id']] = csr['Name']

    return customer_survey_rounds


def get_contacts(sf):
    print('>> get_contacts')

    contacts = set()
    soql = """
    SELECT Id
    FROM  Contact
    WHERE Account.RecordType.Name IN {record_type}
    AND   Last_File_Upload__c = LAST_N_DAYS:2
    """

    query = format_soql(soql, record_type = (sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))

    for contact in sfapi.bulk_query(sf, query):
        contacts.add(contact['Id'])

    return list(contacts)


def get_contact_content_document_links(sf, contacts:list = []):
    print('>> get_contact_content_document_links')

    content_documents = {}

    for batch in batch_iterable(contacts, SF_BATCH_SIZE):
        soql = """
        SELECT ContentDocumentId,
               LinkedEntityId,
               ContentDocument.LatestPublishedVersionId
        FROM   ContentDocumentLink
        WHERE  LinkedEntityId IN {batch}
        """

        query = format_soql(soql, batch=batch)

        for content_document in sfapi.bulk_query(sf, query):
            if content_document['ContentDocumentId'] not in content_documents:
                content_documents[content_document['ContentDocumentId']] = {}

            content_documents[content_document['ContentDocumentId']] = content_document

    return content_documents


def get_contact_content_document_links_deleted(sf, portaldb_collection):
    content_document_ids = set()

    published_content_documents = portaldb_collection.find({'published': True, 'deleted': {'$ne': True}}, {'_id': 0, 'Id': 1})
    published_content_document_ids = [doc['Id'] for doc in published_content_documents]

    for batch in batch_iterable(published_content_document_ids, SF_BATCH_SIZE):
        soql = """
        SELECT Id
        FROM   ContentDocument
        WHERE  Id IN {batch}
        AND    IsDeleted = False
        """

        query = format_soql(soql, batch=batch)

        for content_document in sfapi.bulk_query(sf, query):
            content_document_ids.add(content_document['Id'])

    deleted_content_document_ids = set(published_content_document_ids) - content_document_ids

    return list(deleted_content_document_ids)


def get_content_versions(sf, content_documents:dict = {}, customer_survey_rounds:dict = {}):
    print('>> get_content_versions')

    content_versions = {}
    content_version_ids = [content_document['ContentDocument.LatestPublishedVersionId'] for content_document in content_documents.values()]

    for batch in batch_iterable(content_version_ids, SF_BATCH_SIZE):
        #NOTE: I removed the "Share_in_Client_Area__c = True" clause so we can adhere to when a report is "unpublished"
        #      and we need to remove it from the portal db. But we should keep an eye on speed/performance of this query.
        soql = """
        SELECT Id,
               Title,
               FileExtension,
               ContentDocumentId,
               LastModifiedDate,
               Customer_Survey_Round__c,
               Share_in_Client_Area__c,
               Report_Type__c
        FROM   ContentVersion
        WHERE  Id IN {batch}
        AND    IsLatest = True
        AND    IsDeleted = False
        """

        query = format_soql(soql, batch=batch)

        for content_version in sfapi.bulk_query(sf, query):
            content_document = content_documents.get(content_version['ContentDocumentId'])
            if content_document:
                content_document['ContentVersionId'] = content_version['Id']
                content_document['Title'] = content_version['Title']
                content_document['FileExtension'] = content_version['FileExtension']
                content_document['LastModifiedDate'] = content_version['LastModifiedDate']
                content_document['Customer_Survey_Round__c'] = content_version['Customer_Survey_Round__c']
                content_document['Customer_Survey_Round_Name__c'] = customer_survey_rounds.get(content_version['Customer_Survey_Round__c'])
                content_document['Share_in_Client_Area__c'] = content_version['Share_in_Client_Area__c']
                content_document['Report_Type__c'] = content_version['Report_Type__c']

                content_versions[content_version['ContentDocumentId']] = content_document

    return content_versions


def upload_content_version_to_s3(sf, s3, content_versions: dict = {}, s3_bucket_keys: set = set(), writeit: bool = False):
    print('>> upload_content_version_to_s3')
    print(f'Number of files: {len(content_versions.keys())}')

    supported_file_extensions = {
        "pdf": "application/pdf",
        "xls": "application/vnd.ms-excel",
        "xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "ppt": "application/vnd.ms-powerpoint",
        "pptx": "application/vnd.openxmlformats-officedocument.presentationml.presentation"
    }

    def fetch_from_sf_with_retry(url, max_retries=3, backoff_base=3):
        for attempt in range(max_retries):
            try:
                response = sf._call_salesforce('GET', url)
                if response.ok:
                    return response
                else:
                    print(f"Salesforce response not OK: {response.status_code}")
            except SalesforceGeneralError as e:
                print(f"SalesforceGeneralError on attempt {attempt + 1}: {e}")
            if attempt < max_retries - 1:
                wait_time = backoff_base ** attempt
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
        print(f"FAILED: all retries exhausted for {url}")
        return None

    for idx, content_document in enumerate(content_versions.values()):
        print(f'uploading file {idx + 1}')

        file_extension = content_document.get("FileExtension", "").lower()
        if file_extension not in supported_file_extensions:
            print(f"SKIPPED: unsupported file type '{file_extension}' for {content_document['ContentDocumentId']}")
            continue

        s3_filename = f"{content_document['LinkedEntityId']}-{content_document['ContentDocumentId']}-{content_document['ContentVersionId']}.{file_extension}"
        if s3_filename in s3_bucket_keys:
            print(f" :: skipping file {s3_filename} - already exists in S3 bucket")
            content_document['s3_filename'] = s3_filename
            continue

        sf_url = f"{sf.base_url}/sobjects/ContentVersion/{content_document['ContentVersionId']}/VersionData"
        sf_response = fetch_from_sf_with_retry(sf_url)
        if not sf_response:
            print(f"FAILED: could not download {sf_url}")
            continue

        if writeit:
            s3.put_object(Bucket=settings.SF_SRP_REPORTS_BUCKET, Key=s3_filename, Body=sf_response.content, ContentType=supported_file_extensions[file_extension])
        content_document['s3_filename'] = s3_filename

    return content_versions


def write_to_portaldb(portaldb_collection, content_versions:dict = {}, writeit:bool = False):
    print('>> write_to_portaldb')

    db_batch= []
    sync_date = datetime.datetime.now(tz=datetime.UTC)

    for content_document in content_versions.values():
        if 's3_filename' not in content_document:
            print(f"SKIPPED DB WRITE: no s3_filename for {content_document['ContentDocumentId']}")
            continue

        report = portaldbapi.SrpReport(
            Id=content_document['ContentDocumentId'],
            title=content_document['Title'],
            report_type=content_document['Report_Type__c'],
            published=content_document['Share_in_Client_Area__c'],
            published_date=sync_date,
            last_modified_date=content_document['LastModifiedDate'],
            contact_id=content_document['LinkedEntityId'],
            version_id=content_document['ContentVersionId'],
            customer_survey_round_id=content_document['Customer_Survey_Round__c'],
            customer_survey_round_title=content_document['Customer_Survey_Round_Name__c'],
            s3_filename=content_document['s3_filename'],
            _lastsynced=sync_date,
        )
        db_batch.append(UpdateOne({'Id': content_document['ContentDocumentId']}, {'$set': report.model_dump(by_alias=True)}, upsert=True))

    if writeit and db_batch:
        portaldb_collection.bulk_write(db_batch)


def write_to_portaldb_deleted(portaldb_collection, deleted_content_document_ids:list = [], writeit:bool = False):
    print('>> write_to_portaldb_deleted')

    db_batch = []

    for content_document_id in deleted_content_document_ids:
        db_batch.append(UpdateOne({'Id': content_document_id}, {'$set': {'deleted': True}}, upsert=False))

    if writeit and db_batch:
        portaldb_collection.bulk_write(db_batch)


def main(writeit:bool = False):
    start_time_all = time.time()
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    s3_session = boto3.Session()
    s3 = s3_session.client('s3')
    portaldb = portaldbapi.DocumentDB()
    portaldb_collection = portaldbapi.get_sf_collection(portaldb, 'srpreports')

    s3_bucket_keys = set()

    # grab name (s3 keys) of all files uploaded to date
    # we'll use this to determine if we need to upload a new file
    s3_result = s3.list_objects_v2(Bucket=settings.SF_SRP_REPORTS_BUCKET)
    s3_bucket_keys |= set([k['Key'] for k in s3_result.get('Contents', [])])
    while s3_result['IsTruncated']:
        s3_result = s3.list_objects_v2(Bucket=settings.SF_SRP_REPORTS_BUCKET, ContinuationToken=s3_result['NextContinuationToken'])
        s3_bucket_keys |= set([k['Key'] for k in s3_result.get('Contents', [])])

    # get all customer survey rounds to generate a lookup for the id->title
    customer_survey_rounds = generate_customer_survey_rounds_lookup(sf)

    # get all contacts that:
    # 1. belong to a CRC customer
    # 2. have had a file uploaded/modified in the last 2 days
    contacts = get_contacts(sf)

    # get all content document links (basically each file) for each contact
    content_documents = get_contact_content_document_links(sf, contacts)

    # get the latest content version for each content document
    content_versions = get_content_versions(sf, content_documents, customer_survey_rounds)

    # upload each content version to s3
    content_versions_with_s3_metadata = upload_content_version_to_s3(sf,
                                                                     s3,
                                                                     content_versions,
                                                                     s3_bucket_keys,
                                                                     writeit)

    # write the content versions metadatato the portal DB
    write_to_portaldb(portaldb_collection,
                      content_versions_with_s3_metadata,
                      writeit)

    # get all published content documents that have been flagged as deleted
    deleted_content_documents = get_contact_content_document_links_deleted(sf, portaldb_collection)

    # update the portal DB to flag the deleted content documents
    write_to_portaldb_deleted(portaldb_collection,
                              deleted_content_documents,
                              writeit)

    elapsed_time_all = time.time() - start_time_all
    print(f"TOTAL RUN TIME: {elapsed_time_all:.4f} seconds")

def lambda_handler(event, _):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(writeit=args.writeit)
