import argparse
from lib import sfapi, portaldbapi
from lib.settings import settings


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    # get all active panel managers
    soql = """
        SELECT  Id,
                Account_Manager__c,
                Customer_Survey__c,
                Has_Responded__c
        FROM  Survey_Account_Manager__c
        WHERE Customer_Survey__r.Current_Round__c = TRUE AND Customer_Survey__r.Stage__c = 'Survey Setup'
    """
    customer_survey_ids = set()
    account_managers_by_customer_survey_id = {}
    for row in sfapi.bulk_query(sf, soql):
        customer_survey_id = row["Customer_Survey__c"]
        customer_survey_ids.add(customer_survey_id)
        account_managers_by_customer_survey_id.setdefault(customer_survey_id, {})[row['Account_Manager__c']] = row

    # now figure out SF updates for people who have responded
    batch = []
    for pdb_confirm_account in confirm_accounts_collection.find({'Id': {'$in': list(customer_survey_ids)}}):
        sf_account_managers = account_managers_by_customer_survey_id.get(pdb_confirm_account['Id'], {})
        if not sf_account_managers:
            continue

        for accounts in pdb_confirm_account.get('confirmed_accounts', []):
            am_contact_id = accounts.get('confirm_user_id')
            sf_am = sf_account_managers.get(am_contact_id)
            if not sf_am:
                continue

            if sf_am['Has_Responded__c'].lower() in {'false', None, ''}:
                batch.append({'Id': sf_am['Id'], 'Has_Responded__c': True})

    if batch and writeit:
        sfapi.bulk_update(sf, 'Survey_Account_Manager__c', batch)


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
