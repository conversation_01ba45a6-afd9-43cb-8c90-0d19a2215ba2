from lib import sfapi
import argparse
from lib.settings import settings
import boto3
import re
from PIL import Image
from io import BytesIO

SF_RESOURCE_URL = 'file.force.com'


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    s3_session = boto3.Session()
    s3 = s3_session.client('s3')
    contact_count = 0
    url_not_in_signature_count = 0
    contacts_with_signature_count = 0
    already_uploaded_count = 0
    ref_upload_count = 0
    main_upload_count = 0
    image_download_fail_count = 0

    # list all files in the bucket
    all_bucket_keys = set()
    s3_result = s3.list_objects_v2(Bucket=settings.SF_SIGNATURES_BUCKET)
    all_bucket_keys |= set([k['Key'] for k in s3_result.get('Contents', [])])
    while s3_result['IsTruncated']:
        s3_result = s3.list_objects_v2(Bucket=settings.SF_SIGNATURES_BUCKET, ContinuationToken=s3_result['NextContinuationToken'])
        all_bucket_keys |= set([k['Key'] for k in s3_result.get('Contents', [])])

    # regex to find the `refid` for each image link
    img_re = re.compile(r'refid=([0-9A-Za-z]+)')

    # get the list of contacts
    all_used_s3_keys = set()
    soql = """
        SELECT  Id,
                Signature__c
        FROM  Contact
    """
    for row in sfapi.bulk_query(sf, soql):
        signature = row['Signature__c']
        if not signature:
            continue
        if SF_RESOURCE_URL not in signature:
            url_not_in_signature_count += 1
            continue
        contacts_with_signature_count += 1

        main_image_written_for_contact = False
        for refid in img_re.findall(signature):
            ref_jpg_filename = f"{row['Id']}/{refid}.jpg"
            main_jpg_filename = f"{row['Id']}.jpg"
            all_used_s3_keys.add(ref_jpg_filename)

            # continue with png for now
            ref_png_filename = f"{row['Id']}/{refid}.png"
            main_png_filename = f"{row['Id']}.png"
            all_used_s3_keys.add(ref_png_filename)

            # if they 're already in the bucket, skip
            if ref_png_filename in all_bucket_keys and \
                main_png_filename in all_bucket_keys and \
                ref_jpg_filename in all_bucket_keys and \
                main_jpg_filename in all_bucket_keys:
                already_uploaded_count += 1
                continue

            # ok, grab image from salesforce
            url = f"{sf.base_url}/sobjects/Contact/{row['Id']}/richTextImageFields/Signature__c/{refid}"
            resp = sf._call_salesforce('GET', url)
            if not resp.ok:
                print(f"Failed to download {url}")
                image_download_fail_count
                continue

            # convert to a JPEG
            im = Image.open(BytesIO(resp.content))
            im = im.convert("RGB")
            png_temp = BytesIO()
            im.save(png_temp, format="PNG")
            jpg_temp = BytesIO()
            im.save(jpg_temp, format="JPEG")

            # save the image to the bucket if not already present
            if ref_jpg_filename not in all_bucket_keys:
                if writeit:
                    s3.put_object(Bucket=settings.SF_SIGNATURES_BUCKET, Key=ref_jpg_filename, Body=jpg_temp.getvalue())

                    if ref_png_filename not in all_bucket_keys:
                        s3.put_object(Bucket=settings.SF_SIGNATURES_BUCKET, Key=ref_png_filename, Body=png_temp.getvalue())  # for now
                    ref_upload_count += 1
            else:
                main_image_written_for_contact = True  # if the ref image was already there, we've already written the main image previously

            # save the main image
            if main_jpg_filename not in all_bucket_keys or not main_image_written_for_contact:
                if writeit:
                    s3.put_object(Bucket=settings.SF_SIGNATURES_BUCKET, Key=main_jpg_filename, Body=jpg_temp.getvalue())

                    if main_png_filename not in all_bucket_keys:
                        s3.put_object(Bucket=settings.SF_SIGNATURES_BUCKET, Key=main_png_filename, Body=png_temp.getvalue()) # for now
                    main_upload_count += 1
                main_image_written_for_contact = True

    print(f"Contacts: {contact_count}")
    print(f"Contacts with signature: {contacts_with_signature_count}")
    print(f"Already uploaded: {already_uploaded_count}")
    print(f"Image download failures: {image_download_fail_count}")
    print(f"Ref images uploaded: {ref_upload_count}")
    print(f"Main images uploaded: {main_upload_count}")


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)

