""" Agent to send an email using AWS SES. An email message for a single recipient is added to an SQS queue 
    that triggers this agent. The agent will send the email using AWS SES and log the email in the portaldb.

DATA:
    FROM: AWS SQS (crc-agent-send_email_to_ses-{stack}-queue)
          S3 (banner and signature attachments)
    TO: AWS SES
        PortalDB (email log)
"""
from __future__ import annotations
import argparse
import boto3
import datetime
import jinja2
import json
import requests
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from io import BytesIO
from lib.settings import settings
from pydantic import BaseModel, EmailStr
from sendgrid import SendGridAPIClient
from lib import portaldbapi
from lib.emailapi import EmailServiceConfig


DEFAULT_FROM_NAME = "VerityRI"


class Email(BaseModel):
    from_email: EmailStr
    from_name: str|None = None
    to_email: EmailStr
    template_id: str
    template_data: dict[str, str|None]|None = {}
    custom_args: dict[str, str|None]|None = {}
    sent_at: int|None = None


def get_attachment_from_s3(attachment: str, s3_bucket: str):
    attachment_dict: dict[str, str] = json.loads(attachment)

    s3 = boto3.client('s3')
    response = s3.get_object(Bucket=s3_bucket, Key=attachment_dict['key'])
    response_body = response['Body'].read()

    with BytesIO(response_body) as f:
        content = f.read()

    msg_att = MIMEApplication(content)
    msg_att.add_header('Content-Disposition',attachment_dict['disposition'], filename=attachment_dict['key'])
    msg_att.add_header('Content-Type', attachment_dict['file_type'])
    msg_att.add_header('Content-ID', attachment_dict['content_id'])
    return msg_att


def _make_template(body):
    body = body.replace('{{{', '{{').replace('}}}', '}}')
    template = jinja2.Template(body)
    return template


def get_template(template_id: str) -> None:
    # get the template from S3
    s3 = boto3.client('s3')
    s3_key = f'{template_id}.json'
    response = s3.get_object(Bucket=settings.SG_TEMPLATE_CACHE_BUCKET, Key=s3_key)
    response_body = response['Body'].read().decode('utf-8')
    active_version = json.loads(response_body)

    body_html_template = _make_template(active_version['html_content'])
    body_text_template = _make_template(active_version['plain_content'])
    subject_template = _make_template(active_version['subject'])

    return body_html_template, body_text_template, subject_template


def send_ses_email(ses,
                sender: str,
                to_email: str,
                body_html_template,
                body_text_template,
                subject_template,
                template_data: dict[str, str],
                custom_args: dict[str, str],
                banner_attachment: str|None,
                signature_attachment: str|None) -> str:

    # correct any double slashes in URLs
    for k, v in template_data.items():
        if v.startswith('https://'):
            template_data[k] = v.replace('survey.thereferralrating.com//', 'survey.thereferralrating.com/')

    # render the body
    body_html = body_html_template.render(**template_data).strip()
    body_text = body_text_template.render(**template_data).strip()
    subject = subject_template.render(**template_data).strip()

    # define initial message
    msg = MIMEMultipart('mixed')
    msg['Subject'] = subject
    msg['From'] = sender
    msg['To'] = to_email

    # Add the text and HTML parts to the child container.
    msg_body = MIMEMultipart('alternative')
    msg_body.attach(MIMEText(body_text.encode('utf-8'), 'plain', 'utf-8'))
    msg_body.attach(MIMEText(body_html.encode('utf-8'), 'html', 'utf-8'))
    msg.attach(msg_body)

    if banner_attachment:
        try:
            banner = get_attachment_from_s3(banner_attachment, settings.SF_BANNERS_BUCKET)
            msg.attach(banner)
        except Exception as e:
            print(f"Error fetching banner attachment from S3: {e}")
            raise Exception('Error fetching banner attachment from S3')

    if signature_attachment:
        try:
            signature = get_attachment_from_s3(signature_attachment, settings.SF_SIGNATURES_BUCKET)
            msg.attach(signature)
        except Exception as e:
            print(f"Error fetching signature attachment from S3: {e}")
            raise Exception('Error fetching signature attachment from S3')

    tags = []
    for key, value in custom_args.items():
        tag = {'Name': key, 'Value': value}
        tags.append(tag)

    # send it
    response = ses.send_email(
        FromEmailAddress=sender,
        Destination={
            'ToAddresses': [to_email],
        },
        Content={
            'Raw': {
                'Data': msg.as_bytes()
            },
        },
        EmailTags=tags,
        ConfigurationSetName=settings.ses_configuration_set_name,
    )
    return response['MessageId']


def main(from_email: str, 
         to_email: str, 
         template_id: str, 
         template_data: str, 
         custom_args: str, 
         banner_attachment: str|None, 
         signature_attachment: str|None, 
         from_name: str|None,
         portaldb_record_map: str|None=None) -> str:

    ses = boto3.client('sesv2', aws_access_key_id=settings.ses_access_key, aws_secret_access_key=settings.ses_secret_access_key)
    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)
    es: EmailServiceConfig = EmailServiceConfig(**db.config.find_one({"_id": "email"}))
    collection: portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_EMAILLOG_COLLECTION)

    try:
        # check if email service is enabled
        if not es.enabled:
            raise Exception('Email service is disabled')

        # check if there is restriced recipient domains enabled and the `to_email` is in the list
        if es.restricted_domains and to_email.split('@')[1] not in es.restricted_domains:
            raise Exception('Domain is not permitted to receive emails')
        
        # check if there is restriced sender domains enabled and the `from_email` is in the list
        if es.restricted_sender_domains and from_email.split('@')[1] not in es.restricted_sender_domains:
            raise Exception(f'Domain is not permitted to send emails: {from_email} {from_email.split("@")[1]} {', '.join(es.restricted_sender_domains)} {to_email} {template_id}')

        # check if the template supplied exists
        if template_id not in [id for _, id  in es.templates.items()]:
            raise Exception(f"Template ID {template_id} does not exist")

        body_html_template, body_text_template, subject_template = get_template(template_id)

        # convert template data to dict
        template_data_dict: dict[str, str] = json.loads(template_data)

        # convert custom args to dict
        custom_args_dict: dict[str, str] = json.loads(custom_args)

        # set default from name if not provided
        if not from_name:
            from_name = DEFAULT_FROM_NAME
        
        # build sender string
        sender = f"{from_name} <{from_email}>"

        # create instance of Email for validation
        email: Email = Email(
            from_email=from_email,
            from_name=from_name,
            to_email=to_email,
            template_id=template_id,
            template_data=template_data_dict,
            custom_args=custom_args_dict
        )

        # attempt to send the email
        status_code:int = 0
        message_id: str = ''
        error_message: str = ''
        message_id = send_ses_email(
            ses,
            sender,
            to_email,
            body_html_template,
            body_text_template,
            subject_template,
            template_data_dict,
            custom_args_dict,
            banner_attachment,
            signature_attachment
        )
        print(f"Recipient: {to_email}")
        print(f"Template ID: {template_id}")
        print(f"Message ID: {message_id}")
    except Exception as e:
       error_message = str(e)
       print("Error: {0}".format(e))

    # log the email in the portaldb
    log = {
        'from_email': from_email,
        'from_name': from_name,
        'sender': sender,
        'recipient': to_email,
        'template': template_id,
        'template_data': template_data_dict,
        'event_data': custom_args_dict,
        'send_at': None,
        'ses_message_id': message_id,
        'timestamp': datetime.datetime.now(),
    }
    if error_message:
        log['error'] = error_message

    if portaldb_record_map:
        log['portaldb_record_map'] = portaldb_record_map
    collection.insert_one(log)

    return str(status_code)


def lambda_handler(event, _) -> None:
    if event:
        for record in event["Records"]:
            from_email: str = record['messageAttributes']['fromEmail']['stringValue']
            to_email: str = record['messageAttributes']['toEmail']['stringValue']
            template_id: str = record['messageAttributes']['templateId']['stringValue']
            template_data: str = record['messageAttributes']['templateData']['stringValue']
            custom_args: str = record['messageAttributes']['customArgs']['stringValue']
            banner_attachment: str = record['messageAttributes'].get('banner_attachment', {}).get('stringValue', None)
            signature_attachment: str = record['messageAttributes'].get('signature_attachment', {}).get('stringValue', None)
            fromName: str|None = record['messageAttributes'].get('fromName', {}).get('stringValue', None)
            portaldbRecordMap: str = record['messageAttributes'].get('portaldbRecordMap', {}).get('stringValue', None)
            main(
                from_email,
                to_email,
                template_id,
                template_data,
                custom_args,
                banner_attachment,
                signature_attachment,
                fromName,
                portaldbRecordMap
            )


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--from_email", required=True, help="Email address to send the email from")
    ap.add_argument("--to_email", required=True, help="Email address to send the email to")
    ap.add_argument("--template_id", required=True, help="Sendgrid dynamic template ID")
    ap.add_argument("--template_data", required=True, help="Sendgrid dynamic template data for insertion")
    ap.add_argument("custom_args", help="Metadata to embed into the email for analytics")
    ap.add_argument("banner_attachment", help="Banner to include in the email")
    ap.add_argument("signature_attachment", help="Signature to include in the email")
    ap.add_argument("from_name", help="Name to display in the email")
    ap.add_argument("portaldb_record_map", help="Mapping ID of record to match log too")
    args = ap.parse_args()

    main(
        args.from_email,
        args.to_email,
        args.template_id,
        args.template_data,
        args.custom_args,
        args.banner_attachment,
        args.signature_attachment,
        args.from_name,
        args.portaldb_record_map
    )
