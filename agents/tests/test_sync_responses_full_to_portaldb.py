import copy
from datetime import datetime, date, UTC
import unittest
from unittest.mock import patch, MagicMock
import agents.sync_responses_full_to_portaldb as lib_sync

class TestSyncResponsesFuillToPortaldb(unittest.TestCase):
    def setUp(self):
        pass

    @patch('agents.sync_responses_full_to_portaldb.today')
    def test_get_start_date(self, mock_today):
        mock_today.return_value = datetime(2023, 1, 11, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2022, 10, 1))

        mock_today.return_value = datetime(2023, 2, 28, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2022, 10, 1))

        mock_today.return_value = datetime(2023, 3, 31, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2022, 10, 1))

        mock_today.return_value = datetime(2023, 5, 31, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2023, 1, 1))

        mock_today.return_value = datetime(2023, 6, 30, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2023, 1, 1))

        mock_today.return_value = datetime(2023, 8, 31, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2023, 4, 1))

        mock_today.return_value = datetime(2023, 10, 31, tzinfo=UTC).date()
        result = lib_sync.get_start_date()
        self.assertEqual(result, date(2023, 7, 1))


    @patch('agents.sync_responses_full_to_portaldb.sfapi.get_sf_connection')
    @patch('agents.sync_responses_full_to_portaldb.sfapi.bulk_query')
    def test_get_recent_customer_survey_rounds(self, mock_bulk_query, mock_get_sf_connection):
        mock_sf = MagicMock()
        mock_get_sf_connection.return_value = mock_sf
        mock_bulk_query.return_value = [
            {'Id': 'CS1', 'Name': 'CS1 Name', 'Live_Survey_Start_Date__c': '2023-01-01', 'Customer_Survey_Round__c': 'CSR1', 'Customer_Survey_Round__r.Name': 'Round 1'},
            {'Id': 'CS2', 'Name': 'CS2 Name', 'Live_Survey_Start_Date__c': '2023-02-01', 'Customer_Survey_Round__c': 'CSR2', 'Customer_Survey_Round__r.Name': 'Round 2'},
        ]

        result = lib_sync.get_recent_customer_survey_rounds(mock_sf)
        self.assertEqual(result, ['CSR1', 'CSR2'])

        mock_bulk_query.return_value = []
        result = lib_sync.get_recent_customer_survey_rounds(mock_sf)
        self.assertEqual(result, [])


    @patch('agents.sync_responses_full_to_portaldb.sfapi.get_sf_connection')
    @patch('agents.sync_responses_full_to_portaldb.get_recent_customer_survey_rounds')
    def test_main(self, mock_get_recent, mock_get_sf_connection):
        mock_sf = MagicMock()
        mock_get_sf_connection.return_value = mock_sf
        mock_get_recent.return_value = ['CSR1', 'CSR2']

        with patch('agents.sync_responses_full_to_portaldb.lib_sync_responses.main') as mock_sync:
            lib_sync.main(writeit=True)
            mock_sync.assert_called_once_with(customer_survey_round_ids='CSR1,CSR2', survey_panel_member_id=None, current_round=False, writeit=True, forceall=True)

        # Test with no recent rounds
        mock_get_recent.return_value = []
        with patch('agents.sync_responses_full_to_portaldb.lib_sync_responses.main') as mock_sync:
            lib_sync.main(writeit=True)
            mock_sync.assert_not_called()
