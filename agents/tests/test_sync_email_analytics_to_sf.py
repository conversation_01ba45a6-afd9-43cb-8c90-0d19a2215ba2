import copy
import unittest
from unittest.mock import patch, MagicMock
import agents.sync_email_analytics_to_sf as sync_email_analytics_to_sf

class TestSyncEmailAnalyticsToSf(unittest.TestCase):
    def setUp(self):
        pass


    def test_fetch_email_events(self):
        db_mock = MagicMock()
        sg_collection = db_mock.sendgridevents
        ses_collection = db_mock.sesevents
        sg_collection.find.return_value = [
            dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='abc'),
            dict(email='<EMAIL>', event='delivered', template='live_survey_participant_reminder_1', sg_machine_open=None, survey_panel_member_id='abc'),
            dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='def', survey_panel_member_ids=['def','ghi']),
        ]
        ses_collection.find.return_value = [
            dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='jkl'),
            dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='mno', survey_panel_member_ids=['mno','pqr']),
        ]

        result = sync_email_analytics_to_sf.fetch_email_events(db_mock, starttime=None, endtime=None, survey_panel_member_id=None)
        expected_result = {
            'abc': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='abc'),
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant_reminder_1', sg_machine_open=None, survey_panel_member_id='abc'),
            ],
            'def': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='def', survey_panel_member_ids=['def','ghi']),
            ],
            'ghi': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='def', survey_panel_member_ids=['def','ghi']),
            ],
            'jkl': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='jkl'),
            ],
            'mno': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='mno', survey_panel_member_ids=['mno','pqr']),
            ],
            'pqr': [
                dict(email='<EMAIL>', event='delivered', template='live_survey_participant', sg_machine_open=None, survey_panel_member_id='mno', survey_panel_member_ids=['mno','pqr']),
            ],
        }
        self.assertDictEqual(result, expected_result)
