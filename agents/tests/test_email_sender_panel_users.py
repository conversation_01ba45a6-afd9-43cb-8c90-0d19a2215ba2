import copy
from datetime import datetime
import unittest
from unittest.mock import call, patch, MagicMock
import agents.email_sender_panel_users as email_sender_panel_users


class TestEmailSenderPanelUsers(unittest.TestCase):
    def setUp(self):
        self.base_email = {
            '_id': 'test_id',
            'survey_panel_member_id': 'test_pm_id',
            'survey_panel_member_ids': ['test_pm_id'],
            'sender_email': '<EMAIL>',
            'sender_name': '<PERSON>loggs',
            'recipient': '<EMAIL>',
            'template_name': 'test_template_name',
            'template_data': {},
            'template_metadata': {},
            'scheduled_date_utc': datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None),
            'language': 'en',
            'vertical': 'adv',
            'banner_attachment': None,
            'signature_attachment': None,
            'sf_spm_field': 'Survey_Email_Reminder_2__c',
            'email_service_provider': 'ses',
        }
        self.base_pms = {
            'test_pm_id': {
                'Survey_Email_Triggered__c': False,
                'Survey_Email_Reminder_1__c': False,
                'Survey_Email_Reminder_2__c': False,
                'Survey_Email_Reminder_3__c': False,
            },
            'test_pm_id_2': {
                'Survey_Email_Triggered__c': False,
                'Survey_Email_Reminder_1__c': False,
                'Survey_Email_Reminder_2__c': False,
                'Survey_Email_Reminder_3__c': False,
            },
            'test_pm_id_3': {
                'Survey_Email_Triggered__c': False,
                'Survey_Email_Reminder_1__c': False,
                'Survey_Email_Reminder_2__c': False,
                'Survey_Email_Reminder_3__c': False,
            },
        }


    def get_email(self, number: int) -> dict:
        email = copy.deepcopy(self.base_email)
        email['_id'] = f'test_id_{number}'
        email['survey_panel_member_id'] = f'test_pm_id_{number}'
        email['survey_panel_member_ids'] = [f'test_pm_id_{number}']
        return email


    @patch('agents.email_sender_panel_users.fetch_panel_member_details')
    @patch('agents.email_sender_panel_users.cache_sendgrid_template')
    @patch('agents.email_sender_panel_users.get_scheduled_emails_from_portaldb')
    @patch('agents.email_sender_panel_users.get_customer_survey_rounds_from_sf')
    @patch('agents.email_sender_panel_users.SendGridAPIClient')
    @patch('agents.email_sender_panel_users.emailapi.EmailServiceConfig')
    @patch('agents.email_sender_panel_users.emailapi.EmailService')
    @patch('agents.email_sender_panel_users.sqsapi.SQS')
    @patch('agents.email_sender_panel_users.sfapi.get_sf_connection')
    @patch('agents.email_sender_panel_users.portaldbapi.get_sf_collection')
    @patch('agents.email_sender_panel_users.portaldbapi.DocumentDB')
    @patch('agents.email_sender_panel_users.settings')
    def test_send_via_ses(self, mock_settings, mock_db, mock_sf, mock_get, mock_sqs, mock_email_service, mock_esc, mock_sg, mock_get_csr, mock_get_emails, mock_cache, mock_fetch_panel):
        mock_email_service_instance = mock_email_service.return_value
        mock_db_collection = MagicMock()
        mock_get.return_value = mock_db_collection
        mock_get_csr.return_value = {}
        email1 = self.get_email(1)
        email2 = self.get_email(2)
        email3 = self.get_email(3)
        mock_get_emails.return_value = {'CSR_ID': [email1, email2, email3]}
        mock_fetch_panel.return_value = copy.deepcopy(self.base_pms)

        email_sender_panel_users.main(None, None, None, None, True)

        mock_cache.assert_called()
        mock_email_service_instance.send.assert_called()
        send_at = datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None)
        calls = [
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {'survey_panel_member_ids': 'test_pm_id_1'}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map='scheduledemails-test_id_1', service_provider='ses', ses_delay_offset=0),   
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {'survey_panel_member_ids': 'test_pm_id_2'}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map='scheduledemails-test_id_2', service_provider='ses', ses_delay_offset=1),   
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {'survey_panel_member_ids': 'test_pm_id_3'}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map='scheduledemails-test_id_3', service_provider='ses', ses_delay_offset=2),   
        ]
        mock_email_service_instance.send.assert_has_calls(calls)


    @patch('agents.email_sender_panel_users.fetch_panel_member_details')
    @patch('agents.email_sender_panel_users.cache_sendgrid_template')
    @patch('agents.email_sender_panel_users.get_scheduled_emails_from_portaldb')
    @patch('agents.email_sender_panel_users.get_customer_survey_rounds_from_sf')
    @patch('agents.email_sender_panel_users.SendGridAPIClient')
    @patch('agents.email_sender_panel_users.emailapi.EmailServiceConfig')
    @patch('agents.email_sender_panel_users.emailapi.EmailService')
    @patch('agents.email_sender_panel_users.sqsapi.SQS')
    @patch('agents.email_sender_panel_users.sfapi.get_sf_connection')
    @patch('agents.email_sender_panel_users.portaldbapi.get_sf_collection')
    @patch('agents.email_sender_panel_users.portaldbapi.DocumentDB')
    @patch('agents.email_sender_panel_users.settings')
    def test_send_skip_already_sent(self, mock_settings, mock_db, mock_get, mock_sf, mock_sqs, mock_email_service, mock_esc, mock_sg, mock_get_csr, mock_get_emails, mock_cache, mock_fetch_panel):
        # Test sending scheduled emails when one of the PMs has the scheduled flag already sent in SF
        mock_email_service_instance = mock_email_service.return_value
        mock_db_collection = MagicMock()
        mock_get.return_value = mock_db_collection
        mock_get_csr.return_value = {}
        email1 = self.get_email(1)
        email2 = self.get_email(2)
        email3 = self.get_email(3)
        mock_get_emails.return_value = {'CSR_ID': [email1, email2, email3]}
        pms = copy.deepcopy(self.base_pms)
        pms['test_pm_id_2']['Survey_Email_Reminder_2__c'] = True  # Mark this PM as already sent
        mock_fetch_panel.return_value = pms

        email_sender_panel_users.main(None, None, None, None, True)

        mock_cache.assert_called()
        mock_email_service_instance.send.assert_called()
        send_at = datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None)
        calls = [
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {'survey_panel_member_ids': 'test_pm_id_1'}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map='scheduledemails-test_id_1', service_provider='ses', ses_delay_offset=0),   
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {'survey_panel_member_ids': 'test_pm_id_3'}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map='scheduledemails-test_id_3', service_provider='ses', ses_delay_offset=2),   
        ]
        mock_email_service_instance.send.assert_has_calls(calls)

        mock_db_collection.bulk_write.assert_called_once()
        call_args = mock_db_collection.bulk_write.call_args[0][0]
        self.assertEqual(len(call_args), 3)
        self.assertEqual(call_args[0]._filter['_id'], 'test_id_1')
        self.assertIn('sent_to_sendgrid_date', call_args[0]._doc['$set'])
        self.assertEqual(call_args[1]._filter['_id'], 'test_id_2')
        self.assertIn('skipped_as_scheduled_flag_set_in_sf', call_args[1]._doc['$set'])
        self.assertEqual(call_args[2]._filter['_id'], 'test_id_3')
        self.assertIn('sent_to_sendgrid_date', call_args[2]._doc['$set'])
