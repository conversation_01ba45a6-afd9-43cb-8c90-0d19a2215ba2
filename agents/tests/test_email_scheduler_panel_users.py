import copy
from datetime import datetime, timezone
import pytz
import unittest
from unittest.mock import patch, MagicMock
import agents.email_scheduler_panel_users as email_scheduler_panel_users


class TestEmailSchedulerPanelUsers(unittest.TestCase):
    def setUp(self):
        self.base_survey_panel_member = {
            'Id': 'TESTID',
            'Name': '<EMAIL>',
            'SurveyJsId__c': 'SJS_ID',
            'Contact__r.Id': 'TESTCONTACTID',
            'Contact__r.Email': '<EMAIL>',
            'Contact__r.Name': 'Test User',
            'Contact__r.FirstName': 'Test',
            'Contact__r.LastName': 'User',
            'Contact__r.Language__c': '',
            'Contact__r.Location__r.Timezone__c': '',
            'Survey_Email_Triggered__c': 'false',
            'Survey_Email_Reminder_1__c': 'false',
            'Survey_Email_Reminder_2__c': 'false',
            'Survey_Email_Reminder_3__c': 'false',
            'Survey_Type__c': 'Barometer',
            'Survey_Client__r.Id': 'SC_ID',
            'Survey_Client__r.Survey_Name__c': 'TestSurvey Name',
            'Survey_Client__r.Signatory__r.Id': 'SIGNATORY_ID',
            'Survey_Client__r.Signatory__r.Name': 'Signatory Name',
            'Survey_Client__r.Signatory__r.Email': '<EMAIL>',
            'Survey_Client__r.Signatory__r.Title': '',
            'Survey_Client__r.Signatory__r.Account.Name': 'Test Account',
            'Survey_Client__r.Signatory__r.Banner__c': '',
            'Survey_Client__r.Signatory__r.Signature__c': '<p><img src="https://clientrelationship.file.force.com/servlet/rtaImage?eid=TEST_EID&amp;feoid=TEST_FEOID&amp;refid=TEST_REF" alt="Screenshot 2025-06-05 at 12.35.08\u202fPM.png"></img></p><p><strong style="font-family: sans-serif;">Signatory Name</strong></p><p><span style="font-family: sans-serif;">President &amp; CEO</span></p><p><span style="font-family: sans-serif;">Test Account</span></p><p><span style="font-family: sans-serif;">M. 404.316.3081</span></p>',
            'Survey_Client__r.Customers_Client__c': 'CUST_CLIENT_ID',
            'Survey_Client__r.Customer_Survey__r.Id': 'CUST_SURVEY_ID',
            'Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c': '<EMAIL>',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c': '2025-06-22',
            'Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId': 'REC_TYPE_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c': 'America/New_York',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id': 'CUST_PARENT_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name': 'Cust Parent Name',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id': 'CUST_GRANDPARENT_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name': 'Cust Grandparent Name',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c': 'Level 7',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id': 'CSR_ID',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c': 'false',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name': 'Test SR Account Name',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id': 'SR_ID',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c': 'false',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c': '2025-06-04',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c': '2025-06-11',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c': '2025-06-16',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c': '2025-06-18',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c': '2025-06-04',
        }
        self.account_record_types = {
            "CRC's Customer - Advertising": MagicMock(spec=['Id']),
        }
        self.account_record_types["CRC's Customer - Advertising"].Id = 'REC_TYPE_ID'
        self.expected_result = dict(
            type='panel',
            survey_id='SJS_ID',
            contact_id='TESTCONTACTID',
            survey_panel_member_id='TESTID0',
            survey_client_id='SC_ID',
            customer_survey_id='CUST_SURVEY_ID',
            customer_survey_round_id='CSR_ID',
            survey_round_id='SR_ID',
            sender_email='<EMAIL>',
            sender_name='Signatory Name',
            recipient='<EMAIL>',
            template_name='live_barometer_participant',
            template_data={
                'FirstName': 'Test',
                'LastName': 'User',
                'SurveyDate': '2025-06-04',
                'LiveSurveyEndDate': '2025-06-22',
                'SurveyLink': '/survey/SJS_ID',
                'Banner': '',
                'Signatory': '<p><img src="cid:SIGNATORY_ID" alt="Screenshot 2025-06-05 at 12.35.08\u202fPM.png">' \
                    '</img></p><p><strong style="font-family: sans-serif;">Signatory Name</strong></p>' \
                    '<p><span style="font-family: sans-serif;">President &amp; CEO</span></p>' \
                    '<p><span style="font-family: sans-serif;">Test Account</span></p>' \
                    '<p><span style="font-family: sans-serif;">M. 404.316.3081</span></p>',
                'AgencyBrand': 'Cust Grandparent Name',
                'HoldingGroup': 'Test SR Account Name',
                'OptOutLink': '/optout/TESTCONTACTID',
                'PrivacyPolicyLink': '/privacy/SJS_ID'
            },
            template_metadata={
                'contact_id': 'TESTCONTACTID',
                'survey_panel_member_id': 'TESTID0',
                'survey_client_id': 'SC_ID',
                'customer_survey_id': 'CUST_SURVEY_ID',
                'customer_survey_round_id': 'CSR_ID',
                'tracked_template_name': 'live_barometer_participant'
            },
            language='en',
            vertical='adv',
            banner_attachment=None,
            signature_attachment={
                'content_id': 'SIGNATORY_ID',
                'disposition': 'inline',
                'file_type': 'image/jpeg',
                'key': 'SIGNATORY_ID.jpg'
            },
            scheduled_date_local='2025-06-04T07:30:00-04:00',
            scheduled_date_utc=datetime(2025, 6, 4, 11, 30, tzinfo=pytz.utc),
            scheduled_date_timezone='America/New_York',
            sf_spm_field='Survey_Email_Triggered__c',
            email_service_provider='sendgrid'
        )


    def get_panel_members(self, count=1, pms_per_contact=1):
        panel_members = []
        for idx in range(count):
            panel_member = copy.deepcopy(self.base_survey_panel_member)
            panel_member['Id'] = f'TESTID{idx}'
            panel_member['Name'] = ''
            panel_members.append(panel_member)
        return panel_members


    def test_process_participants_for_ses(self):
        panel_members = self.get_panel_members(1, 1)
        panel_members[0]['Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c'] = 'true'
        contact_profile_count = {}
        contact_survey_names = {}
        scheduled_emails = {}
        result_batch = email_scheduler_panel_users.process_participants('CSR_ID',
                                                                    panel_members, 
                                                                    contact_profile_count,
                                                                    contact_survey_names,
                                                                    1,
                                                                    self.account_record_types,
                                                                    scheduled_emails
                                                                )
    
        self.assertEqual(len(result_batch), 1)
        result = result_batch[0]._doc['$set']
        expected = copy.deepcopy(self.expected_result)
        expected['email_service_provider'] = 'ses'
        result.pop('last_modified', None)  # Ignore last_modified for comparison
        self.maxDiff = None
        self.assertDictEqual(result, expected)
        self.assertEqual(result['email_service_provider'], 'ses')


    def test_process_participants_barometer_m3(self):
        panel_members = self.get_panel_members(1, 1)
        contact_profile_count = {}
        contact_survey_names = {}
        scheduled_emails = {}
        result_batch = email_scheduler_panel_users.process_participants('CSR_ID',
                                                                    panel_members, 
                                                                    contact_profile_count,
                                                                    contact_survey_names,
                                                                    3,
                                                                    self.account_record_types,
                                                                    scheduled_emails
                                                                )
    
        self.assertEqual(len(result_batch), 1)
        result = result_batch[0]._doc['$set']
        expected = copy.deepcopy(self.expected_result)
        expected['template_name'] = 'live_barometer_participant_reminder_2'
        expected['template_metadata']['tracked_template_name'] = 'live_barometer_participant_reminder_2'
        expected['template_data']['Signatory'] = ''
        expected['sender_name'] = 'The VerityRI Team'
        expected['signature_attachment'] = None
        expected['banner_attachment'] = None
        expected['sf_spm_field'] = 'Survey_Email_Reminder_2__c'
        expected['scheduled_date_local'] = '2025-06-16T07:30:00-04:00'
        expected['scheduled_date_utc'] = datetime(2025, 6, 16, 11, 30, tzinfo=pytz.utc)
        result.pop('last_modified', None)  # Ignore last_modified for comparison
        self.maxDiff = None
        self.assertDictEqual(result, expected)


    def test_process_participants_barometer_m4(self):
        panel_members = self.get_panel_members(1, 1)
        contact_profile_count = {}
        contact_survey_names = {}
        scheduled_emails = {}
        result_batch = email_scheduler_panel_users.process_participants('CSR_ID',
                                                                    panel_members, 
                                                                    contact_profile_count,
                                                                    contact_survey_names,
                                                                    4,
                                                                    self.account_record_types,
                                                                    scheduled_emails
                                                                )
    
        self.assertEqual(len(result_batch), 1)
        result = result_batch[0]._doc['$set']
        expected = copy.deepcopy(self.expected_result)
        expected['template_name'] = 'live_barometer_participant_reminder_3'
        expected['template_metadata']['tracked_template_name'] = 'live_barometer_participant_reminder_3'
        expected['template_data']['Signatory'] = ''
        expected['sender_name'] = 'The VerityRI Team'
        expected['signature_attachment'] = None
        expected['banner_attachment'] = None
        expected['sf_spm_field'] = 'Survey_Email_Reminder_3__c'
        expected['scheduled_date_local'] = '2025-06-18T07:30:00-04:00'
        expected['scheduled_date_utc'] = datetime(2025, 6, 18, 11, 30, tzinfo=pytz.utc)
        result.pop('last_modified', None)  # Ignore last_modified for comparison
        self.maxDiff = None
        self.assertDictEqual(result, expected)
