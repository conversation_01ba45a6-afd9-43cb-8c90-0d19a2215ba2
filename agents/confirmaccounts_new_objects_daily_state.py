import argparse
from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
import datetime
import requests
from lib.settings import settings


def preload_sf_data(sf: Salesforce):
    # get all contacts
    all_contacts_by_email = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        all_contacts_by_email[contact.Email.lower()] = contact

    # get all customer client accounts by name
    all_customer_clients_by_name = {}
    soql = format_soql("where RecordType.Name = {record_type}", record_type=sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_customer_clients_by_name[account.Name.lower()] = account

    return all_contacts_by_email, all_customer_clients_by_name


def slack_resolution_errors(messages):
    body = {"blocks": []}
    body["blocks"].append(
        {"type": "rich_text", "elements": 
            [
                {"type": "rich_text_section", "elements": [
                    {"type": "text", "text": "Daily Update: Accounts & contacts created yesterday in CA that do not exist in SF"}
                ]}
            ]
        }
    )
    for m in messages:
        body["blocks"].append({"type": "rich_text", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": m}]}]})
    resp = requests.post(settings.sns_slack_resolution_error_webhook_url, json=body)
    resp.raise_for_status()


def main(writeit):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

    query = {
        "sfsync_date": None,
        "deleted": { "$ne": True },
        "account_managers.0": { "$exists" : True },
        "confirmed_accounts": { "$exists": True, "$ne": {} },
        "accounts_confirmed_by_date": { "$gte": today },
    }

    all_confirm_accounts: list[portaldbapi.ConfirmAccounts] = []
    customer_survey_ids = set()
    agency_ids = set()
    resolution_errors = []

    # gather information on what we're about to sync
    for pdb_customer_survey in confirm_accounts_collection.find(query):
        pdb_customer_survey = portaldbapi.ConfirmAccounts(**pdb_customer_survey)
        all_confirm_accounts.append(pdb_customer_survey)
        customer_survey_ids.add(pdb_customer_survey.Id)
        agency_ids.add(pdb_customer_survey.client_id)

    # if there's nothing to do, exit early
    if not all_confirm_accounts:
        print('No confirm accounts to process')
        return
    
    # get necessary data from salesforce now we know we have something to do
    all_contacts_by_email, all_customer_clients_by_name = preload_sf_data(sf)
    
    for pdb_customer_survey in all_confirm_accounts:
        for account_confirmation_details in pdb_customer_survey.confirmed_accounts:
            confirmed_date = account_confirmation_details.confirm_date.replace(tzinfo=None)
            yesterday = today - datetime.timedelta(days=1)

            # if the confirmation date is not yesterday, skip it (as we only care about yesterday's data)
            if confirmed_date != yesterday:
                continue

            # handle new accounts
            for confirmed_account in account_confirmation_details.accounts:
                if confirmed_account.account_id.startswith('NEW'):
                    resolved_account = all_customer_clients_by_name.get(confirmed_account.account_name.lower())
                    if not resolved_account:
                        message = f"Client: {pdb_customer_survey.client_name} ({pdb_customer_survey.client_id}) | Customer Survey: {pdb_customer_survey.customer_survey_name} ({pdb_customer_survey.Id}) | New account (non-resolvable in SF): {confirmed_account.account_name}"
                        print(message)
                        resolution_errors.append(message)
                        continue
            
            # handle new contacts/panel managers
            for panel_manager in confirmed_account.survey_panel_managers:
                if panel_manager.panel_manager_id.startswith('NEW'):
                    resolved_contact = all_contacts_by_email.get(panel_manager.panel_manager_email.lower())
                    if not resolved_contact:
                        message = f"Client: {pdb_customer_survey.client_name} ({pdb_customer_survey.client_id}) | Customer Survey: {pdb_customer_survey.customer_survey_name} ({pdb_customer_survey.Id}) | Account: {confirmed_account.account_name} ({confirmed_account.account_id}) | New Contact (non-resolvable in SF): {panel_manager}"
                        print(message)
                        resolution_errors.append(message)
                        continue

    # send resolutuion errors to slack
    if writeit:
        if resolution_errors:
            # NOTE: might get rate-limited here, so need to keep an eye on this
            for i in range(0, len(resolution_errors), 10):
                slack_resolution_errors(resolution_errors[i:i+10])


def lambda_handler(event, context):
    main(True)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write out the slack messages")
    args = ap.parse_args()

    main(args.writeit)
