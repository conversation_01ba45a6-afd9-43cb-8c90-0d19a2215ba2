""" Receive SES notifications for delivery, bounce, complaint and write them to the database.
    They'll be process by the sync_email_analytics_to_sf agent at a later point.

DATA:
    FROM: AWS SQS (crc-agent-ses_notifications-{stack}-queue)
    TO: PortalDB (sesevents)
"""
from __future__ import annotations
import argparse
import datetime
import json
from pymongo import InsertOne
from lib import portaldbapi
from lib.settings import settings


TRACKED_EVENTS = {'Delivery', 'Bounce', 'Complaint'}
# Map the SES event types to the ones already track with sendgrid
EVENT_MAP = {
    'Delivery': 'delivered',
    'Bounce': 'bounce',
    'Complaint': 'bounce',
}


def main(messages: list[dict]) -> dict:

    db: portaldbapi.DocumentDB = portaldbapi.DocumentDB()

    # get the tracked templates from settings
    tracked_templates = settings.TRACKED_TEMPLATES.split(',')
    now = datetime.datetime.now(datetime.timezone.utc)

    batch: list = []
    stats = {'template_null': 0, 'untracked_template': 0, 'untracked_event': 0, 'no_panel_member_id': 0, 'processed': 0}
    for message in messages:
        event_type = message.get('eventType')
        if event_type not in TRACKED_EVENTS:
            stats['untracked_event'] += 1
            continue
        if event_type == 'Bounce' and message.get('bounce', {}).get('bounceType') != 'Permanent':
            # only track permanent bounces
            stats['untracked_event'] += 1
            continue

        mail = message.get('mail', {})
        tags = mail.get('tags', {})
        tracked_template_name = tags.get('tracked_template_name')[0] if tags.get('tracked_template_name') else None
        if tracked_template_name is None:
            stats['template_null'] += 1
            continue
        if tracked_template_name not in tracked_templates:
            stats['untracked_template'] += 1
            continue
        # if no survey panel member ID is found on the custom args, skip this email
        survey_panel_member_id = tags.get('survey_panel_member_id')[0] if tags.get('survey_panel_member_id') else None
        if not survey_panel_member_id:
            stats['no_panel_member_id'] += 1
            continue

        survey_panel_member_ids = []
        if tags.get('survey_panel_member_ids'):
            # track all panel members associated with the email
            survey_panel_member_ids = tags.get('survey_panel_member_ids')[0].split(',')

        recipients = mail.get('destination', [])
        # use the same structure of data as the sendgrid events
        email_dict = dict(
            email=recipients[0] if recipients else None,
            event=EVENT_MAP[event_type],
            timestamp=now,
            ses_message_id=mail.get('messageId'),
            survey_panel_member_id=survey_panel_member_id,
            survey_panel_member_ids=survey_panel_member_ids,
            template=tracked_template_name,
            sg_machine_open=False,
        )
        if event_type == 'Bounce':
            # add the bounce reason if available
            email_dict['bounce_reason'] = message.get('bounce', {}).get('bounceSubType')
        if event_type == 'Complaint':
            # add the complaint feedback if available
            email_dict['complaint_feedback'] = message.get('complaint', {}).get('complaintFeedbackType')
        batch.append(InsertOne(email_dict))
        stats['processed'] += 1

        if len(batch) > portaldbapi.MONGO_BATCH_SIZE:
            db.sesevents.bulk_write(batch)
            batch.clear()

    if batch:
        db.sesevents.bulk_write(batch)

    print(f"event stats: {stats}")
    return stats


def lambda_handler(event, _) -> None:
    if event:
        messages = []
        for record in event["Records"]:
            print(f"EVENT RECORD: {record}")
            try:
                # try to decode the message body as json
                # Note: some messages (which we are not interested in) are not json, such as the subscription confirmation success
                sns_message_body = json.loads(record['body'])
                if sns_message_body.get('Type') and sns_message_body.get('Type') == 'SubscriptionConfirmation':
                    # print out the subscription message as we need to url to confirm the subscription
                    print(f"SUBSCRIPTION CONFIRMATION: {sns_message_body}")
                    continue
                messages.append(sns_message_body)
            except json.JSONDecodeError as e:
                print(f"Failed to decode JSON: {e}")
                continue
        main(messages)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("messages", help="List of SES messages to process", type=json.loads)
    args = ap.parse_args()

    main(args.messages)

