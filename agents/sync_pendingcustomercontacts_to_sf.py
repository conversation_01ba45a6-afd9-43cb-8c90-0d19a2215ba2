import pymongo.collation
import pymongo.collection
import pymongo
from lib import sfapi, portaldbapi
from simple_salesforce import Salesforce
import datetime
from lib.settings import settings


SF_BATCH_SIZE = 1000

def sync_batch_to_sf(sf: Salesforce,
                     sf_pending_contacts_to_create: list[sfapi.Contact],
                     pending_contacts_collection: pymongo.collection.Collection) -> None:

    created_pending_customer_contacts: dict[str, sfapi.Contact] = {}
    sfapi.bulk_import_batch_to_sf(sf, None, created_pending_customer_contacts, sf_pending_contacts_to_create, sfapi.Contact)

    portaldb_successful_pending_customer_contact_oids = set()
    for pending_customer_contact in created_pending_customer_contacts.values():
        portaldb_successful_pending_customer_contact_oids.add(pending_customer_contact.InternalId)

    # update portaldb
    update = {
        "$set": {
            "sfsync_date": datetime.datetime.now(datetime.UTC),
        }
    }
    query = {
        "internal_id": {"$in": list(portaldb_successful_pending_customer_contact_oids)},
        "sfsync_date": None
    }
    pending_contacts_collection.update_many(query, update)


def main():
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    pending_contacts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_PENDING_CUSTOMER_CONTACTS_COLLECTION)

    # find all pending contacts that are unsynced
    query = {
        "sfsync_date": None,
    }
    portaldb_pending_contacts = pending_contacts_collection.find(query)

    # sync them
    sf_pending_contacts_to_create = []
    pc_idx = 0
    for portaldb_pending_contact in portaldb_pending_contacts:
        portaldb_pending_contact = portaldbapi.PendingCustomerContact(**portaldb_pending_contact)
        new_pending_contact = sfapi.Contact(
            Id=f"Contact-{portaldb_pending_contact.internal_id}",
            FirstName=portaldb_pending_contact.first_name,
            LastName=portaldb_pending_contact.last_name,
            Email=portaldb_pending_contact.email,
            Internal_Id__c=portaldb_pending_contact.internal_id,
            Pending_New_User__c=True,
        )
        sf_pending_contacts_to_create.append(new_pending_contact)
        pc_idx += 1

        if len(sf_pending_contacts_to_create) > SF_BATCH_SIZE:
            sync_batch_to_sf(sf, sf_pending_contacts_to_create, pending_contacts_collection)
            sf_pending_contacts_to_create.clear()

    if len(sf_pending_contacts_to_create) > 0:
        sync_batch_to_sf(sf, sf_pending_contacts_to_create, pending_contacts_collection)
        sf_pending_contacts_to_create.clear()


def lambda_handler(event, context):
    main()


if __name__ == "__main__":
    main()
