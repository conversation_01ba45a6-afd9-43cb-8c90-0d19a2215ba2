import argparse
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
from simple_salesforce import format_soql, Salesforce
from lib import sfapi, portaldbapi
from lib.settings import settings
from pymongo import UpdateOne


SURVEY_CLIENT_STATES = ('Panel Pending', 'Panel Agreed', 'Survey Ready', 'Survey Out', 'Survey Complete')
CUSTOMER_SURVEY_STATES = ('Survey Clients Agreed', 'Panel Agreed')


def or_none(s: Optional[str]) -> Optional[str]:
    return s or None


def fetch_survey_panel_managers_from_sf(sf) -> Dict[str, Dict[str, portaldbapi.ConfirmAccountsPanelManager]]:
    print("Fetching survey panel managers from Salesforce...")
    survey_panel_managers_by_survey_client:Dict = {}

    soql:str = format_soql("""
        SELECT  Survey_Client__r.Id,
                Contact__r.Id,
                Contact__r.<PERSON><PERSON>,
                Contact__r.Name,
                Contact__r.<PERSON><PERSON>ame,
                Contact__r.LastName
        FROM  Survey_Panel_Manager__c
        WHERE Survey_Client__r.Current_Round__c = TRUE 
        AND   Survey_Client__r.State__c IN {survey_client_states}
    """, survey_client_states=SURVEY_CLIENT_STATES)

    for row in sfapi.bulk_query(sf, soql):
        pdb_panel_manager:portaldbapi.ConfirmAccountsPanelManager = portaldbapi.ConfirmAccountsPanelManager(
            panel_manager_id=row['Contact__r.Id'],
            panel_manager_email=row['Contact__r.Email'],
            panel_manager_name=row['Contact__r.Name'],
            panel_manager_first_name=row['Contact__r.FirstName'],
            panel_manager_last_name=row['Contact__r.LastName']
        )

        survey_panel_managers_by_survey_client.setdefault(row["Survey_Client__r.Id"], {})[row['Contact__r.Id']] = pdb_panel_manager

    return survey_panel_managers_by_survey_client


def fetch_survey_clients_from_sf(sf) -> Tuple[Set[str], List[Dict]]:
    print("Fetching survey clients from Salesforce...")
    agency_customer_ids:Set = set()
    survey_client_sf_results_cache:List[Dict] = []

    soql:str = format_soql("""
        SELECT  Id,
                Survey_Name__c,
                Customer_Survey__r.Id,
                Customer_Survey__r.Customer__c,
                Customers_Client__r.Id,
                Customers_Client__r.Name,
                Signatory__r.Id,
                Signatory__r.Name,
                Signatory__r.Email
        FROM  Survey_Client__c
        WHERE Customer_Survey__r.Current_Round__c = TRUE 
        AND   Customer_Survey__r.State__c IN {customer_survey_states}
    """, customer_survey_states=CUSTOMER_SURVEY_STATES)

    for row in sfapi.bulk_query(sf, soql):
        agency_customer_ids.add(row["Customer_Survey__r.Customer__c"])
        survey_client_sf_results_cache.append(row)

    return agency_customer_ids, survey_client_sf_results_cache


def fetch_ccr_from_sf(sf:Salesforce, agency_customer_ids:Set) -> Dict:
    print("Fetching customer client relationships from Salesforce...")
    ccr_by_agency_id:Dict = {}

    soql = format_soql("""
            SELECT  Customer_Account__c,
                    Customers_Client__c,
                    Account_Label__c
            FROM  Customer_Client_Relationship__c
            WHERE Relationship_Type__c = 'Client' 
            AND   Customer_Account__c in {customer_ids} 
            AND   Customers_Client__c != ''
    """, customer_ids=agency_customer_ids)

    for row in sfapi.bulk_query(sf, soql):
        agency_id:str = row['Customer_Account__c']
        customer_id:str = row['Customers_Client__c']
        ccr_by_agency_id.setdefault(agency_id, {})[customer_id] = row

    return ccr_by_agency_id


def assemble_final_accounts(survey_panel_managers_by_survey_client:Dict, 
                            survey_client_sf_results_cache:List[Dict], 
                            ccr_by_agency_id:Dict) -> Dict:
    print("Assembling final accounts...")
    final_accounts_by_customer_survey_id:Dict = {}

    for row in survey_client_sf_results_cache:
        # grab the ccr (if there is one)
        ccr:Dict = ccr_by_agency_id.get(row["Customer_Survey__r.Customer__c"], {}).get(row['Customers_Client__r.Id'], None)

        # default account name to the customer client name (from the account object)
        # if there is a ccr, use the account label instead
        account_name:str = row['Customers_Client__r.Name']

        if ccr and or_none(ccr['Account_Label__c']):
            account_name:str = ccr['Account_Label__c']

        panel_managers:List = list(survey_panel_managers_by_survey_client.get(row["Id"], {}).values())

        pdb_confirm_account:portaldbapi.ConfirmAccountsAccount = portaldbapi.ConfirmAccountsAccount(
            account_id=row['Customers_Client__r.Id'],
            account_name=account_name,
            in_round=True,
            survey_panel_managers=panel_managers,
            signatory_id=row['Signatory__r.Id'],
            signatory_email=row['Signatory__r.Email'],
            signatory_name=row['Signatory__r.Name'],
            survey_name=row['Survey_Name__c'],
            duplicate_account=False
        )

        final_accounts_by_customer_survey_id.setdefault(row["Customer_Survey__r.Id"], {})[row['Customers_Client__r.Id']] = pdb_confirm_account
    
    return final_accounts_by_customer_survey_id


def write_final_accounts(collection, final_accounts_by_customer_survey_id:Dict) -> None:
    print("Writing final accounts to database...")
    batch:List[Dict] = []
    for customer_survey_id, accounts in final_accounts_by_customer_survey_id.items():
        accounts_json:List[Dict] = [account.model_dump(by_alias=True) for account in accounts.values()]

        batch.append(UpdateOne({"Id": customer_survey_id},
                               {"$set": {"final_confirmed_accounts": accounts_json}}))

    if batch:
        collection.bulk_write(batch)


def main(debug_output_path:str|None = None, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_accounts_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_ACCOUNTS_COLLECTION)

    # get all panel managers for agreed survey clients
    survey_panel_managers_by_survey_client:Dict = fetch_survey_panel_managers_from_sf(sf)

    # get the list of survey clients to process + build customer id list and sf result cache for later iteration
    agency_customer_ids, survey_client_sf_results_cache = fetch_survey_clients_from_sf(sf)
    
    # get the customer client relationships for the agency customers
    ccr_by_agency_id:Dict = fetch_ccr_from_sf(sf, agency_customer_ids)
    
    # now iterate over the survey clients and build the final accounts
    final_accounts_by_customer_survey_id = assemble_final_accounts(survey_panel_managers_by_survey_client,
                                                                   survey_client_sf_results_cache,
                                                                   ccr_by_agency_id)
    
    # spit final output to JSON file for debug/comparison
    if debug_output_path:
        output_data:dict = {
            customer_survey_id: {
                account_id: account.model_dump(by_alias=True)
                for account_id, account in accounts.items()
            }
            for customer_survey_id, accounts in final_accounts_by_customer_survey_id.items()
        }

        output_path = Path(debug_output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        output_path.write_text(json.dumps(output_data, indent=2))
        
    # now generate updates to the confirmpanels objects
    if writeit:
        write_final_accounts(confirm_accounts_collection, final_accounts_by_customer_survey_id)


def lambda_handler(event, context):
    main(
        debug_output_path=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--debug_output_path", type=str, help="Spit out output to JSON file")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.debug_output_path, args.writeit)
