import boto3
import datetime
import argparse
import requests
import csv
import io
from lib import sfapi
from lib.settings import settings
from simple_salesforce import Salesforce, format_soql


SF_BASE_URL = 'https://clientrelationship.lightning.force.com'
AMPLIFY_LOGS_LOOK_BACK_WINDOW = 3


def get_survey_panel_members_from_sf(sf: Salesforce, survey_panel_member_id:str|None) -> dict[str, sfapi.SurveyPanelMember]:
    all_survey_panel_members = {}
    soql = '''
        WHERE Survey_Client__r.Current_Round__c = True 
        AND   Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey' 
        AND   SurveyJsId__c != NULL
    '''
    if survey_panel_member_id:
        soql = soql + " AND Id = {survey_panel_member_id}"
    
    query = format_soql(soql, survey_panel_member_id=survey_panel_member_id)

    for spm in sfapi.get_all(sf, sfapi.SurveyPanelMember, bulk=True, query_suffix=query):
        all_survey_panel_members.setdefault(spm.SurveyJsId, []).append(spm.Id)

    return all_survey_panel_members


def get_survey_access_audit_from_sf(sf: Salesforce, survey_panel_member_id:str|None) -> dict[str, sfapi.SurveyAccessAudit]:
    all_survey_access_audit_by_id = {}
    soql = '''
        WHERE Survey_Panel_Member__r.Survey_Client__r.Current_Round__c = True 
        AND   Survey_Panel_Member__r.Survey_Client__r.Customer_Survey__r.Stage__c = 'Live Survey' 
        AND   Survey_Panel_Member__r.SurveyJsId__c != NULL
    '''
    if survey_panel_member_id:
        soql = soql + " AND Survey_Panel_Member__r.Id = {survey_panel_member_id}"

    query = format_soql(soql, survey_panel_member_id=survey_panel_member_id)
        
    for saa in sfapi.get_all(sf, sfapi.SurveyAccessAudit, bulk=True, query_suffix=query):
        access_date = datetime.datetime.strptime(saa.AccessDate, '%Y-%m-%dT%H:%M:%S.%fZ')
        access_action = saa.AccessAction.replace(' ', '')
        key = f"{saa.SurveyPanelMemberId}-{access_action}-{access_date.strftime('%Y%m%d%H%M%S')}"
        all_survey_access_audit_by_id[key] = saa

    return all_survey_access_audit_by_id


def get_log_data_from_amplify(aws: boto3.client) -> dict[str, dict[str, str]]:
    log_data_by_survey_id = {}

    now = datetime.datetime.now()
    start_date = now.replace(hour=0, minute=0, second=0, microsecond=0) - datetime.timedelta(days=AMPLIFY_LOGS_LOOK_BACK_WINDOW)
    end_date = now

    while start_date <= end_date:
        response = aws.generate_access_logs(startTime=start_date,
                                            endTime=start_date + datetime.timedelta(days=1),
                                            domainName=settings.survey_amplify_domain,
                                            appId=settings.survey_amplify_app_id)
    
        log_presigned_url = response['logUrl']

        r = requests.get(log_presigned_url)
        r.raise_for_status()
        r_content = r.text
        reader = csv.DictReader(io.StringIO(r_content))
        for row in reader:
            access_date = row.get('date')
            access_time = row.get('time')
            uri = row.get('cs-uri-stem')
            request_type = row.get('cs-method')  
            referrer = row.get('cs\\(Referer)')

            # skip if request is not GET or PATCH
            if request_type not in ['GET', 'PATCH']:
                continue

            # skip if request is not for survey app
            if request_type == 'GET' and not is_request_from_app_url(uri):
                continue

            # skip if request is not for survey api
            if request_type == 'PATCH' and not is_request_from_api_url(uri):
                continue

            access_datetime = datetime.datetime.strptime(f'{access_date} {access_time}', '%Y-%m-%d %H:%M:%S')
            survey_id = get_survey_id_from_uri(uri)
            action = determine_action_by_http_request_type(request_type)
            from_sf = determine_referred_from_salesforce(referrer)

            log = dict(
                date=access_datetime,
                action=action,
                from_sf=from_sf,
            )
            
            log_data_by_survey_id.setdefault(survey_id, []).append(log)
        
        start_date += datetime.timedelta(days=1)

    # keeping this logic seperate as not sure if it'll remain or not
    # but group survey actions by the minute to avoid confusing patterns showing in SF
    log_data_by_survey_id = group_survey_actions_by_minute(log_data_by_survey_id)

    return log_data_by_survey_id


def group_survey_actions_by_minute(logs:dict[str, str]) -> dict[str, dict[str, str]]:
    grouped_logs = {}

    for log_id, logs in logs.items():
        grouped = {}
        for log in logs:
            access_date = log['date']
            access_action = log['action']
            truncated_time = access_date.replace(second=0)

            key = (access_action, truncated_time)

            if key not in grouped or access_date < grouped[key]['date']:
                grouped[key] = log

        grouped_logs[log_id] = grouped.values()

    return grouped_logs


def determine_action_by_http_request_type(request_type:str) -> str:
    if request_type == 'GET':
        return 'viewed survey'
    if request_type == 'PATCH':
        return 'submitted survey'


def determine_referred_from_salesforce(referrer:str) -> bool:
    return (SF_BASE_URL in referrer)
    

def get_survey_id_from_uri(uri:str) -> str:
    return uri.split('/')[-1]


def is_request_from_api_url(uri:str) -> bool:
    return uri.startswith('/api/')


def is_request_from_app_url(uri:str) -> bool:
    return uri.startswith('/survey/')


def main(survey_panel_member_id:str|None = None, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    aws = boto3.client('amplify', region_name='eu-west-1')

    new_survey_access_audit = []

    # get log data from amplify
    survey_access_logs = get_log_data_from_amplify(aws)

    # get necessary data from salesforce
    survey_access_audit_by_id = get_survey_access_audit_from_sf(sf, survey_panel_member_id)
    panel_members_by_survey_id = get_survey_panel_members_from_sf(sf, survey_panel_member_id)

    for log_id, logs in survey_access_logs.items():
        survey_id = log_id
        for log in logs:
            access_date = log['date']
            access_action = log['action']
            access_via_salesforce = log['from_sf']
            panel_members_for_survey = panel_members_by_survey_id.get(survey_id, [])

            # skip if survey id is not in survey panel members
            if survey_id not in panel_members_by_survey_id:
                continue

            # only want to update panel members who have new log entries
            panel_members = [
                pm_id for pm_id in panel_members_for_survey 
                if f"{pm_id}-{access_action.replace(' ', '')}-{access_date.strftime('%Y%m%d%H%M%S')}" not in survey_access_audit_by_id
            ]

            # skip if there are no panel members
            if not panel_members:
                continue

            # create survey access audit
            for panel_member in panel_members:
                new_survey_access_audit.append({
                    "Survey_Panel_Member__c": panel_member,
                    "Access_Action__c": access_action,
                    "Access_Date__c": access_date.isoformat(),
                    "Accessed_Via_Salesforce__c": access_via_salesforce,
                })
    
    if writeit:
        if new_survey_access_audit:
            sfapi.bulk_insert(sf, "Survey_Access_Audit__c", new_survey_access_audit)


def lambda_handler(event, context):
    main(
        survey_panel_member_id=None, 
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_panel_member_id', help='ID of the SPM to process')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.writeit)
