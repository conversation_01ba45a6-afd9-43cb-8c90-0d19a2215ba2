from lib import sfapi, portaldbapi, sfimport
from simple_salesforce import format_soql, Salesforce
import requests
import datetime
import argparse
from lib.settings import settings
from pymongo import UpdateOne


#FIXME: this should not be hardcoded here - move to sync agent and store in Mongo collection
JOB_FUNCTION_MAP = {    
    'Procurement & Purchasing': 'Operations',
    'Supply Chain & Logistics': 'Operations',
    'Legal, Risk & Compliance': 'Operations',
    'Quality Assurance': 'Operations',
    'Manufacturing': 'Operations',
}

def preload_sf_data(sf: Salesforce, survey_client_ids, agency_ids, customer_client_ids):
    # preload all relevant account contact relations into the cache
    soql = format_soql("WHERE AccountId IN {agency_ids}", agency_ids=agency_ids)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.AccountContactRelation, bulk=True, query_suffix=soql))

    # preload all relevant panel members into the cache
    soql = format_soql("WHERE Survey_Client__c IN {survey_client_ids}", survey_client_ids=survey_client_ids)
    sfapi.add_items_to_cache(sfimport.sfid_mapping, sfimport.sfcache, sfapi.get_all(sf, sfapi.SurveyPanelMember, bulk=True, query_suffix=soql))

    # get all relevant survey clients
    all_survey_clients_by_id = {}
    soql = format_soql("WHERE Id IN {survey_client_ids}", survey_client_ids=survey_client_ids)
    for cs in sfapi.get_all(sf, sfapi.SurveyClient, sfapi.SurveyClient, query_suffix=soql):
        all_survey_clients_by_id[cs.Id] = cs
        sfimport.sfid_mapping[cs.Id] = cs.Id
        sfimport.sfcache[cs.business_key2()] = cs

    # get all contacts
    all_contacts_by_id = {}
    all_contacts_by_email = {}
    for contact in sfapi.get_all(sf, sfapi.Contact, bulk=True):
        all_contacts_by_id[contact.Id] = contact
        all_contacts_by_email[contact.Email.lower()] = contact
        sfimport.sfid_mapping[contact.Id] = contact.Id
        sfimport.sfcache[contact.business_key2()] = contact

    # get all agency accounts
    all_agency_accounts_by_id = {}
    soql = format_soql("where Id in {agency_ids}", agency_ids=agency_ids)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_agency_accounts_by_id[account.Id] = account
        sfimport.sfid_mapping[account.Id] = account.Id
        sfimport.sfcache[account.business_key2()] = account

    # get all customer clients
    all_customer_client_accounts_by_id = {}
    soql = format_soql("where Id in {customer_client_ids}", customer_client_ids=customer_client_ids)
    for account in sfapi.get_all(sf, sfapi.Account, bulk=True, query_suffix=soql):
        all_customer_client_accounts_by_id[account.Id] = account
        sfimport.sfid_mapping[account.Id] = account.Id
        sfimport.sfcache[account.business_key2()] = account

    # get all active panel managers by survey client
    soql = format_soql("""
        SELECT  Id,
                Contact__c,
                Survey_Client__c
        FROM  Survey_Panel_Manager__c
        WHERE Survey_Client__c IN {survey_client_ids}
    """, survey_client_ids=survey_client_ids)
    panel_managers_by_survey_client_id = {}
    for row in sfapi.bulk_query(sf, soql):
        panel_managers_by_survey_client_id.setdefault(row["Survey_Client__c"], {})[row['Contact__c']] = row['Id']

    return all_contacts_by_email, all_contacts_by_id, all_survey_clients_by_id, all_agency_accounts_by_id, all_customer_client_accounts_by_id, panel_managers_by_survey_client_id


def slack_resolution_errors(messages):
    body = {"blocks": []}
    body["blocks"].append({"type": "rich_text", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": "sync_confirmpanel_to_sf"}]}]})
    for m in messages:
        body["blocks"].append({"type": "rich_text", "elements": [{"type": "rich_text_section", "elements": [{"type": "text", "text": m}]}]})
    resp = requests.post(settings.sns_slack_resolution_error_webhook_url, json=body)
    resp.raise_for_status()


def main(survey_client_id:str|None = None, writeit:bool = False):
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    portaldb = portaldbapi.DocumentDB()
    confirm_panel_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_CONFIRM_PANEL_COLLECTION)
    markets_collection = portaldbapi.get_sf_collection(portaldb, portaldbapi.PORTALDB_MARKETS_COLLECTION)

    markets = markets_collection.find({}, {'Id': 1, 'name': 1})
    markets_name_to_id_map = {m['name']: m['Id'] for m in markets}

    today = datetime.datetime.today().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)

    query = {
        "sfsync_date": None,
        "deleted": { "$ne": True },
        "panel_managers.0": { "$exists" : True },
        "panel_confirmed_by_date": { "$lt": today }
    }
    if survey_client_id:
        query["Id"] = survey_client_id

    all_confirm_panels: list[portaldbapi.ConfirmPanel] = []
    survey_client_ids = set()
    client_ids = set()
    account_ids = set()

    # gather information on what we're about to sync
    for pdb_survey_client in confirm_panel_collection.find(query):
        pdb_survey_client = portaldbapi.ConfirmPanel(**pdb_survey_client)
        all_confirm_panels.append(pdb_survey_client)
        survey_client_ids.add(pdb_survey_client.Id)
        client_ids.add(pdb_survey_client.client_id)
        account_ids.add(pdb_survey_client.account_id)

    # if there's nothing to do, exit early
    if not all_confirm_panels:
        return

    all_contacts_by_email, all_contacts_by_id, all_survey_clients_by_id, all_agency_accounts_by_id, all_customer_clients_by_id, panel_managers_by_survey_client_id = preload_sf_data(sf, survey_client_ids, client_ids, account_ids)

    # sync it!
    resolution_errors = []
    sf_survey_client_updates = {}
    pdb_survey_client_updates = {}
    for pdb_survey_client in all_confirm_panels:
        sf_agency_account = all_agency_accounts_by_id.get(pdb_survey_client.client_id)
        sf_customer_client = all_customer_clients_by_id.get(pdb_survey_client.account_id)

        # merge all panel members across all panel managers confirmations
        panel_members = []
        confirmed_panel: dict[str, portaldbapi.ConfirmPanelMember] = {}
        resolution_failed = False

        # if there's no confirmed panel, use the panel
        if len(pdb_survey_client.confirmed_panel) == 0:
            in_round_panel_members = [member for member in pdb_survey_client.panel if member.in_round]
            pdb_survey_client_confirmed_panel = portaldbapi.ConfirmedPanel(panel=in_round_panel_members)
            panel_members.extend(pdb_survey_client_confirmed_panel.panel)
        else:
            # NOTE: Mongo scaling fix - if required (no rollout now to reduce risk)
            #pdb_survey_client_all_panel = pdb_survey_client.confirmed_panel_last_save_all_panel
            #if pdb_survey_client_all_panel is None:
            #    pdb_survey_client_all_panel = pdb_survey_client.confirmed_panel[0].all_panel
            pdb_survey_client_all_panel = pdb_survey_client.confirmed_panel[0].all_panel
            pdb_survey_client_confirmed_panel = pdb_survey_client.confirmed_panel[0]

            # we want to process all confirrmed panel members, but also want to attempt to resolve any NEW panel members that were 
            # added by the user but not confirmed
            user_confirmed_panel_member_ids = [
                panel_member.contact_id 
                for panel_member in pdb_survey_client_confirmed_panel.panel
            ]
            user_added_panel_members = [
                panel_member.model_copy(update={'not_confirmed_resolving_required': True})
                for panel_member in pdb_survey_client_all_panel
                if panel_member.contact_id.startswith('NEW') and panel_member.contact_id not in user_confirmed_panel_member_ids
            ]

            # merge the confirmed accounts with the user added accounts
            panel_members.extend(pdb_survey_client_confirmed_panel.panel + user_added_panel_members)

        # now merge/resolve the panel members
        for panel_member in panel_members:
            # resolve panel manager if new
            if panel_member.contact_id.startswith('NEW'):
                resolved_contact = all_contacts_by_email.get(panel_member.contact_email.lower())
                if not resolved_contact:
                    # create a new contact!
                    contact_name = panel_member.contact_name.strip().split(' ', 1)
                    #FIXME: this needs tidied and moved
                    job_function = JOB_FUNCTION_MAP.get(panel_member.contact_job_function)
                    job_function_level2 = None
                    if job_function:
                        job_function_level2 = panel_member.contact_job_function
                    else:
                        job_function = panel_member.contact_job_function
                    # FIXME: update confirmpanel_to_portaldb to bring down location ID and use that here
                    location = markets_name_to_id_map.get(panel_member.contact_location)
                    account_site = markets_name_to_id_map.get(panel_member.contact_account_site)
                    resolved_contact = sfimport.get_or_create_cached_contact(sf_customer_client,
                                                                             contact_name[0],
                                                                             contact_name[-1],
                                                                             panel_member.contact_email.lower(),
                                                                             contact_type=panel_member.contact_type,
                                                                             seniority=panel_member.contact_seniority,
                                                                             language=panel_member.contact_language,
                                                                             job_function=job_function,
                                                                             job_function_level2=job_function_level2,
                                                                             job_title=panel_member.contact_job_title,
                                                                             location=location,
                                                                             account_site=account_site,
                                                                             created_in_client_area=True)

                    all_contacts_by_email[resolved_contact.Email.lower()] = resolved_contact
                    all_contacts_by_id[resolved_contact.Id] = resolved_contact

                # patch it
                panel_member.contact_id = resolved_contact.Id

            # skip if we just want it resolved and created
            if panel_member.not_confirmed_resolving_required:
                continue

            # skip if we've already got this one
            if panel_member.contact_id in confirmed_panel:
                continue

            # otherwise, add it to the list!
            confirmed_panel[panel_member.contact_id] = panel_member

        if resolution_failed:
            continue

        sf_contacts_to_patch = {}
        sf_survey_client = all_survey_clients_by_id[pdb_survey_client.Id]
        for panel_member in confirmed_panel.values():
            sf_contact = all_contacts_by_id[panel_member.contact_id]

            spm = sfimport.get_or_create_cached_survey_panel_member(sf_survey_client, sf_contact, None)
            spm.ContactType = panel_member.contact_type
            spm.ContactSeniority = panel_member.contact_seniority
            spm.ContactTitle = panel_member.contact_job_title
            spm.ContactDivision = panel_member.contact_division

            sfimport.get_or_create_cached_account_contact_relation(sf_agency_account, sf_contact)

        # figure out the updates we need to make
        final_confimed_panel = [x.model_dump(by_alias=True) for x in confirmed_panel.values()]
        was_auto_confirmed = bool(len(pdb_survey_client.confirmed_panel) == 0)
        sf_survey_client_updates[pdb_survey_client.Id] = {"Id": pdb_survey_client.Id,
                                                          "State__c": "Panel Agreed",
                                                          "Panel_Auto_Confirmed__c": was_auto_confirmed}
        pdb_survey_client_updates[pdb_survey_client.Id] = UpdateOne({"Id": pdb_survey_client.Id},
                                                                    {"$set": {"sfsync_date": datetime.datetime.now(datetime.UTC),
                                                                              "confirm_status": True,
                                                                              "auto_confirmed": was_auto_confirmed,
                                                                              "final_confirmed_panel": final_confimed_panel}})

    if writeit:
        # commit to salesforce
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_contacts, sfapi.Contact)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_account_contact_relations, sfapi.AccountContactRelation)
        sfapi.bulk_import_batch_to_sf(sf, sfimport.sfid_mapping, sfimport.sfcache, sfimport.new_survey_panel_members, sfapi.SurveyPanelMember)

        # update contact with new data from panel member
        if sf_contacts_to_patch:
            for status in sfapi.bulk_update(sf, "Contact", list(sf_contacts_to_patch.values())):
                sfapi.detect_bulk2_errors(status)

        # update the survey clients to indicate the panel is agreed
        if sf_survey_client_updates:
            for status in sfapi.bulk_update(sf, "Survey_Client__c", list(sf_survey_client_updates.values())):
                sfapi.detect_bulk2_errors(status)

        # reflect that back onto the portaldb confirm accounts now its all written to sf
        if pdb_survey_client_updates:
            confirm_panel_collection.bulk_write(list(pdb_survey_client_updates.values()))

    # let us know if something needs our attention!
    if resolution_errors:
        print("Resolution failures occurred, manual intervention required")
        print(resolution_errors)
        slack_resolution_errors(resolution_errors)


def lambda_handler(event, context):
    main(
        survey_client_id=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--survey_client_id', default=None, help='single survey client to run for')
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.survey_client_id, args.writeit)
