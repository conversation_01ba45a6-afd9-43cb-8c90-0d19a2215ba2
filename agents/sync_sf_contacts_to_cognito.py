import os
import boto3
import argparse
import random
import secrets
import string
from lib.settings import settings
from lib import sfapi, sfimport, portaldbapi
from simple_salesforce import format_soql


user_pool_id = settings.cognito_issuer.split('/')[-1]

def get_cognito_users(boto_client):
    cognito_users = []
    pagination_token = ''
    while pagination_token is not None:
        params = {}
        if pagination_token:
            params['PaginationToken'] = pagination_token

        response = boto_client.list_users(
            UserPoolId=user_pool_id,
            Limit=60,
            **params
        )
        pagination_token = response.get('PaginationToken')

        # turn the attributes into something useful (ie a dict instead of an irritating of list name/value pairs)
        for u in response['Users']:
            attributes = {a['Name']: a['Value'] for a in u['Attributes']}
            u['Attributes'] = attributes

        cognito_users.extend(response['Users'])

    return cognito_users


def get_sf_staff_contacts(sf):
    soql = format_soql("""
SELECT Id,
       Name,
       Email
FROM Contact
WHERE Account.RecordType.Name IN {record_type}
""", record_type=(sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING))

    return list(sfapi.bulk_query(sf, soql))


def create_cognito_user(boto_client, email):
    # make up a temp password
    password = ''.join(secrets.choice(string.ascii_lowercase) for i in range(5))
    password += ''.join(secrets.choice(string.ascii_uppercase) for i in range(5))
    password += ''.join(secrets.choice(string.digits) for i in range(5))
    password += ''.join(secrets.choice("%^&*") for i in range(3))
    password = list(password)
    random.shuffle(password)
    password = ''.join(password)

    # this will create the user in FORCE_CHANGE_PASSWORD state
    boto_client.admin_create_user(
        UserPoolId=user_pool_id,
        Username=email.lower(),
        UserAttributes=[
            {
                'Name': 'email',
                'Value': email.lower()
            },
            {
                'Name': 'email_verified',
                'Value': 'true'
            },
        ],
        MessageAction='SUPPRESS',
    )

    # give the user a temp password to get them out of the above state
    # note, we don't care what the password is, as they will have to change it, but AWS API insists we set one
    # and it has to match the password requirements!
    boto_client.admin_set_user_password(
        UserPoolId=user_pool_id,
        Username=email.lower(),
        Password=password,
        Permanent=True
    )


def disable_cognito_user(boto_client, email):
    boto_client.admin_disable_user(
        UserPoolId=user_pool_id,
        Username=email.lower(),
    )


def enable_cognito_user(boto_client, email):
    boto_client.admin_enable_user(
        UserPoolId=user_pool_id,
        Username=email.lower(),
    )


def handle_defunct_cognito_users(defunct_emails, boto_client, writeit):
    for email in defunct_emails:
        print('Disabling', email)

        if writeit:
            # FIXME: we're seeing an issue with users from third-part IdPs that are present in the user-pool throwing a "UserNotFoundException". Seems to be caused by "username" param in `adnmin_disable_user` being an email and not the uid.
            #       This is a workaround to avoid raising the error, but continue to log it. we should investigate the root cause.
            try:
                disable_cognito_user(boto_client, email)
            except boto_client.exceptions.UserNotFoundException:
                print('User not found in Cognito', email)
            except Exception as e:
                raise e


def handle_missing_cognito_users(missing_emails, all_cognito_users_by_email, boto_client, writeit):
    for email in missing_emails:

        if email in all_cognito_users_by_email:
            print('Enabling', email)
            if writeit:
                enable_cognito_user(boto_client, email)

        else:
            print('Creating', email)
            if writeit:
                create_cognito_user(boto_client, email)


def main(writeit):
    # we only want to auto-sync users in prod
    stack_name = os.environ.get('STACK', None)
    if stack_name not in {'prod'}:
        print('Not running in prod, bailing')
        return

    boto_client = boto3.client('cognito-idp')
    sf = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)

    cognito_users = get_cognito_users(boto_client)
    all_cognito_users_by_email = {u['Attributes']['email'].lower(): u for u in cognito_users}
    enabled_cognito_users_by_email = {u['Attributes']['email'].lower(): u for u in cognito_users if u['Enabled']}

    sf_contacts = get_sf_staff_contacts(sf)
    sf_contacts_by_email = {u['Email'].lower(): u for u in sf_contacts}

    missing_cognito_users = set(sf_contacts_by_email.keys()) - set(enabled_cognito_users_by_email.keys())

    handle_defunct_cognito_users(set(enabled_cognito_users_by_email.keys()) - set(sf_contacts_by_email.keys()), boto_client, writeit)
    handle_missing_cognito_users(missing_cognito_users, all_cognito_users_by_email, boto_client, writeit)


def lambda_handler(event, context):
    main(True)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.writeit)
