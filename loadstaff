#!/bin/sh

set -e

XLSX=$1
IMPORTTYPE=$2
WRITEIT=$3
XLSXBASE=`basename "$XLSX"`

NOSAFEEMAILS=""
if [ "$STACK" = "prod" ]; then
    NOSAFEEMAILS="--nosafeemails"
    read -p "Are you sure you mean to run against prod? (type YES if you are) " PROD_IS_OK
    if [ "$PROD_IS_OK" != "YES" ]; then
        echo "Aborting"
        exit 1
    fi
    export PROD_IS_OK

    if [ "$AGENCY_DEV_PREFIX" != "" ]; then
        echo "AGENCY_DEV_PREFIX must not be set in prod"
        exit 1
    fi
fi

python3 -m devtools.xlsxtocsv "$XLSX"
python3 -m sftools.importstaffreportingcsv "$XLSX.template - dashboardsreports.csv" $IMPORTTYPE $NOSAFEEMAILS $WRITEIT
if [ -f 'duffrecords-staff.csv' ]; then
    mv 'duffrecords-staff.csv' "$XLSXBASE.duffrecords-reporting.csv"
fi

python3 -m sftools.importstaffaccountmanagercsv "$XLSX.account managers.csv" $IMPORTTYPE $NOSAFEEMAILS $WRITEIT
if [ -f 'duffrecords-staff.csv' ]; then
    mv 'duffrecords-staff.csv' "$XLSXBASE.duffrecords-accountmanagers.csv"
fi

python3 -m sftools.importstaffpanelmanagerscsv "$XLSX.survey panel managers.csv" $IMPORTTYPE $NOSAFEEMAILS $WRITEIT
if [ -f 'duffrecords-staff.csv' ]; then
    mv 'duffrecords-staff.csv' "$XLSXBASE.duffrecords-panelmanagers.csv"
fi

python3 -m sftools.importstaffsignatoriescsv "$XLSX.signatories.csv" $IMPORTTYPE $NOSAFEEMAILS $WRITEIT
if [ -f 'duffrecords-staff.csv' ]; then
    mv 'duffrecords-staff.csv' "$XLSXBASE.duffrecords-signatories.csv"
fi
