import gradio as gr
import psycopg2
import argparse
import json
import copy
import datetime
from lib.settings import settings


DIMENSIONS = [
    {
        "name": "Object Name",
        "sql": "object_name",
    },

    {
        "name": "Issue Code",
        "sql": "code",
    },

    {
        "name": "Severity",
        "sql": "level",
    },

    {
        "name": "Active Round",
        "sql": "case when related->>'is_active_round' is NULL then 'unknown' else related->>'is_active_round' end"
    },
]


DESCRIPTIONS = {
    "AccountContactRelation-missing": "A contact is used as a survey panel member but there is no AccountContactRelation record for it",
    "Account-duplicate-business-key": "Multiple accounts with the same Name and Parent Account detected",
    "AccountGroup-AccountId-not-advertising-or-manufacturing": "The Account an AccountGroup is related to is not advertising or manufacturing",
    "AccountGroup-duplicate-business-key": "Duplicate AccountGroup detected",
    "Account-OrganisationalLevel-mismatch": "An account is using an incorrect Organisational Level for the level is it at",
    "Account-OrganisationalLevel-top-of-tree-mismatch": "A top level account is using an incorrect Organisational Level",
    "Account-RecordTypeId-mismatch": "An Account tree has mixed Record Types",
    "Account-UltimateParent-mismatch": "The Ultimate Parent of an Account is incorrect",
    "Contact-AccountManager-role-missing": "A contact is used as an Account Manager but does not have the AccountManager checkbox checked",
    "Contact-duplicate-business-key": "Multiple contacts with the same email address detected",
    "Contact-MultipleAccountRecordTypes": "A single email @domain.com is used for multiple Account Record Types",
    "Contact-MultipleTopLevelAccounts": "A single email @domain.com is used for multiple Top Level Accounts",
    "ContactKeyAccount-Account-missing": "A ContactKeyAccount is missing its Account",
    "ContactKeyAccount-ContactReportView-missing": "A ContactKeyAccount is missing its ContactReportView",
    "ContactKeyAccount-duplicate-business-key": "Duplicate ContactKeyAccount detected",
    "ContactKeyCustomer-ContactReportView-missing": "A ContactKeyCustomer is missing its ContactReportView",
    "ContactKeyCustomer-duplicate-business-key": "Duplicate ContactKeyCustomer detected",
    "ContactKeyMarket-ContactReportView-missing": "A ContactKeyMarket is missing its ContactReportView",
    "ContactKeyMarket-duplicate-business-key": "Duplicate ContactKeyMarket detected",
    "ContactKeyTeam-ContactReportView-missing": "A ContactKeyTeam is missing its ContactReportView",
    "ContactKeyTeam-duplicate-business-key": "Duplicate ContactKeyTeam detected",
    "Contact-PanelManager-role-missing": "A contact is used as a Panel Manager but does not have the PanelManager checkbox checked",
    "ContactReportingCategory-ContactReportView-missing": "A ContactReportingCategory is missing its ContactReportView",
    "ContactReportingCategory-duplicate-business-key": "Duplicate ContactReportingCategory detected",
    "Contact-Signatory-role-missing": "A Contact is used as a Signatory but does not have the Signatory checkbox checked",
    "Contact-with-signature-but-not-signatory": "A Contact has a signature but is not marked as a Signatory",
    "CustomerClientRelationship-AccountGroup-not-part-of-agency-account": "The AccountGroup a CustomerClientRelationship is related to is not relaetd to the same Account",
    "CustomerClientRelationship-CustomerAccountId-missing": "A CustomerClientRelationship is missing its Customer Account",
    "CustomerClientRelationship-duplicate-business-key": "A duplicate CustomerClientRelationship detected",
    "CustomerClientRelationship-missing": "A customer client is used as a Survey Client, but there is no CustomerClientRelationship record for it",
    "CustomerSurveyRound-dates-not-in-order": "The dates of a CustomerSurveyRound are not ascending order",
    "CustomerSurveyRound-dates-not-sane": "The dates of a CustomerSurveyRound are not within 90 days of the max/min dates of its SurveyRound",
    "CustomerSurveyRound-OrganisationalLevel-not-top-of-tree": "A CustomerSurveyRound's Account must be a top level account",
    "CustomerSurvey-dates-not-in-order": "The dates of a CustomerSurvey are not ascending order",
    "CustomerSurvey-dates-not-sane": "The dates of a CustomerSurvey are not within 90 days of the max/min dates of its CustomerSurveyRound",
    "CustomerSurvey-duplicate-business-key": "A duplicate CustomerSurvey detected",
    "Division-AccountId-missing": "A Division is missing its Account",
    "Division-AccountId-not-advertising-or-manufacturing": "A Division is related to an Account that is not advertising or manufacturing",
    "Division-duplicate-business-key": "Duplicate Division detected",
    "SurveyAccountManager-Contact-missing": "A SurveyAccountManager is missing its Contact",
    "SurveyAccountManager-duplicate-business-key": "A Duplicate SurveyAccountManager detected",
    "SurveyClient-duplicate-business-key": "A Duplicate SurveyClient detected",
    "SurveyClient-EmptySurveyClient": "An active SurveyClient has no panel members was detected",
    "SurveyPanelManager-duplicate-business-key": "A Duplicate SurveyPanelManager detected",
    "SurveyPanelMember-duplicate-business-key": "A Duplicate SurveyPanelMember detected",
    "SurveyResponse-duplicate-business-key": "A Duplicate SurveyResponse detected",
    "Team-AccountId-missing": "A Team is missing its Account",
    "Team-AccountId-not-advertising-or-manufacturing": "The Account a Team is related to is not advertising or manufacturing",
    "Team-duplicate-business-key": "Duplicate Team detected",
    "Team-Market-missing": "A Team is missing its Market",
}


def get_dimension_values():
    with psycopg2.connect(settings.monitordb_salesforce_url) as conn:
        with conn.cursor() as cursor:
            # get values of all dimensions
            dimension_values = {}
            dimension_sql = ",".join([x['sql'] for x in DIMENSIONS])
            dimension_select_sql = f"""
        SELECT DISTINCT {dimension_sql}
        FROM validation_issues
        """
            cursor.execute(dimension_select_sql, (datetime.datetime.now() - datetime.timedelta(days=30), ))
            for row in cursor:
                for i, dim in enumerate(DIMENSIONS):
                    dimension_values.setdefault(dim['name'], set())
                    dimension_values[dim['name']].add(row[i])

            return dimension_values


def get_issue_ids(current_state):
    with psycopg2.connect(settings.monitordb_salesforce_url) as conn:
        with conn.cursor() as cursor:
            params = []
            where_sqls = [
                "1=1"
            ]
            for dimension in DIMENSIONS:
                if current_state.get(dimension['name']):
                    where_sqls.append(f"COALESCE({dimension['sql']}, '__NONE__') in %s")

                    selected_values = copy.copy(current_state[dimension['name']])
                    if None in selected_values:
                        selected_values.remove(None)
                        selected_values.append('__NONE__')
                    params.append(tuple(selected_values))

            email_sql = f"""
        SELECT vi_id
        FROM validation_issues
        WHERE {" and ".join(where_sqls)}
        ORDER BY code
        """
            cursor.execute(email_sql, params)
            return list(cursor.fetchall())


def main():
    js_func = """
    function refresh() {
        const url = new URL(window.location);

        if (url.searchParams.get('__theme') !== 'light') {
            url.searchParams.set('__theme', 'light');
            window.location.href = url.href;
        }
    }
    """

    with gr.Blocks(js=js_func) as demo:
        state = gr.State(value={})

        with gr.Row():
            dimension_dropdowns = [
                gr.Dropdown(label=dimension['name'],
                            choices=[],
                            multiselect=True, interactive=True) for dimension in DIMENSIONS
            ]

        object_name = gr.Textbox(label="Object Name")
        description = gr.Textbox(label="Description")
        error_code = gr.Textbox(label="Error Code")
        severity = gr.Textbox(label="Severity")
        position = gr.Textbox(label="Position")
        with gr.Row():
            prev_button = gr.Button(value="Prev")
            next_button = gr.Button(value="Next")
        related = gr.HTML(label="Details", container=True, show_label=True)

        def update_dimension_values():
            result = []
            dimension_values = get_dimension_values()
            for dimension_dropdown in dimension_dropdowns:
                result.append(gr.update(choices=sorted(dimension_values[dimension_dropdown.label], key=lambda x: str(x).lower())))
            return result

        def update_gui(params):
            current_state = params[state]
            current_index = current_state.get('current_index', 0)
            if 'ids' not in current_state:
                ids = current_state['ids'] = get_issue_ids(current_state)
            else:
                ids = current_state['ids']

            if prev_button in params:
                if current_index > 0:
                    current_index -= 1

            elif next_button in params:
                if current_index < len(ids) - 1:
                    current_index += 1

            dropdown_changed = False
            for dropdown_button in dimension_dropdowns:
                if dropdown_button in params:
                    current_state[dropdown_button.label] = params[dropdown_button]
                    dropdown_changed = True
            if dropdown_changed:
                ids = current_state['ids'] = get_issue_ids(current_state)
                current_index = 0
            current_state['current_index'] = current_index

            if current_index >= len(ids):
                return [gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        current_state]

            email_sql = f"""
SELECT object_name, code, level, related
FROM validation_issues
WHERE vi_id = %s
    """
            with psycopg2.connect(settings.monitordb_salesforce_url) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(email_sql, (ids[current_index], ))
                    current_object_name, current_code, current_level, current_related = cursor.fetchone()

                    current_related_html = f"<pre>{json.dumps(current_related, indent=2)}</pre>"

            result = [gr.update(value=current_object_name),
                      gr.update(value=DESCRIPTIONS.get(current_code, "???")),
                      gr.update(value=current_code),
                      gr.update(value=current_level),
                      gr.update(value=f"{current_index + 1}/{len(ids)}"),
                      gr.update(value=current_related_html),
                      current_state]
            return result

        update_gui_outputs = [object_name, description, error_code, severity, position, related, state]

        for dimension_dropdown in dimension_dropdowns:
            dimension_dropdown.change(update_gui, inputs={dimension_dropdown, state}, outputs=update_gui_outputs)

        next_button.click(update_gui, inputs={next_button, state}, outputs=update_gui_outputs)
        prev_button.click(update_gui, inputs={prev_button, state}, outputs=update_gui_outputs)
        demo.load(update_dimension_values, outputs=dimension_dropdowns)
        demo.load(update_gui, inputs={state}, outputs=update_gui_outputs)

    demo.launch(server_name="0.0.0.0",
                server_port=7681,
                share=False,
                auth=('crc', 'em@1ld3bug'))


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
