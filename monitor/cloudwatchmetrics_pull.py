import boto3
import copy
import csv
import io
import psycopg2
import os
import argparse
import datetime
import json
from lib.settings import settings
from lib import sfimport


DB_BATCH_SIZE = 1000

AMPLIFY_ITEMS = [
    {'appid': 'd2bhczol2emhg7', 'name': 'crc-clientarea-eu-west-1-amplify-app-prod'},
    {'appid': 'd33bamnor76dvz', 'name': 'crc-survey-eu-west-1-amplify-app-prod'},
]
APIGATEWAY_ITEMS = [
    {'name': 'crc-api-clientarea-prod'},
    {'name': 'crc-api-survey-prod'},
    {'name': 'crc-api-agents-prod'},
]
LAMBDA_ITEMS = [
    {'name': 'crc-api-clientarea-prod'},
    {'name': 'crc-api-survey-prod'},
    {'name': 'crc-api-agents-prod'},

    {'name': 'crc-agent-cognito_email_trigger-prod'},
    {'name': 'crc-agent-sync_sfschema_to_s3-prod'},
    {'name': 'crc-agent-email_scheduler_panel_users-prod'},
    {'name': 'crc-agent-sync_survey_access_audit_to_sf-prod'},
    {'name': 'crc-agent-email_scheduler_staff_users-prod'},
    {'name': 'crc-agent-sync_signatures_to_s3-prod'},
    {'name': 'crc-agent-sync_banners_to_s3-prod'},
    {'name': 'crc-agent-sync_markets_to_portaldb-prod'},
    {'name': 'crc-agent-send_email_to_sg-prod'},
    {'name': 'crc-agent-sync_finalpanel_to_portaldb-prod'},
    {'name': 'crc-agent-sync_panel_manager_activity_to_sf-prod'},
    {'name': 'crc-agent-sync_confirmpanel_audit_to_sf-prod'},
    {'name': 'crc-agent-sync_finalaccounts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_sfsimple_to_portaldb-prod'},
    {'name': 'crc-agent-survey_setup-prod'},
    {'name': 'crc-agent-email_sender_panel_users-prod'},
    {'name': 'crc-agent-slack_monitoring-prod'},
    {'name': 'crc-agent-sync_pendingcustomercontacts_to_sf-prod'},
    {'name': 'crc-agent-sync_optout_to_sf-prod'},
    {'name': 'crc-agent-sync_confirmpanel_to_sf-prod'},
    {'name': 'crc-agent-sync_reports_to_portaldb-prod'},
    {'name': 'crc-agent-sync_panel_member_status_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_sf_to_s3-prod'},
    {'name': 'crc-agent-sync_confirmpanel_to_portaldb-prod'},
    {'name': 'crc-agent-sync_customersurveyrounds_to_portaldb-prod'},
    {'name': 'crc-agent-sync_survey_updates_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_audit_to_sf-prod'},
    {'name': 'crc-agent-sync_surveyresponses_to_sf-prod'},
    {'name': 'crc-agent-sync_email_analytics_to_sf-prod'},
    {'name': 'crc-agent-sync_customercontacts_to_portaldb-prod'},
    {'name': 'crc-agent-sync_account_manager_activity_to_sf-prod'},
    {'name': 'crc-agent-confirmaccounts_new_objects_daily_state-prod'},
    {'name': 'crc-agent-sync_srpreports_to_portaldb-prod'},
    {'name': 'crc-agent-sync_accesslog_to_sf-prod'},
    {'name': 'crc-agent-sync_sf_contacts_to_cognito-prod'},
    {'name': 'crc-agent-sync_responses_to_portaldb-prod'},
    {'name': 'crc-agent-sync_confirmaccounts_to_sf-prod'},
]
DOCUMENTDB_ITEMS = [
    {'name': 'crc-portaldb-docdb-cluster-eu-west-1-prod'},
]

AMPLIFY_METRICS_TEMPLATE = [
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/AmplifyHosting',
                'MetricName': '5xxErrors',
                'Dimensions': [
                    {
                        'Name': 'App',
                        'Value': '{appid}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "amplify-{name}-5xxErrors-Sum",
        "Dimensions": {
            'service': 'amplify',
            'name': '{name}',
            'metric': '5xxErrors',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/AmplifyHosting',
                'MetricName': 'Requests',
                'Dimensions': [
                    {
                        'Name': 'App',
                        'Value': '{appid}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "amplify-{name}-Requests-Sum",
        "Dimensions": {
            'service': 'amplify',
            'name': '{name}',
            'metric': 'Requests',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/AmplifyHosting',
                'MetricName': 'Latency',
                'Dimensions': [
                    {
                        'Name': 'App',
                        'Value': '{appid}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Average',
        },
        "Label": "amplify-{name}-Latency-Average",
        "Dimensions": {
            'service': 'amplify',
            'name': '{name}',
            'metric': 'Latency',
            'aggregation': 'Average',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/AmplifyHosting',
                'MetricName': 'Latency',
                'Dimensions': [
                    {
                        'Name': 'App',
                        'Value': '{appid}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Maximum',
        },
        "Label": "amplify-{name}-Latency-Maximum",
        "Dimensions": {
            'service': 'amplify',
            'name': '{name}',
            'metric': 'Latency',
            'aggregation': 'Maximum',
        },
    },
]

APIGATEWAY_METRICS_TEMPLATE = [
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/ApiGateway',
                'MetricName': '5XXError',
                'Dimensions': [
                    {
                        'Name': 'ApiName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "apigateway-{name}-5xxErrors-Sum",
        "Dimensions": {
            'service': 'apigateway',
            'name': '{name}',
            'metric': '5xxErrors',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/ApiGateway',
                'MetricName': 'Count',
                'Dimensions': [
                    {
                        'Name': 'ApiName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "apigateway-{name}-Requests-Sum",
        "Dimensions": {
            'service': 'apigateway',
            'name': '{name}',
            'metric': 'Requests',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/ApiGateway',
                'MetricName': 'Latency',
                'Dimensions': [
                    {
                        'Name': 'ApiName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Average',
        },
        "Label": "apigateway-{name}-Latency-Average",
        "Dimensions": {
            'service': 'apigateway',
            'name': '{name}',
            'metric': 'Latency',
            'aggregation': 'Average',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/ApiGateway',
                'MetricName': 'Latency',
                'Dimensions': [
                    {
                        'Name': 'ApiName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Maximum',
        },
        "Label": "apigateway-{name}-Latency-Maximum",
        "Dimensions": {
            'service': 'apigateway',
            'name': '{name}',
            'metric': 'Latency',
            'aggregation': 'Maximum',
        },
    },
]


LAMBDA_METRICS_TEMPLATE = [
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/Lambda',
                'MetricName': 'Errors',
                'Dimensions': [
                    {
                        'Name': 'FunctionName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "lambda-{name}-Errors-Sum",
        "Dimensions": {
            'service': 'lambda',
            'name': '{name}',
            'metric': 'Errors',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/Lambda',
                'MetricName': 'Invocations',
                'Dimensions': [
                    {
                        'Name': 'FunctionName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "lambda-{name}-Invocations-Sum",
        "Dimensions": {
            'service': 'lambda',
            'name': '{name}',
            'metric': 'Invocations',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/Lambda',
                'MetricName': 'Duration',
                'Dimensions': [
                    {
                        'Name': 'FunctionName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Average',
        },
        "Label": "lambda-{name}-Duration-Average",
        "Dimensions": {
            'service': 'lambda',
            'name': '{name}',
            'metric': 'Duration',
            'aggregation': 'Average',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/Lambda',
                'MetricName': 'Duration',
                'Dimensions': [
                    {
                        'Name': 'FunctionName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Maximum',
        },
        "Label": "lambda-{name}-Duration-Maximum",
        "Dimensions": {
            'service': 'lambda',
            'name': '{name}',
            'metric': 'Duration',
            'aggregation': 'Maximum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/Lambda',
                'MetricName': 'Throttles',
                'Dimensions': [
                    {
                        'Name': 'FunctionName',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "lambda-{name}-Throttles-Sum",
        "Dimensions": {
            'service': 'lambda',
            'name': '{name}',
            'metric': 'Throttles',
            'aggregation': 'Sum',
        },
    },
]


DOCUMENTDB_METRICS_TEMPLATE = [
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DatabaseConnections',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "documentdb-{name}-DatabaseConnection-Sum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DatabaseConnection',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'CPUUtilization',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Maximum',
        },
        "Label": "documentdb-{name}-CPUUtilization-Maximum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'CPUUtilization',
            'aggregation': 'Maximum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'CPUUtilization',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Average',
        },
        "Label": "documentdb-{name}-CPUUtilization-Average",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'CPUUtilization',
            'aggregation': 'Average',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'FreeLocalStorage',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Minimum',
        },
        "Label": "documentdb-{name}-FreeLocalStorage-Minimum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'FreeLocalStorage',
            'aggregation': 'Minimum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DocumentsInserted',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "documentdb-{name}-DocumentsInserted-Sum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DocumentsInserted',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DocumentsReturned',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "documentdb-{name}-DocumentsReturned-Sum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DocumentsReturned',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DocumentsUpdated',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "documentdb-{name}-DocumentsUpdated-Sum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DocumentsUpdated',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DocumentsDeleted',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Sum',
        },
        "Label": "documentdb-{name}-DocumentsDeleted-Sum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DocumentsDeleted',
            'aggregation': 'Sum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DBInstanceReplicaLag',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },
                ]
            },
            'Period': 60,
            'Stat': 'Maximum',
        },
        "Label": "documentdb-{name}-DBInstanceReplicaLag-Maximum",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DBInstanceReplicaLag',
            'aggregation': 'Maximum',
        },
    },
    {
        "MetricStat": {
            'Metric': {
                'Namespace': 'AWS/DocDB',
                'MetricName': 'DBInstanceReplicaLag',
                'Dimensions': [
                    {
                        'Name': 'DBClusterIdentifier',
                        'Value': '{name}'
                    },]
            },
            'Period': 60,
            'Stat': 'Average',
        },
        "Label": "documentdb-{name}-DBInstanceReplicaLag-Average",
        "Dimensions": {
            'service': 'documentdb',
            'name': '{name}',
            'metric': 'DBInstanceReplicaLag',
            'aggregation': 'Average',
        },
    },
]


def _apply_doc_params(d, doc_params):
    if isinstance(d, str):
        return d.format(**doc_params)

    elif isinstance(d, dict):
        for key, value in d.items():
            d[key] = _apply_doc_params(value, doc_params)
        return d

    elif isinstance(d, list):
        for i, value in enumerate(d):
            d[i] = _apply_doc_params(value, doc_params)
        return d

    else:
        return d


def pull_metric_timeseries(cloudwatch, start_time, end_time, docs, doc_params):
    # sort out the queries
    queries = []
    dimensions_by_label = {}
    for doc in docs:
        querydoc = _apply_doc_params(copy.deepcopy(doc), doc_params)
        q = {
            "Id": querydoc['Label'].replace('-', '_').lower(),
            "Label": querydoc['Label'],
            'MetricStat': querydoc['MetricStat'],
            'ReturnData': True
        }
        queries.append(q)
        dimensions_by_label[q['Label']] = querydoc['Dimensions']

    # get data, with pagination
    results = {}
    next_token = True
    while next_token:
        if next_token and next_token is not True:
            response = cloudwatch.get_metric_data(
                MetricDataQueries=queries,
                StartTime=start_time,
                EndTime=end_time,
                NextToken=next_token,
            )
        else:
            response = cloudwatch.get_metric_data(
                MetricDataQueries=queries,
                StartTime=start_time,
                EndTime=end_time,
            )

        for result in response['MetricDataResults']:
            label = result['Label']
            results.setdefault(label, {'Timestamps': [], 'Values': []})
            results[label]['Timestamps'] += result['Timestamps']
            results[label]['Values'] += result['Values']
            results[label]['Dimensions'] = dimensions_by_label[label]

            if result['StatusCode'] not in {'Complete', 'Incomplete'}:
                raise Exception(f"Error: {result['StatusCode']}")

        next_token = response.get('NextToken')

    return results


def import_batches(cloudwatch, conn, template, items, datetimefrom, datetimeto):
    batch = []
    for item in items:
        results = pull_metric_timeseries(cloudwatch, datetimefrom, datetimeto, template, item)
        for timeseries_name, data in results.items():
            dimensions = json.dumps(data['Dimensions'])
            values = data['Values']
            for i, timestamp in enumerate(data['Timestamps']):
                batch.append({
                    'name': timeseries_name,
                    'datasource': 'cloudwatch',
                    'datetime': timestamp,
                    'value': values[i],
                    'dimensions': dimensions,
                })

        if len(batch) > DB_BATCH_SIZE:
            sfimport.write_record_batch(conn, 'public.timeseries', batch)
            batch = []

    if batch:
        sfimport.write_record_batch(conn, 'public.timeseries', batch)


def main(datetimefrom, datetimeto):
    cloudwatch = boto3.client('cloudwatch', region_name=os.getenv('AWS_REGION', 'eu-west-1'))
    conn = psycopg2.connect(settings.monitordb_timeseries_url)

    # clear out any old data
    with conn.cursor() as cursor:
        sql = """
DELETE FROM public.timeseries
WHERE datetime >= cast(%s as timestamptz) AND datetime < cast(%s as timestamptz)
AND datasource = 'cloudwatch'
"""
        cursor.execute(sql, (datetimefrom, datetimeto))
        conn.commit()

    import_batches(cloudwatch, conn, AMPLIFY_METRICS_TEMPLATE, AMPLIFY_ITEMS, datetimefrom, datetimeto)
    import_batches(cloudwatch, conn, APIGATEWAY_METRICS_TEMPLATE, APIGATEWAY_ITEMS, datetimefrom, datetimeto)
    import_batches(cloudwatch, conn, LAMBDA_METRICS_TEMPLATE, LAMBDA_ITEMS, datetimefrom, datetimeto)
    import_batches(cloudwatch, conn, DOCUMENTDB_METRICS_TEMPLATE, DOCUMENTDB_ITEMS, datetimefrom, datetimeto)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("datetimefrom", type=str, help="Datetime from (YYYY-MM-DDTHH:MM) inclusive")
    ap.add_argument("datetimeto", type=str, help="Datetime to (YYYY-MM-DDTHH:MM) exclusive")
    args = ap.parse_args()

    datetimefrom = datetime.datetime.strptime(args.datetimefrom, "%Y-%m-%dT%H:%M:%S")
    datetimeto = datetime.datetime.strptime(args.datetimeto, "%Y-%m-%dT%H:%M:%S")
    datetimefrom = datetimefrom.replace(tzinfo=datetime.timezone.utc)
    datetimeto = datetimeto.replace(tzinfo=datetime.timezone.utc)

    main(datetimefrom, datetimeto)
