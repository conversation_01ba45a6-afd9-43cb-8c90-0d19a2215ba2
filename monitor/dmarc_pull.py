import json
import argparse
import psycopg2
from email.parser import Bytes<PERSON><PERSON><PERSON>
from email import policy
import zipfile
import datetime
from io import BytesIO
import gzip
from defusedxml.ElementTree import parse as parsexml
from lib.settings import settings
from lib import sfimport


def identify_file_type(payload: bytes):
    if payload[:4] == b'PK\x03\x04':
        return "zip"
    elif payload[:2] == b'\x1f\x8b':
        return "gzip"
    else:
        return None

def extract_payload_xml(payload: bytes):
    payload_type = identify_file_type(payload)
    if payload_type == 'zip':
        with zipfile.ZipFile(BytesIO(payload)) as z:
            for file_info in z.infolist():
                with z.open(file_info) as extracted_file:
                    return extracted_file.read()

    elif payload_type == 'gzip':
        with gzip.GzipFile(fileobj=BytesIO(payload)) as gz:
            return gz.read()

    else:
        print("Unknown payload type")
        exit()


def remove_namespace(elem):
    """
    function to remove namespace from  doc element
    node_key : xml doc element
    """
    elem = elem[elem.find("}") + 1 :] if elem.startswith("{") else elem
    return elem


def remove_all_namespaces(doc):
    """
    function to remove namespaces from xml
    doc : xml doc element
    """
    for elem in doc.iter():
        elem.tag = remove_namespace(elem.tag)
        elem.attrib = {remove_namespace(key): value for key, value in elem.attrib.items()}
    return doc


def process_dmarc_email(eml: bytes, received_at: datetime.datetime):
    msg = BytesParser(policy=policy.default).parsebytes(eml.encode('utf-8'))

    for part in msg.walk():
        content_type = part.get_content_type()
        if content_type not in {'application/gzip', 'application/zip', 'application/octet-stream'}:
            continue

        xmlbytes = extract_payload_xml(part.get_payload(decode=True))
        root = remove_all_namespaces(parsexml(BytesIO(xmlbytes)))

        # print(xmlbytes.decode('utf-8'))

        org_name = root.find('./report_metadata/org_name').text
        org_email = root.find('./report_metadata/email').text
        report_id = root.find('./report_metadata/report_id').text
        date_range_begin = datetime.datetime.fromtimestamp(int(root.find('./report_metadata/date_range/begin').text), tz=datetime.timezone.utc)
        date_range_end = datetime.datetime.fromtimestamp(int(root.find('./report_metadata/date_range/end').text), tz=datetime.timezone.utc)

        published_policy = {}
        for node in root.findall('./policy_published/*'):
            published_policy[node.tag] = node.text

        for record in root.findall('./record'):
            source_ip = record.find('./row/source_ip').text
            count = record.find('./row/count').text
            action_taken = record.find('./row/policy_evaluated/disposition').text
            dkim_result = record.find('./row/policy_evaluated/dkim').text
            spf_result = record.find('./row/policy_evaluated/spf').text

            identifers = {}
            for node in record.findall('./identifiers/*'):
                identifers[node.tag] = node.text

            auth_details = []
            for node in record.findall('./auth_results/*'):
                if node.tag == 'dkim':
                    dkim = {
                        'type': 'dkim',
                        'domain': node.find('domain').text,
                        'result': node.find('result').text
                    }
                    selector = node.find('selector')
                    if selector is not None:
                        dkim['selector'] = selector.text
                    auth_details.append(dkim)

                elif node.tag == 'spf':
                    spf = {
                        'type': 'spf',
                        'domain': node.find('domain').text,
                        'result': node.find('result').text
                    }
                    scope = node.find('scope')
                    if scope is not None:
                        spf['scope'] = scope.text
                    auth_details.append(spf)

                else:
                    raise ValueError(f"Unknown auth result type: {node.tag}")

            outrow = {
                'received_at': received_at,
                'org_name': org_name,
                'org_email': org_email,
                'report_id': report_id,
                'datetimefrom': date_range_begin,
                'datetimeto': date_range_end,
                'source_ip': source_ip,
                'message_count': count,
                'action_taken': action_taken,
                'dkim_result': dkim_result,
                'spf_result': spf_result,
                'identifiers': json.dumps(identifers),
                'published_policy': json.dumps(published_policy),
                'auth_results': json.dumps(auth_details)
            }
            yield outrow


def main(datetimefrom, datetimeto):
    conn = psycopg2.connect(settings.monitordb_email_url)
    cursor = conn.cursor()

    sql = """
    DELETE FROM public.dmarc
    WHERE received_at >= cast(%s as timestamptz) AND received_at < cast(%s as timestamptz)
    """
    cursor.execute(sql, (datetimefrom, datetimeto))
    conn.commit()

    email_select_sql = """
    SELECT email_id, received_at, eml
    FROM email
    WHERE action = 'received' AND triage_label = 'dmarc'
    AND received_at >= cast(%s as timestamptz) AND received_at < cast(%s as timestamptz)
    """
    cursor.execute(email_select_sql, (datetimefrom, datetimeto))
    emails = cursor.fetchall()

    batch = []
    for email_id, received_at, eml in emails:
        for row in process_dmarc_email(eml, received_at):
            batch.append(row)

    sfimport.write_record_batch(conn, 'public.dmarc', batch)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("datetimefrom", type=str, help="Datetime from (YYYY-MM-DDTHH:MM) inclusive")
    ap.add_argument("datetimeto", type=str, help="Datetime to (YYYY-MM-DDTHH:MM) exclusive")
    args = ap.parse_args()

    datetimefrom = datetime.datetime.strptime(args.datetimefrom, "%Y-%m-%dT%H:%M:%S")
    datetimeto = datetime.datetime.strptime(args.datetimeto, "%Y-%m-%dT%H:%M:%S")
    datetimefrom = datetimefrom.replace(tzinfo=datetime.timezone.utc)
    datetimeto = datetimeto.replace(tzinfo=datetime.timezone.utc)

    main(datetimefrom, datetimeto)
