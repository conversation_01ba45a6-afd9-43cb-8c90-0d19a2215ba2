import gradio as gr
import psycopg2
import argparse
import json
import copy
import datetime
from email.parser import Bytes<PERSON>ars<PERSON>
from email import policy
from fastapi import Response, HTTPException
from lib.settings import settings


DIMENSIONS = [
    {
        "name": "Triage Label",
        "sql": "case when triage_label is null then 'NOT TRIAGED' else triage_label end",
    },

    {
        "name": "Person Type",
        "sql": "case when triage_details->>'person_type' is NULL then 'unknown' else triage_details->>'person_type' end",
    },

    {
        "name": "Contact Known",
        "sql": "case when triage_details->>'from_contact_id' is not NULL then 'true' else 'false' end",
    },

    {
        "name": "Out of Office",
        "sql": "case when triage_details->>'out_of_office'is null then 'unknown' else triage_details->>'out_of_office' end",
    },

    {
        "name": "Top Level Agency",
        "sql": "case when triage_details->>'top_level_agency_account_name' is null then 'unknown' else triage_details->>'top_level_agency_account_name' end",
    },

    {
        "name": "Top Level Customer Client",
        "sql": "case when triage_details->>'top_level_customer_client_account_name' is null then 'unknown' else triage_details->>'top_level_customer_client_account_name' end",
    },
]


def get_dimension_values():
    with psycopg2.connect(settings.monitordb_email_url) as conn:
        with conn.cursor() as cursor:
            # get values of all dimensions
            dimension_values = {}
            dimension_sql = ",".join([x['sql'] for x in DIMENSIONS])
            dimension_select_sql = f"""
        SELECT DISTINCT {dimension_sql}
        FROM email
        WHERE action = 'received'
        AND received_at > cast(%s as timestamptz)
        """
            cursor.execute(dimension_select_sql, (datetime.datetime.now() - datetime.timedelta(days=30), ))
            for row in cursor:
                for i, dim in enumerate(DIMENSIONS):
                    dimension_values.setdefault(dim['name'], set())
                    dimension_values[dim['name']].add(row[i])

            return dimension_values


def get_email_ids(current_state):
    with psycopg2.connect(settings.monitordb_email_url) as conn:
        with conn.cursor() as cursor:
            params = []
            where_sqls = [
                "action = 'received'",
            ]
            for dimension in DIMENSIONS:
                if current_state.get(dimension['name']):
                    where_sqls.append(f"COALESCE({dimension['sql']}, '__NONE__') in %s")

                    selected_values = copy.copy(current_state[dimension['name']])
                    if None in selected_values:
                        selected_values.remove(None)
                        selected_values.append('__NONE__')
                    params.append(tuple(selected_values))

            email_sql = f"""
        SELECT email_id
        FROM email
        WHERE {" and ".join(where_sqls)}
        ORDER BY received_at DESC
        """
            cursor.execute(email_sql, params)
            return list(cursor.fetchall())


def main():
    js_func = """
    function refresh() {
        const url = new URL(window.location);

        if (url.searchParams.get('__theme') !== 'light') {
            url.searchParams.set('__theme', 'light');
            window.location.href = url.href;
        }
    }
    """

    with gr.Blocks(js=js_func) as demo:
        state = gr.State(value={})

        with gr.Row():
            dimension_dropdowns = [
                gr.Dropdown(label=dimension['name'], choices=[], multiselect=True, interactive=True) for dimension in DIMENSIONS
            ]

        subject = gr.Textbox(label="Subject")
        received = gr.Textbox(label="Received")
        from_ = gr.Textbox(label="From")
        to = gr.Textbox(label="To")
        position = gr.Textbox(label="Position")
        with gr.Row():
            prev_button = gr.Button(value="Prev")
            next_button = gr.Button(value="Next")
        triage_details = gr.HTML(label="Triage Details", container=True, show_label=True)
        attachments = gr.HTML(label="Attachments", container=True, show_label=True)
        body = gr.HTML(label="Body", container=True, show_label=True)

        def update_dimension_values():
            result = []
            dimension_values = get_dimension_values()
            for dimension_dropdown in dimension_dropdowns:
                result.append(gr.update(choices=sorted(dimension_values[dimension_dropdown.label], key=lambda x: str(x).lower())))
            return result

        def update_email_gui(params, request: gr.Request):
            current_state = params[state]
            current_email_index = current_state.get('current_email_index', 0)
            if 'email_ids' not in current_state:
                email_ids = current_state['email_ids'] = get_email_ids(current_state)
            else:
                email_ids = current_state['email_ids']

            if prev_button in params:
                if current_email_index > 0:
                    current_email_index -= 1

            elif next_button in params:
                if current_email_index < len(email_ids) - 1:
                    current_email_index += 1

            dropdown_changed = False
            for dropdown_button in dimension_dropdowns:
                if dropdown_button in params:
                    current_state[dropdown_button.label] = params[dropdown_button]
                    dropdown_changed = True
            if dropdown_changed:
                email_ids = current_state['email_ids'] = get_email_ids(current_state)
                current_email_index = 0
            current_state['current_email_index'] = current_email_index

            if current_email_index >= len(email_ids):
                return [gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        gr.update(value=""),
                        current_state]

            email_sql = f"""
SELECT json, eml, triage_details
FROM email
WHERE email_id = %s
    """
            with psycopg2.connect(settings.monitordb_email_url) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(email_sql, (email_ids[current_email_index], ))
                    current_email_json, current_email_eml, current_email_triage_details = cursor.fetchone()
                    current_email_msg = BytesParser(policy=policy.default).parsebytes(current_email_eml.encode('utf-8'))

            # get body and make images work
            body = current_email_msg.get_body(['html', 'plain'])
            body_html = body.get_content().strip() if body else None
            if not body_html:
                body = current_email_msg.get_body(['plain'])
                body_html = body.get_content().strip() if body else None
            if not body_html:
                body_html = ""
            body_html = body_html.replace('cid:', f'/inline/{request.session_hash}/')

            # get attachments/ inlined things
            email_attachments = current_state['attachments'] = []
            email_inlines = current_state['inlines'] = {}
            attachments_html = ""
            for part in current_email_msg.walk():
                if part.get_content_disposition() == 'attachment':
                    email_attachments.append(part)
                    attachments_html += f"<a href='/attachment/{request.session_hash}/{len(email_attachments) - 1}'>{part.get_filename()}</a><br>"

                elif part.get_content_disposition() == 'inline':
                    content_id = part['Content-ID']
                    if content_id:
                        content_id = content_id.strip('<>')
                    email_inlines[content_id] = part

                else:
                    pass

            toEmails = set()
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['toRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['ccRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in current_email_json['bccRecipients']])
            toEmails = ",".join(toEmails)

            fromEmails = set()
            fromEmails.add(current_email_json['sender']['emailAddress']['address'].lower())
            fromEmails.add(current_email_json['from']['emailAddress']['address'].lower())
            fromEmails = ",".join(fromEmails)

            subject = current_email_msg['subject']
            received_at = current_email_json['receivedDateTime']

            if current_email_triage_details:
                current_email_triage_details = copy.deepcopy(current_email_triage_details)
                if current_email_triage_details.get('from_contact_id'):
                    current_email_triage_details['from_contact_id'] = f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Contact/{current_email_triage_details['from_contact_id']}/view'>{current_email_triage_details['from_contact_id']}</a>"
                if current_email_triage_details.get('contact_account_id'):
                    current_email_triage_details['contact_account_id'] = f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Account/{current_email_triage_details['contact_account_id']}/view'>{current_email_triage_details['contact_account_id']}</a>"
                if current_email_triage_details.get('customer_survey_round_id'):
                    current_email_triage_details['customer_survey_round_id'] = f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Customer_Survey_Round__c/{current_email_triage_details['customer_survey_round_id']}/view'>{current_email_triage_details['customer_survey_round_id']}</a>"
                if current_email_triage_details.get('top_level_agency_account_id'):
                    current_email_triage_details['top_level_agency_account_id'] = f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Account/{current_email_triage_details['top_level_agency_account_id']}/view'>{current_email_triage_details['top_level_agency_account_id']}</a>"
                if current_email_triage_details.get('top_level_customer_client_account_id'):
                    current_email_triage_details['top_level_customer_client_account_id'] = f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Account/{current_email_triage_details['top_level_customer_client_account_id']}/view'>{current_email_triage_details['top_level_customer_client_account_id']}</a>"

                current_email_triage_details['survey_panel_managers'] = [f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Survey_Panel_Manager__c/{x}/view'>{x}</a>" for x in current_email_triage_details.get('survey_panel_managers', [])]
                current_email_triage_details['survey_panel_members'] = [f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Survey_Panel_Member__c/{x}/view'>{x}</a>" for x in current_email_triage_details.get('survey_panel_members', [])]
                current_email_triage_details['survey_account_managers'] = [f"<a href='https://clientrelationship.lightning.force.com/lightning/r/Survey_Account_Manager__c/{x}/view'>{x}</a>" for x in current_email_triage_details.get('survey_account_managers', [])]
                triage_details_html = f"<pre>{json.dumps(current_email_triage_details, indent=2)}</pre>"

            else:
                triage_details_html = ""

            result = [gr.update(value=subject),
                      gr.update(value=received_at),
                      gr.update(value=fromEmails),
                      gr.update(value=toEmails),
                      gr.update(value=attachments_html),
                      gr.update(value=f"{current_email_index + 1}/{len(email_ids)}"),
                      gr.update(value=triage_details_html),
                      gr.update(value=body_html),
                      current_state]
            return result
        update_email_gui_outputs = [subject, received, from_, to, attachments, position, triage_details, body, state]

        for dimension_dropdown in dimension_dropdowns:
            dimension_dropdown.change(update_email_gui, inputs={dimension_dropdown, state}, outputs=update_email_gui_outputs)
        next_button.click(update_email_gui, inputs={next_button, state}, outputs=update_email_gui_outputs)
        prev_button.click(update_email_gui, inputs={prev_button, state}, outputs=update_email_gui_outputs)
        demo.load(update_dimension_values, outputs=dimension_dropdowns)
        demo.load(update_email_gui, inputs={state}, outputs=update_email_gui_outputs)

    server_app, _, _ = demo.launch(prevent_thread_lock=True,
                                   server_port=7680,
                                   server_name="0.0.0.0",
                                   share=False,
                                   auth=('crc', 'em@1ld3bug'))

    @server_app.get("/inline/{session_hash}/{cid}")
    def read_inline(session_hash, cid):
        session_state = server_app.state_holder.session_data.get(session_hash, {})
        session_state = session_state.state_data[1]
        inlines = session_state.get('inlines', {})

        if cid not in inlines:
            raise HTTPException(status_code=404, detail="Item not found")
        else:
            inline = inlines[cid]
            return Response(content=inline.get_content(), media_type=inline.get_content_type())

    @server_app.get("/attachment/{session_hash}/{aid}")
    def read_attachmemt(session_hash, aid):
        session_state = server_app.state_holder.session_data.get(session_hash, {})
        session_state = session_state.state_data[1]
        attachments = session_state.get('attachments', [])

        aid = int(aid)
        if aid < 0 or aid >= len(attachments):
            raise HTTPException(status_code=404, detail="Item not found")

        else:
            attachment = attachments[aid]
            filename = attachment.get_filename().encode('latin-1', 'ignore').decode('utf-8')
            return Response(content=attachment.get_content(),
                            media_type=attachment.get_content_type(),
                            headers={"Content-Disposition": f"attachment; filename={filename}"})

    demo.block_thread()


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
