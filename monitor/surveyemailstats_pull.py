import psycopg2
import argparse
import datetime
import json
from lib.settings import settings
from lib import portaldbapi, sfimport


DB_BATCH_SIZE = 1000


def emails_to_send_in_future(portaldb, pgconn, all_sfobjects, datetimefrom, datetimeto):
    pipeline = [
        {
            '$match': {
                'deleted': {'$exists': False},
                'scheduled_date_utc': {'$gte': datetimefrom},
            }
        },
        {
            '$group': {
                '_id': {
                    'customer_survey_round_id': "$customer_survey_round_id",
                    'scheduled_date_utc': "$scheduled_date_utc"
                },
                'count': { '$sum': 1 },
            }
        },
    ]
    results = {}
    for doc in portaldb.client[portaldbapi.PORTALDB_DATABASE].scheduledemails.aggregate(pipeline):
        customer_survey_round = all_sfobjects['CustomerSurveyRound'].get(doc['_id']['customer_survey_round_id'])
        if not customer_survey_round:
            continue
        account = all_sfobjects['Account'].get(customer_survey_round.AccountId)
        if not account:
            continue

        stamp = doc['_id']['scheduled_date_utc'].replace(second=0, microsecond=0)

        row = {
            'name': f"emails-{account.Name}-scheduled-Sum",
            'datasource': 'scheduledemails',
            'datetime': stamp,
            'value': 0,
            'dimensions': json.dumps({
                'service': 'emails',
                'account': account.Name,
                'metric': 'scheduled',
                'aggregation': 'Sum',
            }),
        }

        key = f"emails-{account.Name}-{stamp}"
        results.setdefault(key, row)
        results[key]['value'] += doc['count']

    # clear out any old data
    with pgconn.cursor() as cursor:
        sql = """
DELETE FROM public.timeseries
WHERE datetime >= cast(%s as timestamptz) AND datetime < cast(%s as timestamptz)
AND datasource = 'scheduledemails';
"""
        cursor.execute(sql, (datetimefrom, datetimeto))
        pgconn.commit()

    if results:
        sfimport.write_record_batch(pgconn, 'public.timeseries', list(results.values()))


def emails_previously_sent(portaldb, pgconn, all_sfobjects, datetimefrom, datetimeto):
    q = {
            'timestamp': {'$gte': datetimefrom, '$lt': datetimeto},
            'event': {'$nin': ['open', 'click']}
        }
    results = {}
    for doc in portaldb.client[portaldbapi.PORTALDB_DATABASE].sendgridevents.find(q, {'timestamp': 1, 'survey_panel_member_id': 1, 'event': 1}):
        event = doc['event']

        survey_panel_member = all_sfobjects['SurveyPanelMember'].get(doc['survey_panel_member_id'])
        if not survey_panel_member:
            continue
        survey_client = all_sfobjects['SurveyClient'].get(survey_panel_member.SurveyClientId)
        if not survey_client:
            continue
        customer_survey = all_sfobjects['CustomerSurvey'].get(survey_client.CustomerSurveyId)
        if not customer_survey:
            continue
        customer_survey_round = all_sfobjects['CustomerSurveyRound'].get(customer_survey.CustomerSurveyRoundId)
        if not customer_survey_round:
            continue
        account = all_sfobjects['Account'].get(customer_survey_round.AccountId)
        if not account:
            continue

        stamp = doc['timestamp'].replace(second=0, microsecond=0)

        row = {
            'name': f"emails-{account.Name}-{event}-Sum",
            'datasource': 'sentemails',
            'datetime': stamp,
            'value': 0,
            'dimensions': json.dumps({
                'service': 'emails',
                'account': account.Name,
                'metric': event,
                'aggregation': 'Sum',
            }),
        }

        key = f"emails-{account.Name}-{stamp}-{event}-Sum"
        results.setdefault(key, row)
        results[key]['value'] += 1

    # clear out any old data
    with pgconn.cursor() as cursor:
        sql = """
DELETE FROM public.timeseries
WHERE datetime >= cast(%s as timestamptz) AND datetime < cast(%s as timestamptz)
AND datasource = 'sentemails';
"""
        cursor.execute(sql, (datetimefrom, datetimeto))
        pgconn.commit()

    if results:
        sfimport.write_record_batch(pgconn, 'public.timeseries', list(results.values()))


def main(datetimefrom, datetimeto, all_sfobjects=None):
    conn = psycopg2.connect(settings.monitordb_timeseries_url)
    portaldb = portaldbapi.DocumentDB()

    if not all_sfobjects:
        all_sfobjects = sfimport.load_sf_from_latest_dump(exclude_files={'survey_response.csv'})

    emails_to_send_in_future(portaldb, conn, all_sfobjects, datetimefrom, datetimeto)
    emails_previously_sent(portaldb, conn, all_sfobjects, datetimefrom, datetimeto)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("datetimefrom", type=str, help="Datetime from (YYYY-MM-DDTHH:MM) inclusive")
    ap.add_argument("datetimeto", type=str, help="Datetime to (YYYY-MM-DDTHH:MM) exclusive")
    args = ap.parse_args()

    datetimefrom = datetime.datetime.strptime(args.datetimefrom, "%Y-%m-%dT%H:%M:%S")
    datetimeto = datetime.datetime.strptime(args.datetimeto, "%Y-%m-%dT%H:%M:%S")
    datetimefrom = datetimefrom.replace(tzinfo=datetime.timezone.utc)
    datetimeto = datetimeto.replace(tzinfo=datetime.timezone.utc)

    main(datetimefrom, datetimeto)
