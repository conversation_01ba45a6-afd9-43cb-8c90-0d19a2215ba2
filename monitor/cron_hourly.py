from monitor.surveyschedule import main as surveyschedule
from monitor.responserate import main as responserate
from monitor.s3importer import main as s3importer
from monitor.sfvalidator import main as sfvalidator
from lib import sfimport

def pull():

    all_sfobjects = sfimport.load_sf_from_latest_dump()

    s3importer(all_sfobjects=all_sfobjects)
    surveyschedule(all_sfobjects=all_sfobjects)
    responserate(all_sfobjects=all_sfobjects)
    sfvalidator(all_sfobjects=all_sfobjects)


if __name__ == '__main__':
    pull()
