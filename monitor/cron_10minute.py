import datetime
from lib import sfimport
from monitor.surveyemailstats_pull import main as surveyemailstats_pull
from monitor.pdtemails_pull import main as pdtemails_pull
from monitor.pdtemails_triage import main as pdtemails_triage
from monitor.pdtemailstats_pull import main as pdtemailstats_pull
from monitor.cloudwatchlogsmetrics_pull import main as cloudwatchlogsmetrics_pull



PULL_FREQUENCY_SECONDS = 60 * 10

def pull():
    now = datetime.datetime.now(datetime.timezone.utc)

    # round to nearest PULL_FREQUENCY_SECONDS
    todate = now - datetime.timedelta(seconds=now.second % PULL_FREQUENCY_SECONDS, microseconds=now.microsecond)

    # but we only pull the PREVIOUS chunk since stuff might not have arrived yet
    todate -= datetime.timedelta(seconds=PULL_FREQUENCY_SECONDS)
    fromdate = todate - datetime.timedelta(seconds=PULL_FREQUENCY_SECONDS)

    all_sfobjects = sfimport.load_sf_from_latest_dump()
    surveyemailstats_pull(fromdate, todate, all_sfobjects=all_sfobjects)
    pdtemails_pull()
    pdtemails_triage(all_sfobjects=all_sfobjects)
    pdtemailstats_pull(fromdate, todate)
    cloudwatchlogsmetrics_pull(fromdate, todate, all_sfobjects=all_sfobjects)

if __name__ == '__main__':
    pull()
