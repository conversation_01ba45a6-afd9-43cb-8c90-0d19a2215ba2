import boto3
import re
import os
import json
import argparse
import datetime
import psycopg2
from lib.settings import settings
from lib import sfimport, portaldbapi
from bson import ObjectId


DB_BATCH_SIZE = 1000

def count_log_events(cwlogs, start_time, end_time, log_group_name, filter_pattern, dimensions_fn):
    # get data, with pagination
    results = {}
    next_token = True
    while next_token:
        if next_token and next_token is not True:
            response = cwlogs.filter_log_events(
                logGroupName=log_group_name,
                startTime=int(start_time.timestamp() * 1000),
                endTime=int(end_time.timestamp() * 1000),
                filterPattern=filter_pattern,
                nextToken=next_token,
            )
        else:
            response = cwlogs.filter_log_events(
                logGroupName=log_group_name,
                startTime=int(start_time.timestamp() * 1000),
                endTime=int(end_time.timestamp() * 1000),
                filterPattern=filter_pattern,
            )

        for event in response['events']:
            event_time = event['timestamp']
            event_time = datetime.datetime.fromtimestamp(event_time / 1000, tz=datetime.timezone.utc)
            event_time = event_time.replace(second=0, microsecond=0)

            label, dimensions = dimensions_fn(event['message'])
            if label is None:
                continue

            results.setdefault(label, {'Data': {}})
            results[label]['Data'].setdefault(event_time, 0)
            results[label]['Data'][event_time] += 1
            results[label]['Dimensions'] = dimensions

        next_token = response.get('nextToken')

    return results


def import_batches(conn, results):
    batch = []
    for timeseries_name, data in results.items():
        dimensions = json.dumps(data['Dimensions'])
        for ts, count in data['Data'].items():
            batch.append({
                'name': timeseries_name,
                'datasource': 'cloudwatchlogs',
                'datetime': ts,
                'value': count,
                'dimensions': dimensions,
            })

        if len(batch) > DB_BATCH_SIZE:
            sfimport.write_record_batch(conn, 'public.timeseries', batch)
            batch = []

    if batch:
        sfimport.write_record_batch(conn, 'public.timeseries', batch)


def get_surveyaccounts_submit_data(conn, cwlogs, customer_survey_round_by_id, datetimefrom, datetimeto):

    def account_dimensions(log_message):
        m = re.search(r"POST[\s]+/surveys/(\w+)/accounts[\s]+200", log_message)
        if not m:
            return None, None

        customer_survey_round_id = m.group(1)
        customer_survey_round = customer_survey_round_by_id.get(customer_survey_round_id)
        account_name = customer_survey_round['account'].Name if customer_survey_round else 'Unknown'

        dimensions = {
            'service': 'surveyaccountsetup',
            'account': account_name,
            'metric': 'Submissions',
            'aggregation': 'Sum',
        }
        label = f"surveyaccountsetup-{account_name}-Submissions-Sum"

        return label, dimensions

    results = count_log_events(cwlogs, datetimefrom, datetimeto,
                               '/aws/lambda/crc-api-clientarea-prod',
                               r"%POST[\s]+/surveys/\w+/accounts[\s]+200%",
                               account_dimensions)
    import_batches(conn, results)


def get_surveypanel_submit_data(conn, cwlogs, customer_survey_round_by_id, datetimefrom, datetimeto):

    def panel_dimensions(log_message):
        m = re.search(r"POST[\s]+/surveys/(\w+)/panel[\s]+200", log_message)
        if not m:
            return None, None

        customer_survey_round_id = m.group(1)
        customer_survey_round = customer_survey_round_by_id.get(customer_survey_round_id)
        account_name = customer_survey_round['account'].Name if customer_survey_round else 'Unknown'

        dimensions = {
            'service': 'surveypanelsetup',
            'account': account_name,
            'metric': 'Submissions',
            'aggregation': 'Sum',
        }
        label = f"surveypanelsetup-{account_name}-Submissions-Sum"

        return label, dimensions

    results = count_log_events(cwlogs, datetimefrom, datetimeto,
                            '/aws/lambda/crc-api-clientarea-prod',
                            r"%POST[\s]+/surveys/\w+/panel[\s]+200%",
                            panel_dimensions)
    import_batches(conn, results)


def get_surveyresponse_submit_data(conn, cwlogs, portaldb, customer_survey_round_by_id, datetimefrom, datetimeto):

    surveys_by_oid = {}

    def response_dimensions(log_message):
        m = re.search(r"PATCH[\s]+/survey/(\w+)[\s]+200", log_message)
        if not m:
            return None, None
        oid = m.group(1)

        if not ObjectId.is_valid(oid):
            return None, None

        survey_id = ObjectId(oid)
        if survey_id not in surveys_by_oid:
            survey = portaldb.surveys.find_one({'_id': survey_id})
            surveys_by_oid[survey_id] = survey

        else:
            survey = surveys_by_oid[survey_id]

        if survey:
            customer_survey_round_id = survey['customer_survey_round_ids'][0]
            customer_survey_round = customer_survey_round_by_id.get(customer_survey_round_id)
        else:
            customer_survey_round = None
        account_name = customer_survey_round['account'].Name if customer_survey_round else 'Unknown'

        dimensions = {
            'service': 'surveyresponse',
            'account': account_name,
            'metric': 'Submissions',
            'aggregation': 'Sum',
        }
        label = f"surveyresponse-{account_name}-Submissions-Sum"

        return label, dimensions

    results = count_log_events(cwlogs, datetimefrom, datetimeto,
                            '/aws/lambda/crc-api-survey-prod',
                            r"%PATCH[\s]+/survey/\w+[\s]+200%",
                            response_dimensions)
    import_batches(conn, results)


def get_surveyresponse_badurl_data(conn, cwlogs, datetimefrom, datetimeto):

    def response_dimensions(log_message):
        m = re.search(r"[\s]+/(survey|privacy)/(\w+)[\s]", log_message)
        if not m:
            return None, None
        service = m.group(1)
        oid = m.group(2)

        if ObjectId.is_valid(oid):
            return None, None

        dimensions = {
            'service': 'surveyresponse',
            'service': service,
            'metric': 'BrokenURL',
            'aggregation': 'Sum',
        }
        label = f"surveyresponse-{service}-BrokenURL-Sum"

        return label, dimensions

    results = count_log_events(cwlogs, datetimefrom, datetimeto,
                            '/aws/lambda/crc-api-survey-prod',
                            r"%[\s]+/survey|privacy/\w+[\s]%",
                            response_dimensions)
    import_batches(conn, results)


def get_customer_survey_rounds(all_sfobjects):
    customer_survey_round_by_id = {}
    for customer_survey_round in all_sfobjects['CustomerSurveyRound'].values():
        account = all_sfobjects['Account'].get(customer_survey_round.AccountId)
        if not account:
            continue

        customer_survey_round_by_id[customer_survey_round.Id] = {
            'customer_survey_round': customer_survey_round,
            'account': account,
        }
    return customer_survey_round_by_id


def main(datetimefrom, datetimeto, all_sfobjects=None):
    cwlogs = boto3.client('logs', region_name=os.getenv('AWS_REGION', 'eu-west-1'))
    conn = psycopg2.connect(settings.monitordb_timeseries_url)
    portaldb = portaldbapi.DocumentDB()

    if all_sfobjects is None:
        all_sfobjects = sfimport.load_sf_from_latest_dump(
            include_files=('customer_survey_round.csv',
                           'account.csv'),
        )
    customer_survey_round_by_id = get_customer_survey_rounds(all_sfobjects)

    # clear out any old data
    with conn.cursor() as cursor:
        sql = """
DELETE FROM public.timeseries
WHERE datetime >= cast(%s as timestamptz) AND datetime < cast(%s as timestamptz)
AND datasource = 'cloudwatchlogs'
"""
        cursor.execute(sql, (datetimefrom, datetimeto))
        conn.commit()

    get_surveyaccounts_submit_data(conn, cwlogs, customer_survey_round_by_id, datetimefrom, datetimeto)
    get_surveypanel_submit_data(conn, cwlogs, customer_survey_round_by_id, datetimefrom, datetimeto)
    get_surveyresponse_submit_data(conn, cwlogs, portaldb, customer_survey_round_by_id, datetimefrom, datetimeto)
    get_surveyresponse_badurl_data(conn, cwlogs, datetimefrom, datetimeto)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("datetimefrom", type=str, help="Datetime from (YYYY-MM-DDTHH:MM) inclusive")
    ap.add_argument("datetimeto", type=str, help="Datetime to (YYYY-MM-DDTHH:MM) exclusive")
    args = ap.parse_args()

    datetimefrom = datetime.datetime.strptime(args.datetimefrom, "%Y-%m-%dT%H:%M:%S")
    datetimeto = datetime.datetime.strptime(args.datetimeto, "%Y-%m-%dT%H:%M:%S")
    datetimefrom = datetimefrom.replace(tzinfo=datetime.timezone.utc)
    datetimeto = datetimeto.replace(tzinfo=datetime.timezone.utc)

    main(datetimefrom, datetimeto)
