from lib import sfimport, sfapi
import datetime
import psycopg2
from lib.settings import settings


def create_tables(conn):
    # figure out the desired set of keys
    table_dependencies = {}
    self_referential_tables = {}
    for objname in sfapi.SFAPI_OBJECT_NAMES + sfapi.SFAPI_KEY_OBJECT_NAMES:
        pkcolname = 'Id'
        fks = {}
        sfobject = getattr(sfapi, objname)
        sqlfields = {}
        table_dependencies.setdefault(objname, set())
        for fieldname, fielddetails in sfobject.model_fields.items():

            type = None
            nullable = True
            if fielddetails.annotation is None:
                type = 'character varying'
                nullable = False

            elif fielddetails.annotation == str:
                type = 'character varying'
                nullable = False

            elif fielddetails.annotation == str|None:
                type = 'character varying'

            elif fielddetails.annotation == float|None:
                type = 'real'

            elif fielddetails.annotation == bool|None:
                type = 'boolean'

            elif fielddetails.annotation == bool:
                type = 'boolean'
                nullable = False

            elif fielddetails.annotation == int|None:
                type = 'integer'

            elif fielddetails.annotation == datetime.datetime|None:
                type = 'timestamp with time zone'

            elif fielddetails.annotation == datetime.date|None:
                type = 'timestamp with time zone'

            else:
                print("Unknown type", fielddetails.annotation)
                break

            sqltype = f"{fieldname.lower()} {type}"
            if not nullable:
                sqltype += " NOT NULL"
            sqlfields[fieldname.lower()] = sqltype

            if fielddetails.json_schema_extra and fielddetails.json_schema_extra.get('fk'):
                fk_table, fk_colname = fielddetails.json_schema_extra['fk'].split('.')

                # record what this table is dependent on. Deal with self-referential tables later
                if fk_table == objname:
                    self_referential_tables.setdefault(objname, set()).add(fieldname)
                else:
                    table_dependencies[objname].add(fk_table)

                fk_name = f"fk_{objname.lower()}_{fieldname.lower()}"
                fk_sql = f"ALTER TABLE {objname.lower()} ADD CONSTRAINT {fk_name} FOREIGN KEY ({fieldname.lower()}) REFERENCES {fk_table.lower()}({fk_colname}) ON DELETE SET NULL"
                fks[fk_name] = fk_sql

        # the set currently in the database
        sql = """
select column_name, data_type, is_nullable
from INFORMATION_SCHEMA.COLUMNS
where table_name = %s and table_schema = 'public'
"""
        dbsqlfields = {}
        with conn.cursor() as cursor:
            cursor.execute(sql, (objname.lower(),))
            for colname, datatype, nullable in cursor.fetchall():
                sqltype = f"{colname.lower()} {datatype}"
                if nullable in {'NO', False}:
                    sqltype += " NOT NULL"

                if colname not in sqlfields:
                    print(f"Detected deleted column {objname}.{colname}")
                elif colname in sqlfields:
                    if sqltype != sqlfields[colname]:
                        print(f"Detected changed column {objname}.{colname} ({sqlfields[colname]} -> {sqltype})")

                dbsqlfields[colname] = sqltype

        # new columns needing added to the database
        newsqlfields = {}
        for colname in sqlfields.keys():
            if colname not in dbsqlfields:
                print(f"Detected new column {objname}.{colname}")
                newsqlfields[colname] = sqlfields[colname]

        # create table/ add new columns
        if not dbsqlfields:
            table_sql = f"CREATE TABLE {objname.lower()} ({', '.join(sqlfields)})"
            with conn.cursor() as cursor:
                cursor.execute(table_sql)
                conn.commit()

            pk_sql = f"ALTER TABLE {objname.lower()} ADD PRIMARY KEY ({pkcolname})"
            with conn.cursor() as cursor:
                cursor.execute(pk_sql)
                conn.commit()

        elif newsqlfields:
            colsqls = [f" ADD COLUMN {sqltype}" for colname, sqltype in newsqlfields.items()]
            table_sql = f"ALTER TABLE {objname.lower()} {', '.join(colsqls)}"
            with conn.cursor() as cursor:
                cursor.execute(table_sql)
                conn.commit()

        # disable integrity checks -- we can't rely on a consistent snapshot of the database
        sql = "ALTER TABLE {} DISABLE TRIGGER USER".format(objname.lower())
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()

        # get all existing fks
        sql = """
select constraint_name
FROM information_schema.table_constraints
where table_schema='public' AND table_name=%s and constraint_type = 'FOREIGN KEY'
"""
        with conn.cursor() as cursor:
            cursor.execute(sql, (objname.lower(),))
            dbfks = set([x[0] for x in cursor.fetchall()])

        # add new fks
        for fkname, fksql in fks.items():
            if fkname in dbfks:
                continue
            with conn.cursor() as cursor:
                try:
                    cursor.execute(fksql)
                    conn.commit()
                except Exception as e:
                    conn.rollback()
                    print(f"Failed to create foreign key {fkname} for {objname}: {e.args}")

    # determine table load order based on fk dependencies
    table_load_order = []
    while table_dependencies:
        loadable_tables = [t for t in table_dependencies if not table_dependencies[t]]
        table_load_order.extend(loadable_tables)

        for table_name in loadable_tables:
            del table_dependencies[table_name]
        for table_name, table_deps in table_dependencies.items():
            table_deps -= set(loadable_tables)

        if not loadable_tables:
            raise Exception(f"Cyclic dependency detected! {table_dependencies.keys()}")

    return table_load_order, self_referential_tables


def write_tables(conn, all_sfobjects, table_load_order, self_referential_tables):
    for objname in table_load_order:
        if objname not in all_sfobjects:
            continue

        sfobject = getattr(sfapi, objname)
        fieldnames = [k.lower() for k in sfobject.model_fields.keys()]

        # do we have a self-referential table?
        self_referential_fields = self_referential_tables.get(objname, set())
        if len(self_referential_fields) > 1:
            raise Exception("Multiple self-referential fields detected!")
        elif len(self_referential_fields) == 1:
            self_referential_field = list(self_referential_fields)[0]
        else:
            self_referential_field = None

        # create temp table to load data into
        sql = f"CREATE TEMP TABLE temp_{objname.lower()} (LIKE {objname.lower()})"
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()

        # write the data in batches, ensuring self-referential rows are written in the correct order
        written_ids = set([None])
        toload = set(all_sfobjects[objname].keys())
        total_toload = len(toload)
        while toload:
            batch = []
            written_this_time = 0
            for key in list(toload):
                value = all_sfobjects[objname][key]
                if self_referential_field is None or getattr(value, self_referential_field) in written_ids:
                    batch.append(value.model_dump())
                    toload.remove(key)
                    written_this_time += 1
                    written_ids.add(key)

                if len(batch) >= 10000:
                    sfimport.write_record_batch(conn, f"temp_{objname.lower()}", batch)
                    print(f"{objname}: {len(written_ids)} / {total_toload}")
                    batch = []

            # write any remaining records
            if batch:
                sfimport.write_record_batch(conn, f"temp_{objname.lower()}", batch)
                print(f"{objname}: {len(written_ids)} / {total_toload}")

            # Check for cyclic dependencies - if we didn't write any records this time, we have a cyclic dependency
            if not written_this_time:
                raise Exception(f"Unable to load any records for {objname} -- Cyclic dependency detected!")

        # now merge data into the main table
        sql = f"""
MERGE INTO {objname.lower()} AS target
USING temp_{objname.lower()} AS source
ON target.id = source.id
WHEN MATCHED THEN
    UPDATE SET {', '.join([f"{k} = source.{k}" for k in fieldnames if k not in {'id'}])}
WHEN NOT MATCHED THEN
    INSERT ({', '.join([f"{k}" for k in fieldnames])})
    VALUES ({', '.join([f"source.{k}" for k in fieldnames])})
"""
        with conn.cursor() as cursor:
            cursor.execute(sql)
            conn.commit()

        # find all ids in the table
        sql = f"SELECT DISTINCT id FROM {objname.lower()}"
        with conn.cursor() as cursor:
            cursor.execute(sql)
            db_ids = set([x[0] for x in cursor.fetchall()])

        # delete any ids that are defunct
        ids_to_delete = db_ids - written_ids
        if ids_to_delete:
            sql = f"DELETE FROM {objname.lower()} WHERE id IN ({', '.join(['%s' for _ in ids_to_delete])})"
            with conn.cursor() as cursor:
                cursor.execute(sql, list(ids_to_delete))
                conn.commit()


def main(all_sfobjects=None):
    # settings.monitordb_salesforce_url = "postgresql://postgres@localhost:5432/crc_test"
    with psycopg2.connect(settings.monitordb_salesforce_url) as conn:
        table_load_order, self_referential_tables = create_tables(conn)
        if all_sfobjects is None:
            all_sfobjects = sfimport.load_sf_from_latest_dump()
        write_tables(conn, all_sfobjects, table_load_order, self_referential_tables)


if __name__ == '__main__':
    main()
