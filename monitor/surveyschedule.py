import datetime
import psycopg2
from lib.settings import settings
from lib import sfimport
import copy


def get_customer_survey_dates(all_sfobjects):
    for cs in all_sfobjects['CustomerSurvey'].values():
        csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
        if not csr:
            continue
        csr_account = all_sfobjects['Account'].get(csr.AccountId)
        if not csr_account:
            continue

        alldates = [
            cs.AccountUpdatesStartDate,
            cs.AccountUpdatesEndDate,
            cs.PanelUpdatesStartDate,
            cs.PanelUpdatesEndDate,
            cs.LiveSurveyStartDate,
            cs.LiveSurveyFirstRequest,
            cs.LiveSurveySecondRequest,
            cs.LiveSurveyThirdRequest,
            cs.LiveSurveyFourthRequest,
            cs.LiveSurveyEndDate,
            cs.InsightsStartDate,
        ]
        alldates = [d for d in alldates if d]
        if not alldates:
            continue
        startdate = min(alldates)
        enddate = max(alldates)

        if startdate <= datetime.date.today() <= enddate:
            items_by_date = {}
            if cs.AccountUpdatesStartDate:
                items_by_date[cs.AccountUpdatesStartDate] = {'date': cs.AccountUpdatesStartDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'accounts'}
            if cs.AccountUpdatesEndDate:
                items_by_date[cs.AccountUpdatesEndDate] = {'date': cs.AccountUpdatesEndDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': None}
            if cs.PanelUpdatesStartDate:
                items_by_date[cs.PanelUpdatesStartDate] = {'date': cs.PanelUpdatesStartDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'panels'}
            if cs.PanelUpdatesEndDate:
                items_by_date[cs.PanelUpdatesEndDate] = {'date': cs.PanelUpdatesEndDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': None}
            if cs.LiveSurveyStartDate:
                items_by_date[cs.LiveSurveyStartDate] = {'date': cs.LiveSurveyStartDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'survey'}
            if cs.LiveSurveyFirstRequest:
                items_by_date[cs.LiveSurveyFirstRequest] = {'date': cs.LiveSurveyFirstRequest, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'M1'}
            if cs.LiveSurveySecondRequest:
                items_by_date[cs.LiveSurveySecondRequest] = {'date': cs.LiveSurveySecondRequest, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'M2'}
            if cs.LiveSurveyThirdRequest:
                items_by_date[cs.LiveSurveyThirdRequest] = {'date': cs.LiveSurveyThirdRequest, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'M3'}
            if cs.LiveSurveyFourthRequest:
                items_by_date[cs.LiveSurveyFourthRequest] = {'date': cs.LiveSurveyFourthRequest, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'M4'}
            if cs.LiveSurveyEndDate:
                items_by_date[cs.LiveSurveyEndDate] = {'date': cs.LiveSurveyEndDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': None}
            if cs.InsightsStartDate:
                items_by_date[cs.InsightsStartDate] = {'date': cs.InsightsStartDate, 'top_level_account': csr_account.Name, 'name': cs.Name, 'action': 'insights'}

            cur_item = None
            d = startdate
            while d <= enddate:
                if d in items_by_date:
                    cur_item = items_by_date[d]

                if cur_item:
                    cur_item = copy.copy(cur_item)
                    cur_item['date'] = d
                    yield cur_item

                if cur_item and cur_item['action'] is None:
                    cur_item = None

                d += datetime.timedelta(days=1)


def main(all_sfobjects=None):
    if all_sfobjects is None:
        all_sfobjects = sfimport.load_sf_from_latest_dump(include_files={'customer_survey_round.csv', 'customer_survey.csv', 'account.csv'})

    conn = psycopg2.connect(settings.monitordb_timeseries_url)

    results = []
    for row in get_customer_survey_dates(all_sfobjects):
        results.append(row)

    # clear out any old data
    with conn.cursor() as cursor:
        sql = """
DELETE FROM public.customer_survey
"""
        cursor.execute(sql)
        conn.commit()

    if results:
        sfimport.write_record_batch(conn, 'public.customer_survey', results)


if __name__ == '__main__':
    main()
