* * * * *       cd crc-backend; STACK=prod PROD_IS_OK=YES poetry run python3 -m monitor.cron_1minute    > /tmp/last_log_1minute.txt 2>&1
*/10 * * * *    cd crc-backend; STACK=prod PROD_IS_OK=YES poetry run python3 -m monitor.cron_10minute   > /tmp/last_log_10minute.txt 2>&1
15 0 * * *      cd crc-backend; STACK=prod PROD_IS_OK=YES poetry run python3 -m monitor.cron_daily   > /tmp/last_log_daily.txt 2>&1
15 * * * *      cd crc-backend; STACK=prod PROD_IS_OK=YES poetry run python3 -m monitor.cron_hourly   > /tmp/last_log_hourly.txt 2>&1
