CREATE TABLE if not exists public.email (
	email_id int4 GENERATED BY DEFAULT AS IDENTITY( INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE) NOT NULL,
	json jsonb NULL,
	eml varchar NULL,
	bucket varchar NULL,
	received_at timestamptz NULL,
	message_id varchar NULL,
	"action" varchar NULL,
	"label" varchar NULL,
	triage_label varchar NULL,
	triage_details jsonb NULL,
	CONSTRAINT email_pk PRIMARY KEY (email_id)
);
CREATE INDEX email_bucket_idx ON public.email USING btree (bucket);
CREATE INDEX email_message_id_idx ON public.email USING btree (message_id);
CREATE INDEX email_received_at_idx ON public.email USING btree (received_at);
