import os
import json
import psycopg2
import argparse
import re
from bs4 import BeautifulSoup
from lib.settings import settings


def main(grepfor):
    conn = psycopg2.connect(settings.monitordb_email_url)
    cursor = conn.cursor()

    email_select_sql = """
    SELECT email_id, bucket, action, json
    FROM email
    WHERE bucket != 'dmarc'
    ORDER BY received_at DESC
    """
    cursor.execute(email_select_sql)

    grepfor = grepfor.lower()
    for email_id, bucket, action, j in cursor:
        if not grepfor or grepfor in json.dumps(j).lower():
            soup = BeautifulSoup(j['body']['content'], "html.parser")
            text = soup.get_text(separator=" ").replace("\n", " ").replace("\r", " ")
            text = re.sub(r"\s+", " ", text).strip()

            toEmails = set()
            toEmails |= set([x['emailAddress']['address'].lower() for x in j['toRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in j['ccRecipients']])
            toEmails |= set([x['emailAddress']['address'].lower() for x in j['bccRecipients']])
            toEmails = ",".join(toEmails)

            fromEmails = set()
            fromEmails.add(j['sender']['emailAddress']['address'].lower())
            fromEmails.add(j['from']['emailAddress']['address'].lower())
            fromEmails = ",".join(fromEmails)

            j['text'] = text
            print(email_id, bucket, action)
            print("Subject", j['subject'])
            print("From:", fromEmails)
            print("To:", toEmails)
            print("Date:", j['receivedDateTime'])
            print(text)
            print()

if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("grepfor", help="String to grep for in emails")
    args = ap.parse_args()

    main(args.grepfor)
