import psycopg2
import argparse
import datetime
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch
from email.parser import Bytes<PERSON>arser
from email import policy
from bs4 import BeautifulSoup
from lib import sfapi, sfimport
from lib.settings import settings
import os
import json
import zipfile


OOO_MODEL_NAME = 'crc-oooemail-model-********'


def get_sf_data(all_sfobjects):
    # Now, generate lookups
    contact_by_email = {}
    customer_survey_round_by_root_account_id = {}
    survey_panel_member_by_contact_id = {}
    survey_panel_manager_by_contact_id = {}
    survey_client_by_customer_account_id = {}
    survey_account_manager_by_contact_id = {}
    agency_top_level_account_by_email_domain = {}
    customers_client_top_level_account_by_email_domain = {}
    for o in all_sfobjects['Contact'].values():
        # lookup contact by email address
        email_address = o.Email.strip().lower()
        if email_address in contact_by_email:
            print(f"Duplicate contacts email address detected: {email_address}")
        contact_by_email.setdefault(email_address, []).append(o)

        # lookup account by email domain
        email_domain = email_address.split('@', 1)[1]
        account = all_sfobjects['Account'].get(o.AccountId)

        # map from email domain to agency top level account
        account_type = all_sfobjects['AccountRecordType'].get(account.RecordTypeId)
        top_level_account = all_sfobjects['Account'].get(account.UltimateParent) if account.UltimateParent else account
        if account_type.Name != sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT:
            agency_top_level_account_by_email_domain.setdefault(email_domain, set())
            agency_top_level_account_by_email_domain[email_domain].add(top_level_account.Id)

        else:
            customers_client_top_level_account_by_email_domain.setdefault(email_domain, set())
            customers_client_top_level_account_by_email_domain[email_domain].add(top_level_account.Id)

    for o in all_sfobjects['CustomerSurveyRound'].values():
        bucket = customer_survey_round_by_root_account_id.setdefault(o.AccountId, {})
        deets = bucket.setdefault(o.Id, {})
        deets['csr'] = o
        if o.AccountUpdatesStartDate:
            deets['first_survey_setup_start'] = min(deets.get('first_survey_setup_start', o.AccountUpdatesStartDate), o.AccountUpdatesStartDate)
        if o.LiveSurveyStartDate:
            deets['first_live_survey_start'] = min(deets.get('first_live_survey_start', o.LiveSurveyStartDate), o.LiveSurveyStartDate)
        if o.LiveSurveyEndDate:
            deets['first_live_survey_end'] = min(deets.get('first_live_survey_end', o.LiveSurveyEndDate), o.LiveSurveyEndDate)
        if o.LiveSurveyEndDate:
            deets['last_live_survey_end'] = max(deets.get('last_live_survey_end', o.LiveSurveyEndDate), o.LiveSurveyEndDate)

    for o in all_sfobjects['CustomerSurvey'].values():
        csr = all_sfobjects['CustomerSurveyRound'].get(o.CustomerSurveyRoundId)
        bucket = customer_survey_round_by_root_account_id.setdefault(csr.AccountId, {})
        deets = bucket.setdefault(o.CustomerSurveyRoundId, {})
        if o.AccountUpdatesStartDate:
            deets['first_survey_setup_start'] = min(deets.get('first_survey_setup_start', o.AccountUpdatesStartDate), o.AccountUpdatesStartDate)
        if o.LiveSurveyStartDate:
            deets['first_live_survey_start'] = min(deets.get('first_live_survey_start', o.LiveSurveyStartDate), o.LiveSurveyStartDate)
        if o.LiveSurveyEndDate:
            deets['first_live_survey_end'] = min(deets.get('first_live_survey_end', o.LiveSurveyEndDate), o.LiveSurveyEndDate)
        if o.LiveSurveyEndDate:
            deets['last_live_survey_end'] = max(deets.get('last_live_survey_end', o.LiveSurveyEndDate), o.LiveSurveyEndDate)

    for o in all_sfobjects['SurveyClient'].values():
        survey_client_by_customer_account_id.setdefault(o.CustomersClientId, []).append(o)

    for o in all_sfobjects['SurveyPanelMember'].values():
        sc = all_sfobjects['SurveyClient'].get(o.SurveyClientId)
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if cs.LiveSurveyStartDate and cs.LiveSurveyEndDate:
            survey_panel_member_by_contact_id.setdefault(o.ContactId, [])
            survey_panel_member_by_contact_id[o.ContactId].append(o)

    for o in all_sfobjects['SurveyPanelManager'].values():
        sc = all_sfobjects['SurveyClient'].get(o.SurveyClientId)
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if cs.PanelUpdatesStartDate and cs.LiveSurveyEndDate:
            survey_panel_manager_by_contact_id.setdefault(o.ContactId, [])
            survey_panel_manager_by_contact_id[o.ContactId].append(o)

    for o in all_sfobjects['SurveyAccountManager'].values():
        cs = all_sfobjects['CustomerSurvey'].get(o.CustomerSurveyId)
        if cs.AccountUpdatesStartDate and cs.LiveSurveyEndDate:
            survey_account_manager_by_contact_id.setdefault(o.ContactId, [])
            survey_account_manager_by_contact_id[o.ContactId].append(o)

    # sort them in order of emails so the most frequently seen account for this email domain is first
    for customer_client_id, items in survey_client_by_customer_account_id.items():
        def sc_sort_key(sc):
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            return cs.LiveSurveyStartDate
        survey_client_by_customer_account_id[customer_client_id] = sorted(items, key=sc_sort_key, reverse=True)

    # sort them in order of emails so the most frequently seen account for this email domain is first
    for contact_id, items in survey_panel_member_by_contact_id.items():
        def sc_sort_key(spm):
            sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            return cs.LiveSurveyStartDate
        survey_panel_member_by_contact_id[contact_id] = sorted(items, key=sc_sort_key, reverse=True)

    # sort them in order of emails so the most frequently seen account for this email domain is first
    for contact_id, items in survey_panel_manager_by_contact_id.items():
        def sc_sort_key(spm):
            sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            return cs.LiveSurveyStartDate
        survey_panel_manager_by_contact_id[contact_id] = sorted(items, key=sc_sort_key, reverse=True)

    # sort them in order of emails so the most frequently seen account for this email domain is first
    for contact_id, items in survey_account_manager_by_contact_id.items():
        def sc_sort_key(sam):
            cs = all_sfobjects['CustomerSurvey'].get(sam.CustomerSurveyId)
            return cs.LiveSurveyStartDate
        survey_account_manager_by_contact_id[contact_id] = sorted(items, key=sc_sort_key, reverse=True)

    # sort them in reverse order of first survey setup start so the most recent customer survey round is first
    for root_account_id, customer_survey_round_details in customer_survey_round_by_root_account_id.items():
        customer_survey_round_by_root_account_id[root_account_id] = sorted(customer_survey_round_details.values(), key=lambda x: x.get('first_survey_setup_start', datetime.date.min), reverse=True)

    return all_sfobjects, \
            contact_by_email, \
            customer_survey_round_by_root_account_id, \
            survey_panel_member_by_contact_id, \
            survey_panel_manager_by_contact_id, \
            survey_account_manager_by_contact_id, \
            agency_top_level_account_by_email_domain, \
            customers_client_top_level_account_by_email_domain, \
            survey_client_by_customer_account_id


def determine_if_ooo(tokenizer, model, subject, text):
    # create text and tokenize it
    text = f"{subject} {text or ''}"
    text = text.encode('utf-8', 'ignore').decode('utf-8', 'ignore')
    inputs = tokenizer(text, return_tensors="pt", truncation=True)

    # Perform inference
    with torch.no_grad():
        outputs = model(**inputs)

    # Get the predicted class
    predictions = torch.argmax(outputs.logits, dim=-1)
    return predictions.item() == 1


def determine_if_codeit(from_emails):
    for from_email in from_emails:
        if from_email.endswith("@thgfluently.com"):
            return True
    return False


def extract_email_details(email_json, email_eml):
    email_msg = BytesParser(policy=policy.default).parsebytes(email_eml.encode('utf-8'))

    to_emails = set()
    to_emails |= set([x['emailAddress']['address'].lower() for x in email_json['toRecipients'] if 'address' in x['emailAddress']])
    to_emails |= set([x['emailAddress']['address'].lower() for x in email_json['ccRecipients'] if 'address' in x['emailAddress']])
    to_emails |= set([x['emailAddress']['address'].lower() for x in email_json['bccRecipients'] if 'address' in x['emailAddress']])

    from_emails = set()
    if email_json.get('sender', {}).get('emailAddress', {}).get('address'):
        from_emails.add(email_json['sender']['emailAddress']['address'].lower())
    if email_json.get('from', {}).get('emailAddress', {}).get('address'):
        from_emails.add(email_json['from']['emailAddress']['address'].lower())

    body = email_msg.get_body(['html', 'plain'])
    if body:
        if 'windows-874' in body['Content-Type']:
            body.set_param('charset', 'cp874', replace=True)
        body = body.get_content().strip()
    else:
        body = ""

    soup = BeautifulSoup(body, "html.parser")
    text = soup.get_text(separator=" ").strip()

    subject = email_msg['subject']

    return email_msg, from_emails, to_emails, subject, text


def triage_email(email_id, received_at, bucket, action,
                 email_json, email_eml,
                 ooo_tokenizer, ooo_model,
                 all_sfobjects,
                 contact_by_email,
                 customer_survey_round_by_root_account_id,
                 survey_panel_member_by_contact_id,
                 survey_panel_manager_by_contact_id,
                 survey_account_manager_by_contact_id,
                 agency_top_level_account_by_email_domain,
                 customers_client_top_level_account_by_email_domain,
                 survey_client_by_customer_account_id):
    email_msg, from_emails, to_emails, subject, text = extract_email_details(email_json, email_eml)
    email_date = received_at.date()

    # initial simple triage
    if bucket == 'dmarc':
        return 'dmarc', None

    elif determine_if_codeit(from_emails):
        return 'codeit', None

    # figure out who sent it
    if len(from_emails) > 1:
        print(f"Multiple from emails detected: {from_emails}")

    if not from_emails:
        print(f"No from emails detected: {from_emails}")
        return 'unknown', None

    from_email = from_emails.pop()
    from_email_domain = from_email.split('@', 1)[1]

    # lookup the contact, try and guess which account if we can't find it
    contact_account = None
    from_contact = contact_by_email.get(from_email)
    if not from_contact:
        # figure out the top level agency account for this email's domain
        top_level_account_ids = agency_top_level_account_by_email_domain.get(from_email_domain)
        if top_level_account_ids:
            contact_account = all_sfobjects['Account'].get(list(top_level_account_ids)[0])

        # didn't find one! try the customers client accounts instead
        if contact_account is None:
            top_level_account_ids = customers_client_top_level_account_by_email_domain.get(from_email_domain)
            if top_level_account_ids:
                contact_account = all_sfobjects['Account'].get(list(top_level_account_ids)[0])

    else:
        from_contact = from_contact[0]
        contact_account = all_sfobjects['Account'].get(from_contact.AccountId)

    # determine extra details
    top_level_contact_account = all_sfobjects['Account'].get(contact_account.UltimateParent) if contact_account and contact_account.UltimateParent else contact_account
    contact_account_type = all_sfobjects['AccountRecordType'].get(contact_account.RecordTypeId) if contact_account else None
    is_ooo = determine_if_ooo(ooo_tokenizer, ooo_model, subject, text)

    # figure out if they're someone in the survey
    survey_panel_member_ids = set()
    survey_panel_manager_ids = set()
    survey_account_manager_ids = set()
    if from_contact:
        for survey_panel_member in survey_panel_member_by_contact_id.get(from_contact.Id, []):
            sc = all_sfobjects['SurveyClient'].get(survey_panel_member.SurveyClientId)
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            if cs.LiveSurveyStartDate - datetime.timedelta(days=5) <= email_date <= cs.LiveSurveyEndDate + datetime.timedelta(days=5):
                survey_panel_member_ids.add(survey_panel_member.Id)

        for survey_panel_manager in survey_panel_manager_by_contact_id.get(from_contact.Id, []):
            sc = all_sfobjects['SurveyClient'].get(survey_panel_manager.SurveyClientId)
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            if cs.PanelUpdatesStartDate - datetime.timedelta(days=5) <= email_date <= cs.LiveSurveyEndDate + datetime.timedelta(days=5):
                survey_panel_manager_ids.add(survey_panel_manager.Id)

        for survey_account_manager in survey_account_manager_by_contact_id.get(from_contact.Id, []):
            cs = all_sfobjects['CustomerSurvey'].get(survey_account_manager.CustomerSurveyId)
            if cs.AccountUpdatesStartDate - datetime.timedelta(days=5) <= email_date <= cs.LiveSurveyEndDate + datetime.timedelta(days=5):
                survey_account_manager_ids.add(survey_account_manager.Id)

    # try and figure out the top level agency and customer client accounts
    top_level_agency_account = top_level_customer_client_account = None
    if top_level_contact_account and all_sfobjects['AccountRecordType'].get(top_level_contact_account.RecordTypeId).Name == sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT:
        top_level_customer_client_account = top_level_contact_account

        if survey_panel_member_ids:
            survey_panel_member = all_sfobjects['SurveyPanelMember'].get(list(survey_panel_member_ids)[0])
            sc = all_sfobjects['SurveyClient'].get(survey_panel_member.SurveyClientId)
            cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
            csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
            top_level_agency_account = all_sfobjects['Account'].get(csr.AccountId)

        if not top_level_agency_account:
            possible_survey_clients = survey_client_by_customer_account_id.get(top_level_customer_client_account.Id, [])
            if possible_survey_clients:
                sc = possible_survey_clients[0]
                cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
                csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
                top_level_agency_account = all_sfobjects['Account'].get(csr.AccountId)

    else:
        top_level_agency_account = top_level_contact_account

    # if we know the top level agency account, figure out the dates of their latest customer survey round
    customer_survey_round = None
    if top_level_agency_account:
        customer_survey_rounds = customer_survey_round_by_root_account_id.get(top_level_agency_account.Id, [])
        if customer_survey_rounds:
            customer_survey_round = customer_survey_rounds[0]

    # determine the type of person
    if contact_account_type and contact_account_type.Name in {sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT}:
        person_type = "customerclient"

    elif contact_account_type and contact_account_type.Name in {sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING, sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING}:
        person_type = "agency"

    else:
        person_type = 'unknown'

    triaged_details = {
        "from_email": from_email,
        "from_contact_id": from_contact.Id if from_contact else None,
        "contact_account_id": contact_account.Id if contact_account else None,
        "contact_account_name": contact_account.Name if contact_account else None,
        "contact_account_type": contact_account_type.Name if contact_account_type else None,
        "top_level_agency_account_id": top_level_agency_account.Id if top_level_agency_account else None,
        "top_level_agency_account_name": top_level_agency_account.Name if top_level_agency_account else None,
        "top_level_customer_client_account_id": top_level_customer_client_account.Id if top_level_customer_client_account else None,
        "top_level_customer_client_account_name": top_level_customer_client_account.Name if top_level_customer_client_account else None,
        "subject": subject,
        "out_of_office": is_ooo,
        "person_type": person_type,
        "survey_panel_members": survey_panel_member_ids,
        "survey_panel_managers": survey_panel_manager_ids,
        "survey_account_managers": survey_account_manager_ids,
    }
    if customer_survey_round:
        triaged_details["customer_survey_round_id"] = customer_survey_round['csr'].Id
        triaged_details["first_survey_setup_start"] = customer_survey_round.get('first_survey_setup_start')
        triaged_details["first_live_survey_start"] = customer_survey_round.get('first_live_survey_start')
        triaged_details["first_live_survey_end"] = customer_survey_round.get('first_live_survey_end')
        triaged_details["last_live_survey_end"] = customer_survey_round.get('last_live_survey_end')

    return person_type, triaged_details


def json_serialize(obj):
    if isinstance(obj, (datetime.date, datetime.datetime)):
        return obj.isoformat()

    elif isinstance(obj, set):
        return list(obj)

    raise TypeError(f"Type {type(obj)} not serializable")


def obtain_models():
    if os.path.exists(f"ml_models/{OOO_MODEL_NAME}"):
        return

    with sfimport.s3_cached_download(settings.monitor_assets_bucket, f"models/oooemail/{OOO_MODEL_NAME}.zip") as f:
        zipfile.ZipFile(f).extractall("ml_models")


def main(all_sfobjects=None):
    if all_sfobjects is None:
        all_sfobjects = sfimport.load_sf_from_latest_dump(exclude_files={'survey_response.csv'})

    all_sfobjects, \
        contact_by_email, \
        customer_survey_round_by_root_account_id, \
        survey_panel_member_by_contact_id, \
        survey_panel_manager_by_contact_id, \
        survey_account_manager_by_contact_id, \
        agency_top_level_account_by_email_domain, \
        customers_client_top_level_account_by_email_domain, \
        survey_client_by_customer_account_id = get_sf_data(all_sfobjects)

    conn = psycopg2.connect(settings.monitordb_email_url)
    query_cursor = conn.cursor()
    update_cursor = conn.cursor()

    obtain_models()
    ooo_tokenizer = AutoTokenizer.from_pretrained(f"ml_models/{OOO_MODEL_NAME}")
    ooo_model = AutoModelForSequenceClassification.from_pretrained(f"ml_models/{OOO_MODEL_NAME}")

    email_select_sql = """
    SELECT email_id, received_at, bucket, action, json, eml
    FROM email
    WHERE action = 'received' and triage_label IS NULL
    ORDER BY received_at DESC
    """
    query_cursor.execute(email_select_sql)

    for email_id, received_at, bucket, action, email_json, email_eml in query_cursor:
        print(email_id, received_at, bucket, action)
        try:
            triage_label, triage_details = triage_email(
                email_id, received_at, bucket, action,
                email_json, email_eml,
                ooo_tokenizer, ooo_model,
                all_sfobjects,
                contact_by_email,
                customer_survey_round_by_root_account_id,
                survey_panel_member_by_contact_id,
                survey_panel_manager_by_contact_id,
                survey_account_manager_by_contact_id,
                agency_top_level_account_by_email_domain,
                customers_client_top_level_account_by_email_domain,
                survey_client_by_customer_account_id)

            if triage_details is not None:
                triage_details = json.dumps(triage_details, default=json_serialize)
            update_cursor.execute("UPDATE email SET triage_label = %s, triage_details = %s WHERE email_id = %s", (triage_label, triage_details, email_id))
            conn.commit()

        except Exception as e:
            print(f"Error processing email {email_id}: {e}")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    args = ap.parse_args()

    main()
