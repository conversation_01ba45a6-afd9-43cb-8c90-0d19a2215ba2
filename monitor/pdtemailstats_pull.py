import psycopg2
import argparse
import datetime
import json
from lib.settings import settings
from lib import sfimport


def gather_stats(ts_conn, emails_conn, datetimefrom, datetimeto):

    sql = """
    select  date_trunc('minute', received_at),
            triage_details->>'person_type',
            triage_details->>'top_level_agency_account_name',
            count(*)
    from email
    where triage_details->>'person_type' IN ('agency', 'customerclient')
    and triage_details->>'out_of_office' = 'false'
    and triage_details->>'from_contact_id' is not null
    and action='received'
    and received_at >= cast(%s as timestamptz) and received_at < cast(%s as timestamptz)
    group by date_trunc('minute', received_at), triage_details->>'person_type', triage_details->>'top_level_agency_account_name'
    order by date_trunc('minute', received_at), triage_details->>'person_type', triage_details->>'top_level_agency_account_name'
"""
    results = []
    with emails_conn.cursor() as cursor:
        cursor.execute(sql, (datetimefrom, datetimeto))
        for datetime, person_type, agency_account, count in cursor:

            row = {
                'name': f"pdtemails-{agency_account}-{person_type}-received-Sum",
                'datasource': 'pdtemails',
                'datetime': datetime,
                'value': count,
                'dimensions': json.dumps({
                    'service': 'pdtemails',
                    'account': agency_account,
                    'person_type': person_type,
                    'metric': 'received',
                    'aggregation': 'Sum',
                }),
            }
            results.append(row)

    # clear out any old data
    with ts_conn.cursor() as cursor:
        sql = """
DELETE FROM public.timeseries
WHERE datetime >= cast(%s as timestamptz) AND datetime < cast(%s as timestamptz)
AND datasource = 'pdtemails';
"""
        cursor.execute(sql, (datetimefrom, datetimeto))
        ts_conn.commit()

    if results:
        sfimport.write_record_batch(ts_conn, 'public.timeseries', results)


def main(datetimefrom, datetimeto):
    ts_conn = psycopg2.connect(settings.monitordb_timeseries_url)
    emails_conn = psycopg2.connect(settings.monitordb_email_url)

    gather_stats(ts_conn, emails_conn, datetimefrom, datetimeto)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("datetimefrom", type=str, help="Datetime from (YYYY-MM-DDTHH:MM) inclusive")
    ap.add_argument("datetimeto", type=str, help="Datetime to (YYYY-MM-DDTHH:MM) exclusive")
    args = ap.parse_args()

    datetimefrom = datetime.datetime.strptime(args.datetimefrom, "%Y-%m-%dT%H:%M:%S")
    datetimeto = datetime.datetime.strptime(args.datetimeto, "%Y-%m-%dT%H:%M:%S")
    datetimefrom = datetimefrom.replace(tzinfo=datetime.timezone.utc)
    datetimeto = datetimeto.replace(tzinfo=datetime.timezone.utc)

    main(datetimefrom, datetimeto)
