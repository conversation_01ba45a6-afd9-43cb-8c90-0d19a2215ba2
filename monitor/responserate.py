import datetime
import psycopg2
from lib.settings import settings
from lib import sfimport
import copy
import dns.resolver



def detect_provider(exchange):
    exchange = exchange.lower().strip()

    if exchange.endswith('.protection.outlook.com.'):
        return "Outlook"

    elif exchange.endswith('.mimecast.com.'):
        return "Mimecast"

    elif exchange.endswith('.mimecast.co.za.'):
        return "Mimecast"

    elif exchange.endswith('.pphosted.com.'):
        return "Proofpoint"

    elif exchange.endswith('.ppe-hosted.com.'):
        return "Proofpoint"

    elif exchange.endswith('.google.com.'):
        return "Google"

    elif exchange.endswith('.googlemail.com.'):
        return "Google"

    elif exchange.endswith('.iphmx.com.'):
        return "Cisco Secure Email"

    elif exchange.endswith('.trendmicro.com.'):
        return "Trend Micro"

    elif exchange.endswith('.trendmicro.au.'):
        return "Trend Micro"

    elif exchange.endswith('.trendmicro.eu.'):
        return "Trend Micro"

    elif exchange.endswith('.mailcontrol.com.'):
        return "Forcepoint One Mailcontrol"

    elif exchange.endswith('.messagelabs.com.'):
        return "MessageLabs/Symantec/Broadcom"

    elif exchange.endswith('.barracudanetworks.com.'):
        return "Barracuda Networks"

    elif exchange.endswith('.sophos.com.'):
        return "Sophos Antivirus"

    elif exchange.endswith('.hornetsecurity.com.'):
        return "Hornet Security"

    elif exchange.endswith('.secureserver.net.'):
        return "GoDaddy"

    elif exchange.endswith('.amazonaws.com.'):
        return "AWS"

    return None


def get_response_rates(all_sfobjects):
    results = {}
    alldomains = set()
    for spm in all_sfobjects['SurveyPanelMember'].values():
        contact = all_sfobjects['Contact'].get(spm.ContactId)
        if not contact:
            continue
        sc = all_sfobjects['SurveyClient'].get(spm.SurveyClientId)
        if not sc:
            continue
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)
        if not cs:
            continue
        csr = all_sfobjects['CustomerSurveyRound'].get(cs.CustomerSurveyRoundId)
        if not csr:
            continue

        alldates = [
            cs.LiveSurveyStartDate,
            cs.LiveSurveyFirstRequest,
            cs.LiveSurveySecondRequest,
            cs.LiveSurveyThirdRequest,
            cs.LiveSurveyFourthRequest,
            cs.LiveSurveyEndDate,
        ]
        alldates = [d for d in alldates if d]
        if not alldates:
            continue
        startdate = min(alldates)
        enddate = max(alldates)

        if startdate <= datetime.date.today() <= enddate:
            mailbox, domain = contact.Email.lower().split('@')
            alldomains.add(domain)

            results.setdefault(sc.Id, {'name': sc.Name, 'panel_count': 0, 'panel_responded_count': 0, 'domains': set()})
            results[sc.Id]['panel_count'] += 1
            results[sc.Id]['domains'].add(domain)
            if spm.Responses:
                results[sc.Id]['panel_responded_count'] += 1

    return results, alldomains


def get_domain_mx(alldomains):
    resolver = dns.resolver.Resolver()
    resolver.lifetime = 10
    resolver.nameservers = ['1.1.1.1']

    results = {}
    for domain in alldomains:
        try:
            mx_response = resolver.resolve(domain, 'MX')
        except (dns.resolver.NoAnswer, dns.resolver.LifetimeTimeout, dns.resolver.NXDOMAIN, dns.resolver.NoNameservers) as e:
            print("nope", domain, e)
            continue

        providers = set()
        for mx in sorted(mx_response, key=lambda x: (x.preference, x.exchange)):
            provider = detect_provider(str(mx.exchange))
            if provider:
                providers.add(provider)
            else:
                providers.add(str(mx.exchange))
        results[domain] = providers

    return results


def main(all_sfobjects=None):
    if all_sfobjects is None:
        include_files={'customer_survey_round.csv',
                       'customer_survey.csv',
                       'survey_client.csv',
                       'account.csv',
                       'survey_panel_member.csv',
                       'contact.csv'}
        all_sfobjects = sfimport.load_sf_from_latest_dump(include_files=include_files)
    response_rates, alldomains = get_response_rates(all_sfobjects)
    domain_mx = get_domain_mx(alldomains)

    response_rates = list(response_rates.values())
    for item in response_rates:
        providers = set()
        for domain in item.pop('domains', []):
            providers |= set(domain_mx.get(domain, []))

        item['email_providers'] = ', '.join(sorted(providers))

    # clear out any old data
    conn = psycopg2.connect(settings.monitordb_timeseries_url)
    with conn.cursor() as cursor:
        sql = """
DELETE FROM public.response_rates
"""
        cursor.execute(sql)
        conn.commit()

    if response_rates:
        sfimport.write_record_batch(conn, 'public.response_rates', response_rates)



if __name__ == '__main__':
    main()
