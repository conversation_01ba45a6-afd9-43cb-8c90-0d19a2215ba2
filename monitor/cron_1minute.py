import datetime
from monitor.cloudwatchmetrics_pull import main as cloudwatchmetrics_pull

PULL_FREQUENCY_SECONDS = 60

def pull():
    now = datetime.datetime.now()

    # round to nearest PULL_FREQUENCY_SECONDS
    todate = now - datetime.timedelta(seconds=now.second % PULL_FREQUENCY_SECONDS, microseconds=now.microsecond)

    # but we only pull the PREVIOUS chunk since stuff might not have arrived yet
    todate -= datetime.timedelta(seconds=PULL_FREQUENCY_SECONDS)
    fromdate = todate - datetime.timedelta(seconds=PULL_FREQUENCY_SECONDS)

    cloudwatchmetrics_pull(fromdate, todate)

if __name__ == '__main__':
    pull()
