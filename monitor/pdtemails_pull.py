import requests
import json
import datetime
import psycopg2
from lib.settings import settings


MAILBOXES = {
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
}

MIN_DATETIME = datetime.datetime(2025, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc)

CRC_DOMAINS = {
    'customer-relationship.com',
    'thereferralrating.com',
    'therelationshiprating.com',
    'clientrelationship.com',
    'verityri.com',
}

def determine_action(email_details):
    fromEmails = set()
    if email_details.get('sender', {}).get('emailAddress', {}).get('address'):
        fromEmails.add(email_details['sender']['emailAddress']['address'].lower())
    if email_details.get('from', {}).get('emailAddress', {}).get('address'):
        fromEmails.add(email_details['from']['emailAddress']['address'].lower())

    # determine if this was received or sent by CRC
    action = 'received'
    for email in fromEmails:
        bits = email.lower().split('@', 1)
        if len(bits) < 2:
            continue
        email_domain = bits[1]
        if email_domain in CRC_DOMAINS:
            action = 'sent'
            break

    return action


def iter_items(url, token, params):
    headers = {'Authorization': f'Bearer {token}'}

    next_link = url
    while next_link:
        response = requests.get(next_link, headers=headers, params=params)
        params = None  # wipe params so it doesn't interfere with nextlink
        try:
            response.raise_for_status()
        except:
            print(response.text)
            raise
        result = response.json()
        for item in result['value']:
            yield item

        next_link = result.get('@odata.nextLink')


def get_access_token_client_credentials(tenant_id, client_id, client_secret):
    url = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/token"
    data = {
        'grant_type': 'client_credentials',
        'client_id': client_id,
        'client_secret': client_secret,
        'scope': 'https://graph.microsoft.com/.default',
    }
    response = requests.post(url, data=data)
    response.raise_for_status()
    return response.json()['access_token']


def get_messages_for_user(token, user, lastRecievedDateTime=None):
    url = f"https://graph.microsoft.com/v1.0/users/{user}/messages"

    params = None
    if lastRecievedDateTime:
        params = {'$filter': f'receivedDateTime gt {lastRecievedDateTime.isoformat()}'}
    return iter_items(url, token, params)


def get_raw_message(token, user, message_id):
    url = f"https://graph.microsoft.com/v1.0/users/{user}/messages/{message_id}/$value"
    response = requests.get(url, headers={'Authorization': f'Bearer {token}'})
    response.raise_for_status()
    return response.text


def get_folders_for_user(token, user):
    url = f"https://graph.microsoft.com/v1.0/users/{user}/mailFolders"
    response = requests.get(url, headers={'Authorization': f'Bearer {token}'})
    response.raise_for_status()
    return response.json()


def main():
    conn = psycopg2.connect(settings.monitordb_email_url)
    cursor = conn.cursor()

    access_token = get_access_token_client_credentials(settings.monitor_email_tenant_id,
                                                       settings.monitor_email_client_id,
                                                       settings.monitor_email_client_secret)

    for mailbox in MAILBOXES:
        bucket = mailbox.split('@')[0]

        # find the date of the last message received for this mailbox
        cursor.execute("SELECT MAX(received_at) FROM email WHERE bucket = %s", (bucket,))
        last_received_date_time = cursor.fetchone()[0]
        if not last_received_date_time:
            last_received_date_time = MIN_DATETIME

        # get all message_ids we've already received
        cursor.execute("SELECT DISTINCT message_id FROM email WHERE bucket = %s", (bucket,))
        message_ids = set(x[0] for x in cursor.fetchall())

        # check if the user exists or not!
        try:
            get_folders_for_user(access_token, mailbox)
        except Exception as e:
            print(f"Error with mailbox {mailbox}: {e}")
            continue

        batch_count = 0
        for m in get_messages_for_user(access_token, mailbox, lastRecievedDateTime=last_received_date_time):
            if m['id'] in message_ids:
                continue
            if m.get('isDraft'): # skip draft messages
                continue

            eml = get_raw_message(access_token, mailbox, m['id'])
            action = determine_action(m)

            print(mailbox, bucket, action, m['receivedDateTime'], m['id'])

            # insert new instance
            sql = """
            INSERT INTO email (message_id, received_at, bucket, action, json, eml)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (m['id'], m['receivedDateTime'], bucket, action, json.dumps(m), eml))

            if batch_count > 100:
                conn.commit()
                batch_count = 0
            else:
                batch_count += 1

        if batch_count:
            conn.commit()

if __name__ == '__main__':
    main()
